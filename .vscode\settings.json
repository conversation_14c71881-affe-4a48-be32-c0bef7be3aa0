{"files.associations": {"*.svg": "xml", "qapplication": "cpp", "qcloseevent": "cpp", "qelapsedtimer": "cpp", "qwidget": "cpp", "qscrollarea": "cpp", "xstring": "cpp", "fstream": "cpp", "xiosbase": "cpp", "qdatetime": "cpp", "algorithm": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "bitset": "cpp", "cctype": "cpp", "charconv": "cpp", "chrono": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "deque": "cpp", "exception": "cpp", "expected": "cpp", "filesystem": "cpp", "format": "cpp", "forward_list": "cpp", "functional": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "mutex": "cpp", "new": "cpp", "numeric": "cpp", "optional": "cpp", "ostream": "cpp", "queue": "cpp", "random": "cpp", "ranges": "cpp", "ratio": "cpp", "semaphore": "cpp", "set": "cpp", "source_location": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stdfloat": "cpp", "stop_token": "cpp", "streambuf": "cpp", "string": "cpp", "system_error": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "utility": "cpp", "valarray": "cpp", "variant": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp"}}