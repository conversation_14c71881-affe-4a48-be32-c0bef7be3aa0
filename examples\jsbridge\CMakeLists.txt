﻿
add_executable(jsbridge_demo
        ${CMAKE_SOURCE_DIR}/examples/jsbridge/JsbridgeDemo.cpp
        ${CMAKE_SOURCE_DIR}/src/components/jsbridge/JSBridge.cpp
        ${CMAKE_SOURCE_DIR}/src/components/jsbridge/handler/JSBridgeHandlerFactory.cpp
        ${CMAKE_SOURCE_DIR}/src/utils/Uuid.cpp
        ${CMAKE_SOURCE_DIR}/src/jsbridge_handler/FileJSBridgeHandler.cpp
        ${CMAKE_SOURCE_DIR}/src/diagnostic/log/Log.cpp)

find_package(Qt6 COMPONENTS
        Core
        Gui
        Widgets
        REQUIRED)

target_link_libraries(jsbridge_demo
        Qt::Core
        Qt::Gui
        Qt::Widgets
        QCefView
)

#
# 复制webview目录到输出目录
add_custom_command(TARGET jsbridge_demo POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_directory
        "${CMAKE_SOURCE_DIR}/examples/jsbridge/webview"
        "$<TARGET_FILE_DIR:jsbridge_demo>/webview"
        COMMENT "Copying webview directory to output directory"
)
#
# 复制QCefView的依赖文件到输出目录
# 检查CEF依赖文件目录是否存在
set(QCEFVIEW_BIN_DIR "${QCEFVIEW_LIB_DIR}/bin")
if(EXISTS "${QCEFVIEW_BIN_DIR}/CefView")
    add_custom_command(TARGET jsbridge_demo POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_directory
            "${QCEFVIEW_BIN_DIR}/CefView"
            "$<TARGET_FILE_DIR:jsbridge_demo>/CefView"
            COMMENT "Copying QCefView dependencies (${QCEFVIEW_BUILD_TYPE})"
    )
    message(STATUS "QCefView dependencies will be copied from: ${QCEFVIEW_BIN_DIR}/CefView")
else()
    message(WARNING "QCefView dependencies directory not found: ${QCEFVIEW_BIN_DIR}/CefView")
endif()

# 复制QCefView DLL文件
if(EXISTS "${QCEFVIEW_BIN_DIR}/QCefView.dll")
    add_custom_command(TARGET jsbridge_demo POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${QCEFVIEW_BIN_DIR}/QCefView.dll"
            "$<TARGET_FILE_DIR:jsbridge_demo>"
            COMMENT "Copying QCefView.dll"
    )
else()
    message(WARNING "QCefView.dll not found: ${QCEFVIEW_BIN_DIR}/QCefView.dll")
endif()
#
## 复制zmq动态库到可执行文件目录（仅文件不存在时复制）
#add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
#        COMMAND ${CMAKE_COMMAND} -E copy_if_different
#        ${PROJECT_SOURCE_DIR}/3rd/win/libzmq/lib/libzmq-v143-mt-gd-4_3_6.dll
#        $<TARGET_FILE_DIR:${PROJECT_NAME}>)
#
## 设置目标属性
#set_target_properties(${PROJECT_NAME} PROPERTIES
#        WIN32_EXECUTABLE TRUE
#)

# Qt6 部署配置
if (QT_VERSION_MAJOR EQUAL 6 AND WIN32)
    # 查找windeployqt工具
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)

    if(WINDEPLOYQT_EXECUTABLE)
        # 使用windeployqt自动部署Qt依赖（仅复制文件，不运行程序）
        add_custom_command(TARGET jsbridge_demo POST_BUILD
                COMMAND ${WINDEPLOYQT_EXECUTABLE} --no-translations --no-system-d3d-compiler --no-opengl-sw --qmldir "${CMAKE_SOURCE_DIR}/resources/qml" $<TARGET_FILE:jsbridge_demo>
                COMMENT "Deploying Qt libraries with windeployqt"
                VERBATIM
        )
        message(STATUS "windeployqt found: ${WINDEPLOYQT_EXECUTABLE}")
    else()
        message(WARNING "windeployqt not found. You may need to manually copy Qt DLLs.")

        # 手动复制关键的Qt DLL文件
        add_custom_command(TARGET jsbridge_demo POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${Qt6_DIR}/../../../bin/Qt6Core.dll"
                "${Qt6_DIR}/../../../bin/Qt6Gui.dll"
                "${Qt6_DIR}/../../../bin/Qt6Widgets.dll"
                "${Qt6_DIR}/../../../bin/Qt6Svg.dll"
                "${Qt6_DIR}/../../../bin/Qt6Qml.dll"
                "${Qt6_DIR}/../../../bin/Qt6Quick.dll"
                "${Qt6_DIR}/../../../bin/Qt6Xml.dll"
                $<TARGET_FILE_DIR:jsbridge_demo>
                COMMENT "Manually copying Qt DLLs"
                VERBATIM
        )
    endif()
endif()