﻿//
// Created by HLJY on 2025/6/16.
//
#include <QApplication>
#include <QCefContext.h>
#include <QCefSetting.h>
#include <iostream>
#include <QHBoxLayout>
#include <QMainWindow>
#include <QMessageBox>
#include <QPushButton>

#include "src/components/jsbridge/JSBridge.h"
#include "src/jsbridge_handler/FileJSBridgeHandler.h"
#include "src/diagnostic/log/Log.h"

void myMessageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg) {
    qDebug() << "myMessageOutput: " << msg << " , type: " << type << " , category: " << context.category << " , line: " << context.line
            << " , file: " << context.file << " , function: " << context.function;
}


QMainWindow *_window;

QWidget *_centralWidget;

QHBoxLayout *_cefLayout;
QCefView *_cefView;


void createWindow() {
    // 创建主窗口
    _window = new QMainWindow();

    // 创建一个 QWidget 作为中心部件
    _centralWidget = new QWidget(_window);

    // 创建第一个横向布局用于原有四个按钮
    QHBoxLayout *buttonLayout1 = new QHBoxLayout();

    // 创建四个按钮
    QPushButton *button1 = new QPushButton("QT调用JS方法（无返回值）");
    button1->setMinimumSize(100, 100);
    QObject::connect(button1, &QPushButton::clicked, []() {
        // 生成随机颜色
        QString color = QString("#%1%2%3")
                          .arg(rand() % 256, 2, 16, QChar('0'))
                          .arg(rand() % 256, 2, 16, QChar('0'))
                          .arg(rand() % 256, 2, 16, QChar('0'));
        nlohmann::json j = {
            {"color", color.toStdString()}
        };
        JSBridge::callJs("cefViewName", JSBridgeMsg::newRequest("colorChange",j), [](const JSBridgeContext &result) {
            qDebug() << "callJs response: " << result.getData().serialize();
        });
    });

    QPushButton *button2 = new QPushButton("关闭QCefView");
    button2->setMinimumSize(100, 100);
    QObject::connect(button2, &QPushButton::clicked, []() {
        _cefView->close();
//        delete _cefView;
    });

    QPushButton *button3 = new QPushButton("QT调用JS方法（超时）");
    button3->setMinimumSize(100, 100);
    QObject::connect(button3, &QPushButton::clicked, [button3]() {
        JSBridge::callJs("cefViewName", JSBridgeMsg::newRequest("queryTest", nlohmann::json::object()), [](const JSBridgeContext &result) {
            qDebug() << "callJs response: " << result.getData().serialize();
        }, 1, [button3](const JSBridgeMsg &result) {
            QMetaObject::invokeMethod(button3, [result]() {
                QMessageBox::information(nullptr, // 父窗口，nullptr表示没有父窗口（独立窗口）
                                         "QT调用JS方法（超时）", // 弹窗标题
                                         QString::fromStdString(result.serialize())); // 弹窗内容
            });
        });
    });

    QPushButton *button4 = new QPushButton("按钮4");
    button4->setMinimumSize(100, 100);

    // 将按钮添加到第一个横向布局中
    buttonLayout1->addWidget(button1);
    buttonLayout1->addWidget(button2);
    buttonLayout1->addWidget(button3);
    buttonLayout1->addWidget(button4);

    // 创建第二个横向布局，暂时留空
    _cefLayout = new QHBoxLayout();

    // 创建一个垂直布局，将两个横向布局依次加入
    QVBoxLayout *mainLayout = new QVBoxLayout(_centralWidget);
    mainLayout->addLayout(buttonLayout1);
    mainLayout->addLayout(_cefLayout);

    // 设置中心部件
    _window->setCentralWidget(_centralWidget);

    // 设置窗口标题和大小
    _window->setWindowTitle("jsbridge demo");
    _window->resize(800, 800);

    // 显示窗口
    _window->show();
}

void registerHandler() {
    JSBridge::registerHandler("TestMethod", [](const JSBridgeContext &) {
        qDebug() << "TestMethod Called";
        // 显示一个信息弹窗
        QMessageBox::information(nullptr, // 父窗口，nullptr表示没有父窗口（独立窗口）
                                 "标题", // 弹窗标题
                                 "这是一个信息弹窗！（来自QT）"); // 弹窗内容
    });
    JSBridge::registerAsyncHandler("TestAsyncMethod", [](const JSBridgeContext &con) {
        qDebug() << "TestAsyncMethod Called";

        QThread::sleep(2);

        QMetaObject::invokeMethod(con.getCefView(), [con]() {
            QMessageBox::information(nullptr, // 父窗口，nullptr表示没有父窗口（独立窗口）
                                     "标题", // 弹窗标题
                                     "这是一个信息弹窗！（来自QT）"); // 弹窗内容
        });
    });

    JSBridge::registerHandler("developtool", [](const JSBridgeContext &con) {
        con.getCefView()->showDevTools();
    });

    JSBridge::registerHandler("reload", [](const JSBridgeContext &con) {
        con.getCefView()->browserReload();
    });


    JSBridge::registerHandler("queryTest", [](const JSBridgeContext &con) {
        con.setResult(JSBridgeMsg::newResponse(con.getData(), "来自QT的返回结果" + con.getData().getData()["request"].get<std::string>()));
    });


    JSBridge::registerAsyncHandler("queryTestAsync", [](const JSBridgeContext &con) {
        qDebug() << "queryTestAsync Called";

        QThread::sleep(60);

        con.setResult(JSBridgeMsg::newResponse(con.getData(), "来自QT的返回结果" + con.getData().getData()["request"].get<std::string>()));
    });

    FileJSBridgeHandler file_js_bridge_handler;
    file_js_bridge_handler.registerHandler();
}

QCefContext *_qCefContext;

QCefConfig *_config;

void initCefContext(int argc, char **argv, QApplication &a) {
    // Build a QCefConfig object
    _config = new QCefConfig();
    // Set user agent
    _config->setUserAgent("QCefViewTest");
    // Set log level
    _config->setLogLevel(QCefConfig::LOGSEVERITY_DEFAULT);
    // Set JSBridge object name (default value is QCefViewClient)
    _config->setBridgeObjectName("CallBridge");
    // Port for remote debugging (default is 0, disabling remote debugging)
    _config->setRemoteDebuggingPort(9000);
    // Set background color for all browsers
    // (QCefSetting.setBackgroundColor will overwrite this value for a specific browser instance)
    _config->setBackgroundColor(Qt::lightGray);

    // WindowlessRenderingEnabled is true by default, disable OSR mode by setting it to false
    _config->setWindowlessRenderingEnabled(true);

    // Add command line arguments (any CEF-supported switches or parameters)
    _config->addCommandLineSwitch("use-mock-keychain");
    // config.addCommandLineSwitch("disable-spell-checking");
    // config.addCommandLineSwitch("disable-site-isolation-trials");
    // config.addCommandLineSwitch("enable-aggressive-domstorage-flushing");
    _config->addCommandLineSwitchWithValue("renderer-process-limit", "1");
    // config.addCommandLineSwitchWithValue("disable-features", "BlinkGenPropertyTrees,TranslateUI,site-per-process");
    /**
    *   "--disable-web-security "          // 禁用同源策略
    *    "--allow-file-access-from-files "  // 允许文件访问
    *    "--allow-universal-access-from-files"; // 允许跨域文件访问
    */
    _config->addCommandLineSwitch("--disable-web-security");
    _config->addCommandLineSwitch("--allow-file-access-from-files");
    _config->addCommandLineSwitch("--allow-universal-access-from-files");
    // Create the QCefContext instance, passing the QApplication instance and config
    // The lifecycle of cefContext must match the QApplication instance's lifecycle
    _qCefContext = new QCefContext(&a, argc, argv, _config);
}

QCefSetting *_settings;

void createCefView() {
    // // 创建QCefView设置
    _settings = new QCefSetting();
    _settings->setWebGL(true); // 启用WebGL
    _settings->setJavascript(true); // 启用JavaScript
    _settings->setLocalStorage(true); // 启用本地存储
    _settings->setWindowlessFrameRate(60); // 设置帧率为60FPS
    // settings.setBackgroundColor(Qt::transparent); // 设置背景为透明
    // 创建QCefView小部件
    QString uri = QCoreApplication::applicationDirPath() + "/webView/QCefViewPage.html";

    qDebug() << "uri: " << uri;

    _cefView = new QCefView(uri, _settings, _window);
    _cefView->setMinimumSize(200, 200);
//    _cefView->setAttribute( Qt::WA_DeleteOnClose, true ); // Widget will be deleted automatically when closed

    _cefLayout->addWidget(_cefView);

    JSBridge::init(_cefView, "cefViewName");
}

int main(int argc, char *argv[]) {
//    qInstallMessageHandler(myMessageOutput);

    Log::init();

    registerHandler();

    QApplication a(argc, argv);

    initCefContext(argc, argv, a);

    createWindow();

    createCefView();


    return a.exec();
}
