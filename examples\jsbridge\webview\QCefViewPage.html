﻿<html>

<head>
  <script type="text/javascript">
    function stringToBase64(str) {
      // 首先，使用encodeURIComponent对字符串进行编码，将非ASCII字符转为UTF-8的字节序列（以%xx表示）
      // 然后，我们将%xx替换为单字节字符，这样就能用btoa进行编码
      const utf8Bytes = encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
        return String.fromCharCode(parseInt(p1, 16));
      });
      return btoa(utf8Bytes);
    }

    function saveFileChunk(dir) {
      // 循环生成100个文件块，每个文件块100KB
      /**
       * 参数是json格式
       * filePath 字段 表示文件存储路径
       * chunkIndex 字段 表示文件块的索引（从1开始递增）
       * chunkContent 字段 标识文件块内容，base64编码
       * totalChunks 字段 表示文件总块数。该字段用于判断是否所有分片都已接收。不是所有的chunk都需要包括该字段，但至少一个chunk需要包含该字段。
       */
      for (var i = 1; i <= 99; i++) {
        const KB_100 = 2024;
        const chunkContent = Array.from({ length: KB_100 }, (_, idx) => `test${i}-${idx}`).join('');
        var chunk = {
          filePath: `${dir}/test-中文-test.txt`,
          chunkIndex: i,
          chunkContent: stringToBase64(chunkContent),
          totalChunks: 100

        }
        var query = {
          request: JSON.stringify({
            // 随机id
            "id": "tmid-" + Math.random().toString(36).substr(2, 9),
            "method": "saveFileChunk",
            "data": chunk,
            "message": ""
          }),
          onSuccess: function (response) {
            console.log("Success: " + response);
          },
          onFailure: function (error_code, error_message) {
            alert("Failure: " + error_message + " (Error Code: " + error_code + ")");
          },
        };
        const result = window.cefViewQuery(query);
      }

      console.log("开始写入文件-最后一个");
      var chunkContent = "！！！结束！！！"
      var chunk = {
        filePath: `${dir}/test-中文-test.txt`,
        chunkIndex: 100,
        chunkContent: stringToBase64(chunkContent),
        totalChunks: 100
      }
      var query = {
        request: JSON.stringify({
          // 随机id
          "id": "tmid-" + Math.random().toString(36).substr(2, 9),
          "method": "saveFileChunk",
          "data": chunk,
          "message": ""
        }),
        onSuccess: function (response) {
          console.log("Success: " + response);
        },
        onFailure: function (error_code, error_message) {
          alert("Failure: " + error_message + " (Error Code: " + error_code + ")");
        },
      };
      const result = window.cefViewQuery(query);
    }

    function onSaveFileClicked() {
      // 弹出文件保存对话框
      var query = {
        request: JSON.stringify({
          "id": "tmid-" + Math.random().toString(36).substr(2, 9),
          "method": "showSaveFileDialog",
          "data": '{"dialogTitle": "哈哈哈哈"}',
          "message": ""
        }),
        onSuccess: function (response) {
          // alert("Success: " + response);
          console.log(response);

          const dir = JSON.parse(response)["data"]["selectedDir"];
          if (dir == null || dir == "") {
            return;
          }
          else {
            saveFileChunk(dir);
          }

        },
        onFailure: function (error_code, error_message) {
          alert("Failure: " + error_message + " (Error Code: " + error_code + ")");
        },
      }
      const result = window.cefViewQuery(query);
    }

    function jsCallQtNoReturn() {
      onInvokeMethodClicked('TestMethod', JSON.stringify({
        "id": "1111",
        "method": "TestMethod",
        "data": null,
        "message": ""
      }));
    }
    function jsCallQtAsyncNoReturn() {
      onInvokeMethodClicked('TestAsyncMethod', JSON.stringify({
        "id": "1111",
        "method": "TestAsyncMethod",
        "data": null,
        "message": ""
      }));
    }
    function onInvokeMethodClicked(name, ...arg) {
      // 调用 C++ 代码
      try {
        const result = CallBridge.invoke(name, ...arg);
        console.log(`onInvokeMethodClicked result ${result}`);
      } catch (error) {
        alert(`调用失败！\n异常信息: ${error.message}\n堆栈跟踪: ${error.stack}`);
      }
    }

    //捕获全局键盘事件,若将window.onkeydown改成document.onkeydown则只在当前页面捕获
    window.onkeydown = function (event) {
      var keyCode = event.keyCode || event.which || event.charCode;
      //捕获F12
      if (keyCode == 123) {
        console.log("========>>>打开开发者界面");
        event.preventDefault();//阻止默认事件
        onInvokeMethodClicked("developtool", JSON.stringify({
          "id": "1111",
          "method": "developtool",
          "data": {},
          "message": ""
        }));//调用c++代码打开开发者工具
      }
      // F5
      if (keyCode == 116) {
        console.log("========>>>重新加载界面");
        event.preventDefault();//阻止默认事件
        onInvokeMethodClicked("developtool", JSON.stringify({
          "id": "1111",
          "method": "developtool",
          "data": {},
          "message": ""
        }));//调用c++代码打开开发者工具
        onInvokeMethodClicked("reload", JSON.stringify({
          "id": "1111",
          "method": "reload",
          "data": {},
          "message": ""
        }));//调用c++代码打开开发者工具
      }
      // event.preventDefault(); // 注意：阻止默认事件不能放在外面，会阻止浏览器或者input/textarea的默认事件，应该放在相应的按键组合中去阻止
      return false;
    }

    function onLoad() {
      // 增加一个是将监听,监听一个叫colorChange的函数调用
      window.CallBridge.addEventListener(
              // event name
              "colorChange",
              // event handler
              function (data) {
                // change the background color
                console.log("接收到的数据", data);
                data = JSON.parse(data);

                console.log(
                        "接收到c++请求设置的颜色: ", data.data.color
                );
                document.getElementById("main").style.backgroundColor = data.data.color;

                data["response"] = "hello from js";
                // json序列化为字符串
                const jsonString = JSON.stringify(data);
                onInvokeMethodClicked("__js_callback__", jsonString);
              }
      );
    }

    function onCallBridgeQueryClicked() {
      var query = {
        request: JSON.stringify({
          "id": "1111",
          "method": "queryTest",
          "data": {
            "request": "有返回值的请求-同步"
          },
          "message": ""
        }),
        onSuccess: function (response) {
          alert("Success: " + response);
        },
        onFailure: function (error_code, error_message) {
          alert("Failure: " + error_message + " (Error Code: " + error_code + ")");
        },
      };
      const result = window.cefViewQuery(query);
      console.log(`called: ${result}`);
    }
    function onCallBridgeQueryClickedAsync() {
      var query = {
        request: JSON.stringify({
          "id": "1111",
          "method": "queryTestAsync",
          "data": {
            "request": "有返回值的请求-异步"
          },
          "message": ""
        }),
        onSuccess: function (response) {
          alert("Success: " + response);
        },
        onFailure: function (error_code, error_message) {
          alert("Failure: " + error_message + " (Error Code: " + error_code + ")");
        },
      };
      const result = window.cefViewQuery(query);
      console.log(`called: ${result}`);
    }
  </script>
</head>

<body onload="onLoad()" id="main" class="noselect">
<h1 align="center" style="font-size: 12pt">Web Area</h1>
<label> Test Case for InvokeMethod </label>
<br />
<input id="test_input" type="button" value="JS调QT（无返回值）" onclick="jsCallQtNoReturn()" />
<input id="test_input_async" type="button" value="JS调QT - 异步 （无返回值）" onclick="jsCallQtAsyncNoReturn()" />
<br />
<br />
<br />

<label> Test Case for QCefQuery </label>
<br />
<br />
<input type="button" value="JS调用QT（带返回值）" onclick="onCallBridgeQueryClicked()" />
<input type="button" value="JS调用QT - 异步（带返回值）" onclick="onCallBridgeQueryClickedAsync()" />

<br />
<br />
<input type="button" value="保存文件" onclick="onSaveFileClicked()" />

<video id="videoPlayer" width="160" height="120" controls>
  <source src="file:\\\C:\\Users\\<USER>\\Downloads\\20250701-163156.mp4">
  Your browser does not support the video tag.
</video>

<iframe src="https://html5test.com/" width="100%" height="100%"></iframe>

</body>

</html>
