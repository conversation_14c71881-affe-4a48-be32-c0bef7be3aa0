
find_package(Qt6 COMPONENTS Widgets Core Gui Svg SvgWidgets Qml Quick Xml Network QUIET)

# 服务端
# 设置可执行文件路径
add_executable(server_demo
        ${CMAKE_SOURCE_DIR}/examples/zmq/ServerDemo.cpp
        ${CMAKE_SOURCE_DIR}/src/components/zmq/server/ZmqServer.cpp
        ${CMAKE_SOURCE_DIR}/src/components/zmq/common/ZmqCommon.cpp
        ${CMAKE_SOURCE_DIR}/src/utils/Uuid.cpp
        ${CMAKE_SOURCE_DIR}/src/diagnostic/log/Log.cpp
)
#target_link_libraries(server_demo ${ZEROMQ_LIBRARIES} pthread)
target_link_libraries(server_demo
        Qt6::Widgets
        Qt6::Core
        Qt6::Gui
        Qt6::Svg
        Qt6::SvgWidgets
        Qt6::Qml
        Qt6::Quick
        Qt6::Xml
        Qt6::Network
        # zmq
        ${ZMQ_LIB_NAME} )

# 客户端
add_executable(client_demo
        ${CMAKE_SOURCE_DIR}/examples/zmq/ClientDemo.cpp
        ${CMAKE_SOURCE_DIR}/src/components/zmq/client/ZmqClient.cpp
        ${CMAKE_SOURCE_DIR}/src/components/zmq/common/ZmqCommon.cpp
        ${CMAKE_SOURCE_DIR}/src/utils/Uuid.cpp
        ${CMAKE_SOURCE_DIR}/src/diagnostic/log/Log.cpp

)
#target_link_libraries(client_demo ${ZEROMQ_LIBRARIES} pthread)
target_link_libraries(client_demo
        Qt6::Widgets
        Qt6::Core
        Qt6::Gui
        Qt6::Svg
        Qt6::SvgWidgets
        Qt6::Qml
        Qt6::Quick
        Qt6::Xml
        Qt6::Network
        # zmq
        ${ZMQ_LIB_NAME} )

# 复制动态库到可执行文件目录（仅文件不存在时复制）
# 复制zmq动态库到可执行文件目录（仅文件不存在时复制）
add_custom_command(TARGET client_demo POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${ZMQ_LIB_DIR}/bin/${ZMQ_LIB_NAME}.dll
        $<TARGET_FILE_DIR:client_demo>)

add_custom_command(TARGET server_demo POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        ${ZMQ_LIB_DIR}/bin/${ZMQ_LIB_NAME}.dll
        $<TARGET_FILE_DIR:server_demo>)