﻿//  Asynchronous client-to-server (DEALER to ROUTER)
//
//  While this example runs in a single process, that is to make
//  it easier to start and stop the example. Each task has its own
//  context and conceptually acts as a separate process.

#include <thread>
#include <functional>
#include <string>
#include <mutex>
#include <iostream>
#include <chrono>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>
#include "src/global.h"

#include "zhelpers.hpp"
#include "src/components/zmq/client/ZmqClient.h"
#include "src/components/zmq/common/ZmqCommon.h"
#include "src/diagnostic/log/Log.h"


int main() {
    Log::init();

    s_version();

    // Create client
    // ipc放到用户目录下，避免权限问题
    QString userDir = QStandardPaths::writableLocation(QStandardPaths::HomeLocation);
    if (!QDir(userDir).exists()) {
        qDebug() << "【ipc】 Directory does not exist." << userDir;
    }
    // 命名规则：.hl-ipc-${项目名}
    QString projectDir = ".hl-ipc-hl-white-board";
    QString fullPath = QDir(userDir).filePath(projectDir);
    QString ipcPath = QString("ipc://%1").arg(QDir::cleanPath(fullPath));

    ZmqClient client(ipcPath.toStdString(), "client_demo");

    // Register a handler for a specific method
    client.registerHandler("clientMethod", [](const ZmqMsg &request, ZmqResponseCallback callback) {
        // Process the request
        qDebug() << "=============== Client Received request: " << request.serialize();

        nlohmann::json j = request.getData();
        j["response"] = "Response from client";
        auto response = ZmqMsg::newResponse(request, j);
        callback(response);
    });

    client.start();

    static std::atomic<int> unrespondedCount{0};


    // Create sender thread
    auto clientSender = [&client]() {
        // 统计未接收到响应的数量

        for (int i = 0; i < 10000; ++i) {
            // 回车后发送消息
            qDebug() << "Press Enter to send a message...";
            std::cin.get();

//            // sleep 10ms
//            std::this_thread::sleep_for(std::chrono::milliseconds(10));
//

//            {
//                auto msg = ZmqMsg::newRequest("win-restore", nlohmann::json{
//                });
//                client.sendRequest(msg, [](const ZmqMsg &response) {
//                    std::cout << "Client Received response: " << response << std::endl;
//                });
//            }

            nlohmann::json j = {
                {"request", "Request from Client: " + std::to_string(i)},
            };
            ZmqMsg msg = ZmqMsg::newRequest("serverMethod", j);

            unrespondedCount.fetch_add(1);
            client.sendRequest(msg, [](const ZmqMsg &response) {
                unrespondedCount.fetch_sub(1);
                qDebug() << "=================== Client Received response: " << response.serialize() << "\n\n";
            }, 1, [](const ZmqMsg &response){
                qWarning() << "=================== Client Received timeout response: " << response.serialize() << "\n\n";
            });

            // 每10000条消息检查一次未接收到响应的数量
            if (i % 10000 == 0) {
                 qWarning() << "+++++++++++++++++++Unresponded count: " << unrespondedCount;
            }
        }
    };

    // Start sender thread
    std::thread t1(clientSender);
    // Cleanup
    t1.join();

    qDebug() << "Press Enter to exit...";

    std::cin.get();

    qDebug() << "Exiting..." + std::to_string(unrespondedCount);

    client.stop();



    return 0;
}
