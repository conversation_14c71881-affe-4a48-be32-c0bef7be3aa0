﻿//
//  Weather update server in C++
//  Binds PUB socket to tcp://*:5556
//  Publishes random weather updates
//
#include <zmq.hpp>
#include <stdio.h>
#include <stdlib.h>
#include <random>
#include <iostream>
#include <vector>
#include <thread>
#include <memory>
#include <functional>
#include <string>
#include <chrono>
#include "src/global.h"
#include <QStandardPaths>
#include <QDir>
#include "zhelpers.hpp"
#include "src/components/zmq/server/ZmqServer.h"
#include "src/components/zmq/common/ZmqCommon.h"
#include "src/diagnostic/log/Log.h"

#define within(num) (int) ((float) num * random () / (RAND_MAX + 1.0))

using json = nlohmann::json;

int main() {
    Log::init();

    s_version();

    // Create server with 2 worker threads
    // ipc放到用户目录下，避免权限问题
    QString userDir = QStandardPaths::writableLocation(QStandardPaths::HomeLocation);
    if (!QDir(userDir).exists()) {
        qDebug() << "【ipc】 Directory does not exist." << userDir;
    }
    // 命名规则：.hl-ipc-${项目名}
    QString projectDir = ".hl-ipc-hl-white-board";
    QString fullPath = QDir(userDir).filePath(projectDir);
    QString ipcPath = QString("ipc://%1").arg(QDir::cleanPath(fullPath));
    ZmqServer server(ipcPath.toStdString());

    // Register a handler for a specific method
    server.registerHandler("serverMethod", [](const ZmqMsg &request, ZmqResponseCallback callback) {
        // Process the request
        std::cout << "==================== Server Received request 中文: " << request << std::endl;
        // sleep 1s
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));

        nlohmann::json j = request.getData();
        j["response"] = "Response from server";
        auto response = ZmqMsg::newResponse(request, j);
        callback(response);
    });


    server.start();

    static std::atomic<int> unrespondedCount{0};


    // Create sender thread
    auto serverSender = [&server]() {
        // 回车后发送消息
        for (int i = 0; i < 10000; ++i) {
            qDebug() << "Press Enter to send a message...";
            std::cin.get();
            // sleep 10ms
            std::this_thread::sleep_for(std::chrono::milliseconds(10));

            nlohmann::json j = {
                {"request", " 中文  Request from Server: " + std::to_string(i)},
            };
            ZmqMsg msg = ZmqMsg::newRequest("clientMethod", j);

            unrespondedCount.fetch_add(1);
            server.sendRequest("client_demo", msg, [](const ZmqMsg &response) {
                unrespondedCount.fetch_sub(1);
                qDebug() << "================== Server Received response 中文: " << response.serialize();
            });
        }
    };

    // Start sender thread
    std::thread t1(serverSender);

    // Cleanup
    t1.join();

    server.stop();

    return 0;
}
