#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QEvent>
#include <QMouseEvent>
#include <QRegion>
#include "src/whiteboard/core/WhiteBoardTypes.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

// 前向声明
class WhiteboardView;

/**
 * @brief 主窗口类
 *
 * 重构后的主窗口类，使用WhiteboardView组件作为核心UI，
 * 实现全屏无边框显示模式，专注于白板功能。
 * 支持部分区域鼠标穿透功能。
 */
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    MainWindow(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~MainWindow();

    /**
     * @brief 设置穿透模式
     * @param enabled 是否启用穿透
     */
    void setTransparencyEnabled(bool enabled);
    
    /**
     * @brief 获取穿透模式状态
     * @return 是否启用穿透
     */
    bool isTransparencyEnabled() const { return m_transparencyEnabled; }

    /**
     * @brief 获取白板视图组件
     * @return WhiteboardView指针
     */
    WhiteboardView* whiteboardView() const { return m_whiteboardView; }

protected:
    /**
     * @brief 重写事件处理函数实现部分穿透
     * @param event 事件对象
     * @return 是否处理了事件
     */
    bool event(QEvent* event) override;

    /**
     * @brief 窗口关闭事件处理
     * @param event 关闭事件
     */
    void closeEvent(QCloseEvent *event) override;

private slots:
    /**
     * @brief 响应工具变化，自动切换穿透模式
     * @param toolType 当前选中的工具类型
     */
    void onCurrentToolChanged(ToolType toolType);

private:
    /**
     * @brief 初始化窗口设置
     */
    void setupWindow();
    
    /**
     * @brief 初始化白板视图
     */
    void setupWhiteboardView();

    /**
     * @brief 初始化ZMQ
     */
    void setupZMQ();
    
    /**
     * @brief 设置穿透区域
     * @param region 穿透区域
     */
    void setTransparentRegion(const QRegion& region);
    
    /**
     * @brief 添加穿透形状
     * @param rect 矩形区域
     * @param shapeType 形状类型
     */
    void addTransparentShape(const QRect& rect, QRegion::RegionType shapeType = QRegion::Rectangle);
    
    /**
     * @brief 更新穿透状态
     */
    void updateTransparency();
    
    /**
     * @brief 更新穿透区域（简化版本）
     */
    void updateTransparentRegions();
    
    /**
     * @brief 检查点是否在穿透区域
     * @param pos 点坐标
     * @return 是否穿透
     */
    bool isPointTransparent(const QPoint& pos) const;
    
    /**
     * @brief 连接信号和槽
     */
    void connectSignalsAndSlots();

    /**
     * @brief 根据工具类型判断是否应该启用穿透模式
     * @param toolType 工具类型
     * @return 是否应该穿透
     */
    bool shouldEnableTransparencyForTool(ToolType toolType) const;

    void keyPressEvent(QKeyEvent *event);

    Ui::MainWindow *ui;                     ///< UI对象
    WhiteboardView* m_whiteboardView;       ///< 白板视图组件
    QRegion m_transparentRegion;            ///< 穿透区域
    bool m_transparencyEnabled;             ///< 是否启用穿透
    ToolType m_currentTool;                 ///< 当前工具类型
};

#endif // MAINWINDOW_H
