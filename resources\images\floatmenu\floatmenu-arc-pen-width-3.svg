<?xml version="1.0" encoding="UTF-8"?>
<svg width="148px" height="220px" viewBox="0 0 148 220" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 45</title>
    <defs>
        <path d="M96.7342634,14.1253931 C108.853892,2.82799511 127.837143,3.49454474 139.134541,15.6141734 C150.431939,27.733802 149.765389,46.7170529 137.645761,58.0144509 C124.32655,70.4300475 113.125339,85.4946044 104.011746,103.365626 C89.167262,132.474465 81.2672329,160.311608 80.0392674,187.012913 C79.2781017,203.563962 65.2437786,216.364191 48.6927295,215.603026 C32.1416804,214.84186 19.3414508,200.807537 20.1026165,184.256488 C21.7410896,148.628964 31.9846336,112.534034 50.5608577,76.1075532 C62.7934832,52.1203587 78.1947449,31.4071371 96.7342634,14.1253931 Z" id="path-1"></path>
        <filter x="-2.4%" y="-1.4%" width="104.7%" height="102.9%" filterUnits="objectBoundingBox" id="filter-2">
            <feGaussianBlur stdDeviation="2.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="1" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.37254902   0 0 0 0 0.321568627   0 0 0 0 0.890196078  0 0 0 0.592848558 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
    </defs>
    <g id="V1.2.0" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="白板备份" transform="translate(-2019.3407, -1045.24)">
            <g id="编组-45" transform="translate(2009.3407, 1045.24)">
                <g id="路径-2备份" opacity="0.25" fill-rule="nonzero" transform="translate(83.6302, 110.8525) rotate(6) translate(-83.6302, -110.8525)">
                    <use fill="#FFFFFF" xlink:href="#path-1"></use>
                    <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                </g>
                <path d="M38.0608985,134.387228 C50.657896,122.07432 66.6458504,122.07432 76.1088073,132.128621 C85.5717641,142.182922 85.5717641,157.054567 72.3292967,170.797074 C69.2637036,173.978425 62.5004712,181.84215 60.5488405,184.213058 C51.8076503,194.832157 38.0608985,203.679058 23.657896,194.290179 C9.25489343,184.901301 11.0680873,168.64734 18.5676976,157.054567 C21.416792,152.650485 24.5369718,148.525684 27.9270239,144.690672 C30.53356,141.742016 34.0564961,138.301323 38.0608985,134.387228 Z" id="路径-2备份-14" fill="#5F52E3" fill-rule="nonzero" transform="translate(47.8228, 161.5551) rotate(-27) translate(-47.8228, -161.5551)"></path>
                <circle id="椭圆形备份-22" fill="#FFFFFF" transform="translate(47.592, 161.095) rotate(6) translate(-47.592, -161.095)" cx="47.5920181" cy="161.094964" r="10"></circle>
                <circle id="椭圆形备份-23" fill="#FFFFFF" transform="translate(70.8751, 102.2061) rotate(6) translate(-70.8751, -102.2061)" cx="70.8751266" cy="102.206113" r="7"></circle>
                <path d="M110.211329,57.2174006 C112.420468,57.2174006 114.211329,55.4265396 114.211329,53.2174006 C114.211329,51.0082616 112.420468,49.2174006 110.211329,49.2174006 C108.00219,49.2174006 106.211329,51.0082616 106.211329,53.2174006 C106.211329,55.4265396 108.00219,57.2174006 110.211329,57.2174006 Z" id="椭圆形备份-24" fill="#FFFFFF" transform="translate(110.2113, 53.2174) rotate(6) translate(-110.2113, -53.2174)"></path>
            </g>
        </g>
    </g>
</svg>