<?xml version="1.0" encoding="UTF-8"?>
<svg width="66px" height="66px" viewBox="0 0 66 66" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/HDMI</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#E8B5FF" offset="0%"></stop>
            <stop stop-color="#CC61EB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="V1.2.0" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="白板备份" transform="translate(-3993, -1064)">
            <g id="编组-12" transform="translate(3970, 719)">
                <g id="编组-4" transform="translate(11, 345)">
                    <g id="编组备份" transform="translate(12, 0)">
                        <path d="M45.4455558,45.9278868 C46.2266043,45.1468382 47.4929343,45.1468382 48.2739829,45.9278868 L53.9293107,51.5832146 C54.6922801,52.346184 54.7124926,53.5768225 53.9749906,54.3644363 L53.2410896,55.1482043 C53.2261224,55.1641884 53.2108938,55.1799257 53.1954097,55.1954097 C52.4143612,55.9764583 51.1480312,55.9764583 50.3669826,55.1954097 L44.7116548,49.5400819 C43.9486854,48.7771125 43.9284729,47.546474 44.6659749,46.7588602 L45.3998759,45.9750922 Z M30,10 C41.045695,10 50,18.954305 50,30 C50,41.045695 41.045695,50 30,50 C18.954305,50 10,41.045695 10,30 C10,18.954305 18.954305,10 30,10 Z" id="形状结合" fill="url(#linearGradient-1)"></path>
                        <circle id="椭圆形" fill="#FFFFFF" cx="34.5" cy="16.5" r="3.5"></circle>
                        <circle id="椭圆形备份-6" fill="#FFFFFF" cx="26" cy="15" r="2"></circle>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>