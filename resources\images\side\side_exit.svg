<?xml version="1.0" encoding="UTF-8"?>
<svg width="88px" height="88px" viewBox="0 0 88 88" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 39</title>
    <defs>
        <linearGradient x1="85.4163267%" y1="9.08304278%" x2="50%" y2="72.1834735%" id="linearGradient-1">
            <stop stop-color="#29DA80" offset="0%"></stop>
            <stop stop-color="#6053E3" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="黑板第二期" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="上传课件" transform="translate(-3968, -1336)">
            <g id="编组-11" transform="translate(3554, 46)">
                <g id="编组-39" transform="translate(414, 1290)">
                    <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="88" height="88" rx="20"></rect>
                    <path d="M37,30 C39.209139,30 41,31.790861 41,34 L41,54 C41,56.209139 39.209139,58 37,58 C34.790861,58 33,56.209139 33,54 L33,34 C33,31.790861 34.790861,30 37,30 Z M51,30 C53.209139,30 55,31.790861 55,34 L55,54 C55,56.209139 53.209139,58 51,58 C48.790861,58 47,56.209139 47,54 L47,34 C47,31.790861 48.790861,30 51,30 Z" id="形状结合" fill="#FFFFFF"></path>
                </g>
            </g>
        </g>
    </g>
</svg>