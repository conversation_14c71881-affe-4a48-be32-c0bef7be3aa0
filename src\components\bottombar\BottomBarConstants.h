#ifndef BOTTOMBARCONSTANTS_H
#define BOTTOMBARCONSTANTS_H

#include "../../screen_adaptation/ScreenAdaptationManager.h"
#include "../whiteboardview/ZIndexManager.h"
#include <QString>
#include <QMap>
#include <QRect>

/**
 * @brief 底部操作栏组件常量定义
 * 
 * 定义底部操作栏组件的尺寸、样式和布局相关常量
 * 所有尺寸基于4K分辨率设计，通过屏幕适配管理器进行缩放
 */
namespace BottomBarConstants {

    // ========== 尺寸常量 ==========
    const int BAR_HEIGHT = 93;                      ///< 底部操作栏高度
    const int MARGIN = 40;                          ///< 外边距
    const int CONTENT_PADDING = 20;                 ///< 内容内边距

    // ========== 左右区域宽度常量 ==========
    const int LEFT_AREA_WIDTH = 860;                ///< 左侧区域宽度
    const int RIGHT_AREA_WIDTH = 820;               ///< 右侧区域宽度

    // ========== 按钮和文字常量 ==========
    const int BUTTON_MIN_WIDTH = 300;                ///< 按钮最小宽度
    const int BUTTON_ADD_WIDTH = 180;                ///< 增加白板按钮宽度
    const int BUTTON_EXIT_WIDTH = 280;               ///< 退出按钮宽度
    const int NAVIGATION_BUTTON_SIZE = 40;          ///< 导航按钮尺寸（上一页/下一页）
    const int FONT_SIZE = 44;                       ///< 统一字体大小
    const int ELEMENT_SPACING = 20;                 ///< 元素间距
    const int PAGE_INFO_WIDTH = 200;                ///< 页码信息宽度

    // ========== 字体样式常量 ==========
    const int FONT_WEIGHT = 600;                    ///< 字体粗细
    const int LINE_HEIGHT = 44;                     ///< 行高
    const int LETTER_SPACING = 2;                   ///< 字符间距

    // ========== 图标常量 ==========
    const int ICON_SIZE = 48;                       ///< 图标大小
    const int ICON_TEXT_SPACING = 10;               ///< 图标与文字间距
    const int ICON_TEXT_SPACING_24 = 24;            ///< 图标与文字间距（24px）
    const int LEFT_BUTTON_ICON_SIZE = 24;           ///< 左侧按钮图标大小（原来的2倍）
    const int LEFT_BUTTON_SIZE = 88;                ///< 左侧按钮尺寸（原来的2倍）
    const int ICON_ADD_MARGIN_TOP = 4;               ///< 加号图标下移

    // ========== 样式常量 ==========
    const int BG_ALPHA = 166;                       ///< 背景透明度 (0.65 * 255)
    
    // ========== 获取适配后的尺寸函数 ==========
    
    /**
     * @brief 获取适配后的操作栏高度
     * @return 适配后的高度
     */
    inline int getBarHeight() {
        return ScreenAdaptationConstants::adaptSize(BAR_HEIGHT);
    }
    
    /**
     * @brief 获取适配后的外边距
     * @return 适配后的外边距
     */
    inline int getMargin() {
        return ScreenAdaptationConstants::adaptSize(MARGIN);
    }
    
    /**
     * @brief 获取适配后的内容内边距
     * @return 适配后的内容内边距
     */
    inline int getContentPadding() {
        return ScreenAdaptationConstants::adaptSize(CONTENT_PADDING);
    }
    
    /**
     * @brief 获取适配后的圆角半径（高度的一半）
     * @return 适配后的圆角半径
     */
    inline int getBorderRadius() {
        return getBarHeight() / 2;
    }
    
    /**
     * @brief 获取背景透明度
     * @return 背景透明度值
     */
    inline int getBackgroundAlpha() {
        return BG_ALPHA;
    }

    /**
     * @brief 获取适配后的左侧区域宽度
     * @return 适配后的左侧区域宽度
     */
    inline int getLeftAreaWidth() {
        return ScreenAdaptationConstants::adaptSize(LEFT_AREA_WIDTH);
    }

    /**
     * @brief 获取适配后的右侧区域宽度
     * @return 适配后的右侧区域宽度
     */
    inline int getRightAreaWidth() {
        return ScreenAdaptationConstants::adaptSize(RIGHT_AREA_WIDTH);
    }

    /**
     * @brief 获取适配后的按钮最小宽度
     * @return 适配后的按钮最小宽度
     */
    inline int getButtonMinWidth() {
        return ScreenAdaptationConstants::adaptSize(BUTTON_MIN_WIDTH);
    }

    /**
     * @brief 获取适配后的增加白板按钮宽度
     * @return 适配后的增加白板按钮宽度
     */
    inline int getButtonAddWidth() {
        return ScreenAdaptationConstants::adaptSize(BUTTON_ADD_WIDTH);
    }

    /**
     * @brief 获取适配后的退出按钮宽度
     * @return 适配后的退出按钮宽度
     */
    inline int getButtonExitWidth() {
        return ScreenAdaptationConstants::adaptSize(BUTTON_EXIT_WIDTH);
    }

    /**
     * @brief 获取适配后的导航按钮尺寸
     * @return 适配后的导航按钮尺寸
     */
    inline int getNavigationButtonSize() {
        return ScreenAdaptationConstants::adaptSize(NAVIGATION_BUTTON_SIZE);
    }

    /**
     * @brief 获取适配后的页码信息宽度
     * @return 适配后的页码信息宽度
     */
    inline int getPageInfoWidth() {
        return ScreenAdaptationConstants::adaptSize(PAGE_INFO_WIDTH);
    }

    /**
     * @brief 获取适配后的字体大小
     * @return 适配后的字体大小
     */
    inline int getFontSize() {
        return ScreenAdaptationConstants::adaptSize(FONT_SIZE);
    }

    /**
     * @brief 获取字体粗细
     * @return 字体粗细
     */
    inline int getFontWeight() {
        return FONT_WEIGHT;
    }

    /**
     * @brief 获取适配后的行高
     * @return 适配后的行高
     */
    inline int getLineHeight() {
        return ScreenAdaptationConstants::adaptSize(LINE_HEIGHT);
    }

    /**
     * @brief 获取适配后的字符间距
     * @return 适配后的字符间距
     */
    inline int getLetterSpacing() {
        return ScreenAdaptationConstants::adaptSize(LETTER_SPACING);
    }

    /**
     * @brief 获取适配后的元素间距
     * @return 适配后的元素间距
     */
    inline int getElementSpacing() {
        return ScreenAdaptationConstants::adaptSize(ELEMENT_SPACING);
    }

    /**
     * @brief 获取适配后的图标大小
     * @return 适配后的图标大小
     */
    inline int getIconSize() {
        return ScreenAdaptationConstants::adaptSize(ICON_SIZE);
    }

    /**
     * @brief 获取适配后的图标与文字间距
     * @return 适配后的图标与文字间距
     */
    inline int getIconTextSpacing() {
        return ScreenAdaptationConstants::adaptSize(ICON_TEXT_SPACING);
    }

    /**
     * @brief 获取适配后的图标与文字间距（24px）
     * @return 适配后的图标与文字间距（24px）
     */
    inline int getIconTextSpacing24() {
        return ScreenAdaptationConstants::adaptSize(ICON_TEXT_SPACING_24);
    }

    /**
     * @brief 获取适配后的加号图标下移
     * @return 适配后的加号图标下移
     */
    inline int getIconAddMarginTop() {
        return ScreenAdaptationConstants::adaptSize(ICON_ADD_MARGIN_TOP);
    }

    /**
     * @brief 获取适配后的左侧按钮图标大小
     * @return 适配后的左侧按钮图标大小
     */
    inline int getLeftButtonIconSize() {
        return ScreenAdaptationConstants::adaptSize(LEFT_BUTTON_ICON_SIZE);
    }

    /**
     * @brief 获取适配后的左侧按钮尺寸
     * @return 适配后的左侧按钮尺寸
     */
    inline int getLeftButtonSize() {
        return ScreenAdaptationConstants::adaptSize(LEFT_BUTTON_SIZE);
    }

} // namespace BottomBarConstants

#endif // BOTTOMBARCONSTANTS_H
