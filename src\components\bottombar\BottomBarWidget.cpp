#include "BottomBarWidget.h"
#include <QPainter>
#include <QDebug>
#include <QApplication>
#include <QCoreApplication>

BottomBarWidget::BottomBarWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_leftContainer(nullptr)
    , m_rightContainer(nullptr)
    , m_leftLayout(nullptr)
    , m_rightLayout(nullptr)
    , m_thumbnailButton(nullptr)
    , m_prevPageButton(nullptr)
    , m_nextPageButton(nullptr)
    , m_previousButton(nullptr)
    , m_pageInfoLabel(nullptr)
    , m_nextButton(nullptr)
    , m_whiteboardButton(nullptr)
    , m_exitButton(nullptr)
    , m_iconFontManager(nullptr)
    , m_iconFontLoaded(false)
    , m_initialized(false)
    , m_isWhiteboardMode(true)
    , m_mainWindow(nullptr)
{
    // 设置基本属性
    setObjectName("BottomBarWidget");

    setAttribute(Qt::WA_TranslucentBackground);

    // 如果有父窗口，建立层级关系但保持独立窗口特性
    if (parent) {
        m_mainWindow = parent;
    }

    // 初始化IconFont管理器
    m_iconFontManager = IconFontManager::instance();

    // 立即初始化，使用ScreenUtils获取屏幕尺寸
    initializeUI();

    // 设置初始几何位置
    if (m_mainWindow && m_mainWindow->isVisible()) {
        // 独立窗口模式：使用相对位置管理
        updatePositionRelativeToMainWindow();
    } else {
        // 传统子组件模式或主窗口未准备好时使用默认计算
        QRect initialGeometry = calculateGeometry();
        if (initialGeometry.isValid()) {
            setGeometry(initialGeometry);
        }
    }

    m_initialized = true;
    emit initializationCompleted();
}

BottomBarWidget::~BottomBarWidget()
{
    // 析构函数
}

void BottomBarWidget::updatePosition()
{
    if (!m_initialized || !parent()) {
        return;
    }

    QRect newGeometry = calculateGeometry();
    if (newGeometry.isValid()) {
        setGeometry(newGeometry);
    }
}

void BottomBarWidget::setPageInfo(int current, int total)
{
    if (m_pageInfoLabel) {
        m_pageInfoLabel->setText(QString("%1/%2").arg(current).arg(total));
    }
}

void BottomBarWidget::setWhiteboardMode(bool isWhiteboard)
{
    m_isWhiteboardMode = isWhiteboard;

    // 更新退出按钮文字
    if (m_exitButton) {
        QString exitButtonText = m_isWhiteboardMode ? "关闭白板" : "关闭课件";
        m_exitButton->setText(exitButtonText);
    }

    // 根据白板模式控制左边容器的显示/隐藏
    if (m_leftContainer) {
        if (m_isWhiteboardMode) {
            // 白板模式：隐藏左边容器
            m_leftContainer->hide();
        } else {
            // 非白板模式：显示左边容器
            m_leftContainer->show();
        }
        if (m_mainLayout) {
            m_mainLayout->invalidate();
        }
        updateGeometry();
        update();
        // 强制处理UI更新事件，确保UI变更立即生效
        QCoreApplication::processEvents();
    }

}

void BottomBarWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    drawBackground(&painter);
}



void BottomBarWidget::initializeUI()
{
    try {
        // 检查iconfont是否已在main中初始化
        if (m_iconFontManager) {
            // 假设main中已经初始化成功，直接设置为已加载
            m_iconFontLoaded = true;
        }

        // 创建主布局
        m_mainLayout = new QHBoxLayout(this);
        m_mainLayout->setContentsMargins(
            BottomBarConstants::getContentPadding(),
            0,
            BottomBarConstants::getContentPadding(),
            0
        );
        m_mainLayout->setSpacing(0);

        // 创建左侧容器
        m_leftContainer = new QWidget(this);
        m_leftContainer->setFixedWidth(BottomBarConstants::getLeftAreaWidth());
        m_leftLayout = new QHBoxLayout(m_leftContainer);
        m_leftLayout->setContentsMargins(0, 0, 0, 0);
        m_leftLayout->setSpacing(BottomBarConstants::getElementSpacing());
        m_leftLayout->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);

        // 创建右侧容器
        m_rightContainer = new QWidget(this);
        m_rightContainer->setFixedWidth(BottomBarConstants::getRightAreaWidth());
        m_rightLayout = new QHBoxLayout(m_rightContainer);
        m_rightLayout->setContentsMargins(BottomBarConstants::getContentPadding(), 0, 0, 0);
        m_rightLayout->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);

        // 创建左侧内容
        createLeftContent();

        // 创建右侧内容
        createRightContent();

        // 先加到布局
        m_mainLayout->addWidget(m_leftContainer);
        m_mainLayout->addStretch();
        m_mainLayout->addWidget(m_rightContainer);

        // 立即hide，防止闪烁
        m_leftContainer->hide();
        //强制刷新UI
        if (m_mainLayout) {
            m_mainLayout->invalidate();
        }
        updateGeometry();
        update();




    } catch (const std::exception& e) {
        qCritical() << "BottomBarWidget: UI初始化异常:" << e.what();
    } catch (...) {
        qCritical() << "BottomBarWidget: UI初始化发生未知异常";
    }
}

void BottomBarWidget::createLeftContent()
{
    try {
        m_thumbnailButton = createIconTextButton(IconFonts::Icons::THUMBNAIL, "缩略图栏");
        connect(m_thumbnailButton, &QPushButton::clicked, this, &BottomBarWidget::thumbnailClicked);
        m_leftLayout->addWidget(m_thumbnailButton);

        // 创建简单的虚线分隔符
        QWidget* separator = new QWidget(this);
        separator->setFixedSize(1, BottomBarConstants::getBarHeight() * 0.8);
        separator->setStyleSheet(
            "border-left: 1px dashed rgba(255, 255, 255, 0.5);"
        );
        m_leftLayout->addWidget(separator);

        m_prevPageButton = createIconTextButton(IconFonts::Icons::ARROW_LEFT, "上一页", false); // 图标在左边
        connect(m_prevPageButton, &QPushButton::clicked, this, &BottomBarWidget::prevPPTPageClicked);
        m_leftLayout->addWidget(m_prevPageButton);

        m_nextPageButton = createIconTextButton(IconFonts::Icons::ARROW_RIGHT, "下一页", true); // 图标在右边
        connect(m_nextPageButton, &QPushButton::clicked, this, &BottomBarWidget::nextPPTPageClicked);
        m_leftLayout->addWidget(m_nextPageButton);



    } catch (const std::exception& e) {
        qCritical() << "BottomBarWidget: 左侧内容创建异常:" << e.what();
    } catch (...) {
        qCritical() << "BottomBarWidget: 左侧内容创建发生未知异常";
    }
}

void BottomBarWidget::createRightContent()
{
    try {
        QWidget* navigationGroup = new QWidget(this);
        QHBoxLayout* navLayout = new QHBoxLayout(navigationGroup);
        navLayout->setContentsMargins(0, 0, 0, 0);
        // navLayout->setSpacing(BottomBarConstants::getElementSpacing());
        navLayout->setAlignment(Qt::AlignCenter);

        m_previousButton = createIconButton(IconFonts::Icons::ARROW_LEFT);
        connect(m_previousButton, &QPushButton::clicked, this, &BottomBarWidget::prevPageClicked);
        navLayout->addWidget(m_previousButton);

        m_pageInfoLabel = createTextLabel("0/0");
        m_pageInfoLabel->setMinimumWidth(BottomBarConstants::getPageInfoWidth());
        navLayout->addWidget(m_pageInfoLabel);

        m_nextButton = createIconButton(IconFonts::Icons::ARROW_RIGHT);
        connect(m_nextButton, &QPushButton::clicked, this, &BottomBarWidget::nextPageClicked);
        navLayout->addWidget(m_nextButton);

        m_whiteboardButton = createSvgTextButton(":/images/add.svg", "白板");
        connect(m_whiteboardButton, &QPushButton::clicked, this, &BottomBarWidget::whiteboardClicked);

        QString exitButtonText = m_isWhiteboardMode ? "关闭白板" : "关闭课件";
        m_exitButton = createButton(exitButtonText);
        connect(m_exitButton, &QPushButton::clicked, this, &BottomBarWidget::exitClicked);

        m_rightLayout->addWidget(navigationGroup);
        // m_rightLayout->addStretch();
        m_rightLayout->addWidget(m_whiteboardButton);
        // m_rightLayout->addStretch();
        m_rightLayout->addWidget(m_exitButton);

    } catch (const std::exception& e) {
        qCritical() << "BottomBarWidget: 右侧内容创建异常:" << e.what();
    } catch (...) {
        qCritical() << "BottomBarWidget: 右侧内容创建发生未知异常";
    }
}

void BottomBarWidget::drawBackground(QPainter *painter)
{
    QColor backgroundColor(0, 0, 0, BottomBarConstants::getBackgroundAlpha());
    painter->setBrush(backgroundColor);
    painter->setPen(Qt::NoPen);

    int borderRadius = BottomBarConstants::getBorderRadius();
    int padding = BottomBarConstants::getContentPadding();

    // 只在非白板模式下绘制左边容器背景
    if (m_leftContainer && !m_isWhiteboardMode && m_leftContainer->isVisible()) {
        QRect leftRect = m_leftContainer->geometry();
        leftRect.adjust(-padding, 0, padding, 0);
        painter->drawRoundedRect(leftRect, borderRadius, borderRadius);
    }

    // 始终绘制右边容器背景
    if (m_rightContainer) {
        QRect rightRect = m_rightContainer->geometry();
        rightRect.adjust(-padding, 0, padding, 0);
        painter->drawRoundedRect(rightRect, borderRadius, borderRadius);
    }
}

QRect BottomBarWidget::calculateGeometry()
{
    // 独立窗口模式：优先使用主窗口作为参考
    QWidget* referenceWidget = m_mainWindow ? m_mainWindow : qobject_cast<QWidget*>(parent());
    if (!referenceWidget) {
        return QRect();
    }

    int referenceWidth = referenceWidget->width();
    int referenceHeight = referenceWidget->height();

    int barHeight = BottomBarConstants::getBarHeight();
    int margin = BottomBarConstants::getMargin();

    // 独立窗口模式下，只返回大小，位置由updatePositionRelativeToMainWindow处理
    if (m_mainWindow) {
        // 返回相对大小，位置为0（将由独立窗口位置管理方法处理）
        int width = referenceWidth - 2 * margin;
        int height = barHeight;
        return QRect(0, 0, width, height);
    } else {
        // 传统子组件模式
        int x = margin;
        int y = referenceHeight - barHeight - margin;
        int width = referenceWidth - 2 * margin;
        int height = barHeight;

        return QRect(x, y, width, height);
    }
}

QLabel* BottomBarWidget::createTextLabel(const QString& text)
{
    QLabel* label = new QLabel(text, this);
    label->setAlignment(Qt::AlignCenter);

    QString labelStyle = QString(
        "QLabel {"
        "    font-weight: %1;"
        "    font-size: %2px;"
        "    color: #FFFFFF;"
        "    line-height: %3px;"
        "    letter-spacing: %4px;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(BottomBarConstants::getFontWeight())
     .arg(BottomBarConstants::getFontSize())
     .arg(BottomBarConstants::getLineHeight())
     .arg(BottomBarConstants::getLetterSpacing());

    label->setStyleSheet(labelStyle);
    return label;
}

QPushButton* BottomBarWidget::createButton(const QString& text)
{
    QPushButton* button = new QPushButton(text, this);

    int buttonHeight = BottomBarConstants::getBarHeight();
    int buttonWidth = BottomBarConstants::getButtonExitWidth();
    button->setMinimumHeight(buttonHeight);
    button->setMaximumHeight(buttonHeight);
    button->setFixedWidth(buttonWidth);

    QString buttonStyle = QString(
        "QPushButton {"
        "    font-weight: %1;"
        "    font-size: %2px;"
        "    color: #FFFFFF;"
        "    line-height: %3px;"
        "    letter-spacing: %4px;"
        "    background: transparent;"
        "    border: none;"
        "    padding: 0px 16px;"
        "    border-radius: %5px;"
        "}"
    ).arg(BottomBarConstants::getFontWeight())
     .arg(BottomBarConstants::getFontSize())
     .arg(BottomBarConstants::getLineHeight())
     .arg(BottomBarConstants::getLetterSpacing())
     .arg(buttonHeight / 2);

    button->setStyleSheet(buttonStyle);
    return button;
}

QPushButton* BottomBarWidget::createNavigationButton(const QString& text)
{
    QPushButton* button = new QPushButton(text, this);

    int buttonSize = BottomBarConstants::getNavigationButtonSize();
    button->setFixedSize(buttonSize, buttonSize);

    QString buttonStyle = QString(
        "QPushButton {"
        "    font-weight: %1;"
        "    font-size: %2px;"
        "    color: #FFFFFF;"
        "    line-height: %3px;"
        "    letter-spacing: %4px;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(BottomBarConstants::getFontWeight())
     .arg(BottomBarConstants::getFontSize())
     .arg(BottomBarConstants::getLineHeight())
     .arg(BottomBarConstants::getLetterSpacing());

    button->setStyleSheet(buttonStyle);
    return button;
}

QPushButton* BottomBarWidget::createIconButton(int iconUnicode)
{
    QPushButton* button = new QPushButton(this);

    // 设置按钮为正方形
    int buttonSize = BottomBarConstants::getNavigationButtonSize();
    button->setFixedSize(buttonSize, buttonSize);

    if (m_iconFontLoaded && m_iconFontManager) {
        // 使用iconfont字体设置按钮文字
        QFont iconFont = m_iconFontManager->getFont(BottomBarConstants::getFontSize());
        button->setFont(iconFont);

        // 将Unicode码点转换为字符
        QString iconChar = QString(QChar(iconUnicode));
        button->setText(iconChar);
    } else {
        // 回退到文字显示（调试用）
        button->setText("?");
    }

    // 设置按钮样式 - 无边框、无背景，使用统一字体样式
    QString buttonStyle = QString(
        "QPushButton {"
        "    font-weight: %1;"
        "    font-size: %2px;"
        "    color: #FFFFFF;"
        "    line-height: %3px;"
        "    letter-spacing: %4px;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(BottomBarConstants::getFontWeight())
     .arg(BottomBarConstants::getFontSize())
     .arg(BottomBarConstants::getLineHeight())
     .arg(BottomBarConstants::getLetterSpacing());

    button->setStyleSheet(buttonStyle);
    return button;
}

QPushButton* BottomBarWidget::createSvgTextButton(const QString& svgPath, const QString& text)
{
    QPushButton* button = new QPushButton(this);

    int buttonHeight = BottomBarConstants::getBarHeight();
    button->setMinimumHeight(buttonHeight);
    button->setMaximumHeight(buttonHeight);
    button->setFixedWidth(BottomBarConstants::getButtonAddWidth());

    int iconSize = BottomBarConstants::getIconSize();
    int spacing = BottomBarConstants::getIconTextSpacing();
    int padding = BottomBarConstants::getElementSpacing();

    QHBoxLayout* layout = new QHBoxLayout(button);
    layout->setContentsMargins(padding, 0, padding, 0);
    layout->setSpacing(spacing);
    layout->setAlignment(Qt::AlignCenter);

    // 创建SVG图标容器
    QWidget* iconContainer = new QWidget(button);
    QVBoxLayout* iconLayout = new QVBoxLayout(iconContainer);
    iconLayout->setContentsMargins(0, BottomBarConstants::getIconAddMarginTop(), 0, 0);
    iconLayout->setSpacing(0);

    QSvgWidget* iconWidget = new QSvgWidget(svgPath, iconContainer);
    iconWidget->setFixedSize(iconSize, iconSize);

    iconLayout->addWidget(iconWidget);
    iconContainer->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Preferred);

    // 创建文字标签
    QLabel* textLabel = new QLabel(text, button);
    textLabel->setAlignment(Qt::AlignCenter);

    QString labelStyle = QString(
        "QLabel {"
        "    font-weight: %1;"
        "    font-size: %2px;"
        "    color: #FFFFFF;"
        "    line-height: %3px;"
        "    letter-spacing: %4px;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(BottomBarConstants::getFontWeight())
     .arg(BottomBarConstants::getFontSize())
     .arg(BottomBarConstants::getLineHeight())
     .arg(BottomBarConstants::getLetterSpacing());

    textLabel->setStyleSheet(labelStyle);

    layout->addWidget(iconContainer, 0, Qt::AlignVCenter);
    layout->addWidget(textLabel, 0, Qt::AlignVCenter);

    // 设置按钮样式
    QString buttonStyle = QString(
        "QPushButton {"
        "    background: transparent;"
        "    border: none;"
        "    border-radius: %1px;"
        "    padding: 0px;"
        "}"
    ).arg(buttonHeight / 2);

    button->setStyleSheet(buttonStyle);

    return button;
}



QPushButton* BottomBarWidget::createIconTextButton(int iconUnicode, const QString& text, bool iconOnRight)
{
    QPushButton* button = new QPushButton(this);

    int buttonHeight = BottomBarConstants::getBarHeight();
    button->setMinimumHeight(buttonHeight);
    button->setMaximumHeight(buttonHeight);
    button->setMinimumWidth(BottomBarConstants::getLeftAreaWidth() / 3);

    int spacing = BottomBarConstants::getIconTextSpacing24();
    int padding = BottomBarConstants::getElementSpacing();


    QHBoxLayout* contentLayout = new QHBoxLayout(button);
    contentLayout->setContentsMargins(padding, 0, padding, 0);
    contentLayout->setSpacing(spacing);
    contentLayout->setAlignment(Qt::AlignCenter);

    QLabel* iconLabel = new QLabel(button);
    if (m_iconFontLoaded && m_iconFontManager) {
        QFont iconFont = m_iconFontManager->getFont(BottomBarConstants::getLeftButtonIconSize());
        iconLabel->setFont(iconFont);
        iconLabel->setText(QString(QChar(iconUnicode)));
    } else {
        // 回退到文字显示
        iconLabel->setText("?");
        iconLabel->setStyleSheet("font-size: 24px;");
    }
    iconLabel->setAlignment(Qt::AlignCenter);
    iconLabel->setStyleSheet("color: #FFFFFF;");

    QLabel* textLabel = new QLabel(text, button);
    textLabel->setAlignment(Qt::AlignCenter);

    QString textStyle = QString(
        "QLabel {"
        "    font-weight: %1;"
        "    font-size: %2px;"
        "    color: #FFFFFF;"
        "    line-height: %3px;"
        "    letter-spacing: %4px;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(BottomBarConstants::getFontWeight())
     .arg(BottomBarConstants::getFontSize())
     .arg(BottomBarConstants::getLineHeight())
     .arg(BottomBarConstants::getLetterSpacing());

    textLabel->setStyleSheet(textStyle);

    // 根据iconOnRight参数决定布局顺序
    if (iconOnRight) {
        // 图标在右边：文字 + 图标
        contentLayout->addWidget(textLabel);
        contentLayout->addWidget(iconLabel);
    } else {
        // 图标在左边：图标 + 文字
        contentLayout->addWidget(iconLabel);
        contentLayout->addWidget(textLabel);
    }

    QString buttonStyle = QString(
        "QPushButton {"
        "    background: transparent;"
        "    border: none;"
        "    border-radius: %1px;"
        "    padding: 0px;"
        "}"
    ).arg(buttonHeight / 2);

    button->setStyleSheet(buttonStyle);

    return button;
}

bool BottomBarWidget::isThumbnailButton(QWidget *pWidget) {
    while (pWidget) {
        if (m_thumbnailButton == pWidget) {
            return true;
        }
        pWidget = pWidget->parentWidget();
    }
    return false;
}

void BottomBarWidget::setMainWindow(QWidget* mainWindow)
{
    m_mainWindow = mainWindow;
}

void BottomBarWidget::updatePositionRelativeToMainWindow()
{
    if (m_mainWindow && m_mainWindow->isVisible()) {
        // 保持相对于主窗口的位置（底部中央，带底部间距）
        QPoint mainWindowPos = m_mainWindow->pos();
        QSize mainWindowSize = m_mainWindow->size();

        QRect targetGeometry = calculateGeometry();
        int bottomMargin = BottomBarConstants::getMargin();

        // 计算相对于主窗口的位置（底部中央，预留底部间距）
        int x = mainWindowPos.x() + (mainWindowSize.width() - targetGeometry.width()) / 2;
        int y = mainWindowPos.y() + mainWindowSize.height() - targetGeometry.height() - bottomMargin;

        move(x, y);
        resize(targetGeometry.size());
    }
}

void BottomBarWidget::showIndependentWindow()
{
    // 独立窗口模式显示，先更新位置再显示
    updatePositionRelativeToMainWindow();
    show();
}

void BottomBarWidget::hideIndependentWindow()
{
    // 隐藏独立窗口
    hide();
}
