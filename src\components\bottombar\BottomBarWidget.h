#ifndef BOTTOMBARWIDGET_H
#define BOTTOMBARWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QPaintEvent>
#include <QTimer>
#include <QFrame>
#include <QLabel>
#include <QPushButton>
#include <QSvgWidget>
#include "BottomBarConstants.h"
#include "../iconfont/iconfonts.h"
#include "../iconfont/iconfontmanager.h"
#include "../../utils/ScreenUtils.h"

/**
 * @brief 底部操作栏组件
 * 
 * 提供底部操作栏功能，位于WhiteboardView最底部
 * 特性：
 * - 高度93px，宽度自适应父组件
 * - 背景：rgba(0,0,0,0.65)，圆角为高度的一半
 * - 分为左右两部分，中间内容上下居中
 * - 外边距40px，使用屏幕适配
 * - 层级：ZIndexLevel::BOTTOM_UI_LAYER
 */
class BottomBarWidget : public QWidget
{
    Q_OBJECT


public:
    /**
     * @brief 构造函数
     * @param parent 父组件
     */
    explicit BottomBarWidget(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~BottomBarWidget();
    
    /**
     * @brief 更新位置（在父组件大小改变时调用）
     */
    void updatePosition();

    /**
     * @brief 设置当前页码
     * @param current 当前页码
     * @param total 总页数
     */
    void setPageInfo(int current, int total);

    /**
     * @brief 设置是否在白板模式
     * @param isWhiteboard 是否在白板模式
     */
    void setWhiteboardMode(bool isWhiteboard);


    /**
     * @brief 判断是否是缩略图按钮或其子组件
     */
    bool isThumbnailButton(QWidget *pWidget);

    /**
     * @brief 设置主窗口引用（用于独立窗口定位）
     * @param mainWindow 主窗口指针
     */
    void setMainWindow(QWidget* mainWindow);

    /**
     * @brief 更新相对于主窗口的位置
     */
    void updatePositionRelativeToMainWindow();

    /**
     * @brief 显示独立窗口
     */
    void showIndependentWindow();

    /**
     * @brief 隐藏独立窗口
     */
    void hideIndependentWindow();

signals:
    /**
     * @brief 初始化完成信号
     */
    void initializationCompleted();

    /**
     * @brief 上一页按钮点击信号
     */
    void prevPageClicked();

    /**
     * @brief 下一页按钮点击信号
     */
    void nextPageClicked();

    /**
     * @brief 白板按钮点击信号
     */
    void whiteboardClicked();

    /**
     * @brief 退出按钮点击信号
     */
    void exitClicked();

    /**
     * @brief 缩略图按钮点击信号
     */
    void thumbnailClicked();

    /**
     * @brief 左侧上一页按钮点击信号
     */
    void prevPPTPageClicked();

    /**
     * @brief 左侧下一页按钮点击信号
     */
    void nextPPTPageClicked();

protected:
    /**
     * @brief 绘制事件
     */
    void paintEvent(QPaintEvent *event) override;




private slots:

private:
    /**
     * @brief 初始化UI
     */
    void initializeUI();

    /**
     * @brief 创建左侧内容
     */
    void createLeftContent();

    /**
     * @brief 创建右侧内容
     */
    void createRightContent();

    /**
     * @brief 创建文字标签
     * @param text 文字内容
     * @return 创建的标签
     */
    QLabel* createTextLabel(const QString& text);

    /**
     * @brief 创建按钮
     * @param text 按钮文字
     * @return 创建的按钮
     */
    QPushButton* createButton(const QString& text);

    /**
     * @brief 创建导航按钮（上一页/下一页）
     * @param text 按钮文字
     * @return 创建的按钮
     */
    QPushButton* createNavigationButton(const QString& text);

    /**
     * @brief 创建带iconfont图标的按钮
     * @param iconUnicode 图标Unicode值
     * @return 创建的按钮
     */
    QPushButton* createIconButton(int iconUnicode);

    /**
     * @brief 创建带SVG图标和文字的按钮
     * @param svgPath SVG图标路径
     * @param text 按钮文字
     * @return 创建的按钮
     */
    QPushButton* createSvgTextButton(const QString& svgPath, const QString& text);

    /**
     * @brief 创建左侧带iconfont图标和文字的按钮
     * @param iconUnicode 图标Unicode值
     * @param text 按钮文字
     * @param iconOnRight 图标是否在右边（默认false，图标在左边）
     * @return 创建的按钮
     */
    QPushButton* createIconTextButton(int iconUnicode, const QString& text, bool iconOnRight = false);

    /**
     * @brief 绘制背景
     * @param painter 绘制器
     */
    void drawBackground(QPainter *painter);

    /**
     * @brief 计算组件几何位置
     * @return 几何位置矩形
     */
    QRect calculateGeometry();

private:
    QHBoxLayout* m_mainLayout;          ///< 主布局（水平）
    QWidget* m_leftContainer;           ///< 左侧容器
    QWidget* m_rightContainer;          ///< 右侧容器
    QHBoxLayout* m_leftLayout;          ///< 左侧布局
    QHBoxLayout* m_rightLayout;         ///< 右侧布局

    // 左侧内容
    QPushButton* m_thumbnailButton;     ///< 缩略图按钮
    QPushButton* m_prevPageButton;      ///< 上一页按钮
    QPushButton* m_nextPageButton;      ///< 下一页按钮

    // 右侧内容
    QPushButton* m_previousButton;      ///< 上一页按钮
    QLabel* m_pageInfoLabel;            ///< 页码信息标签
    QPushButton* m_nextButton;          ///< 下一页按钮
    QPushButton* m_whiteboardButton;    ///< 白板按钮
    QPushButton* m_exitButton;          ///< 退出按钮

    // iconfont相关
    IconFontManager* m_iconFontManager; ///< iconfont管理器
    bool m_iconFontLoaded;              ///< iconfont是否加载成功

    bool m_initialized;                 ///< 是否已初始化
    bool m_isWhiteboardMode;            ///< 是否在白板模式

    // 独立窗口管理
    QWidget* m_mainWindow;              ///< 主窗口引用（用于定位）
};

#endif // BOTTOMBARWIDGET_H
