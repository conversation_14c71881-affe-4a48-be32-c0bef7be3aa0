#include "CefViewWidget.h"
#include <QApplication>
#include <QGuiApplication>
#include <QScreen>
#include <QSvgRenderer>
#include <QPainter>
#include <QPixmap>
#include <QIcon>
#include <QtMath>
#include <QtGlobal>
#include <QResizeEvent>
#include "../jsbridge/JSBridge.h"
#include "nlohmann/json.hpp"

using json = nlohmann::json;

CefViewWidget::CefViewWidget(QWidget *parent, const QString &cefName, const bool showFullScreenButton, const bool showControlBar, const bool showDragBar)
    : QWidget(parent)
    , m_cefName(cefName)
    , m_mainLayout(nullptr)
    , m_cefView(nullptr)
    , m_controlBar(nullptr)
    , m_dragBar(nullptr)
    , m_controlLayout(nullptr)
    , m_closeButton(nullptr)
    , m_dragButton(nullptr)
    , m_isDragging(false)
    , m_dragStarted(false)
    , m_dragStartPos()
    , m_windowStartPos()
    , m_showFullScreenButton(showFullScreenButton)
    , m_showControlBar(showControlBar)
   , m_showDragBar(showDragBar)
{
    initializeUI();
    setObjectName(cefName);
    initializeCefView(cefName);
    initializeControlBar();
    
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    
}

CefViewWidget::~CefViewWidget()
{
}

void CefViewWidget::setUrl(const QString& url)
{
    if (m_cefView) {
        m_cefView->navigateToUrl(url);
        this->url = url;
    }
    if (m_dragBar) {
        m_dragBar->hide();
    }
}

void CefViewWidget::setHtml(const QString& html)
{
    if (m_cefView) {
        // 使用data URL方式加载HTML内容
        QString dataUrl = "data:text/html;charset=utf-8," + html.toUtf8().toPercentEncoding();
        m_cefView->navigateToUrl(dataUrl);
    }
}

void CefViewWidget::setCefViewGeometry(const QRect& geometry)
{
    QRect totalGeometry = geometry;
    if (m_showControlBar) {
        totalGeometry.setHeight(geometry.height() + CONTROL_BAR_CONTAINER_HEIGHT);
    }
    setGeometry(totalGeometry);
    if (m_cefView) {
        m_cefView->setMinimumWidth(geometry.width());
        m_cefView->setMinimumHeight(geometry.height());
    }
}

QCefView* CefViewWidget::cefView() const
{
    return m_cefView;
}

void CefViewWidget::setControlBarVisible(bool visible)
{
    if (m_controlBar) {
        m_controlBar->setVisible(visible);

        if (visible) {
            m_mainLayout->setContentsMargins(0, 0, 0, 0);
        } else {
            m_mainLayout->setContentsMargins(0, 0, 0, -CONTROL_BAR_CONTAINER_HEIGHT);
        }
    }
}

void CefViewWidget::setDragBarVisible(bool visible) {
    if (m_dragBar) {
        m_dragBar->setVisible(visible);
    }
}

bool CefViewWidget::isControlBarVisible() const
{
    return m_controlBar ? m_controlBar->isVisible() : false;
}

void CefViewWidget::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        if (isInDragHandle(event->position())) {
            m_dragStartPos = event->globalPosition().toPoint();
            m_windowStartPos = pos();
            m_isDragging = false;
            m_dragStarted = false;

            event->accept();
            return;
        }
    }

    QWidget::mousePressEvent(event);
}

void CefViewWidget::mouseMoveEvent(QMouseEvent* event)
{
    if ((event->buttons() & Qt::LeftButton) && !m_dragStartPos.isNull()) {
        QPoint currentGlobalPos = event->globalPosition().toPoint();
        QPoint delta = currentGlobalPos - m_dragStartPos;

        if (!m_dragStarted) {
            // 判断是否是全屏，是则不允许拖动
            QSize screenSize = QApplication::primaryScreen()->size();
            if (size() == screenSize) {
                qDebug() << "CefViewWidget: mouseMoveEvent, fullscreen, not dragging";
            }
            else {
                qreal distance = qSqrt(delta.x() * delta.x() + delta.y() * delta.y());
                if (distance > DRAG_THRESHOLD) {
                    m_dragStarted = true;
                    m_isDragging = true;
                    emit dragStarted();
                }
            }
        }
        if (m_dragStarted) {
            QPoint newPos = m_windowStartPos + delta;
            
            // 限制窗口不能拖出屏幕边界
            QScreen* screen = QGuiApplication::primaryScreen();
            if (screen) {
                QRect screenGeometry = screen->geometry();
                QSize widgetSize = size();
                
                // 限制左右边界，确保整个窗口都在屏幕内
                int minX = screenGeometry.left();
                int maxX = screenGeometry.right() - widgetSize.width();
                newPos.setX(qBound(minX, newPos.x(), maxX));
                
                // 限制上下边界，确保整个窗口都在屏幕内
                int minY = screenGeometry.top();
                int maxY = screenGeometry.bottom() - widgetSize.height();
                newPos.setY(qBound(minY, newPos.y(), maxY));
            }
            
            move(newPos);
            qDebug() << "CefViewWidget: mouseMoveEvent, dragging, newPos:" << newPos;
        }

        event->accept();
        return;
    }

    QWidget::mouseMoveEvent(event);
}

void CefViewWidget::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        if (m_isDragging) {
            emit dragFinished();
        }

        qDebug() << "CefViewWidget: mouseReleaseEvent, dragging, m_isDragging:" << m_isDragging;
        // 重置拖拽状态
        m_isDragging = false;
        m_dragStarted = false;
        m_dragStartPos = QPoint();
        m_windowStartPos = QPoint();

        event->accept();
        return;
    }

    QWidget::mouseReleaseEvent(event);
}

void CefViewWidget::resizeEvent(QResizeEvent* event)
{
    // 大小变化时，停止拖拽，否则会导致全屏时仍在拖动
    if (m_isDragging) {
        m_isDragging = false;
        m_dragStarted = false;
        qDebug() << "CefViewWidget: resizeEvent, stop dragging";
    }

    QWidget::resizeEvent(event);

    // 窗口大小改变时，更新控制栏位置
    updateControlBarPosition();

    // 大小变化时，停止拖拽，否则会导致全屏时仍在拖动
    m_isDragging = false;
}

void CefViewWidget::showEvent(QShowEvent* event)
{
    emit widgetShown();
    QWidget::showEvent(event);    
}

void CefViewWidget::hideEvent(QHideEvent* event)
{
    emit widgetHidden();
    QWidget::hideEvent(event);
}

void CefViewWidget::onCloseButtonClicked()
{
    emit closeRequested();
    close();
}

void CefViewWidget::onFullscreenButtonClicked()
{
    enterFullScreen(true);
}

void CefViewWidget::enterFullScreen(bool sendEvent) {
    if (m_isFullScreen) {
        qDebug() << "CefViewWidget: 已经是全屏模式，无需再次进入全屏";
        return;
    }
    // 进入全屏
    m_previousGeometry = geometry();
    qDebug() << "CefViewWidget: Entering full screen mode, previous geometry:" << m_previousGeometry;
    m_cefViewPreviousGeometry = m_cefView->geometry();
    qDebug() << "CefViewWidget: Entering full screen mode, previous geometry:" << m_cefViewPreviousGeometry;

    // 调整 CEF 视图大小
    QScreen* screen = QGuiApplication::primaryScreen();
    QRect screenGeometry;
    if (screen) {
        screenGeometry = screen->geometry();
        qDebug() << "CefViewWidget: Entering full screen mode, available geometry:" << screenGeometry;
    } else {
        qDebug() << "CefViewWidget: No primary screen available, cannot enter full screen mode";
        return;
    }
    if (m_dragBar) {
        m_widgetParentMap.insert(m_dragBar, m_dragBar->parentWidget());
        m_dragBar->setParent(nullptr);
    }
    if (m_bottomContainer) {
        m_widgetParentMap.insert(m_bottomContainer, m_bottomContainer->parentWidget());
        m_bottomContainer->setParent(nullptr);
    }

    // 发送消息, 告诉cef进入全屏模式
    if (sendEvent) {
        JSBridge::callJs(m_cefName, JSBridgeMsg::newRequest("enterFullScreen", json{}),
                         [this](const JSBridgeContext &result) {
                             result.setResult(JSBridgeMsg::newResponse(result.getData(), json{}));
                         });
    }
    QTimer::singleShot(100, this, [this, screenGeometry]() {
        this->setGeometry(screenGeometry);
    });
    m_isFullScreen = true;
}

void CefViewWidget::exitFullScreen() {
    // 退出全屏
    if (!m_isFullScreen) {
        qDebug() << "CefViewWidget: 已经退出全屏模式，无需再次退出全屏";
        return;
    }
    // 退出全屏
    if (m_dragBar) {
        m_dragBar->setParent(m_widgetParentMap.value(m_dragBar));
        m_dragBar->show();
    }
    if (m_bottomContainer) {
        m_bottomContainer->setParent(m_widgetParentMap.value(m_bottomContainer));
        m_bottomContainer->show();
    }
    m_widgetParentMap.clear();

    // 恢复窗口大小和位置
    setGeometry(m_previousGeometry);
    m_cefView->setGeometry(m_cefViewPreviousGeometry);

    m_isFullScreen = false;
}

void CefViewWidget::initializeUI()
{
    // 创建主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    setLayout(m_mainLayout);
}

void CefViewWidget::initializeCefView(const QString& cefName)
{
    QCefSetting setting;
    setting.setBackgroundColor(Qt::transparent);
    // 设置帧率
    setting.setWindowlessFrameRate(30);

    m_cefView = new QCefView("about:blank", &setting, this);
    initLoadErrorUI();

    JSBridge::init(m_cefView, cefName);
    m_mainLayout->addWidget(m_cefView, 1);
    
    connect(m_cefView, &QCefView::loadEnd, this, &CefViewWidget::onLoadEnd);
}

void CefViewWidget::initLoadErrorUI() {// 创建按钮（初始不可见）
    m_loadErrorRefreshBtn = new QPushButton("刷新", this);
    m_loadErrorCloseBtn = new QPushButton("关闭", this);
    m_loadErrorRefreshBtn->setVisible(false);
    m_loadErrorCloseBtn->setVisible(false);

    // 按钮样式（可选）
    m_loadErrorRefreshBtn->setStyleSheet("QPushButton{background:#4CAF50; color:white; padding:8px;}");
    m_loadErrorCloseBtn->setStyleSheet("QPushButton{background:#F44336; color:white; padding:8px;}");

    // 连接信号槽
    connect(m_loadErrorRefreshBtn, &QPushButton::clicked, [this]() {
        m_cefView->browserReload(); // 刷新网页
        hideErrorUI();    // 隐藏按钮
    });

    connect(m_loadErrorCloseBtn, &QPushButton::clicked, [this]() {
        emit closeRequested();
        close();
        hideErrorUI();                 // 隐藏按钮
    });

    connect(m_cefView, &QCefView::loadError, [this](const QCefBrowserId& browserId,
                                                    const QCefFrameId& frameId,
                                                    bool isMainFrame,
                                                    int errorCode,
                                                    const QString& errorMsg,
                                                    const QString& failedUrl) {
        qDebug() << "loadError: " << errorCode << ", errorMsg: " << errorMsg << ", failedUrl: " << failedUrl;

        if (!isMainFrame) {
            qDebug() << "loadError: 非主框架，不处理: " << failedUrl << ", errorMsg: " << errorMsg << ", errorCode: " << errorCode;
            return;
        }

        QMetaObject::invokeMethod(this, [this]() {
            showErrorUI();
        });
    });
    connect(m_cefView, &QCefView::loadEnd, [this](const QCefBrowserId& browserId, const QCefFrameId& frameId, bool isMainFrame, int httpStatusCode) {
        if (!isMainFrame) {
            qDebug() << "loadEnd: 非主框架，不处理: " << url;
            return;
        }
        if (httpStatusCode < 400) {
            qDebug() << "loadEnd: 成功加载: " << httpStatusCode;
            return;
        }
        QMetaObject::invokeMethod(this, [this]() {
            showErrorUI();
        });
    });
}

void CefViewWidget::onLoadEnd()
{
    qDebug() << "CefViewWidget::onLoadEnd";
    if (m_dragBar && !m_isFullScreen) {
        // 延迟执行
        QTimer::singleShot(300, this, [this]() {
            if (m_dragBar->isHidden()) {
                m_dragBar->show();
            }
        });
    }
}

// 显示错误按钮
void CefViewWidget::showErrorUI() {
    // 居中按钮
    QSize cefSize = m_cefView->size();
    int btnWidth = 100;
    int btnHeight = 40;
    int x = (cefSize.width() - btnWidth*2 - 20) / 2;
    int y = cefSize.height() - btnHeight - 20;

    m_loadErrorRefreshBtn->setGeometry(x, y, btnWidth, btnHeight);
    m_loadErrorCloseBtn->setGeometry(x + btnWidth + 20, y, btnWidth, btnHeight);

    m_loadErrorRefreshBtn->setVisible(true);
    m_loadErrorCloseBtn->setVisible(true);
    m_loadErrorRefreshBtn->raise(); // 确保按钮在顶层
    m_loadErrorCloseBtn->raise();
}

// 隐藏按钮
void CefViewWidget::hideErrorUI() {
    m_loadErrorRefreshBtn->setVisible(false);
    m_loadErrorCloseBtn->setVisible(false);
}

void CefViewWidget::initializeControlBar()
{
    if (m_showControlBar) {
        m_controlBar = new QWidget(this);
        m_controlBar->setFixedHeight(CONTROL_BAR_CONTAINER_HEIGHT);
        m_controlBar->setStyleSheet("QWidget { background: transparent; }");

        m_controlLayout = new QHBoxLayout(m_controlBar);
        m_controlLayout->setContentsMargins(0, 0, 0, 0);
        m_controlLayout->setSpacing(0);

        m_closeButton = new QPushButton(this);
        m_closeButton->setFixedSize(ScreenAdaptationConstants::adaptSize(48), ScreenAdaptationConstants::adaptSize(48));
        m_closeButton->setToolTip("关闭");

        m_closeButton->setIcon(QIcon(":/images/close.svg"));
        m_closeButton->setIconSize(QSize(ScreenAdaptationConstants::adaptSize(43), ScreenAdaptationConstants::adaptSize(43)));

        m_closeButton->setStyleSheet(
                "QPushButton {"
                "    width: 23px;"
                "    height: 23px;"
                "    background: rgba(0, 0, 0, 180);"  // 半透明黑色背景
                "    border-radius: 5px;"
                "    opacity: 0.75;"
                "    color: white;"
                "    border: none;"
                "    text-align: center;"
                "}"
                "QPushButton:hover {"
                "    opacity: 1.0;"
                "    background: rgba(51, 51, 51, 180);"  // 悬停时稍亮的半透明背景
                "}"
                "QPushButton:pressed {"
                "    background: rgba(85, 85, 85, 180);"  // 按下时更亮的半透明背景
                "}"
        );

        connect(m_closeButton, &QPushButton::clicked, this, &CefViewWidget::onCloseButtonClicked);

        m_controlLayout->addWidget(m_closeButton);

        // 全屏按钮
        if (m_showFullScreenButton) {
            m_fullscreenSpacing = new QSpacerItem(ScreenAdaptationConstants::adaptSize(80), 0, QSizePolicy::Fixed, QSizePolicy::Minimum);
            m_controlLayout->addItem(m_fullscreenSpacing);
            m_fullscreenButton = new QPushButton(this);
            m_fullscreenButton->setFixedSize(ScreenAdaptationConstants::adaptSize(48), ScreenAdaptationConstants::adaptSize(48));
            m_fullscreenButton->setToolTip("全屏");
            // todo 需替换为实际图标和大小
            m_fullscreenButton->setIcon(QIcon(":/images/icons/fullscreen.svg"));
            m_fullscreenButton->setIconSize(QSize(ScreenAdaptationConstants::adaptSize(43), ScreenAdaptationConstants::adaptSize(43)));
            m_fullscreenButton->setStyleSheet(
                    "QPushButton {"
                    "    width: 23px;"
                    "    height: 23px;"
                    "    background: rgba(0, 0, 0, 180);"  // 半透明黑色背景
                    "    border-radius: 5px;"
                    "    opacity: 0.75;"
                    "    color: white;"
                    "    border: none;"
                    "    text-align: center;"
                    "}"
                    "QPushButton:hover {"
                    "    opacity: 1.0;"
                    "    background: rgba(51, 51, 51, 180);"  // 悬停时稍亮的半透明背景
                    "}"
                    "QPushButton:pressed {"
                    "    background: rgba(85, 85, 85, 180);"  // 按下时更亮的半透明背景
                    "}"
            );

            connect(m_fullscreenButton, &QPushButton::clicked, this, &CefViewWidget::onFullscreenButtonClicked);

            m_controlLayout->addWidget(m_fullscreenButton);
        }

        // 调整控制栏大小以适应内容
        m_controlBar->adjustSize();

        m_bottomContainer = new QWidget(this);
        m_bottomContainer->setFixedHeight(CONTROL_BAR_CONTAINER_HEIGHT);
        m_bottomContainer->setStyleSheet("QWidget { background: transparent; }");

        m_controlBar->setParent(m_bottomContainer);

        QHBoxLayout *containerLayout = new QHBoxLayout(m_bottomContainer);
        containerLayout->setContentsMargins(0, 0, 0, 0);
        containerLayout->addStretch();
        containerLayout->addWidget(m_controlBar);
        containerLayout->addStretch();

        m_mainLayout->addWidget(m_bottomContainer, 0);
    }

    if (m_showDragBar) {
        m_dragBar = new DragBar(this);

        updateControlBarPosition();
        m_dragBar->hide();
        m_dragBar->raise();
    }
}

QPushButton* CefViewWidget::createSvgButton(const QString& iconPath, const QSize& size)
{
    QPushButton* button = new QPushButton(this);
    button->setFixedSize(size);

    // 尝试加载SVG图标
    QSvgRenderer renderer(iconPath);
    if (renderer.isValid()) {
        // 创建白色图标
        QPixmap pixmap(size);
        pixmap.fill(Qt::transparent);

        QPainter painter(&pixmap);
        painter.setRenderHint(QPainter::Antialiasing);

        // 设置白色画笔
        painter.setPen(Qt::white);
        painter.setBrush(Qt::white);

        // 渲染SVG
        renderer.render(&painter);

        button->setIcon(QIcon(pixmap));
        button->setIconSize(size);
    } else {
        // 设置文本作为备选
        if (iconPath.contains("exit")) {
            button->setText("×");
        } else if (iconPath.contains("floating")) {
            button->setText("≡");
        }
        button->setStyleSheet(button->styleSheet() + "color: white; font-weight: bold;");
    }

    return button;
}

void CefViewWidget::updateControlBarPosition()
{
    if (!m_dragBar) {
        return;
    }

    // 用实际宽度居中
    int x = (width() - m_dragBar->width()) / 2;
    int y = ScreenAdaptationConstants::adaptSize(30);

    m_dragBar->move(x, y);
}

bool CefViewWidget::isInDragHandle(const QPointF& pos) const
{
    if (!m_dragBar) {
        return false;
    }

    QRect dragBarRect = m_dragBar->geometry();
    
    // 扩大拖拽区域，增加边距使拖拽更容易
    int dragAreaMargin = ScreenAdaptationConstants::adaptSize(DRAG_AREA_MARGIN);
    dragBarRect.adjust(-dragAreaMargin, -dragAreaMargin, dragAreaMargin, dragAreaMargin);

    bool isInDragBar = dragBarRect.contains(pos.toPoint());

    return isInDragBar;
}

void CefViewWidget::setFullscreenButtonVisible(bool visible)
{
    if (m_fullscreenButton) {
        m_fullscreenButton->setVisible(visible);
    }
    if (m_controlLayout && m_fullscreenSpacing) {
        if (visible) {
            if (m_controlLayout->indexOf(m_fullscreenSpacing) == -1) {
                // spacing应在closeButton后面
                int closeIdx = m_controlLayout->indexOf(m_closeButton);
                m_controlLayout->insertItem(closeIdx + 1, m_fullscreenSpacing);
            }
        } else {
            m_controlLayout->removeItem(m_fullscreenSpacing);
        }
    }
}

bool CefViewWidget::isFullscreenButtonVisible() const
{
    return m_fullscreenButton ? m_fullscreenButton->isVisible() : false;
}

#include "CefViewWidget.moc"
