#include "ArcContentManager.h"
#include "../utils/FloatMenuUtils.h"
#include <QWidget>
#include <QtMath>

ArcContentManager::ArcContentManager(QObject *parent)
    : QObject(parent)
    , m_currentVisibleTool("")
    , m_parentWidget(nullptr)
    , m_diskCenter(0, 0)
{
}

ArcContentManager::~ArcContentManager()
{
    for (auto it = m_arcContents.begin(); it != m_arcContents.end(); ++it) {
        if (it.value()) {
            it.value()->deleteLater();
        }
    }
    m_arcContents.clear();
    m_arcConfigs.clear();
}

void ArcContentManager::setParentWidget(QWidget* parentWidget)
{
    if (m_parentWidget != parentWidget) {
        m_parentWidget = parentWidget;

        for (auto it = m_arcContents.begin(); it != m_arcContents.end(); ++it) {
            if (it.value()) {
                it.value()->setParent(m_parentWidget);
                it.value()->raise();
            }
        }
        updateAllArcContentPositions();
    }
}

void ArcContentManager::registerArcContent(const QString& toolName,
                                          ArcContentWidget* arcContent,
                                          const ArcContentConfig& config)
{
    if (!FloatMenuUtils::validatePointer(arcContent, "ArcContentManager", "弧形内容")) {
        return;
    }

    if (m_arcContents.contains(toolName)) {
        removeArcContent(toolName);
    }

    if (m_parentWidget) {
        arcContent->setParent(m_parentWidget);
        // 子widget模式下，使用raise()确保在父widget内的层级
        arcContent->raise();

        // 设置属性允许超出父widget边界显示
        arcContent->setAttribute(Qt::WA_TranslucentBackground);
        arcContent->setAttribute(Qt::WA_NoSystemBackground);
    }

    m_arcContents[toolName] = arcContent;
    m_arcConfigs[toolName] = config;

    // 设置扩展尺寸并更新位置
    arcContent->setExpandSize(config.expandSize);
    updateArcContentPosition(toolName);

    // 确保arc内容初始状态为隐藏
    arcContent->hide();

}

void ArcContentManager::removeArcContent(const QString& toolName)
{
    if (!m_arcContents.contains(toolName)) {
        return;
    }

    ArcContentWidget* arcContent = m_arcContents[toolName];
    if (arcContent) {
        if (m_currentVisibleTool == toolName) {
            m_currentVisibleTool.clear();
        }
        disconnect(arcContent, nullptr, this, nullptr);
        arcContent->deleteLater();
    }

    m_arcContents.remove(toolName);
    m_arcConfigs.remove(toolName);
}

void ArcContentManager::showArcContent(const QString& toolName, bool animated)
{
    if (!m_arcContents.contains(toolName)) {
        return;
    }

    ArcContentWidget* arcContent = m_arcContents[toolName];
    if (!arcContent) {
        return;
    }

    if (m_currentVisibleTool == toolName && arcContent->isContentVisible()) {
        return;
    }

    const ArcContentConfig& config = m_arcConfigs[toolName];
    if (config.autoHide) {
        hideAllArcContent(animated);
    }

    // 子widget模式下，确保在父widget内的层级顺序
    arcContent->raise();
    arcContent->showContent(animated);
    m_currentVisibleTool = toolName;

    qDebug() << "ArcContentManager: 显示arc内容" << toolName
             << "位置:" << arcContent->pos() << "尺寸:" << arcContent->size()
             << "父widget尺寸:" << (m_parentWidget ? m_parentWidget->size() : QSize());

    emit arcContentShown(toolName);
}

void ArcContentManager::hideArcContent(const QString& toolName, bool animated)
{
    if (!m_arcContents.contains(toolName)) {
        return;
    }

    ArcContentWidget* arcContent = m_arcContents[toolName];
    if (!arcContent || !arcContent->isContentVisible()) {
        return;
    }

    arcContent->hideContent(animated);

    if (m_currentVisibleTool == toolName) {
        m_currentVisibleTool.clear();
    }

    emit arcContentHidden(toolName);
}

void ArcContentManager::hideAllArcContent(bool animated)
{
    for (auto it = m_arcContents.begin(); it != m_arcContents.end(); ++it) {
        if (it.value() && it.value()->isContentVisible()) {
            it.value()->hideContent(animated);
        }
    }

    if (!m_currentVisibleTool.isEmpty()) {
        QString previousTool = m_currentVisibleTool;
        m_currentVisibleTool.clear();
        emit arcContentHidden(previousTool);
    }
}

void ArcContentManager::switchArcContent(const QString& toolName, bool animated)
{
    QString previousTool = m_currentVisibleTool;

    if (hasArcContent(toolName)) {
        showArcContent(toolName, animated);

        if (!previousTool.isEmpty() && previousTool != toolName) {
            emit arcContentSwitched(previousTool, toolName);
        }
    } else {
        hideAllArcContent(animated);
    }
}

ArcContentWidget* ArcContentManager::getArcContent(const QString& toolName) const
{
    return m_arcContents.value(toolName, nullptr);
}

bool ArcContentManager::hasArcContent(const QString& toolName) const
{
    return m_arcContents.contains(toolName);
}

QString ArcContentManager::getCurrentVisibleTool() const
{
    return m_currentVisibleTool;
}

void ArcContentManager::updateDiskCenter(const QPointF& centerPos)
{
    if (m_diskCenter != centerPos) {
        m_diskCenter = centerPos;
        updateAllArcContentPositions();
    }
}



QPointF ArcContentManager::calculateArcContentPosition(const ArcContentConfig& config) const
{
    int actualSize = ScreenAdaptationConstants::adaptSize(703) + config.expandSize * 2;

    // 计算arc内容的中心位置（相对于圆盘中心的偏移）
    QPointF arcCenter = m_diskCenter + config.offset;

    // 计算arc内容widget的左上角位置
    QPointF position = arcCenter - QPointF(actualSize / 2.0, actualSize / 2.0);

    return position;
}

void ArcContentManager::updateArcContentPosition(const QString& toolName)
{
    if (!m_arcContents.contains(toolName) || !m_arcConfigs.contains(toolName)) {
        return;
    }

    ArcContentWidget* arcContent = m_arcContents[toolName];
    const ArcContentConfig& config = m_arcConfigs[toolName];

    if (arcContent) {
        // 确保arc内容的尺寸正确
        int actualSize = ScreenAdaptationConstants::adaptSize(703) + config.expandSize * 2;
        arcContent->resize(actualSize, actualSize);

        // 设置位置
        QPointF position = calculateArcContentPosition(config);
        arcContent->move(position.toPoint());
        arcContent->setCenterOffset(config.offset);

        qDebug() << "ArcContentManager: 更新arc内容位置" << toolName
                 << "尺寸:" << actualSize << "位置:" << position
                 << "圆盘中心:" << m_diskCenter << "偏移:" << config.offset;
    }
}

void ArcContentManager::updateAllArcContentPositions()
{
    for (auto it = m_arcContents.begin(); it != m_arcContents.end(); ++it) {
        updateArcContentPosition(it.key());
    }
}


