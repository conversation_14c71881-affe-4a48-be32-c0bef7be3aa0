#ifndef ARCCONTENTMANAGER_H
#define ARCCONTENTMANAGER_H

#include <QObject>
#include <QMap>
#include <QString>
#include <QPointF>
#include <QSizeF>
#include "ArcContentWidget.h"

/**
 * @brief 弧形内容配置结构
 */
struct ArcContentConfig {
    ArcPosition position;       ///< 弧形位置
    QPointF offset;            ///< 相对于圆盘中心的偏移
    int expandSize;            ///< 扩展尺寸（在307*307基础上扩展的像素值）
    bool autoHide;             ///< 是否自动隐藏其他内容
    bool showArcBackground;    ///< 是否显示弧形背景（m_arcBackgroundRenderer） 

    ArcContentConfig(ArcPosition pos = ArcPosition::Top,
                    const QPointF& off = QPointF(0, 0),
                    int expand = 0,
                    bool hide = true,
                    bool showBg = true)
        : position(pos), offset(off), expandSize(expand), autoHide(hide), showArcBackground(showBg) {}
};

/**
 * @brief 弧形内容管理器
 *
 * 负责管理不同菜单项对应的弧形内容展示
 * 支持配置化添加新的弧形内容
 */
class ArcContentManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit ArcContentManager(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~ArcContentManager();

    /**
     * @brief 设置父窗口
     * @param parentWidget 父窗口
     */
    void setParentWidget(QWidget* parentWidget);

    /**
     * @brief 注册弧形内容
     * @param toolName 工具名称
     * @param arcContent 弧形内容组件
     * @param config 配置信息
     */
    void registerArcContent(const QString& toolName,
                           ArcContentWidget* arcContent,
                           const ArcContentConfig& config);

    /**
     * @brief 移除弧形内容
     * @param toolName 工具名称
     */
    void removeArcContent(const QString& toolName);

    /**
     * @brief 显示指定工具的弧形内容
     * @param toolName 工具名称
     * @param animated 是否使用动画
     */
    void showArcContent(const QString& toolName, bool animated = true);

    /**
     * @brief 隐藏指定工具的弧形内容
     * @param toolName 工具名称
     * @param animated 是否使用动画
     */
    void hideArcContent(const QString& toolName, bool animated = true);

    /**
     * @brief 隐藏所有弧形内容
     * @param animated 是否使用动画
     */
    void hideAllArcContent(bool animated = true);

    /**
     * @brief 切换弧形内容显示
     * @param toolName 工具名称
     * @param animated 是否使用动画
     */
    void switchArcContent(const QString& toolName, bool animated = true);

    /**
     * @brief 获取指定工具的弧形内容
     * @param toolName 工具名称
     * @return 弧形内容组件，如果不存在返回nullptr
     */
    ArcContentWidget* getArcContent(const QString& toolName) const;

    /**
     * @brief 检查是否有指定工具的弧形内容
     * @param toolName 工具名称
     * @return 是否存在
     */
    bool hasArcContent(const QString& toolName) const;

    /**
     * @brief 获取当前显示的弧形内容工具名称
     * @return 工具名称，如果没有显示返回空字符串
     */
    QString getCurrentVisibleTool() const;

    /**
     * @brief 更新圆盘中心位置
     * @param centerPos 圆盘中心位置（全局坐标）
     */
    void updateDiskCenter(const QPointF& centerPos);



signals:
    /**
     * @brief 弧形内容显示信号
     * @param toolName 工具名称
     */
    void arcContentShown(const QString& toolName);

    /**
     * @brief 弧形内容隐藏信号
     * @param toolName 工具名称
     */
    void arcContentHidden(const QString& toolName);

    /**
     * @brief 弧形内容切换信号
     * @param fromTool 之前的工具名称
     * @param toTool 当前的工具名称
     */
    void arcContentSwitched(const QString& fromTool, const QString& toTool);



private:
    /**
     * @brief 计算弧形内容的实际位置
     * @param config 配置信息
     * @return 实际位置（全局坐标）
     */
    QPointF calculateArcContentPosition(const ArcContentConfig& config) const;

    /**
     * @brief 更新弧形内容位置
     * @param toolName 工具名称
     */
    void updateArcContentPosition(const QString& toolName);

    /**
     * @brief 更新所有弧形内容位置
     */
    void updateAllArcContentPositions();

private:
    // 弧形内容存储
    QMap<QString, ArcContentWidget*> m_arcContents;     ///< 弧形内容映射
    QMap<QString, ArcContentConfig> m_arcConfigs;       ///< 弧形内容配置映射

    // 状态管理
    QString m_currentVisibleTool;                       ///< 当前显示的工具名称
    QWidget* m_parentWidget;                            ///< 父窗口

    // 圆盘信息
    QPointF m_diskCenter;                               ///< 圆盘中心位置（全局坐标）


};

#endif // ARCCONTENTMANAGER_H
