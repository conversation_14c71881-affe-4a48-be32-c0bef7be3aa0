#include "ArcContentWidget.h"
#include <QPaintEvent>
#include <QMouseEvent>
#include <QCoreApplication>
#include <QtMath>

ArcContentWidget::ArcContentWidget(ArcPosition position, QWidget *parent)
    : QWidget(parent)
    , m_position(position)
    , m_centerOffset(0, 0)
    , m_visible(false)
    , m_opacity(0.0)
    , m_scale(0.0)
    , m_baseSize(FloatMenuConstants::ARC_CONTENT_BASE_SIZE() , FloatMenuConstants::ARC_CONTENT_BASE_SIZE() )
    , m_expandSize(0)
    , m_showArcBackground(true)  // 默认显示弧形背景
    , m_arcBackgroundRenderer(nullptr)
    , m_showAnimation(nullptr)
    , m_hideAnimation(nullptr)
    , m_opacityAnimation(nullptr)
    , m_scaleAnimation(nullptr)
{
    // 改为子widget模式，移除独立窗口标志
    // setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_NoSystemBackground);
    setMouseTracking(true);

    hide();

    m_arcBackgroundRenderer = FloatMenuUtils::loadSvgRenderer(
        FloatMenuConstants::ResourcePaths::ARC_BACKGROUND_SVG,
        this,
        "ArcContentWidget"
    );

    initializeAnimations();
    resize(m_baseSize);
}

ArcContentWidget::~ArcContentWidget()
{
}

void ArcContentWidget::setCenterOffset(const QPointF& offset)
{
    if (m_centerOffset != offset) {
        m_centerOffset = offset;
        // 注意：位置设置由ArcContentManager统一管理，这里只保存偏移值
        update();
    }
}

void ArcContentWidget::showContent(bool animated)
{
    if (m_visible) return;

    m_visible = true;
    show();

    if (animated && m_showAnimation) {
        m_showAnimation->start();
    } else {
        setOpacity(1.0);
        setScale(1.0);
        emit showCompleted();
    }
}

void ArcContentWidget::hideContent(bool animated)
{
    if (!m_visible) return;

    m_visible = false;

    if (animated && m_hideAnimation) {
        m_hideAnimation->start();
    } else {
        setOpacity(0.0);
        setScale(0.0);
        hide();
        emit hideCompleted();
    }
}

void ArcContentWidget::setOpacity(qreal opacity)
{
    if (qFuzzyCompare(m_opacity, opacity)) return;

    m_opacity = qBound(0.0, opacity, 1.0);
    update();
}

void ArcContentWidget::setScale(qreal scale)
{
    if (qFuzzyCompare(m_scale, scale)) return;

    m_scale = qMax(0.0, scale);
    update();
}

void ArcContentWidget::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)

    if (m_opacity <= 0.0 || m_scale <= 0.0) return;

    QPainter painter(this);

    FloatMenuUtils::setupHighQualityRendering(&painter);
    painter.setOpacity(m_opacity);

    painter.save();

    if (!qFuzzyCompare(m_scale, 1.0)) {
        QPointF center = rect().center();
        painter.translate(center);
        painter.scale(m_scale, m_scale);
        painter.translate(-center);
    }

    QRectF arcRect;
    QPointF center = rect().center();
    arcRect.setSize(QSizeF(FloatMenuConstants::ARC_CONTENT_BASE_SIZE() , FloatMenuConstants::ARC_CONTENT_BASE_SIZE() ));
    arcRect.moveCenter(center);

    QRectF svgRect;
    svgRect.setSize(QSizeF(FloatMenuConstants::ARC_CONTENT_SVG_WIDTH() , FloatMenuConstants::ARC_CONTENT_SVG_HEIGHT() ));
    svgRect.moveTopLeft(QPointF(arcRect.right() - FloatMenuConstants::ARC_CONTENT_SVG_WIDTH() , arcRect.top()));

    painter.save();
    qreal rotationAngle = getRotationAngle();
    if (!qFuzzyIsNull(rotationAngle)) {
        painter.translate(center);
        painter.rotate(rotationAngle);
        painter.translate(-center);
    }
    drawArcBackground(&painter, svgRect);
    painter.restore();

    drawArcInnerContent(&painter, arcRect);
    drawArcContent(&painter, rect());

    painter.restore();
}

void ArcContentWidget::drawArcBackground(QPainter* painter, const QRectF& rect)
{
    // 如果不显示弧形背景，直接返回
    if (!m_showArcBackground) {
        return;
    }
    
    if (!m_arcBackgroundRenderer || !m_arcBackgroundRenderer->isValid()) return;

    painter->save();
    m_arcBackgroundRenderer->render(painter, rect);
    painter->restore();
}
QRectF ArcContentWidget::getContentRect() const
{
    QRectF bgRect = rect();
    return bgRect.adjusted(10, 10, -10, -10);
}

qreal ArcContentWidget::getRotationAngle() const
{
    switch (m_position) {
        case ArcPosition::Right:
            return 0.0;
        case ArcPosition::Top:
            return -90.0;
        case ArcPosition::Left:
            return 180.0;
        case ArcPosition::Bottom:
            return 90.0;
        default:
            return 0.0;
    }
}

QSize ArcContentWidget::getRotatedSize(const QSize& originalSize) const
{
    qreal angle = getRotationAngle();
    qreal radians = qDegreesToRadians(qAbs(angle));
    qreal cosAngle = qAbs(qCos(radians));
    qreal sinAngle = qAbs(qSin(radians));

    int newWidth = qCeil(originalSize.width() * cosAngle + originalSize.height() * sinAngle);
    int newHeight = qCeil(originalSize.width() * sinAngle + originalSize.height() * cosAngle);

    return QSize(newWidth, newHeight);
}

void ArcContentWidget::setBaseSize(const QSize& size)
{
    if (m_baseSize != size) {
        m_baseSize = size;
        
        QSize actualSize = m_baseSize + QSize(m_expandSize * 2, m_expandSize * 2);
        resize(actualSize);
    }
}

void ArcContentWidget::setExpandSize(int expandSize)
{
    if (m_expandSize != expandSize) {
        m_expandSize = expandSize;
        
        QSize actualSize = m_baseSize + QSize(m_expandSize * 2, m_expandSize * 2);
        resize(actualSize);
    }
}

void ArcContentWidget::setShowArcBackground(bool show)
{
    if (m_showArcBackground != show) {
        m_showArcBackground = show;
        update(); // 触发重绘
    }
}

void ArcContentWidget::initializeAnimations()
{
    m_opacityAnimation = new QPropertyAnimation(this, "opacity", this);
    m_opacityAnimation->setDuration(300);
    m_opacityAnimation->setEasingCurve(QEasingCurve::OutQuad);

    m_scaleAnimation = new QPropertyAnimation(this, "scale", this);
    m_scaleAnimation->setDuration(400);
    m_scaleAnimation->setEasingCurve(QEasingCurve::OutBack);

    m_showAnimation = new QParallelAnimationGroup(this);
    m_showAnimation->addAnimation(m_opacityAnimation);
    m_showAnimation->addAnimation(m_scaleAnimation);

    m_hideAnimation = new QParallelAnimationGroup(this);

    QPropertyAnimation* hideOpacityAnim = new QPropertyAnimation(this, "opacity", this);
    hideOpacityAnim->setDuration(200);
    hideOpacityAnim->setEasingCurve(QEasingCurve::InQuad);

    QPropertyAnimation* hideScaleAnim = new QPropertyAnimation(this, "scale", this);
    hideScaleAnim->setDuration(200);
    hideScaleAnim->setEasingCurve(QEasingCurve::InQuad);

    m_hideAnimation->addAnimation(hideOpacityAnim);
    m_hideAnimation->addAnimation(hideScaleAnim);

    connect(m_showAnimation, &QParallelAnimationGroup::finished,
            this, &ArcContentWidget::onShowAnimationFinished);
    connect(m_hideAnimation, &QParallelAnimationGroup::finished,
            this, &ArcContentWidget::onHideAnimationFinished);

    m_opacityAnimation->setStartValue(0.0);
    m_opacityAnimation->setEndValue(1.0);
    m_scaleAnimation->setStartValue(0.0);
    m_scaleAnimation->setEndValue(1.0);

    hideOpacityAnim->setStartValue(1.0);
    hideOpacityAnim->setEndValue(0.0);
    hideScaleAnim->setStartValue(1.0);
    hideScaleAnim->setEndValue(0.0);
}

void ArcContentWidget::onShowAnimationFinished()
{
    emit showCompleted();
}

void ArcContentWidget::onHideAnimationFinished()
{
    hide();
    emit hideCompleted();
}

void ArcContentWidget::mousePressEvent(QMouseEvent* event)
{
    // 默认实现：直接调用基类处理，不进行事件传播
    // 子类应该重写此方法来处理特定的鼠标事件
    QWidget::mousePressEvent(event);
}

void ArcContentWidget::mouseMoveEvent(QMouseEvent* event)
{
    // 默认实现：直接调用基类处理，不进行事件传播
    QWidget::mouseMoveEvent(event);
}

void ArcContentWidget::mouseReleaseEvent(QMouseEvent* event)
{
    // 默认实现：直接调用基类处理，不进行事件传播
    QWidget::mouseReleaseEvent(event);
}


