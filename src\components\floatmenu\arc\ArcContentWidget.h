#ifndef ARCCONTENTWIDGET_H
#define ARCCONTENTWIDGET_H

#include <QWidget>
#include <QPainter>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QSvgRenderer>
#include <QPixmap>
#include "../utils/FloatMenuConstants.h"
#include "../utils/FloatMenuUtils.h"

/**
 * @brief 弧形内容位置枚举
 */
enum class ArcPosition {
    Top,        ///< 圆盘上方
    Right,      ///< 圆盘右侧
    Bottom,     ///< 圆盘下方
    Left        ///< 圆盘左侧
};

/**
 * @brief 弧形内容基类
 *
 * 提供统一的弧形背景绘制和基础功能
 * 所有具体的弧形内容都应继承此类
 */
class ArcContentWidget : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(qreal opacity READ opacity WRITE setOpacity)
    Q_PROPERTY(qreal scale READ scale WRITE setScale)

public:
    /**
     * @brief 构造函数
     * @param position 弧形位置
     * @param parent 父窗口
     */
    explicit ArcContentWidget(ArcPosition position, QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    virtual ~ArcContentWidget();

    /**
     * @brief 获取弧形位置
     * @return 弧形位置
     */
    ArcPosition position() const { return m_position; }

    /**
     * @brief 设置相对于圆盘中心的偏移
     * @param offset 偏移量
     */
    void setCenterOffset(const QPointF& offset);

    /**
     * @brief 获取相对于圆盘中心的偏移
     * @return 偏移量
     */
    QPointF centerOffset() const { return m_centerOffset; }

    /**
     * @brief 显示弧形内容
     * @param animated 是否使用动画
     */
    virtual void showContent(bool animated = true);

    /**
     * @brief 隐藏弧形内容
     * @param animated 是否使用动画
     */
    virtual void hideContent(bool animated = true);

    /**
     * @brief 获取是否可见
     * @return 是否可见
     */
    bool isContentVisible() const { return m_visible; }

    /**
     * @brief 获取透明度
     * @return 透明度值 (0.0-1.0)
     */
    qreal opacity() const { return m_opacity; }

    /**
     * @brief 设置透明度
     * @param opacity 透明度值 (0.0-1.0)
     */
    void setOpacity(qreal opacity);

    /**
     * @brief 获取缩放比例
     * @return 缩放比例
     */
    qreal scale() const { return m_scale; }

    /**
     * @brief 设置缩放比例
     * @param scale 缩放比例
     */
    void setScale(qreal scale);

    /**
     * @brief 设置扩展尺寸
     * @param expandSize 扩展尺寸（在307*307基础上扩展的像素值）
     */
    void setExpandSize(int expandSize);

    /**
     * @brief 获取扩展尺寸
     * @return 扩展尺寸
     */
    int expandSize() const { return m_expandSize; }

    /**
     * @brief 设置是否显示弧形背景
     * @param show 是否显示弧形背景
     */
    void setShowArcBackground(bool show);

    /**
     * @brief 获取是否显示弧形背景
     * @return 是否显示弧形背景
     */
    bool isShowArcBackground() const { return m_showArcBackground; }

protected:
    /**
     * @brief 绘制事件
     * @param event 绘制事件
     */
    void paintEvent(QPaintEvent* event) override;

    /**
     * @brief 绘制弧形背景
     * @param painter 画笔
     * @param rect 绘制区域
     */
    virtual void drawArcBackground(QPainter* painter, const QRectF& rect);

    /**
     * @brief 绘制弧形内容（扩展区域）
     * @param painter 画笔
     * @param rect 绘制区域（包含扩展尺寸）
     *
     * 子类需要重写此方法来绘制具体内容
     */
    virtual void drawArcContent(QPainter* painter, const QRectF& rect) = 0;

    /**
     * @brief 绘制弧形内部内容（固定307*307区域）
     * @param painter 画笔
     * @param rect 绘制区域（固定307*307，不受扩展影响）
     *
     * 子类可以重写此方法在圆弧SVG内部绘制内容
     */
    virtual void drawArcInnerContent(QPainter* painter, const QRectF& rect) {
        Q_UNUSED(painter)
        Q_UNUSED(rect)
        // 默认空实现，子类可选择性重写
    }

    /**
     * @brief 获取内容区域矩形
     * @return 内容区域矩形
     */
    virtual QRectF getContentRect() const;

    /**
     * @brief 根据位置获取旋转角度
     * @return 旋转角度（度）
     */
    qreal getRotationAngle() const;

    /**
     * @brief 根据旋转角度调整widget尺寸
     * @param originalSize 原始尺寸
     * @return 调整后的尺寸
     */
    QSize getRotatedSize(const QSize& originalSize) const;

    /**
     * @brief 设置基础尺寸（未旋转时的尺寸）
     * @param size 基础尺寸
     */
    void setBaseSize(const QSize& size);

    /**
     * @brief 获取基础尺寸
     * @return 基础尺寸
     */
    QSize baseSize() const { return m_baseSize; }

    /**
     * @brief 初始化动画
     */
    virtual void initializeAnimations();

    /**
     * @brief 鼠标按下事件 - 默认实现传播事件到父widget
     * @param event 鼠标事件
     */
    void mousePressEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标移动事件 - 默认实现传播事件到父widget
     * @param event 鼠标事件
     */
    void mouseMoveEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标释放事件 - 默认实现传播事件到父widget
     * @param event 鼠标事件
     */
    void mouseReleaseEvent(QMouseEvent* event) override;

signals:
    /**
     * @brief 显示完成信号
     */
    void showCompleted();

    /**
     * @brief 隐藏完成信号
     */
    void hideCompleted();

    /**
     * @brief 内容点击信号
     * @param position 点击位置
     */
    void contentClicked(const QPointF& position);

private slots:
    /**
     * @brief 显示动画完成槽
     */
    void onShowAnimationFinished();

    /**
     * @brief 隐藏动画完成槽
     */
    void onHideAnimationFinished();

protected:
    // 基本属性
    ArcPosition m_position;                 ///< 弧形位置
    QPointF m_centerOffset;                 ///< 相对于圆盘中心的偏移
    bool m_visible;                         ///< 是否可见
    qreal m_opacity;                        ///< 透明度
    qreal m_scale;                          ///< 缩放比例
    QSize m_baseSize;                       ///< 基础尺寸（固定307*307）
    int m_expandSize;                       ///< 扩展尺寸（在基础尺寸上扩展的像素值）
    bool m_showArcBackground;               ///< 是否显示弧形背景

    // 弧形背景资源
    QSvgRenderer* m_arcBackgroundRenderer;  ///< SVG渲染器
    QPixmap m_arcBackgroundPixmap;          ///< 缓存的背景图片

    // 动画
    QParallelAnimationGroup* m_showAnimation;   ///< 显示动画组
    QParallelAnimationGroup* m_hideAnimation;   ///< 隐藏动画组
    QPropertyAnimation* m_opacityAnimation;     ///< 透明度动画
    QPropertyAnimation* m_scaleAnimation;       ///< 缩放动画
};

#endif // ARCCONTENTWIDGET_H
