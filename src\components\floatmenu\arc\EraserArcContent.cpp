#include "EraserArcContent.h"
#include <QPainter>
#include <QMouseEvent>
#include <QSvgRenderer>
#include <QtMath>
#include <QPainterPath>
#include <QLinearGradient>

EraserArcContent::EraserArcContent(ArcPosition position, QWidget *parent)
    : ArcContentWidget(position, parent)
    , m_currentSize(10.0, 10.0)
    , m_currentEraserType(1)
    , m_clearControlSvgRenderer(nullptr)
    , m_clearControlAngle(DEFAULT_CLEAR_ANGLE)
    , m_isDraggingClearControl(false)
{
    initializeResources();
    m_eraserButtonRects.resize(ERASER_ICON_COUNT);
}

EraserArcContent::~EraserArcContent()
{
    if (m_clearControlSvgRenderer) {
        delete m_clearControlSvgRenderer;
        m_clearControlSvgRenderer = nullptr;
    }
}

void EraserArcContent::setCurrentEraserType(int type)
{
    if (m_currentEraserType != type) {
        m_currentEraserType = qBound(0, type, ERASER_ICON_COUNT - 1);

        // 根据类型设置对应的大小（使用统一的常量定义）
        switch (m_currentEraserType) {
            case 0: // 小
                m_currentSize = FloatMenuConstants::ERASER_SIZE_SMALL() ;
                break;
            case 1: // 中
                m_currentSize = FloatMenuConstants::ERASER_SIZE_MEDIUM() ;
                break;
            case 2: // 大
                m_currentSize = FloatMenuConstants::ERASER_SIZE_LARGE() ;
                break;
            default:
                m_currentSize = FloatMenuConstants::ERASER_SIZE_MEDIUM() ;
                break;
        }

        update();
        emit eraserTypeChanged(m_currentEraserType);
        emit sizeChanged(m_currentSize);
    }
}

void EraserArcContent::resetClearControlState()
{
    // 重置清屏控制状态
    m_isDraggingClearControl = false;
    m_clearControlAngle = DEFAULT_CLEAR_ANGLE;
    m_lastMousePos = QPointF();

    update();
}

void EraserArcContent::hideContent(bool animated)
{
    // 在隐藏之前重置清屏控制状态
    resetClearControlState();
    ArcContentWidget::hideContent(animated);
}

void EraserArcContent::drawArcContent(QPainter* painter, const QRectF& rect)
{
    Q_UNUSED(painter)
    Q_UNUSED(rect)
}

void EraserArcContent::drawArcInnerContent(QPainter* painter, const QRectF& rect)
{
    painter->save();

    FloatMenuUtils::setupHighQualityRendering(painter);

    if (!m_eraserPixmap.isNull()) {
        drawEraserIcons(painter, rect);
    }

    // 绘制右下角的橡皮擦清空图片
    ********************(painter, rect);

    // 绘制清屏控制
    drawClearControl(painter, rect);

    painter->restore();
}

void EraserArcContent::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        QPointF pos = event->position();

        // 检查是否点击了清屏控制
        if (isPointInClearControl(pos)) {
            m_isDraggingClearControl = true;
            m_lastMousePos = pos;
            event->accept();
            return;
        }

        // 检查是否点击了橡皮擦按钮
        for (int i = 0; i < m_eraserButtonRects.size(); ++i) {
            if (m_eraserButtonRects[i].contains(pos)) {
                setCurrentEraserType(i);
                event->accept();
                return;
            }
        }

        // 如果点击了arc内容区域但不是特定按钮，发出点击信号并接受事件
        // 防止事件传播到父widget触发意外的拖拽操作
        emit contentClicked(pos);
        event->accept(); // 接受事件，阻止传播到父widget
    }

    // 调用基类方法
    ArcContentWidget::mousePressEvent(event);
}

void EraserArcContent::mouseMoveEvent(QMouseEvent* event)
{
    if (m_isDraggingClearControl) {
        QPointF pos = event->position();
        QPointF delta = pos - m_lastMousePos;

        // 基于鼠标移动距离计算角度变化
        // 在44-78度弧线范围内：
        // - 鼠标向左下移动（-x, +y）= 角度增加（图标向左下移动）
        // - 鼠标向右上移动（+x, -y）= 角度减少（图标向右上移动）
        qreal angleDelta = (-delta.x() + delta.y()) * 0.3;  // 左下移动增加角度

        qreal newAngle = m_clearControlAngle + angleDelta;

        // 限制角度范围在44到78度之间
        newAngle = qBound(DEFAULT_CLEAR_ANGLE, newAngle, MAX_CLEAR_ANGLE);

        if (qAbs(newAngle - m_clearControlAngle) > 0.1) {  // 减小更新阈值
            m_clearControlAngle = newAngle;
            update();
        }

        m_lastMousePos = pos;
        event->accept();
        return;
    }

    // 调用基类方法，确保事件传播到父widget
    ArcContentWidget::mouseMoveEvent(event);
}

void EraserArcContent::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton && m_isDraggingClearControl) {
        m_isDraggingClearControl = false;

        // 检查是否到达了78度
        if (qAbs(m_clearControlAngle - MAX_CLEAR_ANGLE) < 2.0) {  // 减小容差
            emit slideToEraseTriggered();
        }

        // 自动回到44度
        m_clearControlAngle = DEFAULT_CLEAR_ANGLE;
        update();
        event->accept();
        return;
    }

    // 调用基类方法，确保事件传播到父widget
    ArcContentWidget::mouseReleaseEvent(event);
}

void EraserArcContent::drawEraserIcons(QPainter* painter, const QRectF& rect)
{
    // 定义三种不同的图标尺寸
    QVector<int> iconSizes = {FloatMenuConstants::ERASER_SMALL_ICON_SIZE(), FloatMenuConstants::ERASER_MEDIUM_ICON_SIZE(), FloatMenuConstants::ERASER_LARGE_ICON_SIZE()};

    // 绘制每个橡皮擦图标
    for (int i = 0; i < ERASER_ICON_COUNT; ++i) {
        QPointF iconPos = getEraserIconPosition(i, rect);
        int iconSize = iconSizes[i];

        QRectF iconRect(iconPos.x() - iconSize/2,
                       iconPos.y() - iconSize/2,
                       iconSize, iconSize);

        // 更新按钮区域用于点击检测
        int areaSize = FloatMenuConstants::ERASER_CLICK_AREA_SIZE();
        m_eraserButtonRects[i] = QRectF(iconPos.x() - areaSize/2,
                       iconPos.y() - areaSize/2,
                       areaSize, areaSize);

        // 设置透明度：选中的橡皮擦100%，其他60%
        qreal opacity = (i == m_currentEraserType) ? 1.0 : 0.6;
        painter->setOpacity(opacity);

        // 如果是当前选中的橡皮擦类型，绘制渐变边框
        if (i == m_currentEraserType) {
            drawSelectionBorder(painter, m_eraserButtonRects[i]);
        }

        // 绘制橡皮擦PNG图标
        if (i < m_cachedEraserPixmaps.size() && !m_cachedEraserPixmaps[i].isNull()) {
            // 启用高质量渲染
            painter->setRenderHint(QPainter::SmoothPixmapTransform, true);

            // 使用预缓存的图标，避免实时缩放
            painter->drawPixmap(iconRect.toRect(), m_cachedEraserPixmaps[i]);
        }
    }
    painter->setOpacity(1.0);
}

QPointF EraserArcContent::getEraserIconPosition(int index, const QRectF& rect) const
{
    // 计算图标在弧形底部的位置，参考PenArcContent的颜色按钮布局
    QPointF center = rect.center();
    qreal radius = qMin(rect.width(), rect.height()) * 0.42;

    // 图标沿着弧形底部排列
    int totalIcons = ERASER_ICON_COUNT;
    qreal startAngle = -75.0;  // 起始角度（度）
    qreal endAngle = -35.0;   // 结束角度（度）
    qreal angleStep = (endAngle - startAngle) / (totalIcons - 1);
    qreal angle = startAngle + index * angleStep;

    // 转换为弧度
    qreal radians = qDegreesToRadians(angle);

    // 计算位置
    qreal x = center.x() + radius * qCos(radians);
    qreal y = center.y() + radius * qSin(radians);

    return QPointF(x, y);
}

void EraserArcContent::drawSelectionBorder(QPainter* painter, const QRectF& rect)
{
    painter->save();

    // 启用高质量渲染
    painter->setRenderHint(QPainter::Antialiasing, true);

    // 获取设备像素比例
    qreal devicePixelRatio = painter->device()->devicePixelRatio();

    // 创建与圆盘边框相同的渐变效果
    QRectF borderRect = rect.adjusted(-1, -1, 1, 1);  // 扩展2px作为边框
    QPointF center = borderRect.center();
    qreal radius = qMin(borderRect.width(), borderRect.height()) / 2.0;

    // 创建线性渐变，180度方向（从上到下）
    QLinearGradient gradient(borderRect.topLeft(), borderRect.bottomLeft());
    gradient.setColorAt(0.0, QColor(41, 218, 128, 255));   // 顶部绿色
    gradient.setColorAt(1.0, QColor(96, 83, 227, 255));    // 底部紫色

    // 设置2px的画笔宽度
    QPen borderPen(QBrush(gradient), 2.0 * devicePixelRatio);
    borderPen.setCosmetic(true);  // 确保线宽不受变换影响

    painter->setPen(borderPen);
    painter->setBrush(Qt::NoBrush);

    // 绘制圆形边框
    QPainterPath borderPath;
    borderPath.addEllipse(borderRect);
    painter->drawPath(borderPath);

    painter->restore();
}

void EraserArcContent::********************(QPainter* painter, const QRectF& rect)
{
    if (m_eraserClearPixmap.isNull()) {
        return;
    }

    painter->save();

    // 启用高质量渲染
    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->setRenderHint(QPainter::SmoothPixmapTransform, true);
    painter->setRenderHint(QPainter::LosslessImageRendering, true);

    qreal imageWidth = FloatMenuConstants::ERASER_CLEAR_IMAGE_WIDTH();
    qreal imageHeight = FloatMenuConstants::ERASER_CLEAR_IMAGE_HEIGHT();
    qreal offsetX = FloatMenuConstants::ERASER_CLEAR_IMAGE_OFFSETX();
    qreal offsetY = FloatMenuConstants::ERASER_CLEAR_IMAGE_OFFSETY();
    QRectF imageRect(rect.right() - imageWidth - offsetX,
                     rect.bottom() - imageHeight - offsetY,
                     imageWidth,
                     imageHeight);

    // 获取设备像素比例
    qreal devicePixelRatio = painter->device()->devicePixelRatio();

    // 计算高分辨率尺寸
    QSize targetSize = imageRect.size().toSize();
    QSize highResSize = targetSize * devicePixelRatio;

    // 缩放图片到高分辨率尺寸
    QPixmap scaledPixmap = m_eraserClearPixmap.scaled(
        highResSize,
        Qt::KeepAspectRatio,
        Qt::SmoothTransformation
    );

    // 设置设备像素比例
    scaledPixmap.setDevicePixelRatio(devicePixelRatio);

    // 绘制图片
    painter->drawPixmap(imageRect, scaledPixmap, scaledPixmap.rect());

    painter->restore();
}

void EraserArcContent::drawClearControl(QPainter* painter, const QRectF& rect)
{
    if (!m_clearControlSvgRenderer || !m_clearControlSvgRenderer->isValid()) {
        return;
    }

    painter->save();

    // 启用高质量渲染
    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->setRenderHint(QPainter::SmoothPixmapTransform, true);

    // 获取清屏控制位置
    QPointF controlPos = getClearControlPosition(m_clearControlAngle, rect);

    qreal CLEAR_CONTROL_SIZE = FloatMenuConstants::ERASER_CLEAR_CONTROL_SIZE();
    // 计算控制区域
    m_clearControlRect = QRectF(controlPos.x() - CLEAR_CONTROL_SIZE/2,
                               controlPos.y() - CLEAR_CONTROL_SIZE/2,
                               CLEAR_CONTROL_SIZE,
                               CLEAR_CONTROL_SIZE);

    // 绘制清屏控制SVG
    m_clearControlSvgRenderer->render(painter, m_clearControlRect);

    painter->restore();
}

void EraserArcContent::initializeResources()
{
    // 加载橡皮擦PNG文件，考虑设备像素比例
    m_eraserPixmap.load(FloatMenuConstants::ResourcePaths::ERASER_PNG);
    if (m_eraserPixmap.isNull()) {
        qWarning() << "EraserArcContent: 无法加载橡皮擦PNG文件 -" << FloatMenuConstants::ResourcePaths::ERASER_PNG;
    } else {

        // 预先缓存不同尺寸的橡皮擦图标以提高绘制质量和性能
        QVector<int> iconSizes = {
            FloatMenuConstants::ERASER_SMALL_ICON_SIZE(),
            FloatMenuConstants::ERASER_MEDIUM_ICON_SIZE(),
            FloatMenuConstants::ERASER_LARGE_ICON_SIZE()
        };

        m_cachedEraserPixmaps.resize(iconSizes.size());
        for (int i = 0; i < iconSizes.size(); ++i) {
            int size = iconSizes[i];
            m_cachedEraserPixmaps[i] = m_eraserPixmap.scaled(
                QSize(size, size) * devicePixelRatioF(),
                Qt::KeepAspectRatio,
                Qt::SmoothTransformation
            );
            m_cachedEraserPixmaps[i].setDevicePixelRatio(devicePixelRatioF());
        }
    }

    // 加载清空webp（使用统一的资源加载函数）
    m_eraserClearPixmap = FloatMenuUtils::loadWebPImage(
        FloatMenuConstants::ResourcePaths::ERASER_CLEAR_WEBP,
        "EraserArcContent",
        devicePixelRatioF()
    );

    // 加载清屏控制SVG（使用统一的资源加载函数）
    m_clearControlSvgRenderer = FloatMenuUtils::loadSvgRenderer(
        FloatMenuConstants::ResourcePaths::CLEAR_CONTROL_SVG,
        this,
        "EraserArcContent"
    );
}

QPointF EraserArcContent::getClearControlPosition(qreal angle, const QRectF& rect) const
{
    // 计算清屏控制在弧线上的位置，与getEraserIconPosition使用完全相同的计算方式
    QPointF center = rect.center();
    qreal radius = qMin(rect.width(), rect.height()) * 0.42;  // 使用与橡皮擦图标相同的半径

    // 转换角度为弧度（与getEraserIconPosition完全一致）
    qreal radians = qDegreesToRadians(angle);

    // 计算位置（与getEraserIconPosition完全一致）
    qreal x = center.x() + radius * qCos(radians);
    qreal y = center.y() + radius * qSin(radians);

    return QPointF(x, y);
}

bool EraserArcContent::isPointInClearControl(const QPointF& pos) const
{
    return m_clearControlRect.contains(pos);
}

qreal EraserArcContent::calculateAngleFromMousePos(const QPointF& mousePos, const QRectF& rect) const
{
    QPointF center = rect.center();
    QPointF delta = mousePos - center;

    // 计算角度（弧度转度数）
    qreal angle = qRadiansToDegrees(qAtan2(delta.y(), delta.x()));

    // 确保角度在0-360范围内
    if (angle < 0) {
        angle += 360;
    }

    return angle;
}
