#ifndef ERASERARCCONTENT_H
#define ERASERARCCONTENT_H

#include "ArcContentWidget.h"
#include <QPixmap>
#include <QSvgRenderer>

/**
 * @brief 橡皮擦弧形内容组件
 * 
 * 显示橡皮擦工具的相关设置，包括大小选择、清除功能等
 */
class EraserArcContent : public ArcContentWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit EraserArcContent(ArcPosition position = ArcPosition::Right, QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~EraserArcContent();

    /**
     * @brief 获取当前橡皮擦大小
     * @return 大小值
     */
    QSizeF getCurrentSize() const { return m_currentSize; }

    /**
     * @brief 设置当前选中的橡皮擦类型
     * @param type 橡皮擦类型 (0=小, 1=中, 2=大)
     */
    void setCurrentEraserType(int type);

    /**
     * @brief 获取当前选中的橡皮擦类型
     * @return 橡皮擦类型
     */
    int getCurrentEraserType() const { return m_currentEraserType; }

    /**
     * @brief 重置清屏控制状态
     *
     * 当弧形内容被隐藏时调用，确保清屏控制状态被正确重置
     */
    void resetClearControlState();

    /**
     * @brief 隐藏弧形内容（重写基类方法）
     * @param animated 是否使用动画
     */
    void hideContent(bool animated = true) override;

signals:
    /**
     * @brief 大小改变信号
     * @param size 新大小
     */
    void sizeChanged(QSizeF size);

    /**
     * @brief 橡皮擦类型改变信号
     * @param type 新类型
     */
    void eraserTypeChanged(int type);

    /**
     * @brief 滑动清屏信号
     */
    void slideToEraseTriggered();

protected:
    /**
     * @brief 绘制弧形内容
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawArcContent(QPainter* painter, const QRectF& rect) override;

    /**
     * @brief 绘制弧形内部内容
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawArcInnerContent(QPainter* painter, const QRectF& rect) override;

    /**
     * @brief 鼠标按下事件
     * @param event 鼠标事件
     */
    void mousePressEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标移动事件
     * @param event 鼠标事件
     */
    void mouseMoveEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标释放事件
     * @param event 鼠标事件
     */
    void mouseReleaseEvent(QMouseEvent* event) override;

private:
    // 橡皮擦属性
    QSizeF m_currentSize;                          ///< 当前大小
    int m_currentEraserType;                    ///< 当前选中的橡皮擦类型 (0=小, 1=中, 2=大)
    
    // 图片资源
    QPixmap m_eraserPixmap;                     ///< 橡皮擦PNG图片
    QVector<QPixmap> m_cachedEraserPixmaps;     ///< 缓存的不同尺寸橡皮擦图标
    QPixmap m_eraserClearPixmap;                ///< 橡皮擦清空图片
    QSvgRenderer* m_clearControlSvgRenderer;    ///< 清屏控制SVG渲染器
    
    // 按钮区域
    QVector<QRectF> m_eraserButtonRects;        ///< 橡皮擦按钮区域
    
    // 清屏控制相关
    qreal m_clearControlAngle;                  ///< 清屏控制当前角度
    bool m_isDraggingClearControl;              ///< 是否正在拖拽清屏控制
    QPointF m_lastMousePos;                     ///< 上次鼠标位置
    QRectF m_clearControlRect;                  ///< 清屏控制区域
    
        

    // 布局常量（使用统一的常量定义）
    int CONTENT_PADDING = FloatMenuConstants::ERASER_CONTENT_PADDING();
    static constexpr int ERASER_ICON_COUNT = FloatMenuConstants::ERASER_ICON_COUNT;
    int LARGE_ICON_SIZE = FloatMenuConstants::ERASER_LARGE_ICON_SIZE();
    int MEDIUM_ICON_SIZE = FloatMenuConstants::ERASER_MEDIUM_ICON_SIZE();
    int SMALL_ICON_SIZE = FloatMenuConstants::ERASER_SMALL_ICON_SIZE();

    static constexpr qreal DEFAULT_CLEAR_ANGLE = FloatMenuConstants::ERASER_DEFAULT_CLEAR_ANGLE;
    static constexpr qreal MAX_CLEAR_ANGLE = FloatMenuConstants::ERASER_MAX_CLEAR_ANGLE;
    int CLEAR_CONTROL_SIZE = FloatMenuConstants::ERASER_CLEAR_CONTROL_SIZE();
    int ERASER_CLICK_AREA_SIZE = FloatMenuConstants::ERASER_CLICK_AREA_SIZE();

    
    /**
     * @brief 初始化资源（SVG和WebP）
     */
    void initializeResources();
    
    /**
     * @brief 绘制橡皮擦图标
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawEraserIcons(QPainter* painter, const QRectF& rect);
    
    /**
     * @brief 获取橡皮擦图标位置
     * @param index 图标索引 (0-2)
     * @param rect 绘制区域
     * @return 图标位置
     */
    QPointF getEraserIconPosition(int index, const QRectF& rect) const;
    
    /**
     * @brief 绘制选中状态的渐变边框
     * @param painter 画笔
     * @param rect 图标区域
     */
    void drawSelectionBorder(QPainter* painter, const QRectF& rect);
    
    /**
     * @brief 绘制右下角的橡皮擦清空图片
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawEraserClearImage(QPainter* painter, const QRectF& rect);
    
    /**
     * @brief 绘制清屏控制
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawClearControl(QPainter* painter, const QRectF& rect);
    
    /**
     * @brief 获取清屏控制位置
     * @param angle 角度
     * @param rect 绘制区域
     * @return 控制位置
     */
    QPointF getClearControlPosition(qreal angle, const QRectF& rect) const;
    
    /**
     * @brief 检查点是否在清屏控制区域内
     * @param pos 点位置
     * @return 是否在区域内
     */
    bool isPointInClearControl(const QPointF& pos) const;
    
    /**
     * @brief 根据鼠标位置计算角度
     * @param mousePos 鼠标位置
     * @param rect 绘制区域
     * @return 角度值
     */
    qreal calculateAngleFromMousePos(const QPointF& mousePos, const QRectF& rect) const;
};

#endif // ERASERARCCONTENT_H
