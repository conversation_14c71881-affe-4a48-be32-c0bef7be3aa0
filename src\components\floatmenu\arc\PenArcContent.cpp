#include "PenArcContent.h"
#include <QPainter>
#include <QMouseEvent>
#include <QSvgRenderer>
#include <QtMath>
#include <QPixmap>
#include <QColorDialog>
#include <QPainterPath>
#include <QFile>
#include <QDebug>
#include <QtXml/QDomDocument>
#include "../../screen_adaptation/ScreenAdaptationManager.h"
#include "../utils/FloatMenuConstants.h"
using namespace FloatMenuConstants;

// SVG交互常量定义
const QStringList PenArcContent::COLOR_IDS = {"color-red", "color-blue", "color-yellow", "color-white", "color-custom"};
const QStringList PenArcContent::PEN_IDS = {"big", "middle", "small"};
const QStringList PenArcContent::TOOL_CLICK_IDS = {"solid-click", "dashed-click", "marker-click"};
const QStringList PenArcContent::TOOL_ACTIVE_GROUPS = {"solid-active", "dashed-active", "marker-active"};
const QStringList PenArcContent::CLICKABLE_IDS = COLOR_IDS + PEN_IDS + TOOL_CLICK_IDS;

PenArcContent::PenArcContent(ArcPosition position, QWidget *parent)
    : ArcContentWidget(position, parent)
    , m_currentColor(FloatMenuConstants::DEFAULT_PEN_COLOR)
    , m_currentWidth(1)
    , m_currentPenType(FloatMenuConstants::DEFAULT_PEN_TYPE)
    , m_showArcBackground(true)  // 默认显示弧形背景
    , m_svgRenderer(nullptr)
    , m_targetRect(QRect(0,0,0,0))
    , m_selectedColorId("color-yellow")
    , m_selectedPenId("small")
    , m_selectedToolId("solid-active")
{
    // 创建SVG渲染器
    m_svgRenderer = new QSvgRenderer(this);
    
    // 固定加载SVG资源文件
    QFile svgFile(":/images/floatmenu/floatmenu-arc-pen.svg");
    if (svgFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QString svgContent = svgFile.readAll();
        svgFile.close();
        loadSvgContent(svgContent);
    } else {
        qWarning() << "PenArcContent: 无法加载SVG资源文件:/images/floatmenu/floatmenu-arc-pen.svg";
    }
}

PenArcContent::~PenArcContent()
{
    // SVG渲染器在父对象中自动清理
}

void PenArcContent::setCurrentColor(const QColor& color)
{
    if (m_currentColor != color) {
        m_currentColor = color;
        
        // 根据颜色更新选中的颜色ID
        if (color == QColor("#55aa00")) {
            m_selectedColorId = "color-red";
        } else if (color == Qt::red) {
            m_selectedColorId = "color-blue";
        } else if (color == QColor("#ffaa00")) {
            m_selectedColorId = "color-yellow";
        } else if (color == Qt::white) {
            m_selectedColorId = "color-white";
        } else {
            m_selectedColorId = "color-custom";
        }
        
        updateSvgDisplay();
        emit colorChanged(color);
    }
}

void PenArcContent::setCurrentWidth(int width)
{
    if (m_currentWidth != width) {
        m_currentWidth = qBound(1, width, 3);
        
        // 根据宽度更新选中的画笔粗细ID
        if (width == 1) {
            m_selectedPenId = "small";
        } else if (width == 2) {
            m_selectedPenId = "middle";
        } else if (width == 3) {
            m_selectedPenId = "big";
        }
        
        updateSvgDisplay();
        emit widthChanged(m_currentWidth);
    }
}

void PenArcContent::setCurrentPenType(int type)
{
    if (m_currentPenType != type) {
        m_currentPenType = qBound(0, type, 2);
        
        // 根据类型更新选中的工具ID
        switch (type) {
            case PEN_TYPE_SOLID: m_selectedToolId = "solid-active"; break;   // 马克笔
            case PEN_TYPE_DASHED: m_selectedToolId = "dashed-active"; break;  // 虚线
            case PEN_TYPE_HIGHLIGHTER: m_selectedToolId = "marker-active"; break;  // 荧光笔
            default: m_selectedToolId = "solid-active"; break;
        }
        
        updateSvgDisplay();
        emit penTypeChanged(m_currentPenType);
    }
}

void PenArcContent::drawArcBackground(QPainter* painter, const QRectF& rect)
{
    // 如果不显示弧形背景，直接返回
    if (!m_showArcBackground) {
        return;
    }
    
    // 调用基类方法绘制弧形背景（m_arcBackgroundRenderer）
    ArcContentWidget::drawArcBackground(painter, rect);
}

void PenArcContent::drawArcContent(QPainter* painter, const QRectF& rect)
{
    // 去掉drawArcContent的内容，按照要求
    Q_UNUSED(painter)
    Q_UNUSED(rect)
}

void PenArcContent::drawArcInnerContent(QPainter* painter, const QRectF& rect)
{
    if (m_svgRenderer && m_svgRenderer->isValid()) {
        painter->save();
        FloatMenuUtils::setupHighQualityRendering(painter);
    
        // SVG原始viewBox - 使用屏幕适配
        qreal svgWidth = ScreenAdaptationConstants::adaptSize(750);
        qreal svgHeight = ScreenAdaptationConstants::adaptSize(375);
        QRectF svgViewBox(rect.right() - svgWidth, rect.top() - ScreenAdaptationConstants::adaptSize(50), svgWidth, svgHeight);
    
        m_targetRect = svgViewBox;
        m_svgRenderer->render(painter, m_targetRect);
    
        painter->restore();
    }
}

void PenArcContent::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        QPointF pos = event->position();

        // 使用SVG交互的点击检测
        QString elementId = hitTestSvgElement(pos);
        if (!elementId.isEmpty()) {
            QString elementType;
            if (COLOR_IDS.contains(elementId)) {
                elementType = "颜色";
            } else if (PEN_IDS.contains(elementId)) {
                elementType = "笔粗";
            } else if (TOOL_CLICK_IDS.contains(elementId)) {
                elementType = "工具";
            } else {
                elementType = "其它";
            }
            
            qDebug() << "PenArcContent点击命中: id=" << elementId << ", 类型=" << elementType;
            
            onSvgElementClicked(elementId, elementType);
                event->accept();
                return;
        }

        // 如果点击了arc内容区域但不是特定按钮，发出点击信号并接受事件
        // 防止事件传播到父widget触发意外的拖拽操作
        emit contentClicked(pos);
        event->accept(); // 接受事件，阻止传播到父widget
    }

    // 调用基类方法
    ArcContentWidget::mousePressEvent(event);
}

// 新的SVG交互方法实现

bool PenArcContent::loadSvgContent(const QString& svgContent)
{
    m_svgTemplate = svgContent;
    
    if (m_svgTemplate.isEmpty()) {
        qDebug() << "PenArcContent: SVG内容为空";
        return false;
    }
    
    updateSvgDisplay();
    return true;
}

void PenArcContent::updateSvgDisplay()
{
    if (m_svgTemplate.isEmpty()) {
        return;
    }
    
    QString renderedSvg = renderSvgWithSelection();
    
    m_svgRenderer->load(renderedSvg.toUtf8());
    if (!m_svgRenderer->isValid()) {
        qDebug() << "PenArcContent: SVG渲染失败";
        return;
    }
    
    // 触发重绘
    update();
}

QString PenArcContent::renderSvgWithSelection()
{
    QDomDocument doc;
    doc.setContent(m_svgTemplate);
    
    // 动态设置SVG的viewBox为更小的尺寸
    // QDomElement svgElement = doc.documentElement();
    
    // 颜色高亮：只显示选中颜色的高亮描边circle，其它隐藏
    for (const QString& id : COLOR_IDS) {
        QDomElement group;
        QDomNodeList groups = doc.elementsByTagName("g");
        for (int i = 0; i < groups.count(); ++i) {
            QDomElement g = groups.at(i).toElement();
            if (g.attribute("id") == id) {
                group = g;
                break;
            }
        }
        if (!group.isNull()) {
            QDomNodeList circles = group.elementsByTagName("circle");
            for (int i = 0; i < circles.count(); ++i) {
                QDomElement circle = circles.at(i).toElement();
                // 只处理高亮描边circle（假设高亮circle有stroke-width>1或其它区分特征）
                if (circle.hasAttribute("stroke") && circle.hasAttribute("stroke-width") && circle.attribute("stroke-width").toDouble() > 1) {
                    if (id == m_selectedColorId)
                        circle.setAttribute("display", "inline"); // 选中显示
                    else
                        circle.setAttribute("display", "none");   // 未选中隐藏
                }
            }
        }
    }
    
    // 只显示选中笔粗的path，其它隐藏
    for (const QString& id : PEN_IDS) {
        QString pathId = "path-" + id;
        QDomNodeList paths = doc.elementsByTagName("path");
        for (int i = 0; i < paths.count(); ++i) {
            QDomElement path = paths.at(i).toElement();
            if (path.attribute("id") == pathId) {
                if (id == m_selectedPenId)
                    path.setAttribute("display", "inline");
                else
                    path.setAttribute("display", "none");
            }
        }
    }
    
    // 只显示选中工具active组，其它隐藏
    for (const QString& groupId : TOOL_ACTIVE_GROUPS) {
        QDomNodeList groups = doc.elementsByTagName("g");
        for (int i = 0; i < groups.count(); ++i) {
            QDomElement g = groups.at(i).toElement();
            if (g.attribute("id") == groupId) {
                if (groupId == m_selectedToolId)
                    g.setAttribute("display", "inline");
                else
                    g.setAttribute("display", "none");
            }
        }
    }
    
    return doc.toString();
}

void PenArcContent::onSvgElementClicked(const QString& elementId, const QString& elementType)
{
    // 根据类型处理点击并发射具体信号
    if (elementType == "颜色") {
        m_selectedColorId = elementId;
        
        // 根据选中的颜色ID更新当前颜色
        if (elementId == "color-red") {
            setCurrentColor(QColor("#55aa00"));
        } else if (elementId == "color-blue") {
            setCurrentColor(Qt::red);
        } else if (elementId == "color-yellow") {
            setCurrentColor(QColor("#ffaa00"));
        } else if (elementId == "color-white") {
            setCurrentColor(Qt::white);
        } else if (elementId == "color-custom") {
            // 弹出颜色选择对话框
            showColorDialog();
        }
    }
    else if (elementType == "笔粗") {
        m_selectedPenId = elementId;
        
        // 根据选中的笔粗ID更新当前粗细
        if (elementId == "small") {
            setCurrentWidth(1);
        } else if (elementId == "middle") {
            setCurrentWidth(2);
        } else if (elementId == "big") {
            setCurrentWidth(3);
        }
    }
    else if (elementType == "工具") {
        QString activeGroup;
        if (elementId == "solid-click") activeGroup = "solid-active";
        else if (elementId == "dashed-click") activeGroup = "dashed-active";
        else if (elementId == "marker-click") activeGroup = "marker-active";
        
        if (!activeGroup.isEmpty()) {
            m_selectedToolId = activeGroup;
            
            // 根据选中的工具更新当前画笔类型
            if (activeGroup == "dashed-active") {
                setCurrentPenType(PEN_TYPE_DASHED);  // 虚线
            } else if (activeGroup == "marker-active") {
                setCurrentPenType(PEN_TYPE_HIGHLIGHTER);  // 荧光笔
            } else if (activeGroup == "solid-active") {
                setCurrentPenType(PEN_TYPE_SOLID);  // 马克笔
            }
        }
    }
}

QString PenArcContent::hitTestSvgElement(const QPointF& scenePos)
{
    if (!m_svgRenderer || !m_svgRenderer->isValid()) {
        return QString();
    }
    
    // 使用与渲染相同的偏移量（屏幕适配后的值）
    QPointF adjustedPos = scenePos - QPointF(
        ScreenAdaptationConstants::adaptSize(55), 
        ScreenAdaptationConstants::adaptSize(100)
    );
    
    // 计算SVG坐标转换比例
    // 原始SVG viewBox: 750x375, 当前渲染viewBox: 适配后的尺寸
    qreal currentWidth = ScreenAdaptationConstants::adaptSize(750);
    qreal currentHeight = ScreenAdaptationConstants::adaptSize(375);
    qreal scaleX = 750.0 / currentWidth;  // 原始宽度 / 当前宽度
    qreal scaleY = 375.0 / currentHeight; // 原始高度 / 当前高度
    
    // 将点击坐标转换为原始SVG坐标系统
    QPointF svgPos((scenePos.x() - m_targetRect.left()) * scaleX, (scenePos.y() - m_targetRect.top()) * scaleY);
    
    // 只遍历CLICKABLE_IDS
    for (const QString &id : CLICKABLE_IDS) {
        QRectF bounds = m_svgRenderer->boundsOnElement(id);
        if (bounds.contains(svgPos)) {
            qDebug() << "PenArcContent点击命中: id=" << id << ", scenePos=" << scenePos << ", adjustedPos=" << adjustedPos << ", svgPos=" << svgPos << ", bounds=" << bounds;
            return id;
        }
    }
    
    return QString();
}

void PenArcContent::showColorDialog()
{
    // 创建颜色选择对话框
    QColorDialog colorDialog(m_currentColor, this);
    colorDialog.setWindowTitle("选择画笔颜色");
    
    // 设置对话框选项
    colorDialog.setOptions(QColorDialog::DontUseNativeDialog);
    
    // 显示对话框并获取结果
    if (colorDialog.exec() == QDialog::Accepted) {
        QColor selectedColor = colorDialog.selectedColor();
        if (selectedColor.isValid()) {
            setCurrentColor(selectedColor);
        }
    }
}
