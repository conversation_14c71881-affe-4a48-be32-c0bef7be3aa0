#ifndef PENARCCONTENT_H
#define PENARCCONTENT_H

#include "ArcContentWidget.h"
#include <QColor>

class QSvgRenderer;

/**
 * @brief 画笔弧形内容组件
 * 
 * 显示画笔工具的相关设置，包括颜色选择、粗细调节等
 */
class PenArcContent : public ArcContentWidget
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param position 弧形位置
     * @param parent 父窗口
     */
    explicit PenArcContent(ArcPosition position = ArcPosition::Top, QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~PenArcContent();

    /**
     * @brief 设置当前画笔颜色
     * @param color 颜色值
     */
    void setCurrentColor(const QColor& color);

    /**
     * @brief 获取当前画笔颜色
     * @return 颜色值
     */
    QColor getCurrentColor() const { return m_currentColor; }

    /**
     * @brief 设置当前画笔粗细
     * @param width 粗细值
     */
    void setCurrentWidth(int width);

    /**
     * @brief 获取当前画笔粗细
     * @return 粗细值
     */
    int getCurrentWidth() const { return m_currentWidth; }

    /**
     * @brief 设置当前画笔类型
     * @param type 画笔类型
     */
    void setCurrentPenType(int type);

    /**
     * @brief 获取当前画笔类型
     * @return 画笔类型
     */
    int getCurrentPenType() const { return m_currentPenType; }

signals:
    /**
     * @brief 颜色改变信号
     * @param color 新颜色
     */
    void colorChanged(const QColor& color);

    /**
     * @brief 粗细改变信号
     * @param width 新粗细
     */
    void widthChanged(int width);

    /**
     * @brief 画笔类型改变信号
     * @param type 新画笔类型
     */
    void penTypeChanged(int type);

protected:
    /**
     * @brief 绘制弧形内容（扩展区域）
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawArcContent(QPainter* painter, const QRectF& rect) override;

    /**
     * @brief 绘制弧形背景
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawArcBackground(QPainter* painter, const QRectF& rect) override;

    /**
     * @brief 绘制弧形内部内容（固定307*307区域）
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawArcInnerContent(QPainter* painter, const QRectF& rect) override;

    /**
     * @brief 鼠标按下事件
     * @param event 鼠标事件
     */
    void mousePressEvent(QMouseEvent* event) override;

private:
    /**
     * @brief 加载SVG内容
     * @param svgContent SVG内容字符串
     * @return 是否加载成功
     */
    bool loadSvgContent(const QString& svgContent);

    /**
     * @brief 更新SVG显示状态
     */
    void updateSvgDisplay();

    /**
     * @brief 根据当前选中状态渲染SVG
     * @return 渲染后的SVG字符串
     */
    QString renderSvgWithSelection();

    /**
     * @brief 处理SVG元素点击
     * @param elementId 元素ID
     * @param elementType 元素类型
     */
    void onSvgElementClicked(const QString& elementId, const QString& elementType);

    /**
     * @brief 检测鼠标点击位置对应的SVG元素
     * @param scenePos 场景坐标位置
     * @return 被点击的元素ID，如果无命中则返回空字符串
     */
    QString hitTestSvgElement(const QPointF& scenePos);

    /**
     * @brief 显示颜色选择对话框
     */
    void showColorDialog();

private:
    // 画笔属性
    QColor m_currentColor;                      ///< 当前颜色
    int m_currentWidth;                         ///< 当前粗细
    int m_currentPenType;                       ///< 当前画笔类型 (0=虚线, 1=荧光笔, 2=马克笔)
    bool m_showArcBackground;                   ///< 是否显示弧形背景
    
    // SVG交互相关
    QSvgRenderer* m_svgRenderer;                ///< SVG渲染器
    QString m_svgTemplate;                      ///< SVG模板内容
    QString m_selectedColorId;                  ///< 当前选中的颜色ID
    QString m_selectedPenId;                    ///< 当前选中的画笔粗细ID
    QString m_selectedToolId;                   ///< 当前选中的工具ID
    QRectF m_targetRect;                         ///< SVG目标绘制区域
    
    // SVG交互常量
    static const QStringList COLOR_IDS;         ///< 可交互颜色ID常量
    static const QStringList PEN_IDS;           ///< 可交互笔粗ID常量  
    static const QStringList TOOL_CLICK_IDS;    ///< 所有可点击工具ID
    static const QStringList TOOL_ACTIVE_GROUPS;///< 工具激活组ID
    static const QStringList CLICKABLE_IDS;     ///< 所有可点击元素ID
};

#endif // PENARCCONTENT_H
