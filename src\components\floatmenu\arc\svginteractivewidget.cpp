#include "svginteractivewidget.h"
#include <QFile>
#include <QDebug>
#include <QMessageBox>

// 常量定义
const QStringList COLOR_IDS = {"color-red", "color-blue", "color-yellow", "color-white", "color-custom"};
const QStringList PEN_IDS = {"big", "middle", "small"};
const QStringList TOOL_CLICK_IDS = {"solid-click", "dashed-click", "marker-click"};
const QStringList TOOL_ACTIVE_GROUPS = {"solid-active", "dashed-active", "marker-active"};
const QStringList CLICKABLE_IDS = COLOR_IDS + PEN_IDS + TOOL_CLICK_IDS;

SvgInteractiveWidget::SvgInteractiveWidget(QWidget *parent)
    : QWidget(parent)
    , m_layout(nullptr)
    , m_view(nullptr)
    , m_scene(nullptr)
    , m_renderer(nullptr)
    , m_svgItem(nullptr)
    , m_selectedId("color-red")
    , m_selectedPenId("big")
    , m_selectedTool("solid-active")
{
    initializeWidget();
}

SvgInteractiveWidget::~SvgInteractiveWidget()
{
    if (m_svgItem) {
        delete m_svgItem;
    }
    if (m_renderer) {
        delete m_renderer;
    }
    if (m_scene) {
        delete m_scene;
    }
}

void SvgInteractiveWidget::initializeWidget()
{
    m_layout = new QVBoxLayout(this);
    m_layout->setContentsMargins(0, 0, 0, 0);
    
    m_scene = new QGraphicsScene(this);
    m_renderer = new QSvgRenderer(this);
    
    m_view = new SvgInteractiveView(m_scene, m_renderer, this);
    m_view->setRenderHint(QPainter::Antialiasing);
    
    m_layout->addWidget(m_view);
    
    // 连接信号
    connect(m_view, &SvgInteractiveView::elementClicked,
            this, &SvgInteractiveWidget::onElementClicked);
}

bool SvgInteractiveWidget::loadSvgFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "无法打开SVG文件:" << filePath;
        return false;
    }
    
    QString content = file.readAll();
    file.close();
    
    return loadSvgContent(content);
}

bool SvgInteractiveWidget::loadSvgContent(const QString &svgContent)
{
    m_svgTemplate = svgContent;
    
    if (m_svgTemplate.isEmpty()) {
        qDebug() << "SVG内容为空";
        return false;
    }
    
    updateSvgDisplay();
    return true;
}

void SvgInteractiveWidget::setSelectedColorId(const QString &colorId)
{
    if (COLOR_IDS.contains(colorId)) {
        m_selectedId = colorId;
        updateSvgDisplay();
    }
}

void SvgInteractiveWidget::setSelectedPenId(const QString &penId)
{
    if (PEN_IDS.contains(penId)) {
        m_selectedPenId = penId;
        updateSvgDisplay();
    }
}

void SvgInteractiveWidget::setSelectedTool(const QString &tool)
{
    if (TOOL_ACTIVE_GROUPS.contains(tool)) {
        m_selectedTool = tool;
        updateSvgDisplay();
    }
}

void SvgInteractiveWidget::onElementClicked(const QString &elementId, const QString &elementType)
{
    // 发射通用点击信号
    emit elementClicked(elementId, elementType);
    
    // 根据类型处理点击并发射具体信号
    if (elementType == "颜色") {
        m_selectedId = elementId;
        updateSvgDisplay();
        emit colorClicked(elementId);
    }
    else if (elementType == "笔粗") {
        m_selectedPenId = elementId;
        updateSvgDisplay();
        emit penSizeClicked(elementId);
    }
    else if (elementType == "工具") {
        QString activeGroup;
        if (elementId == "solid-click") activeGroup = "solid-active";
        else if (elementId == "dashed-click") activeGroup = "dashed-active";
        else if (elementId == "marker-click") activeGroup = "marker-active";
        
        if (!activeGroup.isEmpty()) {
            m_selectedTool = activeGroup;
            updateSvgDisplay();
            emit toolClicked(elementId);
        }
    }
}

void SvgInteractiveWidget::updateSvgDisplay()
{
    if (m_svgTemplate.isEmpty()) {
        return;
    }
    
    QString renderedSvg = renderSvgWithSelection();
    
    m_renderer->load(renderedSvg.toUtf8());
    if (!m_renderer->isValid()) {
        qDebug() << "SVG渲染失败";
        return;
    }
    
    // 重新创建SVG图元
    if (m_svgItem) {
        m_scene->removeItem(m_svgItem);
        delete m_svgItem;
    }
    
    m_svgItem = new QGraphicsSvgItem();
    m_svgItem->setSharedRenderer(m_renderer);
    m_scene->addItem(m_svgItem);
    m_scene->update();
}

QString SvgInteractiveWidget::renderSvgWithSelection()
{
    QDomDocument doc;
    doc.setContent(m_svgTemplate);
    
    // 颜色高亮：只显示选中颜色的高亮描边circle，其它隐藏
    for (const QString& id : COLOR_IDS) {
        QDomElement group;
        QDomNodeList groups = doc.elementsByTagName("g");
        for (int i = 0; i < groups.count(); ++i) {
            QDomElement g = groups.at(i).toElement();
            if (g.attribute("id") == id) {
                group = g;
                break;
            }
        }
        if (!group.isNull()) {
            QDomNodeList circles = group.elementsByTagName("circle");
            for (int i = 0; i < circles.count(); ++i) {
                QDomElement circle = circles.at(i).toElement();
                // 只处理高亮描边circle（假设高亮circle有stroke-width>1或其它区分特征）
                if (circle.hasAttribute("stroke") && circle.hasAttribute("stroke-width") && circle.attribute("stroke-width").toDouble() > 1) {
                    if (id == m_selectedId)
                        circle.setAttribute("display", "inline"); // 选中显示
                    else
                        circle.setAttribute("display", "none");   // 未选中隐藏
                }
            }
        }
    }
    
    // 只显示选中笔粗的path，其它隐藏
    for (const QString& id : PEN_IDS) {
        QString pathId = "path-" + id;
        QDomNodeList paths = doc.elementsByTagName("path");
        for (int i = 0; i < paths.count(); ++i) {
            QDomElement path = paths.at(i).toElement();
            if (path.attribute("id") == pathId) {
                if (id == m_selectedPenId)
                    path.setAttribute("display", "inline");
                else
                    path.setAttribute("display", "none");
            }
        }
    }
    
    // 只显示选中工具active组，其它隐藏
    for (const QString& groupId : TOOL_ACTIVE_GROUPS) {
        QDomNodeList groups = doc.elementsByTagName("g");
        for (int i = 0; i < groups.count(); ++i) {
            QDomElement g = groups.at(i).toElement();
            if (g.attribute("id") == groupId) {
                if (groupId == m_selectedTool)
                    g.setAttribute("display", "inline");
                else
                    g.setAttribute("display", "none");
            }
        }
    }
    
    return doc.toString();
}

// SvgInteractiveView 实现
SvgInteractiveView::SvgInteractiveView(QGraphicsScene *scene, QSvgRenderer *renderer, QWidget *parent)
    : QGraphicsView(scene, parent)
    , m_renderer(renderer)
{
}

void SvgInteractiveView::mousePressEvent(QMouseEvent *event)
{
    QPointF scenePos = mapToScene(event->pos());
    
    // 只遍历CLICKABLE_IDS
    for (const QString &id : CLICKABLE_IDS) {
        QRectF bounds = m_renderer->boundsOnElement(id);
        if (bounds.contains(scenePos)) {
            QString type;
            if (COLOR_IDS.contains(id)) type = "颜色";
            else if (PEN_IDS.contains(id)) type = "笔粗";
            else if (TOOL_CLICK_IDS.contains(id)) type = "工具";
            else type = "其它";
            
            qDebug() << "点击命中: id=" << id << ", 类型=" << type << ", scenePos=" << scenePos << ", bounds=" << bounds;
            
            emit elementClicked(id, type);
            return;
        }
    }
    
    QGraphicsView::mousePressEvent(event);
}

#include "svginteractivewidget.moc" 