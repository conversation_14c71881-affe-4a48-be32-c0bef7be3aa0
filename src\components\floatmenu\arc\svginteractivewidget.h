#ifndef SVGINTERACTIVEWIDGET_H
#define SVGINTERACTIVEWIDGET_H

#include <QWidget>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QtSvg/QSvgRenderer>
#include <QtSvgWidgets/QGraphicsSvgItem>
#include <QMouseEvent>
#include <QtXml/QDomDocument>
#include <QVBoxLayout>

// 可交互颜色id常量
extern const QStringList COLOR_IDS;
// 可交互笔粗id常量  
extern const QStringList PEN_IDS;
// 所有可点击元素id（颜色+笔粗+工具）
extern const QStringList TOOL_CLICK_IDS;
extern const QStringList TOOL_ACTIVE_GROUPS;
extern const QStringList CLICKABLE_IDS;

class SvgInteractiveView;

class SvgInteractiveWidget : public QWidget
{
    Q_OBJECT

public:
    explicit SvgInteractiveWidget(QWidget *parent = nullptr);
    ~SvgInteractiveWidget();

    // 加载SVG文件
    bool loadSvgFile(const QString &filePath);
    
    // 加载SVG内容
    bool loadSvgContent(const QString &svgContent);
    
    // 获取当前选中状态
    QString getSelectedColorId() const { return m_selectedId; }
    QString getSelectedPenId() const { return m_selectedPenId; }
    QString getSelectedTool() const { return m_selectedTool; }
    
    // 设置选中状态
    void setSelectedColorId(const QString &colorId);
    void setSelectedPenId(const QString &penId);
    void setSelectedTool(const QString &tool);

signals:
    // 点击事件信号
    void colorClicked(const QString &colorId);
    void penSizeClicked(const QString &penId);
    void toolClicked(const QString &toolId);
    void elementClicked(const QString &elementId, const QString &elementType);

private slots:
    void onElementClicked(const QString &elementId, const QString &elementType);

private:
    void initializeWidget();
    void updateSvgDisplay();
    QString renderSvgWithSelection();

private:
    QVBoxLayout *m_layout;
    SvgInteractiveView *m_view;
    QGraphicsScene *m_scene;
    QSvgRenderer *m_renderer;
    QGraphicsSvgItem *m_svgItem;
    
    QString m_svgTemplate;
    QString m_selectedId;
    QString m_selectedPenId; 
    QString m_selectedTool;
};

// 自定义视图类，处理SVG点击交互
class SvgInteractiveView : public QGraphicsView
{
    Q_OBJECT

public:
    SvgInteractiveView(QGraphicsScene *scene, QSvgRenderer *renderer, QWidget *parent = nullptr);

signals:
    void elementClicked(const QString &elementId, const QString &elementType);

protected:
    void mousePressEvent(QMouseEvent *event) override;

private:
    QSvgRenderer *m_renderer;
};

#endif // SVGINTERACTIVEWIDGET_H 