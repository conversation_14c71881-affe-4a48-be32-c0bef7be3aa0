#include "FloatMenuStateManager.h"
#include "../utils/FloatMenuUtils.h"

FloatMenuStateManager* FloatMenuStateManager::s_instance = nullptr;

FloatMenuStateManager::FloatMenuStateManager(QObject *parent)
    : QObject(parent)
{
    resetToDefaults();
}

FloatMenuStateManager::~FloatMenuStateManager()
{
}

FloatMenuStateManager* FloatMenuStateManager::instance()
{
    if (!s_instance) {
        s_instance = new FloatMenuStateManager();
    }
    return s_instance;
}



void FloatMenuStateManager::setExpanded(bool expanded)
{
    if (m_currentState.expanded != expanded) {
        m_currentState.expanded = expanded;
        emit expandedChanged(expanded);

    }
}

void FloatMenuStateManager::setSelectedTool(const QString& toolName)
{
    if (m_currentState.selectedTool != toolName) {
        QString previousTool = m_currentState.selectedTool;
        m_currentState.selectedTool = toolName;
        emit selectedToolChanged(toolName);

    }
}

void FloatMenuStateManager::clearSelectedTool()
{
    setSelectedTool("");
}

void FloatMenuStateManager::setVisible(bool visible)
{
    if (m_currentState.visible != visible) {
        m_currentState.visible = visible;
        emit visibilityChanged(visible);

    }
}

void FloatMenuStateManager::setPosition(const QPointF& position)
{
    if (m_currentState.position != position) {
        m_currentState.position = position;
        emit positionChanged(position);

    }
}



void FloatMenuStateManager::setPenColor(const QColor& color)
{
    if (m_currentState.penColor != color) {
        m_currentState.penColor = color;
        emit penColorChanged(color);

    }
}

void FloatMenuStateManager::setPenWidth(qreal width)
{
    qreal clampedWidth = FloatMenuUtils::clamp(width, 0.5, 50.0);
    
    if (!FloatMenuUtils::fuzzyCompare(m_currentState.penWidth, clampedWidth)) {
        m_currentState.penWidth = clampedWidth;
        emit penWidthChanged(clampedWidth);

    }
}

void FloatMenuStateManager::setPenType(int type)
{
    int clampedType = FloatMenuUtils::clamp(type,
                                           FloatMenuConstants::PEN_TYPE_SOLID,
                                           FloatMenuConstants::PEN_TYPE_HIGHLIGHTER);
    
    if (m_currentState.penType != clampedType) {
        m_currentState.penType = clampedType;
        emit penTypeChanged(clampedType);
    }
}


void FloatMenuStateManager::setEraserType(int type)
{
    int clampedType = FloatMenuUtils::clamp(type, 0, 2);

    if (m_currentState.eraserType != clampedType) {
        m_currentState.eraserType = clampedType;

        switch (clampedType) {
            case 0:
                m_currentState.eraserSize = FloatMenuConstants::ERASER_SIZE_SMALL() ;
                break;
            case 1:
                m_currentState.eraserSize = FloatMenuConstants::ERASER_SIZE_MEDIUM() ;
                break;
            case 2:
                m_currentState.eraserSize = FloatMenuConstants::ERASER_SIZE_LARGE() ;
                break;
        }
        
        emit eraserSizeChanged(m_currentState.eraserSize);
        
        QString typeName = QString("类型%1").arg(clampedType);
    }
}



void FloatMenuStateManager::setGraphicToolType(int type)
{
    if (m_currentState.graphicToolType != type) {
        m_currentState.graphicToolType = type;
        emit graphicToolTypeChanged(type);
        
        QString typeName = (type == FloatMenuConstants::NO_SELECTION) ? 
                          "无选中" : QString("类型%1").arg(type);
    }
}



void FloatMenuStateManager::setVisibleArcContent(const QString& toolName)
{
    if (m_currentState.visibleArcContent != toolName) {
        QString previousTool = m_currentState.visibleArcContent;
        m_currentState.visibleArcContent = toolName;
        emit visibleArcContentChanged(toolName);

    }
}

void FloatMenuStateManager::setFunctionalTool(const QString& toolName)
{
    if (m_currentState.functionalTool != toolName) {
        m_currentState.functionalTool = toolName;
        // 功能工具变化时不发送信号，因为这是内部逻辑
    }
}

void FloatMenuStateManager::resetToDefaults()
{
    m_currentState = FloatMenuState();
    emit stateReset();
}
