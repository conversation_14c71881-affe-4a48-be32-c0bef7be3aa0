#ifndef FLOATMENUSTATEMANAGER_H
#define FLOATMENUSTATEMANAGER_H

#include <QObject>
#include <QString>
#include <QColor>
#include <QPointF>
#include "../utils/FloatMenuConstants.h"

/**
 * @file FloatMenuStateManager.h
 * @brief FloatMenu 组件的状态管理器
 * 
 * 负责管理 FloatMenu 及其子组件的状态，
 * 提供统一的状态访问和修改接口，
 * 解耦组件间的直接依赖关系
 */

/**
 * @brief FloatMenu 状态数据结构
 */
struct FloatMenuState {
    // 菜单基本状态
    bool expanded = false;                              // 是否展开
    QString selectedTool = "";                          // 选中的工具（UI显示用，空表示无选中）
    QString functionalTool = "select";                  // 功能工具（实际使用的工具，默认套索）
    bool visible = false;                               // 是否可见
    QPointF position = QPointF(0, 0);                   // 菜单位置
    
    // 画笔状态
    QColor penColor = FloatMenuConstants::DEFAULT_PEN_COLOR;     // 画笔颜色
    qreal penWidth = FloatMenuConstants::DEFAULT_PEN_WIDTH() ;      // 画笔线宽

    int penType = FloatMenuConstants::DEFAULT_PEN_TYPE;          // 画笔类型

    // 橡皮擦状态
    QSizeF eraserSize = FloatMenuConstants::ERASER_SIZE_MEDIUM() ;     // 橡皮擦大小
    int eraserType = 1;                                          // 橡皮擦类型（0=小, 1=中, 2=大）
    
    // 图形工具状态
    int graphicToolType = FloatMenuConstants::NO_SELECTION;     // 图形工具类型
    
    // 弧形内容状态
    QString visibleArcContent = "";                             // 当前显示的弧形内容
    
    // 自动收起状态
    bool autoCollapseEnabled = true;                            // 是否启用自动收起
    int autoCollapseDelay = FloatMenuConstants::AUTO_COLLAPSE_DELAY; // 自动收起延迟
};

/**
 * @brief FloatMenu 状态管理器
 * 
 * 采用单例模式，提供全局的状态管理服务
 * 负责状态的读取、修改、验证和通知
 */
class FloatMenuStateManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return 状态管理器实例
     */
    static FloatMenuStateManager* instance();

    /**
     * @brief 获取当前状态的只读引用
     * @return 当前状态
     */
    const FloatMenuState& getCurrentState() const { return m_currentState; }

    // ========== 菜单基本状态管理 ==========
    
    /**
     * @brief 设置菜单展开状态
     * @param expanded 是否展开
     */
    void setExpanded(bool expanded);
    
    /**
     * @brief 获取菜单展开状态
     * @return 是否展开
     */
    bool isExpanded() const { return m_currentState.expanded; }
    
    /**
     * @brief 设置选中的工具
     * @param toolName 工具名称
     */
    void setSelectedTool(const QString& toolName);
    
    /**
     * @brief 获取选中的工具
     * @return 工具名称
     */
    QString getSelectedTool() const { return m_currentState.selectedTool; }
    
    /**
     * @brief 清除选中的工具
     */
    void clearSelectedTool();

    /**
     * @brief 获取功能工具（实际使用的工具）
     * @return 工具名称
     */
    QString getFunctionalTool() const { return m_currentState.functionalTool; }

    /**
     * @brief 设置功能工具（实际使用的工具）
     * @param toolName 工具名称
     */
    void setFunctionalTool(const QString& toolName);

    /**
     * @brief 设置菜单可见性
     * @param visible 是否可见
     */
    void setVisible(bool visible);
    
    /**
     * @brief 获取菜单可见性
     * @return 是否可见
     */
    bool isVisible() const { return m_currentState.visible; }
    
    /**
     * @brief 设置菜单位置
     * @param position 新位置
     */
    void setPosition(const QPointF& position);
    
    /**
     * @brief 获取菜单位置
     * @return 当前位置
     */
    QPointF getPosition() const { return m_currentState.position; }

    // ========== 画笔状态管理 ==========
    
    /**
     * @brief 设置画笔颜色
     * @param color 画笔颜色
     */
    void setPenColor(const QColor& color);
    
    /**
     * @brief 获取画笔颜色
     * @return 画笔颜色
     */
    QColor getPenColor() const { return m_currentState.penColor; }
    
    /**
     * @brief 设置画笔线宽
     * @param width 画笔线宽
     */
    void setPenWidth(qreal width);
    
    /**
     * @brief 获取画笔线宽
     * @return 画笔线宽
     */
    qreal getPenWidth() const { return m_currentState.penWidth; }
    
    /**
     * @brief 设置画笔类型
     * @param type 画笔类型
     */
    void setPenType(int type);
    
    /**
     * @brief 获取画笔类型
     * @return 画笔类型
     */
    int getPenType() const { return m_currentState.penType; }

    // ========== 橡皮擦状态管理 ==========
    
    /**
     * @brief 获取橡皮擦大小
     * @return 橡皮擦大小
     */
    QSizeF getEraserSize() const { return m_currentState.eraserSize; }
    
    /**
     * @brief 设置橡皮擦类型
     * @param type 橡皮擦类型
     */
    void setEraserType(int type);
    
    /**
     * @brief 获取橡皮擦类型
     * @return 橡皮擦类型
     */
    int getEraserType() const { return m_currentState.eraserType; }

    // ========== 图形工具状态管理 ==========
    
    /**
     * @brief 设置图形工具类型
     * @param type 图形工具类型
     */
    void setGraphicToolType(int type);
    
    /**
     * @brief 获取图形工具类型
     * @return 图形工具类型
     */
    int getGraphicToolType() const { return m_currentState.graphicToolType; }

    // ========== 弧形内容状态管理 ==========
    
    /**
     * @brief 设置可见的弧形内容
     * @param toolName 工具名称
     */
    void setVisibleArcContent(const QString& toolName);
    
    /**
     * @brief 获取可见的弧形内容
     * @return 工具名称
     */
    QString getVisibleArcContent() const { return m_currentState.visibleArcContent; }

    // ========== 状态重置和验证 ==========
    
    /**
     * @brief 重置所有状态到默认值
     */
    void resetToDefaults();

signals:
    // 菜单基本状态变化信号
    void expandedChanged(bool expanded);
    void selectedToolChanged(const QString& toolName);
    void visibilityChanged(bool visible);
    void positionChanged(const QPointF& position);
    
    // 画笔状态变化信号
    void penColorChanged(const QColor& color);
    void penWidthChanged(qreal width);
    void penTypeChanged(int type);
    
    // 橡皮擦状态变化信号
    void eraserSizeChanged(QSizeF size);
    
    // 图形工具状态变化信号
    void graphicToolTypeChanged(int type);
    
    // 弧形内容状态变化信号
    void visibleArcContentChanged(const QString& toolName);
    
    // 状态重置信号
    void stateReset();

private:
    /**
     * @brief 私有构造函数（单例模式）
     * @param parent 父对象
     */
    explicit FloatMenuStateManager(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~FloatMenuStateManager();
    
    /**
     * @brief 禁用拷贝构造函数
     */
    FloatMenuStateManager(const FloatMenuStateManager&) = delete;
    
    /**
     * @brief 禁用赋值操作符
     */
    FloatMenuStateManager& operator=(const FloatMenuStateManager&) = delete;

private:
    static FloatMenuStateManager* s_instance;   ///< 单例实例
    FloatMenuState m_currentState;              ///< 当前状态
};

#endif // FLOATMENUSTATEMANAGER_H
