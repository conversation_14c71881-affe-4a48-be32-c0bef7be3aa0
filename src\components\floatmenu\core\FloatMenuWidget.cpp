#include "FloatMenuWidget.h"
#include "../arc/ArcContentManager.h"
#include "../arc/PenArcContent.h"
#include "../arc/EraserArcContent.h"
#include "../graphics/GraphicToolPanel.h"
#include "../../../whiteboard/core/WhiteBoardTypes.h"
#include <QPainter>
#include <QApplication>
#include <QScreen>
#include <QMouseEvent>
#include <QMoveEvent>
#include <QResizeEvent>
#include <QHideEvent>
#include <QShowEvent>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QEasingCurve>
#include <QtMath>
#include <QTimer>
#include <QTimer>
#include <QElapsedTimer>

FloatMenuWidget::FloatMenuWidget(QWidget *parent)
    : QWidget(parent)
    , m_stateManager(FloatMenuStateManager::instance())
    , m_expandAnimationGroup(nullptr)
    , m_collapseAnimationGroup(nullptr)
    , m_showAnimation(nullptr)
    , m_hideAnimation(nullptr)
    , m_sectorRotationAnimation(nullptr)
    , m_backgroundScale(0.0)
    , m_backgroundOpacity(0.0)
    , m_starScale(0.0)
    , m_starOpacity(0.0)
    , m_sectorScale(0.0)
    , m_sectorOpacity(0.0)
    , m_toolButtonsOpacity(0.0)
    , m_toolButtonsScale(0.0)
    , m_sectorRotation(60.0)
    , m_isDragging(false)
    , m_hoveredToolIndex(-1)
    , m_centerButtonHovered(false)
    , m_dragStarted(false)
    , m_hasDragged(false)
    , m_clickedToolIndex(-1)
    , m_iconFontManager(nullptr)
    , m_iconFontLoaded(false)
    , m_coreOffset(0, 0)
    , m_arcContentManager(nullptr)
    , m_graphicToolPanel(nullptr)
    , m_autoCollapseTimer(nullptr)
    , m_svgManager(nullptr)
    , m_mainWindow(nullptr)
{
    // 启动防重复点击计时器
    m_lastClickTimer.start();

    setAttribute(Qt::WA_TranslucentBackground);

    // 如果有父窗口，设置适当的属性来维持层级关系
    if (!parent) {
        // 无父窗口时才使用WindowStaysOnTopHint
        setWindowFlags(windowFlags() | Qt::WindowStaysOnTopHint);
    }

    // 调整窗口大小
    const int arcSpace = ScreenAdaptationConstants::adaptSize(255);
    const int panelSpace = FloatMenuConstants::GRAPHIC_PANEL_WIDTH();
    int maxWidth = CORE_WIDGET_SIZE + panelSpace + ScreenAdaptationConstants::adaptSize(276);
    int maxHeight = CORE_WIDGET_SIZE + arcSpace * 2;
    resize(maxWidth, maxHeight);

    const int coreLeftOffset = arcSpace;
    const int coreBottomOffset = ScreenAdaptationConstants::adaptSize(149);
    m_coreOffset = QPointF(coreLeftOffset, maxHeight - CORE_WIDGET_SIZE - coreBottomOffset);

    setMouseTracking(true);
    m_iconFontManager = IconFontManager::instance();

    initializeSvgManager();

    // 初始化自动收起定时器
    m_autoCollapseTimer = new QTimer(this);
    m_autoCollapseTimer->setSingleShot(true);
    m_autoCollapseTimer->setInterval(AUTO_COLLAPSE_DELAY);
    connect(m_autoCollapseTimer, &QTimer::timeout, this, &FloatMenuWidget::onAutoCollapseTimeout);

    // 初始化组件
    initializeTools();
    initializeAnimations();
    loadResources();
    initializeArcContentManager();
    setInitialPosition();
    connectStateManagerSignals();
    initializePerformanceOptimizations();

    // 设置widget属性
    setAttribute(Qt::WA_NoSystemBackground);
    setAutoFillBackground(false);

    // 延迟发送默认工具类型信号
    QTimer::singleShot(200, this, [this]() {
        emit toolTypeChanged(ToolType::Lasso);
        qDebug() << "FloatMenuWidget: 延迟发送默认套索工具类型信号";
    });
}

void FloatMenuWidget::connectStateManagerSignals()
{
    // 转发状态管理器的信号
    connect(m_stateManager, &FloatMenuStateManager::expandedChanged,
            this, &FloatMenuWidget::expandedChanged);
    connect(m_stateManager, &FloatMenuStateManager::selectedToolChanged,
            this, &FloatMenuWidget::selectedToolChanged);

    connect(m_stateManager, &FloatMenuStateManager::penColorChanged,
            this, &FloatMenuWidget::penColorChanged);
    connect(m_stateManager, &FloatMenuStateManager::penWidthChanged,
            this, &FloatMenuWidget::penWidthChanged);
    connect(m_stateManager, &FloatMenuStateManager::penTypeChanged,
            this, &FloatMenuWidget::penTypeChanged);

    connect(m_stateManager, &FloatMenuStateManager::eraserSizeChanged,
            this, [this](const QSizeF& size) {
                emit eraserSizeChanged(size);
            });

    connect(m_stateManager, &FloatMenuStateManager::graphicToolTypeChanged,
            this, &FloatMenuWidget::graphicToolTypeChanged);
}

void FloatMenuWidget::initializePerformanceOptimizations()
{
    // 获取设备像素比例
    qreal devicePixelRatio = devicePixelRatioF();

    // 预加载SVG缓存
    FloatMenuUtils::preloadSvgCache(devicePixelRatio);

    // 设置widget属性以优化渲染性能
    setAttribute(Qt::WA_OpaquePaintEvent, false);  // 支持透明背景
    setAttribute(Qt::WA_NoSystemBackground, true); // 不使用系统背景
    setAttribute(Qt::WA_TranslucentBackground, true); // 支持半透明

    // 启用硬件加速（如果可用）
    if (QApplication::testAttribute(Qt::AA_UseOpenGLES)) {
        setAttribute(Qt::WA_AcceptTouchEvents, true);
    }
}

bool FloatMenuWidget::isExpanded() const
{
    return m_stateManager->isExpanded();
}

QString FloatMenuWidget::selectedTool() const
{
    return m_stateManager->getSelectedTool();
}

FloatMenuWidget::~FloatMenuWidget()
{
    // 清理弧形内容管理器
    if (m_arcContentManager) {
        delete m_arcContentManager;
        m_arcContentManager = nullptr;
    }

    // 清理图形工具面板
    if (m_graphicToolPanel) {
        delete m_graphicToolPanel;
        m_graphicToolPanel = nullptr;
    }
}

void FloatMenuWidget::initializeTools()
{
    // 清空现有工具
    m_tools.clear();

    // 加载iconfont字体
    if (m_iconFontManager) {
        m_iconFontLoaded = m_iconFontManager->loadDefaultIconFont();
        if (!m_iconFontLoaded) {
            qWarning() << "FloatMenuWidget: 无法加载iconfont字体";
        }
    }

    m_tools.append(FloatMenuTool("pen", IconFonts::Icons::PEN_NORMAL, 0, QColor("#E74C3C")));        // 0xe665
    m_tools.append(FloatMenuTool("eraser", IconFonts::Icons::ERASER_ALT, 60, QColor("#F39C12")));    // 0xe667
    m_tools.append(FloatMenuTool("graphic", IconFonts::Icons::GRAPHIC, 120, QColor("#3498DB")));     // 0xe643
    m_tools.append(FloatMenuTool("clear", IconFonts::Icons::SAVE_BOARD, 180, QColor("#9B59B6"), true));                   // 清除图标
    m_tools.append(FloatMenuTool("undo", IconFonts::Icons::UNDO, 240, QColor("#2ECC71"), true));    // 0xe647
    m_tools.append(FloatMenuTool("passThrough", IconFonts::Icons::SELECT_TOOL2, 300, QColor("#34495E"))); // 0xe662

    // 初始化工具按钮缩放值
    m_toolButtonScales.resize(m_tools.size());
    for (int i = 0; i < m_toolButtonScales.size(); ++i) {
        m_toolButtonScales[i] = 1.0;
    }
}

void FloatMenuWidget::initializeAnimations()
{
    m_expandAnimationGroup = new QParallelAnimationGroup(this);

    // 背景圆盘缩放动画 - 400ms OutBack overshoot=1.1
    QPropertyAnimation* bgScaleExpandAnim = new QPropertyAnimation(this, "backgroundScale");
    bgScaleExpandAnim->setDuration(400);
    QEasingCurve bgScaleCurve(QEasingCurve::OutBack);
    bgScaleCurve.setOvershoot(1.1);
    bgScaleExpandAnim->setEasingCurve(bgScaleCurve);
    bgScaleExpandAnim->setStartValue(0.0);
    bgScaleExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(bgScaleExpandAnim);

    // 背景圆盘透明度动画 - 300ms OutQuad
    QPropertyAnimation* bgOpacityExpandAnim = new QPropertyAnimation(this, "backgroundOpacity");
    bgOpacityExpandAnim->setDuration(300);
    bgOpacityExpandAnim->setEasingCurve(QEasingCurve::OutQuad);
    bgOpacityExpandAnim->setStartValue(0.0);
    bgOpacityExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(bgOpacityExpandAnim);

    // 星形背景缩放动画 - 400ms OutBack overshoot=1.1
    QPropertyAnimation* starScaleExpandAnim = new QPropertyAnimation(this, "starScale");
    starScaleExpandAnim->setDuration(400);
    starScaleExpandAnim->setEasingCurve(bgScaleCurve); // 复用相同曲线
    starScaleExpandAnim->setStartValue(0.0);
    starScaleExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(starScaleExpandAnim);

    // 星形背景透明度动画 - 300ms OutQuad
    QPropertyAnimation* starOpacityExpandAnim = new QPropertyAnimation(this, "starOpacity");
    starOpacityExpandAnim->setDuration(300);
    starOpacityExpandAnim->setEasingCurve(QEasingCurve::OutQuad);
    starOpacityExpandAnim->setStartValue(0.0);
    starOpacityExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(starOpacityExpandAnim);

    // 扇形指示器缩放动画 - 400ms OutBack overshoot=1.1
    QPropertyAnimation* sectorScaleExpandAnim = new QPropertyAnimation(this, "sectorScale");
    sectorScaleExpandAnim->setDuration(400);
    sectorScaleExpandAnim->setEasingCurve(bgScaleCurve); // 复用相同曲线
    sectorScaleExpandAnim->setStartValue(0.0);
    sectorScaleExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(sectorScaleExpandAnim);

    // 扇形指示器透明度动画 - 300ms OutQuad
    QPropertyAnimation* sectorOpacityExpandAnim = new QPropertyAnimation(this, "sectorOpacity");
    sectorOpacityExpandAnim->setDuration(300);
    sectorOpacityExpandAnim->setEasingCurve(QEasingCurve::OutQuad);
    sectorOpacityExpandAnim->setStartValue(0.0);
    sectorOpacityExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(sectorOpacityExpandAnim);

    // 工具按钮透明度动画 - 400ms OutQuad
    QPropertyAnimation* toolOpacityExpandAnim = new QPropertyAnimation(this, "toolButtonsOpacity");
    toolOpacityExpandAnim->setDuration(400);
    toolOpacityExpandAnim->setEasingCurve(QEasingCurve::OutQuad);
    toolOpacityExpandAnim->setStartValue(0.0);
    toolOpacityExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(toolOpacityExpandAnim);

    // 工具按钮缩放动画 - 600ms OutBack overshoot=1.1
    QPropertyAnimation* toolScaleExpandAnim = new QPropertyAnimation(this, "toolButtonsScale");
    toolScaleExpandAnim->setDuration(600);
    QEasingCurve toolScaleCurve(QEasingCurve::OutBack);
    toolScaleCurve.setOvershoot(1.1);
    toolScaleExpandAnim->setEasingCurve(toolScaleCurve);
    toolScaleExpandAnim->setStartValue(0.0);
    toolScaleExpandAnim->setEndValue(1.0);
    m_expandAnimationGroup->addAnimation(toolScaleExpandAnim);

    // 创建收起动画组
    m_collapseAnimationGroup = new QParallelAnimationGroup(this);

    // 收起动画（反向，使用OutQuad）
    QPropertyAnimation* bgScaleCollapseAnim = new QPropertyAnimation(this, "backgroundScale");
    bgScaleCollapseAnim->setDuration(300);
    bgScaleCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    bgScaleCollapseAnim->setStartValue(1.0);
    bgScaleCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(bgScaleCollapseAnim);

    QPropertyAnimation* bgOpacityCollapseAnim = new QPropertyAnimation(this, "backgroundOpacity");
    bgOpacityCollapseAnim->setDuration(300);
    bgOpacityCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    bgOpacityCollapseAnim->setStartValue(1.0);
    bgOpacityCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(bgOpacityCollapseAnim);

    QPropertyAnimation* starScaleCollapseAnim = new QPropertyAnimation(this, "starScale");
    starScaleCollapseAnim->setDuration(300);
    starScaleCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    starScaleCollapseAnim->setStartValue(1.0);
    starScaleCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(starScaleCollapseAnim);

    QPropertyAnimation* starOpacityCollapseAnim = new QPropertyAnimation(this, "starOpacity");
    starOpacityCollapseAnim->setDuration(300);
    starOpacityCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    starOpacityCollapseAnim->setStartValue(1.0);
    starOpacityCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(starOpacityCollapseAnim);

    QPropertyAnimation* sectorScaleCollapseAnim = new QPropertyAnimation(this, "sectorScale");
    sectorScaleCollapseAnim->setDuration(300);
    sectorScaleCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    sectorScaleCollapseAnim->setStartValue(1.0);
    sectorScaleCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(sectorScaleCollapseAnim);

    QPropertyAnimation* sectorOpacityCollapseAnim = new QPropertyAnimation(this, "sectorOpacity");
    sectorOpacityCollapseAnim->setDuration(300);
    sectorOpacityCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    sectorOpacityCollapseAnim->setStartValue(1.0);
    sectorOpacityCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(sectorOpacityCollapseAnim);

    QPropertyAnimation* toolOpacityCollapseAnim = new QPropertyAnimation(this, "toolButtonsOpacity");
    toolOpacityCollapseAnim->setDuration(300);
    toolOpacityCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    toolOpacityCollapseAnim->setStartValue(1.0);
    toolOpacityCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(toolOpacityCollapseAnim);

    QPropertyAnimation* toolScaleCollapseAnim = new QPropertyAnimation(this, "toolButtonsScale");
    toolScaleCollapseAnim->setDuration(300);
    toolScaleCollapseAnim->setEasingCurve(QEasingCurve::OutQuad);
    toolScaleCollapseAnim->setStartValue(1.0);
    toolScaleCollapseAnim->setEndValue(0.0);
    m_collapseAnimationGroup->addAnimation(toolScaleCollapseAnim);

    // 连接动画完成信号
    connect(m_expandAnimationGroup, &QParallelAnimationGroup::finished,
            this, &FloatMenuWidget::onExpandAnimationFinished);
    connect(m_collapseAnimationGroup, &QParallelAnimationGroup::finished,
            this, &FloatMenuWidget::onCollapseAnimationFinished);

    // 连接所有动画到重绘
    for (int i = 0; i < m_expandAnimationGroup->animationCount(); ++i) {
        if (auto* propAnim = qobject_cast<QPropertyAnimation*>(m_expandAnimationGroup->animationAt(i))) {
            connect(propAnim, &QPropertyAnimation::valueChanged,
                    this, QOverload<>::of(&QWidget::update));
        }
    }
    for (int i = 0; i < m_collapseAnimationGroup->animationCount(); ++i) {
        if (auto* propAnim = qobject_cast<QPropertyAnimation*>(m_collapseAnimationGroup->animationAt(i))) {
            connect(propAnim, &QPropertyAnimation::valueChanged,
                    this, QOverload<>::of(&QWidget::update));
        }
    }

    // 创建显示/隐藏动画组
    m_showAnimation = new QParallelAnimationGroup(this);
    m_hideAnimation = new QParallelAnimationGroup(this);

    // 透明度动画
    QPropertyAnimation* showOpacityAnim = new QPropertyAnimation(this, "windowOpacity");
    showOpacityAnim->setDuration(400);
    showOpacityAnim->setStartValue(0.0);
    showOpacityAnim->setEndValue(1.0);
    showOpacityAnim->setEasingCurve(QEasingCurve::OutQuad);
    m_showAnimation->addAnimation(showOpacityAnim);

    QPropertyAnimation* hideOpacityAnim = new QPropertyAnimation(this, "windowOpacity");
    hideOpacityAnim->setDuration(300);
    hideOpacityAnim->setStartValue(1.0);
    hideOpacityAnim->setEndValue(0.0);
    hideOpacityAnim->setEasingCurve(QEasingCurve::InQuad);
    m_hideAnimation->addAnimation(hideOpacityAnim);

    // 连接隐藏动画完成信号
    connect(m_hideAnimation, &QParallelAnimationGroup::finished,
            this, &QWidget::hide);

    // 创建扇形旋转动画
    m_sectorRotationAnimation = new QPropertyAnimation(this, "sectorRotation", this);
    m_sectorRotationAnimation->setDuration(300);
    m_sectorRotationAnimation->setEasingCurve(QEasingCurve::OutQuad);

    // 连接扇形旋转动画到重绘
    connect(m_sectorRotationAnimation, &QPropertyAnimation::valueChanged,
            this, QOverload<>::of(&QWidget::update));
}

void FloatMenuWidget::loadResources()
{
    // 加载背景图片
    m_starBackgroundPixmap.load(":/images/whiteboard-star-bg.png");
    m_sectorIndicatorPixmap.load(":/images/whiteboard-sector.png");
    m_centerButtonPixmap.load(":/images/whiteboard-tooldics-center.png");

    if (m_starBackgroundPixmap.isNull()) {
        qWarning() << "FloatMenuWidget: 无法加载星形背景图片";
    }
    if (m_sectorIndicatorPixmap.isNull()) {
        qWarning() << "FloatMenuWidget: 无法加载扇形指示器图片";
    }
    if (m_centerButtonPixmap.isNull()) {
        qWarning() << "FloatMenuWidget: 无法加载中心按钮图片";
    }


}

void FloatMenuWidget::setInitialPosition()
{
    // 独立窗口模式，优先相对于主窗口定位
    if (m_mainWindow && m_mainWindow->isVisible()) {
        // 相对于主窗口设置位置（左下角）
        QPoint mainWindowPos = m_mainWindow->pos();
        QSize mainWindowSize = m_mainWindow->size();
        const int windowMargin = 20; // 使用更小的边距，更靠近窗口边缘
        int x = mainWindowPos.x() + windowMargin;
        int y = mainWindowPos.y() + mainWindowSize.height() - height() - windowMargin;
        move(x, y);
        qDebug() << "FloatMenuWidget: 设置初始位置相对于主窗口左下角:" << QPoint(x, y)
                 << "主窗口位置:" << mainWindowPos << "主窗口大小:" << mainWindowSize;
    } else {
        // 独立窗口模式，设置初始位置（屏幕左下角）
        QScreen* screen = QApplication::primaryScreen();
        if (screen) {
            QRect screenGeometry = screen->availableGeometry();
            const int screenMargin = 20; // 使用更小的边距，更靠近屏幕边缘
            int x = screenGeometry.left() + screenMargin;
            int y = screenGeometry.bottom() - height() - screenMargin;
            move(x, y);
            qDebug() << "FloatMenuWidget: 设置初始位置为屏幕左下角:" << QPoint(x, y)
                     << "屏幕几何:" << screenGeometry << "窗口大小:" << size();
        } else {
            qDebug() << "FloatMenuWidget: 无法获取屏幕信息";
        }
    }
}

void FloatMenuWidget::setExpanded(bool expanded)
{
    if (m_stateManager->isExpanded() != expanded) {
        m_stateManager->setExpanded(expanded);

        // 停止当前动画
        m_expandAnimationGroup->stop();
        m_collapseAnimationGroup->stop();

        // 根据展开状态处理弧形内容
        if (m_arcContentManager) {
            if (!expanded) {
                // 收起圆盘时，立即隐藏所有弧形内容，确保不会独立显示
                m_arcContentManager->hideAllArcContent(false); // 不使用动画，立即隐藏
            } else {
                // 展开圆盘时，只有当有选中工具时才显示弧形内容
                if (!m_stateManager->getSelectedTool().isEmpty()) {
                    m_arcContentManager->showArcContent(m_stateManager->getSelectedTool(), true);
                } else {
                    // 没有选中工具时，确保所有弧形内容都隐藏
                    m_arcContentManager->hideAllArcContent(false);
                }
            }
        }

        // 根据展开状态处理图形工具面板
        if (m_graphicToolPanel) {
            if (!expanded) {
                // 收起圆盘时，隐藏图形工具面板
                m_graphicToolPanel->hidePanel(true);
            } else {
                // 展开圆盘时，如果当前选中的是图形工具，则显示面板
                if (m_stateManager->getSelectedTool() == "graphic") {
                    // 先确定面板位置，再显示
                    QPointF globalCenter = mapToGlobal(getCoreRect().center().toPoint());
                    m_graphicToolPanel->determineInitialPosition(globalCenter);
                    m_graphicToolPanel->showPanel(true);
                }
            }
        }

        // 控制自动收起定时器
        if (expanded) {
            // 展开时启动自动收起定时器
            startAutoCollapseTimer();
        } else {
            // 收起时停止自动收起定时器
            stopAutoCollapseTimer();
        }

        // 启动相应动画
        if (expanded) {
            m_expandAnimationGroup->start();
        } else {
            m_collapseAnimationGroup->start();
        }

        emit expandedChanged(expanded);
        emit menuToggled(expanded);


    }
}

void FloatMenuWidget::setSelectedTool(const QString& toolName)
{
    if (m_stateManager->getSelectedTool() != toolName) {
        QString previousTool = m_stateManager->getSelectedTool();
        m_stateManager->setSelectedTool(toolName);



        // 重绘中心按钮以显示新图标
        update();

        // 计算目标旋转角度并启动动画
        qreal targetAngle = 60; // 默认角度（无选中状态）
        
        if (!toolName.isEmpty()) {
            // 有选中工具时，计算对应角度
            for (const auto& tool : m_tools) {
                if (tool.name == toolName) {
                    targetAngle = tool.angle + 60; // 加60度修正
                    break;
                }
            }
        }
        // 如果toolName为空（取消选中），则使用默认角度60度

        // 启动扇形旋转动画
        m_sectorRotationAnimation->stop();
        m_sectorRotationAnimation->setStartValue(m_sectorRotation);
        m_sectorRotationAnimation->setEndValue(targetAngle);
        m_sectorRotationAnimation->start();

        // 注意：selectedToolChanged 信号现在由状态管理器发出

        // 特殊处理图形工具：如果选中图形工具但未选择具体图形，发送选择工具信号
        if (toolName == "graphic" && m_stateManager->getGraphicToolType() == FloatMenuConstants::NO_SELECTION) {
            emit toolSelected("graphic_-1");
        }

        // 处理弧形内容切换
        if (m_arcContentManager) {
            if (toolName.isEmpty()) {
                // 取消选中时，隐藏所有弧形内容
                m_arcContentManager->hideAllArcContent(true);
            } else {
                // 选中工具时，切换到对应的弧形内容
                m_arcContentManager->switchArcContent(toolName);
            }
        }

        // 处理图形工具面板
        if (m_graphicToolPanel) {
            if (toolName == "graphic") {
                // 先确定面板位置，再显示图形工具面板
                QPointF globalCenter = mapToGlobal(getCoreRect().center().toPoint());
                m_graphicToolPanel->determineInitialPosition(globalCenter);
                m_graphicToolPanel->showPanel(true);
            } else {
                // 隐藏图形工具面板（包括取消选中的情况）
                m_graphicToolPanel->hidePanel(true);
            }
        }
    }
}

void FloatMenuWidget::clearSelectedTool()
{
    if (!m_stateManager->getSelectedTool().isEmpty()) {
        QString previousTool = m_stateManager->getSelectedTool();
        setSelectedTool("");  // UI显示为无选中
        m_stateManager->setFunctionalTool("select");  // 功能上使用套索工具
        emit toolDeselected(previousTool);  // 发送取消选中信号
        emit toolSelected("select");        // 发送选中套索工具信号

    }
}

void FloatMenuWidget::showFloatingWindow()
{
    // 独立窗口模式
    show();

    // 设置初始透明度并启动显示动画
    setWindowOpacity(0.0);
    if (m_showAnimation) {
        m_showAnimation->start();
    } else {
        // 如果动画未初始化，直接显示
        setWindowOpacity(1.0);
    }

    qDebug() << "FloatMenuWidget: 显示独立窗口，位置:" << pos() << "大小:" << size();
}

void FloatMenuWidget::hideFloatingWindow()
{
    // 隐藏FloatMenuWidget时，确保所有弧形内容也被隐藏
    if (m_arcContentManager) {
        m_arcContentManager->hideAllArcContent(false); // 立即隐藏，不使用动画
    }

    // 启动隐藏动画
    m_hideAnimation->start();
}

void FloatMenuWidget::toggleFloatingWindow()
{
    if (isVisible()) {
        hideFloatingWindow();
    } else {
        showFloatingWindow();
    }
}

void FloatMenuWidget::setMainWindow(QWidget* mainWindow)
{
    m_mainWindow = mainWindow;
}

void FloatMenuWidget::updatePositionRelativeToMainWindow()
{
    // 在动画期间或拖拽期间不自动更新位置，避免位置跳跃
    if (m_expandAnimationGroup->state() == QAbstractAnimation::Running ||
        m_collapseAnimationGroup->state() == QAbstractAnimation::Running ||
        m_isDragging || m_dragStarted) {
        qDebug() << "FloatMenuWidget: 动画或拖拽期间跳过位置更新";
        return;
    }

    if (m_mainWindow && m_mainWindow->isVisible()) {
        // 保持相对于主窗口的位置
        QRect mainWindowRect = m_mainWindow->geometry();
        QSize widgetSize = size();
        const int windowMargin = 20; // 使用更小的边距，更靠近窗口边缘

        // 计算期望位置（左下角）
        int x = mainWindowRect.left() + windowMargin;
        int y = mainWindowRect.bottom() - widgetSize.height() - windowMargin;

        // 确保位置在主窗口范围内
        QPoint constrainedPos = constrainPositionToMainWindow(QPoint(x, y));

        move(constrainedPos);
        qDebug() << "FloatMenuWidget: 更新位置相对于主窗口:" << constrainedPos;
    }
}

void FloatMenuWidget::setBackgroundScale(qreal scale)
{
    if (qAbs(m_backgroundScale - scale) > 0.001) {
        m_backgroundScale = qBound(0.0, scale, 2.0);
        update();
    }
}

void FloatMenuWidget::setBackgroundOpacity(qreal opacity)
{
    if (qAbs(m_backgroundOpacity - opacity) > 0.001) {
        m_backgroundOpacity = qBound(0.0, opacity, 1.0);
        update();
    }
}

void FloatMenuWidget::setStarScale(qreal scale)
{
    if (qAbs(m_starScale - scale) > 0.001) {
        m_starScale = qBound(0.0, scale, 2.0);
        update();
    }
}

void FloatMenuWidget::setStarOpacity(qreal opacity)
{
    if (qAbs(m_starOpacity - opacity) > 0.001) {
        m_starOpacity = qBound(0.0, opacity, 1.0);
        update();
    }
}

void FloatMenuWidget::setSectorScale(qreal scale)
{
    if (qAbs(m_sectorScale - scale) > 0.001) {
        m_sectorScale = qBound(0.0, scale, 2.0);
        update();
    }
}

void FloatMenuWidget::setSectorOpacity(qreal opacity)
{
    if (qAbs(m_sectorOpacity - opacity) > 0.001) {
        m_sectorOpacity = qBound(0.0, opacity, 1.0);
        update();
    }
}

void FloatMenuWidget::setToolButtonsOpacity(qreal opacity)
{
    if (qAbs(m_toolButtonsOpacity - opacity) > 0.001) {
        m_toolButtonsOpacity = qBound(0.0, opacity, 1.0);
        update();
    }
}

void FloatMenuWidget::setToolButtonsScale(qreal scale)
{
    if (qAbs(m_toolButtonsScale - scale) > 0.001) {
        m_toolButtonsScale = qBound(0.0, scale, 2.0);
        update();
    }
}

void FloatMenuWidget::setSectorRotation(qreal rotation)
{
    if (qAbs(m_sectorRotation - rotation) > 0.1) {
        m_sectorRotation = rotation;
        update(); // 触发重绘
    }
}

void FloatMenuWidget::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)

    QPainter painter(this);

    // 启用高质量抗锯齿（使用统一的渲染设置）
    FloatMenuUtils::setupHighQualityRendering(&painter);

    // 绘制渐变边框
    drawGradientBorder(&painter);

    // 绘制背景圆盘
    drawBackground(&painter);

    // 绘制星形背景
    drawStarBackground(&painter);

    // 绘制扇形选中指示器
    drawSectorIndicator(&painter);

    // 绘制工具按钮
    drawToolButtons(&painter);

    // 绘制中心按钮
    drawCenterButton(&painter);
}

void FloatMenuWidget::drawGradientBorder(QPainter* painter)
{
    if (m_backgroundScale <= 0.0 || m_backgroundOpacity <= 0.0) return;

    painter->save();

    // 设置透明度和缩放
    painter->setOpacity(m_backgroundOpacity);

    // 计算边框矩形 - 比背景圆盘大5像素
    qreal borderSize = BACKGROUND_SIZE + 10; // 比背景大10像素（每边5像素）
    qreal scale = m_backgroundScale;
    QPointF coreCenter = getCoreRect().center();
    QRectF borderRect(
        coreCenter.x() - (borderSize * scale) / 2,
        coreCenter.y() - (borderSize * scale) / 2,
        borderSize * scale,
        borderSize * scale
    );

    // 创建线性渐变 - 从左上到右下
    QLinearGradient gradient(borderRect.topLeft(), borderRect.bottomRight());
    gradient.setColorAt(0.0, QColor(41, 218, 128));  // #29DA80
    gradient.setColorAt(1.0, QColor(96, 83, 227));   // #6053E3

    // 绘制渐变边框
    painter->setBrush(QBrush(gradient));
    painter->setPen(Qt::NoPen);
    painter->drawEllipse(borderRect);

    painter->restore();
}

void FloatMenuWidget::drawBackground(QPainter* painter)
{
    if (m_backgroundScale <= 0.0 || m_backgroundOpacity <= 0.0) return;

    painter->save();

    // 设置透明度和缩放
    painter->setOpacity(m_backgroundOpacity);

    // 计算缩放后的矩形
    qreal scale = m_backgroundScale;
    QPointF coreCenter = getCoreRect().center();
    QRectF bgRect(
        coreCenter.x() - (BACKGROUND_SIZE  * scale) / 2,
        coreCenter.y() - (BACKGROUND_SIZE * scale) / 2,
        BACKGROUND_SIZE  * scale,
        BACKGROUND_SIZE  * scale
    );

    // 绘制白色背景圆盘
    painter->setBrush(QColor(255, 255, 255));
    painter->setPen(Qt::NoPen);
    painter->drawEllipse(bgRect);

    painter->restore();
}

void FloatMenuWidget::drawStarBackground(QPainter* painter)
{
    if (m_starScale <= 0.0 || m_starOpacity <= 0.0 || m_starBackgroundPixmap.isNull()) return;

    painter->save();

    // 设置透明度和缩放
    painter->setOpacity(m_starOpacity);

    qreal scale = m_starScale;
    QPointF coreCenter = getCoreRect().center();
    QRectF targetRect(
        coreCenter.x() - (CORE_WIDGET_SIZE * scale) / 2,
        coreCenter.y() - (CORE_WIDGET_SIZE * scale) / 2,
        CORE_WIDGET_SIZE * scale,
        CORE_WIDGET_SIZE * scale
    );

    // 绘制星形背景
    painter->drawPixmap(targetRect, m_starBackgroundPixmap, m_starBackgroundPixmap.rect());

    painter->restore();
}

void FloatMenuWidget::drawSectorIndicator(QPainter* painter)
{
    // 只有在选中了工具时才显示扇形指示器
    if (m_stateManager->getSelectedTool().isEmpty() || m_sectorScale <= 0.0 || m_sectorOpacity <= 0.0 || m_sectorIndicatorPixmap.isNull()) return;

    painter->save();

    // 设置透明度和缩放
    painter->setOpacity(m_sectorOpacity);

    // 设置变换
    QPointF coreCenter = getCoreRect().center();
    painter->translate(coreCenter);
    painter->rotate(m_sectorRotation); // 使用动画旋转角度
    painter->scale(m_sectorScale, m_sectorScale);

    // 绘制扇形指示器（右下角对齐原点）
    painter->drawPixmap(FloatMenuConstants::SECTOR_RECT(), m_sectorIndicatorPixmap, m_sectorIndicatorPixmap.rect());

    painter->restore();
}

void FloatMenuWidget::drawToolButtons(QPainter* painter)
{
    if (!m_stateManager->isExpanded() || m_toolButtonsScale <= 0.0 || m_toolButtonsOpacity <= 0.0) return;

    painter->save();

    // 设置工具按钮整体透明度
    painter->setOpacity(m_toolButtonsOpacity);

    for (int i = 0; i < m_tools.size(); ++i) {
        const FloatMenuTool& tool = m_tools[i];

        QPointF toolPos = calculateToolPosition(i);

        qreal scale = m_toolButtonsScale;
        if (i == m_hoveredToolIndex) {
            scale *= 1.1;
        }

        if (i < m_toolButtonScales.size()) {
            scale *= m_toolButtonScales[i];
        }

        drawToolIcon(painter, toolPos, tool, scale);
    }

    painter->restore();
}

void FloatMenuWidget::drawCenterButton(QPainter* painter)
{
    painter->save();

    // 计算缩放（悬停时放大）
    qreal hoverScale = m_centerButtonHovered ? 1.1 : 1.0;
    qreal finalScale = hoverScale;

    // 计算中心按钮矩形
    qreal scaledSize = CENTER_BUTTON_SIZE  * finalScale;
    QPointF coreCenter = getCoreRect().center();
    QRectF centerRect(
        coreCenter.x() - scaledSize / 2,
        coreCenter.y() - scaledSize / 2,
        scaledSize,
        scaledSize
    );

    if (m_stateManager->isExpanded()) {

        // 绘制简单阴影效果
        painter->setBrush(QColor(0, 0, 0, 32)); // #20000000
        QRectF shadowRect = centerRect.adjusted(-2, -2, 2, 2);
        painter->setPen(Qt::NoPen);
        painter->drawEllipse(shadowRect);

        // 绘制毛玻璃效果背景
        QColor bgColor = QColor(236, 235, 243, 153); // #ECEBF3 with 0.6 opacity
        painter->setBrush(bgColor);
        painter->drawEllipse(centerRect);

    } else {

        qreal borderWidth = 4.0;
        QRectF borderRect = centerRect.adjusted(-borderWidth, -borderWidth, borderWidth, borderWidth);

        // 创建线性渐变 - 从上到下 (180deg)
        QLinearGradient borderGradient(borderRect.topLeft(), borderRect.bottomLeft());
        borderGradient.setColorAt(0.0, QColor(41, 218, 128));  // rgba(41, 218, 128, 1)
        borderGradient.setColorAt(1.0, QColor(96, 83, 227));   // rgba(96, 83, 227, 1)

        // 绘制渐变边框
        painter->setBrush(QBrush(borderGradient));
        painter->setPen(Qt::NoPen);
        painter->drawEllipse(borderRect);

        // 绘制白色背景
        painter->setBrush(QColor(255, 255, 255)); // #FFFFFF
        painter->drawEllipse(centerRect);
    }

    // 绘制中心图片
    drawCenterIcon(painter, centerRect);

    painter->restore();
}

void FloatMenuWidget::drawToolIcon(QPainter* painter, const QPointF& center, const FloatMenuTool& tool, qreal scale)
{
    if (scale <= 0.0) return;

    painter->save();

    // 设置透明度
    painter->setOpacity(scale);

    // 计算图标矩形
    qreal iconSize = TOOL_BUTTON_SIZE  * scale;
    QRectF iconRect(
        center.x() - iconSize / 2,
        center.y() - iconSize / 2,
        iconSize,
        iconSize
    );

    // 检查是否使用SVG图标
    bool useSvg = m_toolUseSvg.value(tool.name, false);
    if (tool.name == "pen" && m_stateManager->getSelectedTool() != "pen") {
        useSvg = false;
    }
    if (useSvg && m_svgManager && m_toolSvgIds.contains(tool.name)) {
        // 使用SVG图标渲染
        QString svgId = m_toolSvgIds[tool.name];
        if (m_svgManager->isSvgLoaded(svgId)) {
            m_svgManager->renderSvg(painter, svgId, iconRect);
            painter->restore();
            return;
        }
    }

    if (m_iconFontLoaded && m_iconFontManager) {
        // 使用iconfont字体绘制图标
        QFont iconFont = m_iconFontManager->getFont(qRound(25 * scale));
        painter->setFont(iconFont);
        painter->setPen(QColor(0, 0, 0)); // 黑色图标

        // 将Unicode码点转换为字符
        QString iconChar = QString(QChar(tool.iconUnicode));
        painter->drawText(iconRect, Qt::AlignCenter, iconChar);
    } else {
        // 回退到文字显示
        painter->setPen(QColor(0, 0, 0)); // 黑色文字
        painter->setFont(QFont("Arial", qRound(25 * scale), QFont::Bold));

        QString fallbackText;
        if (tool.name == "pen") fallbackText = "笔";
        else if (tool.name == "eraser") fallbackText = "擦";
        else if (tool.name == "graphic") fallbackText = "图";
        else if (tool.name == "clear") fallbackText = "清";
        else if (tool.name == "undo") fallbackText = "撤";
        else if (tool.name == "select") fallbackText = "选";
        else fallbackText = tool.name.left(1);

        painter->drawText(iconRect, Qt::AlignCenter, fallbackText);
    }

    painter->restore();
}

QPointF FloatMenuWidget::calculateToolPosition(int toolIndex) const
{
    if (toolIndex < 0 || toolIndex >= m_tools.size()) {
        return getCoreRect().center();
    }

    const FloatMenuTool& tool = m_tools[toolIndex];
    qreal angleRad = qDegreesToRadians(tool.angle - 90); // -90度修正，使0度指向上方

    qreal radius = TOOL_RADIUS  * m_toolButtonsScale;
    QPointF coreCenter = getCoreRect().center();
    qreal x = coreCenter.x() + qCos(angleRad) * radius;
    qreal y = coreCenter.y() + qSin(angleRad) * radius;

    return QPointF(x, y);
}

// 槽函数实现
void FloatMenuWidget::onAnimationProgressChanged()
{
    update();
}

void FloatMenuWidget::onExpandAnimationFinished()
{
}

void FloatMenuWidget::onCollapseAnimationFinished()
{
}

// 鼠标事件处理
void FloatMenuWidget::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否有动画正在运行，如果有则忽略鼠标事件
        if (m_expandAnimationGroup->state() == QAbstractAnimation::Running ||
            m_collapseAnimationGroup->state() == QAbstractAnimation::Running) {
            event->ignore();
            return;
        }

        // 重置自动收起定时器
        resetAutoCollapseTimer();

        // 检查是否点击了工具按钮（优先级最高）
        int toolIndex = getClickedToolIndex(event->position());
        if (toolIndex >= 0) {
            // 点击了工具按钮，接受事件但不启动拖拽
            event->accept();
            return;
        }

        // 简化拖拽逻辑：根据展开状态确定拖拽区域
        QPointF center = getCoreRect().center();
        qreal distance = QLineF(event->position(), center).length();
        bool canDrag = false;

        if (m_stateManager->isExpanded()) {
            // 圆盘展开状态：整个圆盘区域都可以拖拽（无论arc是否展开）
            canDrag = distance <= BACKGROUND_SIZE / 2.0;
        } else {
            // 圆盘收起状态：只有中心按钮区域可以拖拽
            canDrag = distance <= CENTER_BUTTON_SIZE / 2.0;
        }

        if (!canDrag) {
            // 不在拖拽区域内，忽略事件以实现点击穿透
            event->ignore();
            return;
        }

        // 动画期间禁止开始拖拽操作
        if (m_expandAnimationGroup->state() == QAbstractAnimation::Running ||
            m_collapseAnimationGroup->state() == QAbstractAnimation::Running) {
            qDebug() << "FloatMenuWidget: 动画期间禁止拖拽";
            event->accept(); // 接受事件但不启动拖拽
            return;
        }

        // 只有在确认可以拖拽且无动画时才设置拖拽相关变量
        m_dragStartPos = event->globalPosition().toPoint();
        m_windowStartPos = pos();
        m_isDragging = false;
        m_dragStarted = false;
        m_hasDragged = false;

        qDebug() << "FloatMenuWidget: 设置拖拽起始状态，位置:" << event->position()
                 << "全局位置:" << m_dragStartPos;

        // 中心拖拽区域内可以拖拽，发送点击信号
        emit floatMenuClicked();
        event->accept();
    } else {
        event->accept();
    }
}

void FloatMenuWidget::mouseMoveEvent(QMouseEvent* event)
{
    // 检查是否有动画正在运行，如果有则停止拖拽操作
    if (m_expandAnimationGroup->state() == QAbstractAnimation::Running ||
        m_collapseAnimationGroup->state() == QAbstractAnimation::Running) {
        // 如果动画开始了但拖拽还在进行，强制停止拖拽
        if (m_isDragging || m_dragStarted) {
            qDebug() << "FloatMenuWidget: 动画开始，强制停止拖拽";
            m_isDragging = false;
            m_dragStarted = false;
            m_hasDragged = false;
            m_dragStartPos = QPoint();
        }
        return;
    }

    if (event->buttons() & Qt::LeftButton && !m_dragStartPos.isNull()) {
        QPoint currentGlobalPos = event->globalPosition().toPoint();
        QPoint delta = currentGlobalPos - m_dragStartPos;

        // 检查是否开始拖拽
        if (!m_dragStarted) {
            qreal distance = qSqrt(delta.x() * delta.x() + delta.y() * delta.y());
            if (distance > DRAG_THRESHOLD) {
                m_dragStarted = true;
                m_isDragging = true;
                m_hasDragged = true;
                qDebug() << "FloatMenuWidget: 开始拖拽，移动距离:" << distance;
            }
        } else {
            // 已经开始拖拽，确保hasDragged标志为true
            m_hasDragged = true;
        }

        // 如果正在拖拽，移动窗口
        if (m_dragStarted) {
            QPoint newPos = m_windowStartPos + delta;

            // 限制在主窗口范围内
            newPos = constrainPositionToMainWindow(newPos);

            move(newPos);

            // 立即更新弧形内容位置，确保完全同步
            updateArcContentPositions();

            emit positionChanged(newPos);
        }
    } else {
        // 鼠标在菜单区域内移动时重置定时器
        resetAutoCollapseTimer();

        // 更新悬停状态（只在展开状态下检测工具按钮悬停）
        int toolIndex = -1;
        if (m_stateManager->isExpanded()) {
            toolIndex = getClickedToolIndex(event->position());
        }
        if (m_hoveredToolIndex != toolIndex) {
            m_hoveredToolIndex = toolIndex;
            update();
        }

        bool centerHovered = isCenterButtonClicked(event->position());
        if (m_centerButtonHovered != centerHovered) {
            m_centerButtonHovered = centerHovered;
            update();
        }
    }
}

void FloatMenuWidget::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否是有效的拖拽操作：必须经过mouseMoveEvent且实际移动了
        bool isValidDrag = m_hasDragged && (m_isDragging || m_dragStarted);

        // 如果设置了拖拽变量但没有经过有效的移动，说明是误触发，重置状态
        if ((m_isDragging || m_dragStarted || !m_dragStartPos.isNull()) && !isValidDrag) {
            qDebug() << "FloatMenuWidget: 检测到无效拖拽（未经过移动），重置状态";
            m_isDragging = false;
            m_dragStarted = false;
            m_hasDragged = false;
            m_dragStartPos = QPoint();
            // 继续处理为点击事件
        }

        if (!m_hasDragged) {
            // 检查是否点击了工具按钮
            int toolIndex = getClickedToolIndex(event->position());
            if (toolIndex >= 0 && toolIndex < m_tools.size()) {
                // 启动点击反馈动画
                startToolButtonClickAnimation(toolIndex);
                handleToolClick(m_tools[toolIndex].name, event);
                return;
            }

            // 检查是否点击了中心按钮
            if (isCenterButtonClicked(event->position())) {
                // 切换展开状态
                setExpanded(!m_stateManager->isExpanded());
                emit menuToggled(m_stateManager->isExpanded());
                return;
            }
        }

        // 重置拖拽状态
        bool wasDragging = m_isDragging || m_dragStarted;
        m_isDragging = false;
        m_dragStarted = false;
        m_hasDragged = false; // 重置拖拽标志
        m_dragStartPos = QPoint(); // 重置拖拽起始位置

        if (wasDragging) {
            qDebug() << "FloatMenuWidget: 拖拽结束，重置拖拽状态";
        }
    }
}

void FloatMenuWidget::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit floatMenuDoubleClicked();
    }
}

void FloatMenuWidget::enterEvent(QEnterEvent* event)
{
    Q_UNUSED(event)
    // 鼠标进入菜单区域时重置自动收起定时器
    resetAutoCollapseTimer();
    QWidget::enterEvent(event);
}

void FloatMenuWidget::leaveEvent(QEvent* event)
{
    // 清除悬停状态
    if (m_hoveredToolIndex != -1) {
        m_hoveredToolIndex = -1;
        update();
    }

    if (m_centerButtonHovered) {
        m_centerButtonHovered = false;
        update();
    }

    // 鼠标离开菜单区域时启动自动收起定时器
    startAutoCollapseTimer();

    QWidget::leaveEvent(event);
}

void FloatMenuWidget::moveEvent(QMoveEvent* event)
{
    QWidget::moveEvent(event);
    // 更新弧形内容位置
    updateArcContentPositions();
}

void FloatMenuWidget::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
    // 更新弧形内容位置
    updateArcContentPositions();
}

void FloatMenuWidget::hideEvent(QHideEvent* event)
{
    // 确保在任何隐藏情况下都隐藏圆弧菜单
    if (m_arcContentManager) {
        m_arcContentManager->hideAllArcContent(false); // 立即隐藏，不使用动画
    }

    QWidget::hideEvent(event);
}

void FloatMenuWidget::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);

    // 显示时更新弧形内容位置
    updateArcContentPositions();

    // 根据当前状态决定是否显示弧形内容
    if (m_arcContentManager && m_stateManager->isExpanded() && !m_stateManager->getSelectedTool().isEmpty()) {
        m_arcContentManager->showArcContent(m_stateManager->getSelectedTool(), false);
    }

    qDebug() << "FloatMenuWidget: showEvent - 窗口显示";
}

int FloatMenuWidget::getClickedToolIndex(const QPointF& pos) const
{
    if (!m_stateManager->isExpanded() || m_toolButtonsScale <= 0.0 || m_toolButtonsOpacity <= 0.0) return -1;

    for (int i = 0; i < m_tools.size(); ++i) {
        QPointF toolPos = calculateToolPosition(i);
        qreal distance = QLineF(pos, toolPos).length();

        if (distance <= TOOL_BUTTON_SIZE) {
            return i;
        }
    }

    return -1;
}

bool FloatMenuWidget::isCenterButtonClicked(const QPointF& pos) const
{
    QPointF center = getCoreRect().center();
    qreal distance = QLineF(pos, center).length();
    return distance <= CENTER_BUTTON_SIZE / 2;
}

bool FloatMenuWidget::isInCenterDragArea(const QPointF& pos) const
{
    QPointF center = getCoreRect().center();
    qreal distance = QLineF(pos, center).length();

    if (m_stateManager->isExpanded()) {
        // 展开状态：整个背景圆盘都可以拖拽
        qreal dragRadius = BACKGROUND_SIZE / 2.0;
        return distance <= dragRadius;
    } else {
        // 收起状态：只有中心按钮区域可以拖拽
        qreal dragRadius = CENTER_BUTTON_SIZE / 2.0;
        return distance <= dragRadius;
    }
}

bool FloatMenuWidget::isInValidClickArea(const QPointF& pos) const
{
    QPointF center = getCoreRect().center();
    qreal distance = QLineF(pos, center).length();

    if (m_stateManager->isExpanded()) {
        // 圆盘展开状态：核心圆盘区域 + 可见的arc内容区域
        qreal coreRadius = BACKGROUND_SIZE / 2.0;

        if (distance <= coreRadius) {
            return true;
        }

        // 检查是否在可见的arc内容区域内
        QString currentTool = m_stateManager->getSelectedTool();
        if (!currentTool.isEmpty() && m_arcContentManager) {
            ArcContentWidget* arcContent = m_arcContentManager->getArcContent(currentTool);
            if (arcContent && arcContent->isContentVisible()) {
                QPoint arcLocalPos = arcContent->mapFromParent(pos.toPoint());
                if (arcContent->rect().contains(arcLocalPos)) {
                    return true;
                }
            }
        }

        return false;
    } else {
        // 圆盘收起状态：只有中心按钮区域
        qreal centerRadius = CENTER_BUTTON_SIZE / 2.0;
        return distance <= centerRadius;
    }
}

QPoint FloatMenuWidget::constrainPositionToMainWindow(const QPoint& pos) const
{
    if (!m_mainWindow) {
        return pos; // 如果没有主窗口，返回原位置
    }

    QRect mainWindowRect = m_mainWindow->geometry();
    QSize widgetSize = size();

    // 限制位置在主窗口范围内
    int constrainedX = qMax(mainWindowRect.left() -  widgetSize.width() / 5,
                           qMin(pos.x(), mainWindowRect.right() -  widgetSize.width() / 2));
    int constrainedY = qMax(mainWindowRect.top() - widgetSize.height() / 2,
                           qMin(pos.y(), mainWindowRect.bottom() - widgetSize.height() / 3 * 2));

    return QPoint(constrainedX, constrainedY);
}

void FloatMenuWidget::handleToolClick(const QString& toolName, QMouseEvent* event)
{
    // 防重复响应：检查距离上次点击是否超过1秒
    if (m_lastClickTimer.elapsed() < CLICK_DEBOUNCE_MS) {
        qDebug() << "FloatMenuWidget: 防重复响应，忽略点击" << toolName;
        return;
    }

    // 重置计时器
    m_lastClickTimer.restart();

    // 重置自动收起定时器
    resetAutoCollapseTimer();

    // 根据工具类型执行不同操作
    if (toolName == "clear") {
        emit saveBoard(event->globalPosition().toPoint());
    } else if (toolName == "undo") {
        emit undoAction();
    } else {
        // 普通工具处理：检查是否为当前选中的工具
        if (m_stateManager->getSelectedTool() == toolName) {
            // 如果点击的是已选中的工具，则取消UI选中，但功能上回到套索工具
            setSelectedTool("");  // UI显示为无选中
            m_stateManager->setFunctionalTool("select");  // 功能上使用套索工具
            emit toolDeselected(toolName);  // 发送取消选中信号
            emit toolTypeChanged(ToolType::Lasso);  // 发送PassThrough工具类型信号

        } else {
            // 如果点击的是其他工具，则设置为选中状态
            setSelectedTool(toolName);
            m_stateManager->setFunctionalTool(toolName);  // 功能工具也同步更新

            if (toolName == "select") {
                emit toolTypeChanged(ToolType::Lasso);
            } else if (toolName == "graphic") {
                onGraphicToolChanged(m_stateManager->getGraphicToolType());
            } else {
                emit toolSelected(toolName);
            }
        }
    }
}

void FloatMenuWidget::onToolButtonClickAnimationFinished()
{
    // 重置点击的工具索引
    m_clickedToolIndex = -1;
}

void FloatMenuWidget::startToolButtonClickAnimation(int toolIndex)
{
    if (toolIndex < 0 || toolIndex >= m_toolButtonScales.size()) {
        return;
    }

    m_clickedToolIndex = toolIndex;

    // 使用简单的定时器实现点击反馈动画
    QTimer* clickTimer = new QTimer(this);
    clickTimer->setSingleShot(true);

    // 设置缩放值
    setToolButtonScale(toolIndex, 0.8);
    update();

    // 100ms后恢复
    connect(clickTimer, &QTimer::timeout, this, [this, toolIndex, clickTimer]() {
        setToolButtonScale(toolIndex, 1.0);
        update();
        onToolButtonClickAnimationFinished();
        clickTimer->deleteLater();
    });

    clickTimer->start(100);
}

qreal FloatMenuWidget::getToolButtonScale(int index) const
{
    if (index >= 0 && index < m_toolButtonScales.size()) {
        return m_toolButtonScales[index];
    }
    return 1.0;
}

void FloatMenuWidget::setToolButtonScale(int index, qreal scale)
{
    if (index >= 0 && index < m_toolButtonScales.size()) {
        m_toolButtonScales[index] = scale;
        update();
    }
}

QVariant FloatMenuWidget::property(const char *name) const
{
    QString propName(name);

    // 检查是否是工具按钮缩放属性
    if (propName.startsWith("toolButtonScale")) {
        bool ok;
        int index = propName.mid(15).toInt(&ok); // "toolButtonScale".length() = 15
        if (ok && index >= 0 && index < m_toolButtonScales.size()) {
            return m_toolButtonScales[index];
        }
    }

    return QWidget::property(name);
}

bool FloatMenuWidget::setProperty(const char *name, const QVariant &value)
{
    QString propName(name);

    // 检查是否是工具按钮缩放属性
    if (propName.startsWith("toolButtonScale")) {
        bool ok;
        int index = propName.mid(15).toInt(&ok); // "toolButtonScale".length() = 15
        if (ok && index >= 0 && index < m_toolButtonScales.size()) {
            qreal scale = value.toReal();
            m_toolButtonScales[index] = scale;
            update();
            return true;
        }
    }

    return QWidget::setProperty(name, value);
}

QRectF FloatMenuWidget::getCoreRect() const
{
    return QRectF(m_coreOffset.x(), m_coreOffset.y(), CORE_WIDGET_SIZE, CORE_WIDGET_SIZE);
}

void FloatMenuWidget::addExternalComponentRect(const QRectF& rect)
{
    if (!m_externalComponentRects.contains(rect)) {
        m_externalComponentRects.append(rect);
        // 独立窗口使用固定大小，不需要动态调整
    }
}

void FloatMenuWidget::removeExternalComponentRect(const QRectF& rect)
{
    if (m_externalComponentRects.removeOne(rect)) {
        // 独立窗口使用固定大小，不需要动态调整
    }
}

void FloatMenuWidget::clearExternalComponentRects()
{
    if (!m_externalComponentRects.isEmpty()) {
        m_externalComponentRects.clear();
        // 独立窗口使用固定大小，不需要动态调整
    }
}

void FloatMenuWidget::initializeArcContentManager()
{
    // 创建弧形内容管理器
    m_arcContentManager = new ArcContentManager(this);
    m_arcContentManager->setParentWidget(this);


    // 创建画笔弧形内容
    PenArcContent* penArcContent = new PenArcContent(ArcPosition::Top, this);
    penArcContent->setShowArcBackground(false);
    ArcContentConfig penConfig(ArcPosition::Top, QPointF(0, 0), 70); // 保持原始位置
    m_arcContentManager->registerArcContent("pen", penArcContent, penConfig);

    // 注册画笔arc内容区域为外部组件
    registerArcContentArea("pen", penConfig);

    FloatMenuUtils::connectArcContentClicked(
        penArcContent,
        this,
        [this]() { resetAutoCollapseTimer(); },
        "画笔"
    );

    connect(penArcContent, &PenArcContent::penTypeChanged,
            this, [this](int penType) {
                m_stateManager->setPenType(penType);
                updatePenToolIcon(penType, m_stateManager->getPenColor());
                resetAutoCollapseTimer();

            });

    // 连接画笔颜色改变信号
    connect(penArcContent, &PenArcContent::colorChanged,
            this, [this](const QColor& color) {
                m_stateManager->setPenColor(color);
                updatePenToolIcon(m_stateManager->getPenType(), color);
                resetAutoCollapseTimer();

            });

    // 连接画笔线宽改变信号
    connect(penArcContent, &PenArcContent::widthChanged,
            this, [this](int width) {
                qreal actualWidth = 2.0; 
                switch (width) {
                    case 1: actualWidth = 2.0; break;  // 小尺寸
                    case 2: actualWidth = 5.0; break;  // 中尺寸
                    case 3: actualWidth = 8.0; break;  // 大尺寸
                    default: actualWidth = 2.0; break; // 默认小尺寸
                }
                emit penWidthChanged(actualWidth);  // 发送信号给外部
                resetAutoCollapseTimer(); // 重置定时器

            });

    // 创建橡皮擦弧形内容（子widget模式）
    EraserArcContent* eraserArcContent = new EraserArcContent(ArcPosition::Right, this);
    ArcContentConfig eraserConfig(ArcPosition::Right, QPointF(0, 0), 20);
    m_arcContentManager->registerArcContent("eraser", eraserArcContent, eraserConfig);

    // 注册橡皮擦arc内容区域为外部组件
    registerArcContentArea("eraser", eraserConfig);

    // 连接橡皮擦弧形内容信号（使用统一的连接函数）
    FloatMenuUtils::connectArcContentClicked(
        eraserArcContent,
        this,
        [this]() { resetAutoCollapseTimer(); },
        "橡皮擦"
    );

    // 连接橡皮擦大小改变信号
    connect(eraserArcContent, &EraserArcContent::sizeChanged,
            this, [this](const QSizeF& size) {
                onEraserSizeChanged(size);
            });

    // 连接滑动清屏信号
    connect(eraserArcContent, &EraserArcContent::slideToEraseTriggered,
            this, &FloatMenuWidget::clearCanvas);

    m_graphicToolPanel = new GraphicToolPanel(this);

    m_graphicToolPanel->setDiskRadius(BACKGROUND_SIZE / 2);

    // 连接图形工具面板信号
    connect(m_graphicToolPanel, &GraphicToolPanel::graphicToolChanged,
            this, &FloatMenuWidget::onGraphicToolChanged);

    connect(m_graphicToolPanel, &GraphicToolPanel::panelClicked,
            this, [this](const QPointF& pos) {
                resetAutoCollapseTimer(); // 重置定时器

            });

    // 连接弧形内容管理器信号
    connect(m_arcContentManager, &ArcContentManager::arcContentShown,
            this, [this](const QString& toolName) {
                resetAutoCollapseTimer(); // 重置定时器

            });

    connect(m_arcContentManager, &ArcContentManager::arcContentHidden,
            this, [this](const QString& toolName) {
                resetAutoCollapseTimer(); // 重置定时器

            });

    // 显示默认选中工具的弧形内容
    if (!m_stateManager->getSelectedTool().isEmpty()) {
        m_arcContentManager->showArcContent(m_stateManager->getSelectedTool(), false);
    } else {
        // 默认不选中任何工具，隐藏所有弧形内容
        m_arcContentManager->hideAllArcContent(false);
    }

    // 发送初始的画笔设置信号
    QTimer::singleShot(100, this, [this]() {
        // 发送默认画笔设置信号
        emit penTypeChanged(m_stateManager->getPenType());      // 发送默认画笔类型（实线笔）
        emit penColorChanged(m_stateManager->getPenColor());    // 发送默认画笔颜色（红色）
        emit penWidthChanged(FloatMenuConstants::DEFAULT_PEN_WIDTH() );  // 发送默认画笔线宽
    });
}

void FloatMenuWidget::registerArcContentArea(const QString& toolName, const ArcContentConfig& config)
{
    // 计算arc内容占用的区域（相对于核心区域中心）
    int actualSize = ScreenAdaptationConstants::adaptSize(653) + config.expandSize * 2;
    QRectF arcRect(
        config.offset.x() - actualSize / 2,
        config.offset.y() - actualSize / 2,
        actualSize,
        actualSize
    );

    // 注册为外部组件区域
    addExternalComponentRect(arcRect);
}

void FloatMenuWidget::updateArcContentPositions()
{
    if (!m_arcContentManager) return;

    QPointF coreCenter = getCoreRect().center();

    m_arcContentManager->updateDiskCenter(coreCenter);

    if (m_graphicToolPanel) {
        QPointF globalCenter = mapToGlobal(coreCenter.toPoint());
        m_graphicToolPanel->updatePosition(globalCenter);
    }
}

void FloatMenuWidget::onAutoCollapseTimeout()
{
    // 只有在展开状态下才执行自动收起
    if (m_stateManager->isExpanded()) {

        setExpanded(false);
    }
}



void FloatMenuWidget::resetAutoCollapseTimer()
{
    if (m_autoCollapseTimer && m_stateManager->isExpanded()) {
        m_autoCollapseTimer->stop();
        m_autoCollapseTimer->start();

    }
}

void FloatMenuWidget::startAutoCollapseTimer()
{
    if (m_autoCollapseTimer && m_stateManager->isExpanded()) {
        m_autoCollapseTimer->start();

    }
}

void FloatMenuWidget::stopAutoCollapseTimer()
{
    if (m_autoCollapseTimer) {
        m_autoCollapseTimer->stop();

    }
}

void FloatMenuWidget::onGraphicToolChanged(int toolType)
{
    resetAutoCollapseTimer();

    m_stateManager->setGraphicToolType(toolType);

    update();

    ToolType convertedToolType;
    QString toolName;
    bool validTool = true;
    
    switch (toolType) {
        case 0:
            convertedToolType = ToolType::Line;
            toolName = "直线";
            break;
        case 1:
            convertedToolType = ToolType::DashedLine;
            toolName = "虚线";
            break;
        case 2:
            convertedToolType = ToolType::Arrow;
            toolName = "箭头";
            break;
        case 3:
            convertedToolType = ToolType::Circle;
            toolName = "圆形";
            break;
        case 4:
            convertedToolType = ToolType::Ellipse;
            toolName = "椭圆";
            break;
        case 5:
            convertedToolType = ToolType::Rectangle;
            toolName = "矩形";
            break;
        case 6:
            convertedToolType = ToolType::Square;
            toolName = "正方形";
            break;
        case 7:
            convertedToolType = ToolType::Triangle;
            toolName = "三角形";
            break;
        case 8:
            convertedToolType = ToolType::RightTriangle;
            toolName = "直角三角形";
            break;
        case -1:
            // 取消选中图形工具
            validTool = false;
            break;
        default:
            qWarning() << "FloatMenuWidget: 无效的图形工具类型:" << toolType;
            validTool = false;
            break;
    }
    
    if (validTool) {
        // 发出工具类型改变信号，让WhiteboardView处理

        emit toolTypeChanged(convertedToolType);
        

    }
    
    emit toolSelected("graphic_" + QString::number(toolType));
}

QPair<int, QString> FloatMenuWidget::getCurrentCenterIcon() const
{
    // 根据当前选中的工具返回对应的图标
    if (m_stateManager->getSelectedTool() == "pen") {
        // 选中画笔工具时，返回特殊标识表示使用SVG图标
        return QPair<int, QString>(-1, "pen_svg");
    } else if (m_stateManager->getSelectedTool() == "eraser") {
        // 选中橡皮擦时，显示橡皮擦图标
        return QPair<int, QString>(IconFonts::Icons::ERASER_ALT, "橡皮擦");
    } else if (m_stateManager->getSelectedTool() == "graphic") {
        // 选中图形工具时
        if (m_stateManager->getGraphicToolType() >= 0) {
            // 如果选中了具体的图形工具，显示对应的图形图标
            switch (m_stateManager->getGraphicToolType()) {
                case 0: return QPair<int, QString>(IconFonts::Icons::LINE_TOOL, "直线");
                case 1: return QPair<int, QString>(IconFonts::Icons::DASHED_LINE, "虚线");
                case 2: return QPair<int, QString>(IconFonts::Icons::ARROW_ALT, "箭头");
                case 3: return QPair<int, QString>(IconFonts::Icons::CIRCLE, "圆形");
                case 4: return QPair<int, QString>(IconFonts::Icons::ELLIPSE, "椭圆");
                case 5: return QPair<int, QString>(IconFonts::Icons::RECTANGLE, "矩形");
                case 6: return QPair<int, QString>(IconFonts::Icons::SQUARE, "正方形");
                case 7: return QPair<int, QString>(IconFonts::Icons::TRIANGLE, "三角形");
                case 8: return QPair<int, QString>(IconFonts::Icons::RIGHT_TRIANGLE, "直角三角形");
                default:
                    // 未知的图形工具类型，显示通用图形图标
                    return QPair<int, QString>(IconFonts::Icons::GRAPHIC, "图形");
            }
        } else {
            // 选中图形工具但未选择具体图形时，对应白板中的选择功能
            return QPair<int, QString>(IconFonts::Icons::GRAPHIC, "选择");
        }
    }

    // 默认情况下不显示特定图标
    return QPair<int, QString>(0, "");
}

void FloatMenuWidget::drawCenterIcon(QPainter* painter, const QRectF& centerRect)
{
    // 在展开状态时，始终显示默认的中心按钮图片
    if (m_stateManager->isExpanded()) {
        if (!m_centerButtonPixmap.isNull()) {
            painter->drawPixmap(centerRect, m_centerButtonPixmap, m_centerButtonPixmap.rect());
        }
        return;
    }

    // 在收起状态时，获取当前应该显示的图标
    QPair<int, QString> iconInfo = getCurrentCenterIcon();

    // 特殊处理pen工具的SVG图标
    if (iconInfo.second == "pen_svg") {
        // 使用pen工具的SVG图标
        if (m_svgManager && m_stateManager->getPenType() < m_penIconSvgIds.size()) {
            QString currentSvgId = m_penIconSvgIds[m_stateManager->getPenType()];
            if (!currentSvgId.isEmpty() && m_svgManager->isSvgLoaded(currentSvgId)) {
                // 计算SVG图标的绘制区域（稍微小一点，留出边距）
                QRectF svgRect = centerRect.adjusted(8, 8, -8, -8);
                m_svgManager->renderSvg(painter, currentSvgId, svgRect);
                return;
            }
        }

        // 如果SVG加载失败，回退到iconfont图标
        iconInfo.first = IconFonts::Icons::PEN_NORMAL;
        iconInfo.second = "画笔";
    }

    if (iconInfo.first == 0) {
        // 没有特定图标，绘制默认的中心按钮图片
        if (!m_centerButtonPixmap.isNull()) {
            painter->drawPixmap(centerRect, m_centerButtonPixmap, m_centerButtonPixmap.rect());
        }
        return;
    }

    // 绘制iconfont图标
    painter->save();

    if (m_iconFontLoaded && m_iconFontManager) {
        // 使用iconfont字体绘制图标
        QFont iconFont = m_iconFontManager->getFont(qRound(centerRect.height() * 0.5)); // 图标大小为按钮高度的50%
        painter->setFont(iconFont);

        // 设置图标颜色（黑色）
        painter->setPen(QColor(0, 0, 0));

        // 将Unicode码点转换为字符
        QString iconChar = QString(QChar(iconInfo.first));
        painter->drawText(centerRect, Qt::AlignCenter, iconChar);
    } else {
        // 回退到文字显示（调试用）
        painter->setPen(QColor(0, 0, 0)); // 黑色文字
        painter->setFont(QFont("Arial", qRound(centerRect.height() * 0.3), QFont::Bold));

        // 显示图标名称的第一个字符
        QString fallbackText = iconInfo.second.left(1);
        painter->drawText(centerRect, Qt::AlignCenter, fallbackText);
    }

    painter->restore();
}



void FloatMenuWidget::initializeSvgManager()
{
    // 创建SVG管理器
    m_svgManager = SvgManager::instance();
    // 初始化pen工具SVG图标
    initializePenToolIcons();
}

void FloatMenuWidget::updatePenToolIcon(int penType, const QColor& color)
{
    if (!m_svgManager) {
        qWarning() << "FloatMenuWidget: SVG管理器未初始化";
        return;
    }

    // 更新状态管理器中的pen工具状态
    int boundedPenType = qBound(0, penType, 2);
    m_stateManager->setPenType(boundedPenType);
    m_stateManager->setPenColor(color);

    // 检查SVG ID列表是否有效
    if (boundedPenType >= m_penIconSvgIds.size()) {
        qWarning() << "FloatMenuWidget: pen工具类型超出范围:" << boundedPenType;
        return;
    }

    QString currentSvgId = m_penIconSvgIds[boundedPenType];

    if (!m_svgManager->isSvgLoaded(currentSvgId)) {
        qWarning() << "FloatMenuWidget: pen工具SVG未加载:" << currentSvgId;
        return;
    }

    // 重置SVG到原始状态
    m_svgManager->resetSvg(currentSvgId);

    // 修改SVG中class为"selected"的元素颜色
    bool success = m_svgManager->modifyElementsByClass(currentSvgId, "selected", color, "fill");

    if (success) {
        // 更新pen工具的SVG配置
        m_toolSvgIds["pen"] = currentSvgId;
        m_toolUseSvg["pen"] = true;

        // 触发重绘
        update();


    } else {
        qWarning() << "FloatMenuWidget: 更新pen工具图标失败";
    }
}

void FloatMenuWidget::initializePenToolIcons()
{
    if (!m_svgManager) {
        qWarning() << "FloatMenuWidget: SVG管理器未初始化";
        return;
    }

    m_penIconSvgIds.clear();

    // pen工具图标SVG文件路径列表
    QStringList penIconPaths = {
        ":/images/floatmenu/floatmenu-arc-pen-line-icon.svg",         // 0: 实线笔
        ":/images/floatmenu/floatmenu-arc-pen-dashedline-icon.svg",  // 1: 虚线
        ":/images/floatmenu/floatmenu-arc-pen-marker-icon.svg"      // 2: 马克笔
    };

    // 加载每个SVG文件
    for (int i = 0; i < penIconPaths.size(); ++i) {
        QString svgPath = penIconPaths[i];
        QString svgId = QString("pen_icon_%1").arg(i);

        bool success = m_svgManager->loadSvg(svgPath, svgId);
        if (success) {
            m_penIconSvgIds.append(svgId);
        } else {
            qWarning() << "FloatMenuWidget: 加载pen工具图标SVG失败:" << svgPath;
            m_penIconSvgIds.append("");
        }
    }

    // 设置默认的pen工具图标
    updatePenToolIcon(m_stateManager->getPenType(), m_stateManager->getPenColor());
}

void FloatMenuWidget::onEraserSizeChanged(const QSizeF& size)
{
    resetAutoCollapseTimer(); // 重置定时器
    emit eraserSizeChanged(size);
}

QSizeF FloatMenuWidget::getCurrentEraserSize() const
{
    if (m_stateManager) {
        return m_stateManager->getEraserSize();
    }
    return FloatMenuConstants::ERASER_SIZE_MEDIUM() ; // 默认值
}
