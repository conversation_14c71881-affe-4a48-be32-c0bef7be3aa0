#ifndef FLOATMENUWIDGET_H
#define FLOATMENUWIDGET_H

#include <QWidget>
#include <QTimer>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QGraphicsOpacityEffect>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QPoint>
#include <QVector>
#include <QMap>

// 添加iconfont相关头文件
#include "../../iconfont/iconfontmanager.h"
#include "../../iconfont/iconfonts.h"

// 添加SVG管理器头文件
#include "../../svg/SvgManager.h"

// 添加白板类型定义
#include "../../../whiteboard/core/WhiteBoardTypes.h"

// 添加FloatMenu常量和工具函数
#include "../utils/FloatMenuConstants.h"
#include "../utils/FloatMenuUtils.h"
#include "FloatMenuStateManager.h"

// 前向声明
struct ArcContentConfig;
class ArcContentManager;
class ArcContentWidget;
class GraphicToolPanel;



/**
 * @brief 浮动菜单工具项结构
 */
struct FloatMenuTool {
    QString name;           ///< 工具名称
    int iconUnicode;        ///< iconfont Unicode码点
    qreal angle;           ///< 角度位置（度）
    QColor color;          ///< 工具颜色
    bool isAction;         ///< 是否为动作类工具（如清除、撤销）

    FloatMenuTool(const QString& n, int unicode, qreal a, const QColor& c, bool action = false)
        : name(n), iconUnicode(unicode), angle(a), color(c), isAction(action) {}
};

/**
 * @brief 基于QPainter的浮动菜单组件
 *
 * 替代QML实现，提供更好的性能和架构一致性
 */
class FloatMenuWidget : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(bool expanded READ isExpanded WRITE setExpanded NOTIFY expandedChanged)
    Q_PROPERTY(QString selectedTool READ selectedTool WRITE setSelectedTool NOTIFY selectedToolChanged)
    Q_PROPERTY(qreal backgroundScale READ backgroundScale WRITE setBackgroundScale)
    Q_PROPERTY(qreal backgroundOpacity READ backgroundOpacity WRITE setBackgroundOpacity)
    Q_PROPERTY(qreal starScale READ starScale WRITE setStarScale)
    Q_PROPERTY(qreal starOpacity READ starOpacity WRITE setStarOpacity)
    Q_PROPERTY(qreal sectorScale READ sectorScale WRITE setSectorScale)
    Q_PROPERTY(qreal sectorOpacity READ sectorOpacity WRITE setSectorOpacity)
    Q_PROPERTY(qreal toolButtonsOpacity READ toolButtonsOpacity WRITE setToolButtonsOpacity)
    Q_PROPERTY(qreal toolButtonsScale READ toolButtonsScale WRITE setToolButtonsScale)
    Q_PROPERTY(qreal sectorRotation READ sectorRotation WRITE setSectorRotation)

public:
    /**
     * @brief 构造函数（创建独立窗口）
     * @param parent 应始终为nullptr
     */
    explicit FloatMenuWidget(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~FloatMenuWidget();

    /**
     * @brief 获取展开状态
     * @return 是否展开
     */
    bool isExpanded() const;

    /**
     * @brief 设置展开状态
     * @param expanded 展开状态
     */
    void setExpanded(bool expanded);

    /**
     * @brief 获取选中的工具
     * @return 工具名称
     */
    QString selectedTool() const;

    /**
     * @brief 设置选中的工具
     * @param toolName 工具名称
     */
    void setSelectedTool(const QString& toolName);

    /**
     * @brief 清除选中的工具（取消选中）
     */
    void clearSelectedTool();

    /**
     * @brief 显示浮动窗口
     */
    void showFloatingWindow();

    /**
     * @brief 隐藏浮动窗口
     */
    void hideFloatingWindow();

    /**
     * @brief 切换显示状态
     */
    void toggleFloatingWindow();

    /**
     * @brief 设置主窗口引用（用于独立窗口定位）
     * @param mainWindow 主窗口指针
     */
    void setMainWindow(QWidget* mainWindow);

    /**
     * @brief 更新相对于主窗口的位置
     */
    void updatePositionRelativeToMainWindow();

    /**
     * @brief 更新pen工具图标
     * @param penType 画笔类型 (0=虚线, 1=荧光笔, 2=马克笔)
     * @param color 选中的颜色
     */
    void updatePenToolIcon(int penType, const QColor& color);

    /**
     * @brief 获取当前橡皮擦大小
     * @return 橡皮擦大小
     */
    QSizeF getCurrentEraserSize() const;

protected:
    /**
     * @brief 绘制事件
     * @param event 绘制事件
     */
    void paintEvent(QPaintEvent* event) override;

    /**
     * @brief 隐藏事件（重写以确保圆弧菜单同步隐藏）
     * @param event 隐藏事件
     */
    void hideEvent(QHideEvent* event) override;

    /**
     * @brief 显示事件（重写以确保圆弧菜单正确显示）
     * @param event 显示事件
     */
    void showEvent(QShowEvent* event) override;

    /**
     * @brief 重写属性访问以支持动态工具按钮缩放属性
     */
    QVariant property(const char *name) const;
    bool setProperty(const char *name, const QVariant &value);


    /**
     * @brief 鼠标按下事件
     * @param event 鼠标事件
     */
    void mousePressEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标移动事件
     * @param event 鼠标事件
     */
    void mouseMoveEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标释放事件
     * @param event 鼠标事件
     */
    void mouseReleaseEvent(QMouseEvent* event) override;

    /**
     * @brief 鼠标双击事件
     * @param event 鼠标事件
     */
    void mouseDoubleClickEvent(QMouseEvent* event) override;

    /**
     * @brief 进入事件
     * @param event 进入事件
     */
    void enterEvent(QEnterEvent* event) override;

    /**
     * @brief 离开事件
     * @param event 离开事件
     */
    void leaveEvent(QEvent* event) override;

    /**
     * @brief 移动事件
     * @param event 移动事件
     */
    void moveEvent(QMoveEvent* event) override;

    /**
     * @brief 大小改变事件
     * @param event 大小改变事件
     */
    void resizeEvent(QResizeEvent* event) override;

signals:
    /**
     * @brief 展开状态改变信号
     * @param expanded 是否展开
     */
    void expandedChanged(bool expanded);

    /**
     * @brief 选中工具改变信号
     * @param toolName 工具名称
     */
    void selectedToolChanged(const QString& toolName);

    /**
     * @brief 工具选中信号
     * @param toolName 工具名称
     */
    void toolSelected(const QString& toolName);

    /**
     * @brief 工具类型改变信号
     * @param toolType 工具类型
     */
    void toolTypeChanged(ToolType toolType);

    /**
     * @brief 工具取消选中信号
     * @param toolName 工具名称
     */
    void toolDeselected(const QString& toolName);

    /**
     * @brief 菜单切换信号
     * @param expanded 是否展开
     */
    void menuToggled(bool expanded);

    /**
     * @brief 浮动菜单点击信号
     */
    void floatMenuClicked();

    /**
     * @brief 浮动菜单双击信号
     */
    void floatMenuDoubleClicked();

    /**
     * @brief 位置改变信号
     * @param newPosition 新位置
     */
    void positionChanged(const QPoint& newPosition);

    /**
     * @brief 清除画布信号
     */
    void clearCanvas();

    /**
     * @brief 撤销操作信号
     */
    void undoAction();

    /**
     * @brief 橡皮擦大小改变信号
     * @param size 橡皮擦大小
     */
    void eraserSizeChanged(const QSizeF& size);

    /**
     * @brief 图形工具类型改变信号
     * @param type 图形工具类型
     */
    void graphicToolTypeChanged(int type);

    /**
     * @brief 画笔颜色改变信号
     * @param color 新的画笔颜色
     */
    void penColorChanged(const QColor& color);

    /**
     * @brief 画笔线宽改变信号
     * @param width 新的画笔线宽（实际像素值）
     */
    void penWidthChanged(qreal width);

    /**
     * @brief 画笔类型改变信号
     * @param type 画笔类型 (0=虚线, 1=马克笔, 2=实线笔)
     */
    void penTypeChanged(int type);

    /**
     * @brief 保存板书
     * @param pos 点击位置
     */
    void saveBoard(QPoint pos);

private slots:
    /**
     * @brief 动画进度改变槽函数
     */
    void onAnimationProgressChanged();

    /**
     * @brief 展开动画完成槽函数
     */
    void onExpandAnimationFinished();

    /**
     * @brief 收起动画完成槽函数
     */
    void onCollapseAnimationFinished();

    /**
     * @brief 工具按钮点击动画完成槽函数
     */
    void onToolButtonClickAnimationFinished();

    /**
     * @brief 自动收起定时器超时槽函数
     */
    void onAutoCollapseTimeout();

    /**
     * @brief 更新弧形内容位置
     */
    void updateArcContentPositions();

    /**
     * @brief 重置自动收起定时器
     */
    void resetAutoCollapseTimer();

    /**
     * @brief 启动自动收起定时器
     */
    void startAutoCollapseTimer();

    /**
     * @brief 停止自动收起定时器
     */
    void stopAutoCollapseTimer();

    /**
     * @brief 处理图形工具变化
     * @param toolType 图形工具类型（-1表示取消选中）
     */
    void onGraphicToolChanged(int toolType);

    /**
     * @brief 处理橡皮擦大小改变
     * @param size 新的橡皮擦大小
     */
    void onEraserSizeChanged(const QSizeF& size);



private:
    /**
     * @brief 初始化工具数据
     */
    void initializeTools();

    /**
     * @brief 初始化动画
     */
    void initializeAnimations();

    /**
     * @brief 加载资源文件
     */
    void loadResources();

    /**
     * @brief 设置初始位置
     */
    void setInitialPosition();

    /**
     * @brief 获取核心圆盘区域的矩形
     * @return 核心区域矩形
     */
    QRectF getCoreRect() const;

    /**
     * @brief 添加外部组件区域
     * @param rect 外部组件占用的矩形区域（相对于核心区域中心）
     */
    void addExternalComponentRect(const QRectF& rect);

    /**
     * @brief 移除外部组件区域
     * @param rect 要移除的矩形区域
     */
    void removeExternalComponentRect(const QRectF& rect);

    /**
     * @brief 清除所有外部组件区域
     */
    void clearExternalComponentRects();


    /**
     * @brief 启动工具按钮点击动画
     * @param toolIndex 工具索引
     */
    void startToolButtonClickAnimation(int toolIndex);

    /**
     * @brief 绘制背景圆盘
     * @param painter 画笔
     */
    void drawBackground(QPainter* painter);

    /**
     * @brief 绘制渐变边框
     * @param painter 画笔
     */
    void drawGradientBorder(QPainter* painter);

    /**
     * @brief 绘制星形背景
     * @param painter 画笔
     */
    void drawStarBackground(QPainter* painter);

    /**
     * @brief 绘制扇形选中指示器
     * @param painter 画笔
     */
    void drawSectorIndicator(QPainter* painter);

    /**
     * @brief 绘制工具按钮
     * @param painter 画笔
     */
    void drawToolButtons(QPainter* painter);

    /**
     * @brief 绘制中心按钮
     * @param painter 画笔
     */
    void drawCenterButton(QPainter* painter);

    /**
     * @brief 获取当前应该显示的中心图标信息
     * @return 图标Unicode码点和名称的pair，如果没有特定图标则返回{0, ""}
     */
    QPair<int, QString> getCurrentCenterIcon() const;

    /**
     * @brief 绘制中心图标
     * @param painter 画笔
     * @param centerRect 中心按钮矩形
     */
    void drawCenterIcon(QPainter* painter, const QRectF& centerRect);

    /**
     * @brief 绘制工具图标
     * @param painter 画笔
     * @param center 中心点
     * @param tool 工具信息
     * @param scale 缩放比例
     */
    void drawToolIcon(QPainter* painter, const QPointF& center, const FloatMenuTool& tool, qreal scale = 1.0);

    /**
     * @brief 计算工具按钮位置
     * @param toolIndex 工具索引
     * @return 按钮中心位置
     */
    QPointF calculateToolPosition(int toolIndex) const;

    /**
     * @brief 获取点击的工具索引
     * @param pos 点击位置
     * @return 工具索引，-1表示未点击工具
     */
    int getClickedToolIndex(const QPointF& pos) const;

    /**
     * @brief 检查是否点击了中心按钮
     * @param pos 点击位置
     * @return 是否点击中心按钮
     */
    bool isCenterButtonClicked(const QPointF& pos) const;

    /**
     * @brief 检查是否在拖拽区域内
     * 展开状态：整个背景圆盘可拖拽（排除工具按钮）
     * 收起状态：只有中心按钮区域可拖拽
     * @param pos 点击位置
     * @return 是否在拖拽区域内
     */
    bool isInCenterDragArea(const QPointF& pos) const;

    /**
     * @brief 检查是否在有效点击区域内（用于点击穿透判断）
     * @param pos 点击位置
     * @return 是否在有效点击区域内
     */
    bool isInValidClickArea(const QPointF& pos) const;

    /**
     * @brief 限制位置在主窗口范围内
     * @param pos 要限制的位置
     * @return 限制后的位置
     */
    QPoint constrainPositionToMainWindow(const QPoint& pos) const;



    /**
     * @brief 处理工具点击
     * @param toolName 工具名称
     */
    void handleToolClick(const QString& toolName, QMouseEvent* event);

    /**
     * @brief 初始化弧形内容管理器
     */
    void initializeArcContentManager();

    /**
     * @brief 注册arc内容区域为外部组件
     * @param toolName 工具名称
     * @param config arc内容配置
     */
    void registerArcContentArea(const QString& toolName, const ArcContentConfig& config);

    /**
     * @brief 连接状态管理器信号
     */
    void connectStateManagerSignals();

    /**
     * @brief 初始化性能优化
     */
    void initializePerformanceOptimizations();

    /**
     * @brief 初始化SVG管理器
     */
    void initializeSvgManager();

    /**
     * @brief 初始化pen工具SVG图标
     */
    void initializePenToolIcons();

    /**
     * @brief 分层动画属性访问器
     */
    qreal backgroundScale() const { return m_backgroundScale; }
    void setBackgroundScale(qreal scale);

    qreal backgroundOpacity() const { return m_backgroundOpacity; }
    void setBackgroundOpacity(qreal opacity);

    qreal starScale() const { return m_starScale; }
    void setStarScale(qreal scale);

    qreal starOpacity() const { return m_starOpacity; }
    void setStarOpacity(qreal opacity);

    qreal sectorScale() const { return m_sectorScale; }
    void setSectorScale(qreal scale);

    qreal sectorOpacity() const { return m_sectorOpacity; }
    void setSectorOpacity(qreal opacity);

    qreal toolButtonsOpacity() const { return m_toolButtonsOpacity; }
    void setToolButtonsOpacity(qreal opacity);

    qreal toolButtonsScale() const { return m_toolButtonsScale; }
    void setToolButtonsScale(qreal scale);

    qreal sectorRotation() const { return m_sectorRotation; }
    void setSectorRotation(qreal rotation);

    /**
     * @brief 获取工具按钮缩放值
     * @param index 工具索引
     * @return 缩放值
     */
    qreal getToolButtonScale(int index) const;

    /**
     * @brief 设置工具按钮缩放值
     * @param index 工具索引
     * @param scale 缩放值
     */
    void setToolButtonScale(int index, qreal scale);

private:
    // 状态管理
    FloatMenuStateManager* m_stateManager;  ///< 状态管理器

    // 基本属性
    QVector<FloatMenuTool> m_tools;     ///< 工具列表

    // 尺寸常量（使用统一的常量定义）
    int CORE_WIDGET_SIZE = FloatMenuConstants::CORE_WIDGET_SIZE();
    int BACKGROUND_SIZE  = FloatMenuConstants::BACKGROUND_SIZE();
    int CENTER_BUTTON_SIZE  = FloatMenuConstants::CENTER_BUTTON_SIZE();
    int TOOL_BUTTON_SIZE  = FloatMenuConstants::TOOL_BUTTON_SIZE();
    int TOOL_RADIUS  = FloatMenuConstants::TOOL_RADIUS();
    int CORE_MARGIN  = FloatMenuConstants::CORE_MARGIN();

    // 动画相关
    QParallelAnimationGroup* m_expandAnimationGroup;   ///< 展开动画组
    QParallelAnimationGroup* m_collapseAnimationGroup; ///< 收起动画组
    QParallelAnimationGroup* m_showAnimation;          ///< 显示动画组
    QParallelAnimationGroup* m_hideAnimation;          ///< 隐藏动画组
    QPropertyAnimation* m_sectorRotationAnimation;     ///< 扇形旋转动画

    // 分层动画进度
    qreal m_backgroundScale;                    ///< 背景圆盘缩放
    qreal m_backgroundOpacity;                  ///< 背景圆盘透明度
    qreal m_starScale;                          ///< 星形背景缩放
    qreal m_starOpacity;                        ///< 星形背景透明度
    qreal m_sectorScale;                        ///< 扇形指示器缩放
    qreal m_sectorOpacity;                      ///< 扇形指示器透明度
    qreal m_toolButtonsOpacity;                 ///< 工具按钮透明度
    qreal m_toolButtonsScale;                   ///< 工具按钮缩放
    qreal m_sectorRotation;                     ///< 扇形当前旋转角度
    QVector<qreal> m_toolButtonScales;          ///< 工具按钮缩放值
    int m_clickedToolIndex;                     ///< 被点击的工具索引

    // 交互状态
    bool m_isDragging;                  ///< 是否正在拖拽
    QPoint m_dragStartPos;              ///< 拖拽起始位置
    QPoint m_windowStartPos;            ///< 窗口起始位置
    int m_hoveredToolIndex;             ///< 悬停的工具索引
    bool m_centerButtonHovered;         ///< 中心按钮是否悬停
    bool m_dragStarted;                 ///< 是否已开始拖拽
    bool m_hasDragged;                  ///< 是否已发生拖拽
    int DRAG_THRESHOLD  = FloatMenuConstants::DRAG_THRESHOLD(); ///< 拖拽阈值

    // 防重复响应
    QElapsedTimer m_lastClickTimer;     ///< 上次点击时间计时器
    static constexpr int CLICK_DEBOUNCE_MS = 300; ///< 防重复点击间隔（毫秒）

    // 资源
    QPixmap m_starBackgroundPixmap;     ///< 星形背景图片
    QPixmap m_sectorIndicatorPixmap;    ///< 扇形指示器图片
    QPixmap m_centerButtonPixmap;       ///< 中心按钮图片
    IconFontManager* m_iconFontManager; ///< iconfont管理器
    bool m_iconFontLoaded;              ///< iconfont字体是否加载成功

    // 外部组件管理
    QVector<QRectF> m_externalComponentRects; ///< 外部组件占用的区域列表
    QPointF m_coreOffset;                     ///< 核心区域在widget中的偏移

    // 弧形内容管理
    ArcContentManager* m_arcContentManager;   ///< 弧形内容管理器

    // 图形工具面板
    GraphicToolPanel* m_graphicToolPanel;     ///< 图形工具面板


    // 自动收起功能
    QTimer* m_autoCollapseTimer;              ///< 自动收起定时器
    static constexpr int AUTO_COLLAPSE_DELAY = FloatMenuConstants::AUTO_COLLAPSE_DELAY; ///< 自动收起延迟时间（毫秒）

    // SVG管理
    SvgManager* m_svgManager;                 ///< SVG管理器
    QMap<QString, QString> m_toolSvgIds;      ///< 工具名称到SVG ID的映射
    QMap<QString, bool> m_toolUseSvg;         ///< 工具是否使用SVG图标

    // Pen工具SVG图标管理
    QStringList m_penIconSvgIds;              ///< pen工具图标SVG ID列表 (虚线、荧光笔、马克笔)

    // 主窗口引用（用于独立窗口定位）
    QWidget* m_mainWindow;                    ///< 主窗口指针

};

#endif // FLOATMENUWIDGET_H
