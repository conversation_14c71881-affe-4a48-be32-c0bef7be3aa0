#include "GraphicToolPanel.h"
#include "../../iconfont/iconfonts.h"
#include <QPainter>
#include <QApplication>
#include <QScreen>
#include <QMouseEvent>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QEasingCurve>
#include <QtMath>

GraphicToolPanel::GraphicToolPanel(QWidget *parent)
    : QWidget(parent)
    , m_visible(false)
    , m_opacity(0.0)
    , m_scale(1.0)
    , m_diskRadius(FloatMenuConstants::BACKGROUND_SIZE()  / 2)
    , m_currentPosition(PanelPosition::Right)
    , m_currentGraphicTool(NO_SELECTION)
    , m_iconFontManager(nullptr)
    , m_iconFontLoaded(false)
    , m_showAnimation(nullptr)
    , m_hideAnimation(nullptr)
{
    // 改为子widget模式，移除独立窗口标志
    if (parent) {
        setAttribute(Qt::WA_TranslucentBackground);
    } else {
        setAttribute(Qt::WA_TranslucentBackground);
    }

    // 设置属性允许超出父widget边界显示
    setAttribute(Qt::WA_NoSystemBackground);
    setAutoFillBackground(false);

    setFixedSize(PANEL_WIDTH, PANEL_HEIGHT);
    setMouseTracking(true);

    m_iconFontManager = IconFontManager::instance();
    initializeGraphicTools();
    initializeAnimations();
    hide();


}

GraphicToolPanel::~GraphicToolPanel()
{

}

void GraphicToolPanel::initializeGraphicTools()
{
    m_graphicTools.clear();
    if (m_iconFontManager) {
        m_iconFontLoaded = m_iconFontManager->loadDefaultIconFont();
        if (!m_iconFontLoaded) {
            qWarning() << "GraphicToolPanel: 无法加载iconfont字体";
        }
    }
    
    m_graphicTools.append(GraphicTool(0, IconFonts::Icons::LINE_TOOL, "直线"));
    m_graphicTools.append(GraphicTool(1, IconFonts::Icons::DASHED_LINE, "虚线"));
    m_graphicTools.append(GraphicTool(2, IconFonts::Icons::ARROW_ALT, "箭头"));

    m_graphicTools.append(GraphicTool(3, IconFonts::Icons::CIRCLE, "圆形"));
    m_graphicTools.append(GraphicTool(4, IconFonts::Icons::ELLIPSE, "椭圆"));
    m_graphicTools.append(GraphicTool(5, IconFonts::Icons::RECTANGLE, "矩形"));
    m_graphicTools.append(GraphicTool(6, IconFonts::Icons::SQUARE, "正方形"));
    m_graphicTools.append(GraphicTool(7, IconFonts::Icons::TRIANGLE, "三角形"));
    m_graphicTools.append(GraphicTool(8, IconFonts::Icons::RIGHT_TRIANGLE, "直角三角形"));

    m_graphicToolButtonRects.resize(m_graphicTools.size());
    

}

void GraphicToolPanel::initializeAnimations()
{

    m_showAnimation = new QParallelAnimationGroup(this);
    

    QPropertyAnimation* showOpacityAnim = new QPropertyAnimation(this, "opacity");
    showOpacityAnim->setDuration(300);
    showOpacityAnim->setStartValue(0.0);
    showOpacityAnim->setEndValue(1.0);
    showOpacityAnim->setEasingCurve(QEasingCurve::OutQuad);
    m_showAnimation->addAnimation(showOpacityAnim);
    

    QPropertyAnimation* showScaleAnim = new QPropertyAnimation(this, "scale");
    showScaleAnim->setDuration(400);
    showScaleAnim->setStartValue(0.8);
    showScaleAnim->setEndValue(1.0);
    QEasingCurve showScaleCurve(QEasingCurve::OutBack);
    showScaleCurve.setOvershoot(1.1);
    showScaleAnim->setEasingCurve(showScaleCurve);
    m_showAnimation->addAnimation(showScaleAnim);
    

    m_hideAnimation = new QParallelAnimationGroup(this);
    
    // 透明度动画
    QPropertyAnimation* hideOpacityAnim = new QPropertyAnimation(this, "opacity");
    hideOpacityAnim->setDuration(200);
    hideOpacityAnim->setStartValue(1.0);
    hideOpacityAnim->setEndValue(0.0);
    hideOpacityAnim->setEasingCurve(QEasingCurve::InQuad);
    m_hideAnimation->addAnimation(hideOpacityAnim);
    
    // 缩放动画
    QPropertyAnimation* hideScaleAnim = new QPropertyAnimation(this, "scale");
    hideScaleAnim->setDuration(200);
    hideScaleAnim->setStartValue(1.0);
    hideScaleAnim->setEndValue(0.8);
    hideScaleAnim->setEasingCurve(QEasingCurve::InQuad);
    m_hideAnimation->addAnimation(hideScaleAnim);
    

    connect(m_showAnimation, &QParallelAnimationGroup::finished,
            this, &GraphicToolPanel::onShowAnimationFinished);
    connect(m_hideAnimation, &QParallelAnimationGroup::finished,
            this, &GraphicToolPanel::onHideAnimationFinished);
    
    // 连接动画到重绘
    connect(showOpacityAnim, &QPropertyAnimation::valueChanged,
            this, QOverload<>::of(&QWidget::update));
    connect(showScaleAnim, &QPropertyAnimation::valueChanged,
            this, QOverload<>::of(&QWidget::update));
    connect(hideOpacityAnim, &QPropertyAnimation::valueChanged,
            this, QOverload<>::of(&QWidget::update));
    connect(hideScaleAnim, &QPropertyAnimation::valueChanged,
            this, QOverload<>::of(&QWidget::update));
    

}

void GraphicToolPanel::setCurrentGraphicTool(int toolType)
{
    if (m_currentGraphicTool != toolType) {
        m_currentGraphicTool = toolType;
        update();
        emit graphicToolChanged(toolType);
        

    }
}

void GraphicToolPanel::clearSelection()
{
    setCurrentGraphicTool(NO_SELECTION);
}

void GraphicToolPanel::showPanel(bool animated)
{
    if (m_visible) return;

    m_visible = true;

    // 停止当前动画
    m_hideAnimation->stop();

    // 子widget模式下的显示逻辑
    if (parent()) {
        // 子控件模式
        show();
        raise();
    } else {
        // 独立窗口模式（兼容性保留）
        show();
    }

    if (animated) {
        // 启动显示动画
        m_showAnimation->start();
    } else {
        // 直接设置最终状态
        setOpacity(1.0);
        setScale(1.0);
        emit showCompleted();
    }


}

void GraphicToolPanel::determineInitialPosition(const QPointF& diskCenter)
{
    // 只在展开时判断一次位置：右边放不下就放左边
    PanelPosition newPosition = detectBestPositionSimple(diskCenter);

    if (m_currentPosition != newPosition) {
        m_currentPosition = newPosition;
        QString positionName = (newPosition == PanelPosition::Left) ? "左侧" : "右侧";
        qDebug() << "GraphicToolPanel: 确定显示位置为" << positionName;
    }
}

void GraphicToolPanel::hidePanel(bool animated)
{
    if (!m_visible) return;
    
    m_visible = false;
    
    // 停止当前动画
    m_showAnimation->stop();
    
    if (animated) {
        // 启动隐藏动画
        m_hideAnimation->start();
    } else {
        // 直接隐藏
        setOpacity(0.0);
        setScale(0.8);
        hide();
        emit hideCompleted();
    }
    

}

void GraphicToolPanel::setOpacity(qreal opacity)
{
    if (qAbs(m_opacity - opacity) > 0.001) {
        m_opacity = qBound(0.0, opacity, 1.0);
        update();
    }
}

void GraphicToolPanel::setScale(qreal scale)
{
    if (qAbs(m_scale - scale) > 0.001) {
        m_scale = qBound(0.1, scale, 2.0);
        update();
    }
}

void GraphicToolPanel::updatePosition(const QPointF& diskCenter)
{
    // 移动时不重新判断位置，使用当前已确定的位置
    QPoint panelPos;

    if (parent()) {
        // 子widget模式：直接使用父widget的本地坐标系统
        QWidget* parentWidget = qobject_cast<QWidget*>(parent());
        if (parentWidget) {
            QPointF localDiskCenter = parentWidget->mapFromGlobal(diskCenter.toPoint());

            // 根据当前位置计算面板在父widget坐标系中的位置
            int x, y;
            if (m_currentPosition == PanelPosition::Left) {
                // 左侧位置
                x = qRound(localDiskCenter.x() - m_diskRadius - PANEL_MARGIN - PANEL_WIDTH);
                y = qRound(localDiskCenter.y() - PANEL_HEIGHT / 2);
            } else {
                // 右侧位置（默认）
                x = qRound(localDiskCenter.x() + m_diskRadius + PANEL_MARGIN);
                y = qRound(localDiskCenter.y() - PANEL_HEIGHT / 2);
            }

            panelPos = QPoint(x, y);
        }
    } else {
        // 独立窗口模式：使用全局坐标
        panelPos = calculatePositionForPreference(diskCenter, m_currentPosition);
    }

    move(panelPos);
}

void GraphicToolPanel::setDiskRadius(int radius)
{
    if (m_diskRadius != radius) {
        m_diskRadius = radius;

    }
}

GraphicToolPanel::PanelPosition GraphicToolPanel::detectBestPosition(const QPointF& diskCenter) const
{
    // 获取当前屏幕
    QScreen* screen = QApplication::screenAt(diskCenter.toPoint());
    if (!screen) {
        screen = QApplication::primaryScreen();
    }
    
    if (!screen) {
        // 如果无法获取屏幕信息，返回默认位置
        return PanelPosition::Right;
    }
    
    QRect screenGeometry = screen->availableGeometry();
    
    // 计算各个方向的可用空间
    qreal rightSpace = screenGeometry.right() - diskCenter.x() - m_diskRadius;
    qreal leftSpace = diskCenter.x() - m_diskRadius - screenGeometry.left();
    qreal topSpace = diskCenter.y() - m_diskRadius - screenGeometry.top();
    qreal bottomSpace = screenGeometry.bottom() - diskCenter.y() - m_diskRadius;
    
    // 检查右侧是否有足够空间（优先选择）
    if (rightSpace >= PANEL_WIDTH + PANEL_MARGIN) {
        return PanelPosition::Right;
    }
    
    // 检查左侧是否有足够空间
    if (leftSpace >= PANEL_WIDTH + PANEL_MARGIN) {
        return PanelPosition::Left;
    }
    
    // 检查下方是否有足够空间
    if (bottomSpace >= PANEL_HEIGHT + PANEL_MARGIN) {
        return PanelPosition::Bottom;
    }
    
    // 检查上方是否有足够空间
    if (topSpace >= PANEL_HEIGHT + PANEL_MARGIN) {
        return PanelPosition::Top;
    }
    
    // 如果所有方向都没有足够空间，选择空间最大的方向
    qreal maxSpace = qMax(qMax(rightSpace, leftSpace), qMax(topSpace, bottomSpace));
    
    if (maxSpace == rightSpace) {
        return PanelPosition::Right;
    } else if (maxSpace == leftSpace) {
        return PanelPosition::Left;
    } else if (maxSpace == bottomSpace) {
        return PanelPosition::Bottom;
    } else {
        return PanelPosition::Top;
    }
}

QPoint GraphicToolPanel::calculatePositionForPreference(const QPointF& diskCenter, PanelPosition position) const
{
    int x, y;
    
    switch (position) {
        case PanelPosition::Right:
            // 面板位于圆盘右侧
            x = qRound(diskCenter.x() + m_diskRadius + PANEL_MARGIN);
            y = qRound(diskCenter.y() - PANEL_HEIGHT / 2);
            break;
            
        case PanelPosition::Left:
            // 面板位于圆盘左侧
            x = qRound(diskCenter.x() - m_diskRadius - PANEL_MARGIN - PANEL_WIDTH);
            y = qRound(diskCenter.y() - PANEL_HEIGHT / 2);
            break;
            
        case PanelPosition::Top:
            // 面板位于圆盘上方
            x = qRound(diskCenter.x() - PANEL_WIDTH / 2);
            y = qRound(diskCenter.y() - m_diskRadius - PANEL_MARGIN - PANEL_HEIGHT);
            break;
            
        case PanelPosition::Bottom:
            // 面板位于圆盘下方
            x = qRound(diskCenter.x() - PANEL_WIDTH / 2);
            y = qRound(diskCenter.y() + m_diskRadius + PANEL_MARGIN);
            break;
            
        default:
            // 默认右侧
            x = qRound(diskCenter.x() + m_diskRadius + PANEL_MARGIN);
            y = qRound(diskCenter.y() - PANEL_HEIGHT / 2);
            break;
    }
    
    // 确保面板不会超出屏幕边界
    QScreen* screen = QApplication::screenAt(diskCenter.toPoint());
    if (screen) {
        QRect screenGeometry = screen->availableGeometry();
        
        // 限制X坐标
        x = qMax(screenGeometry.left(), qMin(x, screenGeometry.right() - PANEL_WIDTH));
        
        // 限制Y坐标
        y = qMax(screenGeometry.top(), qMin(y, screenGeometry.bottom() - PANEL_HEIGHT));
    }
    
    return QPoint(x, y);
}

GraphicToolPanel::PanelPosition GraphicToolPanel::detectBestPositionSimple(const QPointF& diskCenter) const
{
    // 如果是子widget模式，基于父widget边界判断
    if (parent()) {
        QWidget* parentWidget = qobject_cast<QWidget*>(parent());
        if (parentWidget) {
            // 将全局坐标转换为父widget的本地坐标
            QPointF localDiskCenter = parentWidget->mapFromGlobal(diskCenter.toPoint());
            QRect parentGeometry = parentWidget->rect();

            // 计算左右两个方向在父widget内的可用空间
            qreal rightSpace = parentGeometry.right() - localDiskCenter.x() - m_diskRadius;
            qreal leftSpace = localDiskCenter.x() - m_diskRadius - parentGeometry.left();

            // 检测右侧是否放得下，放不下就放左侧
            bool canFitOnRight = (rightSpace >= PANEL_WIDTH + PANEL_MARGIN);
            bool canFitOnLeft = (leftSpace >= PANEL_WIDTH + PANEL_MARGIN);

            qDebug() << "GraphicToolPanel: 父widget模式位置判断 - 右侧空间:" << rightSpace << "左侧空间:" << leftSpace;
            qDebug() << "GraphicToolPanel: 右侧可放置:" << canFitOnRight << "左侧可放置:" << canFitOnLeft;

            // 优先右侧，右侧放不下且左侧放得下就放左侧
            if (canFitOnRight) {
                return PanelPosition::Right;
            } else if (canFitOnLeft) {
                return PanelPosition::Left;
            } else {
                // 两侧都放不下，选择右侧（强制显示）
                return PanelPosition::Right;
            }
        }
    }

    // 独立窗口模式，基于屏幕边界判断（原有逻辑）
    QScreen* screen = QApplication::screenAt(diskCenter.toPoint());
    if (!screen) {
        screen = QApplication::primaryScreen();
    }

    if (!screen) {
        return PanelPosition::Right;
    }

    QRect screenGeometry = screen->availableGeometry();
    qreal rightSpace = screenGeometry.right() - diskCenter.x() - m_diskRadius;
    qreal leftSpace = diskCenter.x() - m_diskRadius - screenGeometry.left();

    bool canFitOnRight = (rightSpace >= PANEL_WIDTH + PANEL_MARGIN);
    bool canFitOnLeft = (leftSpace >= PANEL_WIDTH + PANEL_MARGIN);

    if (canFitOnRight) {
        return PanelPosition::Right;
    } else if (canFitOnLeft) {
        return PanelPosition::Left;
    } else {
        return PanelPosition::Right;
    }
}

void GraphicToolPanel::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event)
    
    if (m_opacity <= 0.0) return;
    
    QPainter painter(this);
    
    // 启用高质量抗锯齿
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);
    painter.setRenderHint(QPainter::TextAntialiasing, true);
    
    // 设置透明度
    painter.setOpacity(m_opacity);
    
    // 计算缩放后的绘制区域
    QRectF drawRect = rect();
    if (qAbs(m_scale - 1.0) > 0.001) {
        qreal scaledWidth = drawRect.width() * m_scale;
        qreal scaledHeight = drawRect.height() * m_scale;
        qreal offsetX = (drawRect.width() - scaledWidth) / 2;
        qreal offsetY = (drawRect.height() - scaledHeight) / 2;
        drawRect = QRectF(offsetX, offsetY, scaledWidth, scaledHeight);
        
        // 应用缩放变换
        painter.translate(drawRect.center());
        painter.scale(m_scale, m_scale);
        painter.translate(-rect().center());
        drawRect = rect();
    }
    
    // 绘制面板背景
    drawPanelBackground(&painter, drawRect);
    
    // 绘制标题
    drawTitle(&painter, drawRect);
    
    // 绘制图形工具图标
    drawGraphicToolIcons(&painter, drawRect);
}

void GraphicToolPanel::drawPanelBackground(QPainter* painter, const QRectF& rect)
{
    painter->save();
    
    // 绘制黑色半透明背景
    QColor bgColor(0, 0, 0, qRound(0.65 * 255)); // rgba(0,0,0,0.65)
    painter->setBrush(bgColor);
    painter->setPen(Qt::NoPen);
    painter->drawRoundedRect(rect, PANEL_RADIUS, PANEL_RADIUS);
    
    painter->restore();
}

void GraphicToolPanel::drawTitle(QPainter* painter, const QRectF& rect)
{
    painter->save();
    
    // 设置标题字体和颜色
    QFont titleFont("Microsoft YaHei", 12);
    painter->setFont(titleFont);
    
    QColor titleColor(255, 255, 255, qRound(0.65 * 255)); // rgba(255,255,255,0.65)
    painter->setPen(titleColor);
    
    // 计算标题区域
    QRectF titleRect(
        rect.left() + PANEL_PADDING,
        rect.top() + PANEL_PADDING,
        rect.width() - 2 * PANEL_PADDING,
        TITLE_HEIGHT
    );
    
    // 绘制标题
    painter->drawText(titleRect, Qt::AlignLeft | Qt::AlignVCenter, "图形");
    
    painter->restore();
}

void GraphicToolPanel::drawGraphicToolIcons(QPainter* painter, const QRectF& rect)
{
    painter->save();
    
    // 计算图标区域
    QRectF iconsRect(
        rect.left() + PANEL_PADDING,
        rect.top() + PANEL_PADDING + TITLE_HEIGHT + ICON_SPACING_V,
        rect.width() - 2 * PANEL_PADDING,
        rect.height() - 2 * PANEL_PADDING - TITLE_HEIGHT - ICON_SPACING_V
    );
    
    // 设置图标字体
    QFont iconFont;
    if (m_iconFontLoaded && m_iconFontManager) {
        iconFont = m_iconFontManager->getFont(ICON_SIZE);
    } else {
        iconFont = QFont("Arial", ICON_SIZE / 2, QFont::Bold);
    }
    painter->setFont(iconFont);
    
    // 绘制图标
    for (int i = 0; i < m_graphicTools.size(); ++i) {
        const GraphicTool& tool = m_graphicTools[i];
        
        // 计算图标位置
        QRectF iconRect = getGraphicToolButtonRect(i, iconsRect);
        m_graphicToolButtonRects[i] = iconRect;
        
        // 如果是选中状态，绘制背景
        if (tool.type == m_currentGraphicTool && m_currentGraphicTool != NO_SELECTION) {
            painter->save();
            
            // 绘制33*33的背景，让32*32的图标居中
            QRectF bgRect(
                iconRect.center().x() - 16.5, // 33/2 = 16.5
                iconRect.center().y() - 16.5,
                33,
                33
            );
            QColor bgColor(95, 82, 227, qRound(0.45 * 255)); // rgba(95, 82, 227, 0.45)
            painter->setBrush(bgColor);
            painter->setPen(Qt::NoPen);
            painter->drawRoundedRect(bgRect, 12, 12);
            
            painter->restore();
        }
        
        // 设置图标颜色（默认白色）
        QColor iconColor(255, 255, 255, 255); // 白色图标
        painter->setPen(iconColor);
        
        if (m_iconFontLoaded && m_iconFontManager) {
            // 使用iconfont字体绘制图标
            QString iconChar = QString(QChar(tool.iconUnicode));
            painter->drawText(iconRect, Qt::AlignCenter, iconChar);
        } else {
            // 回退到文字显示（调试用）
            painter->drawText(iconRect, Qt::AlignCenter, tool.name.left(1));
        }
    }
    
    painter->restore();
}

QRectF GraphicToolPanel::getGraphicToolButtonRect(int index, const QRectF& iconsRect) const
{
    if (index < 0 || index >= m_graphicTools.size()) {
        return QRectF();
    }
    
    int row, col;
    
    if (index < ICONS_PER_ROW_1) {
        // 第一排（3个图标）
        row = 0;
        col = index;
    } else {
        // 第二排（6个图标）
        row = 1;
        col = index - ICONS_PER_ROW_1;
    }
    
    // 计算图标位置（左对齐）
    qreal x = iconsRect.left() + col * (ICON_SIZE + ICON_SPACING_H);
    qreal y = iconsRect.top() + row * (ICON_SIZE + ICON_SPACING_V);
    
    return QRectF(x, y, ICON_SIZE, ICON_SIZE);
}

void GraphicToolPanel::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        QPointF pos = event->position();
        
        // 检查是否点击了图形工具按钮
        for (int i = 0; i < m_graphicToolButtonRects.size(); ++i) {
            if (m_graphicToolButtonRects[i].contains(pos)) {
                // 点击了图形工具按钮
                int clickedToolType = m_graphicTools[i].type;
                
                // 如果点击的是当前已选中的工具，则取消选中
                if (m_currentGraphicTool == clickedToolType) {
                    setCurrentGraphicTool(NO_SELECTION);
                } else {
                    // 否则选中该工具
                    setCurrentGraphicTool(clickedToolType);
                }
                
                emit panelClicked(pos);
                event->accept();
                return;
            }
        }
        
        // 点击了面板其他区域
        emit panelClicked(pos);
        event->accept();
    }
    
    QWidget::mousePressEvent(event);
}

void GraphicToolPanel::onShowAnimationFinished()
{
    emit showCompleted();

}

void GraphicToolPanel::onHideAnimationFinished()
{
    hide();
    emit hideCompleted();

} 
