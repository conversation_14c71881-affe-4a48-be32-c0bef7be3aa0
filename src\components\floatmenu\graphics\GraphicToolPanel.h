#ifndef GRAPHICTOOLPANEL_H
#define GRAPHICTOOLPANEL_H

#include <QWidget>
#include <QPainter>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QMouseEvent>
#include "../../iconfont/IconFontManager.h"
#include "../utils/FloatMenuConstants.h"
#include "../utils/FloatMenuUtils.h"

/**
 * @brief 图形工具面板组件
 * 
 * 显示图形工具选择面板，包括各种几何图形工具
 * 这是一个矩形的黑色半透明面板，位于圆盘右侧
 */
class GraphicToolPanel : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(qreal opacity READ opacity WRITE setOpacity)
    Q_PROPERTY(qreal scale READ scale WRITE setScale)

public:
    /**
     * @brief 构造函数
     * @param parent 父窗口
     */
    explicit GraphicToolPanel(QWidget *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~GraphicToolPanel();

    /**
     * @brief 设置当前选中的图形工具
     * @param toolType 图形工具类型，-1表示取消选中
     */
    void setCurrentGraphicTool(int toolType);

    /**
     * @brief 获取当前选中的图形工具
     * @return 图形工具类型，-1表示无选中
     */
    int getCurrentGraphicTool() const { return m_currentGraphicTool; }

    /**
     * @brief 清除当前选中状态
     */
    void clearSelection();

    /**
     * @brief 检查是否有工具被选中
     * @return 是否有工具被选中
     */
    bool hasSelection() const { return m_currentGraphicTool != NO_SELECTION; }

    /**
     * @brief 显示面板
     * @param animated 是否使用动画
     */
    void showPanel(bool animated = true);

    /**
     * @brief 隐藏面板
     * @param animated 是否使用动画
     */
    void hidePanel(bool animated = true);

    /**
     * @brief 获取是否可见
     * @return 是否可见
     */
    bool isPanelVisible() const { return m_visible; }

    /**
     * @brief 获取透明度
     * @return 透明度值 (0.0-1.0)
     */
    qreal opacity() const { return m_opacity; }

    /**
     * @brief 设置透明度
     * @param opacity 透明度值 (0.0-1.0)
     */
    void setOpacity(qreal opacity);

    /**
     * @brief 获取缩放比例
     * @return 缩放比例
     */
    qreal scale() const { return m_scale; }

    /**
     * @brief 设置缩放比例
     * @param scale 缩放比例
     */
    void setScale(qreal scale);

    /**
     * @brief 设置相对于圆盘中心的位置
     * @param diskCenter 圆盘中心位置（全局坐标）
     */
    void updatePosition(const QPointF& diskCenter);

    /**
     * @brief 设置圆盘半径
     * @param radius 圆盘半径
     */
    void setDiskRadius(int radius);

    /**
     * @brief 面板位置偏好枚举
     */
    enum class PanelPosition {
        Right,      ///< 圆盘右侧（默认）
        Left,       ///< 圆盘左侧
        Top,        ///< 圆盘上方
        Bottom      ///< 圆盘下方
    };

    /**
     * @brief 确定面板的初始显示位置（只在展开时调用）
     * @param diskCenter 圆盘中心位置（全局坐标）
     */
    void determineInitialPosition(const QPointF& diskCenter);

    /**
     * @brief 获取当前面板位置
     * @return 当前面板位置
     */
    PanelPosition getCurrentPosition() const { return m_currentPosition; }

signals:
    /**
     * @brief 图形工具改变信号
     * @param toolType 新的图形工具类型
     */
    void graphicToolChanged(int toolType);

    /**
     * @brief 显示完成信号
     */
    void showCompleted();

    /**
     * @brief 隐藏完成信号
     */
    void hideCompleted();

    /**
     * @brief 面板点击信号
     * @param position 点击位置
     */
    void panelClicked(const QPointF& position);

protected:
    /**
     * @brief 绘制事件
     * @param event 绘制事件
     */
    void paintEvent(QPaintEvent* event) override;

    /**
     * @brief 鼠标按下事件
     * @param event 鼠标事件
     */
    void mousePressEvent(QMouseEvent* event) override;

private slots:
    /**
     * @brief 显示动画完成槽
     */
    void onShowAnimationFinished();

    /**
     * @brief 隐藏动画完成槽
     */
    void onHideAnimationFinished();

private:
    /**
     * @brief 初始化图形工具数据
     */
    void initializeGraphicTools();

    /**
     * @brief 初始化动画
     */
    void initializeAnimations();

    /**
     * @brief 绘制面板背景
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawPanelBackground(QPainter* painter, const QRectF& rect);

    /**
     * @brief 绘制标题
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawTitle(QPainter* painter, const QRectF& rect);

    /**
     * @brief 绘制图形工具图标
     * @param painter 画笔
     * @param rect 绘制区域
     */
    void drawGraphicToolIcons(QPainter* painter, const QRectF& rect);

    /**
     * @brief 获取图形工具按钮区域
     * @param index 按钮索引
     * @param iconsRect 图标区域
     * @return 按钮区域
     */
    QRectF getGraphicToolButtonRect(int index, const QRectF& iconsRect) const;

    /**
     * @brief 检测最佳面板位置
     * @param diskCenter 圆盘中心位置（全局坐标）
     * @return 最佳位置偏好
     */
    PanelPosition detectBestPosition(const QPointF& diskCenter) const;

    /**
     * @brief 简化的位置检测（只检测左右两侧）
     * @param diskCenter 圆盘中心位置（全局坐标）
     * @return 最佳位置偏好（Left或Right）
     */
    PanelPosition detectBestPositionSimple(const QPointF& diskCenter) const;

    /**
     * @brief 根据位置偏好计算面板位置
     * @param diskCenter 圆盘中心位置（全局坐标）
     * @param position 位置偏好
     * @return 面板左上角位置
     */
    QPoint calculatePositionForPreference(const QPointF& diskCenter, PanelPosition position) const;

    /**
     * @brief 图形工具数据结构
     */
    struct GraphicTool {
        int type;           ///< 工具类型
        int iconUnicode;    ///< 图标Unicode
        QString name;       ///< 工具名称
        
        GraphicTool(int t, int icon, const QString& n)
            : type(t), iconUnicode(icon), name(n) {}
    };

private:
    // 面板状态
    bool m_visible;                             ///< 是否可见
    qreal m_opacity;                            ///< 透明度
    qreal m_scale;                              ///< 缩放比例
    
    // 圆盘信息
    int m_diskRadius;                           ///< 圆盘半径
    
    // 位置信息
    PanelPosition m_currentPosition;            ///< 当前面板位置
    
    // 图形工具属性
    int m_currentGraphicTool;                   ///< 当前选中的图形工具
    
    // 图形工具数据
    QVector<GraphicTool> m_graphicTools;        ///< 图形工具列表
    QVector<QRectF> m_graphicToolButtonRects;   ///< 图形工具按钮区域
    
    // IconFont管理器
    IconFontManager* m_iconFontManager;         ///< IconFont管理器
    bool m_iconFontLoaded;                      ///< IconFont是否加载成功
    
    // 动画
    QParallelAnimationGroup* m_showAnimation;   ///< 显示动画组
    QParallelAnimationGroup* m_hideAnimation;   ///< 隐藏动画组
    
    // 布局常量（使用统一的常量定义）
   int PANEL_WIDTH = FloatMenuConstants::GRAPHIC_PANEL_WIDTH();
   int PANEL_HEIGHT = FloatMenuConstants::GRAPHIC_PANEL_HEIGHT();
   int PANEL_RADIUS = FloatMenuConstants::GRAPHIC_PANEL_RADIUS();
   int PANEL_PADDING = FloatMenuConstants::GRAPHIC_PANEL_PADDING();
   int ICON_SIZE = FloatMenuConstants::GRAPHIC_ICON_SIZE();
   int ICON_SPACING_H = FloatMenuConstants::GRAPHIC_ICON_SPACING_H();
   int ICON_SPACING_V = FloatMenuConstants::GRAPHIC_ICON_SPACING_V();
   int TITLE_HEIGHT = FloatMenuConstants::GRAPHIC_TITLE_HEIGHT();
   int ICONS_PER_ROW_1 = FloatMenuConstants::GRAPHIC_ICONS_PER_ROW_1;
   int ICONS_PER_ROW_2 = FloatMenuConstants::GRAPHIC_ICONS_PER_ROW_2;
   int ICON_ROWS = FloatMenuConstants::GRAPHIC_ICON_ROWS;
   int PANEL_MARGIN = FloatMenuConstants::GRAPHIC_PANEL_MARGIN();

    // 选中状态常量（使用统一的常量定义）
    static constexpr int NO_SELECTION = FloatMenuConstants::NO_SELECTION;
};

#endif // GRAPHICTOOLPANEL_H 
