#include "FloatMenuSvgCache.h"
#include <QPainter>
#include <QApplication>
#include <QDateTime>

FloatMenuSvgCache* FloatMenuSvgCache::s_instance = nullptr;

FloatMenuSvgCache::FloatMenuSvgCache(QObject *parent)
    : QObject(parent)
    , m_enabled(true)
{
    m_config = SvgCacheConfig();
}

FloatMenuSvgCache::~FloatMenuSvgCache()
{
    QMutexLocker locker(&m_mutex);
    
    for (auto it = m_renderers.begin(); it != m_renderers.end(); ++it) {
        delete it.value();
    }
    m_renderers.clear();
    m_cache.clear();

}

FloatMenuSvgCache* FloatMenuSvgCache::instance()
{
    if (!s_instance) {
        s_instance = new FloatMenuSvgCache();
    }
    return s_instance;
}

void FloatMenuSvgCache::setConfig(const SvgCacheConfig& config)
{
    QMutexLocker locker(&m_mutex);
    m_config = config;

    if (m_cache.size() > m_config.maxCacheSize) {
        int removeCount = m_cache.size() - m_config.maxCacheSize;
        auto it = m_cache.begin();
        for (int i = 0; i < removeCount && it != m_cache.end(); ++i) {
            it = m_cache.erase(it);
        }
    }
}

QPixmap FloatMenuSvgCache::getCachedPixmap(const QString& svgPath, 
                                          const QSize& size, 
                                          qreal devicePixelRatio,
                                          const QColor& color)
{
    if (!m_enabled) {

        QSvgRenderer renderer(svgPath);
        return renderSvgToPixmap(&renderer, size, devicePixelRatio, color);
    }
    
    QMutexLocker locker(&m_mutex);

    QString cacheKey = generateCacheKey(svgPath, size, devicePixelRatio, color);


    if (m_cache.contains(cacheKey)) {
        return m_cache[cacheKey].pixmap;
    }
    QSvgRenderer* renderer = nullptr;
    if (m_renderers.contains(svgPath)) {
        renderer = m_renderers[svgPath];
    } else {
        renderer = FloatMenuUtils::loadSvgRenderer(svgPath, this, "FloatMenuSvgCache");
        if (renderer) {
            m_renderers[svgPath] = renderer;
        } else {
            return QPixmap();
        }
    }

    QPixmap pixmap = renderSvgToPixmap(renderer, size, devicePixelRatio, color);

    if (m_cache.size() >= m_config.maxCacheSize) {
        auto it = m_cache.begin();
        m_cache.erase(it);
    }
    m_cache[cacheKey] = SvgCacheItem(pixmap);
    return pixmap;
}

void FloatMenuSvgCache::preloadSvg(const QString& svgPath, 
                                  const QList<QSize>& sizes, 
                                  qreal devicePixelRatio)
{
    if (!m_enabled || !m_config.enablePreloading) {
        return;
    }
    
    for (const QSize& size : sizes) {
        // 预加载时不指定颜色
        getCachedPixmap(svgPath, size, devicePixelRatio);
    }
}

void FloatMenuSvgCache::preloadFloatMenuSvgs(qreal devicePixelRatio)
{
    if (!m_enabled || !m_config.enablePreloading) {
        return;
    }
    
    // 预加载常用的SVG文件和尺寸
    QList<QSize> commonSizes = {
        QSize(FloatMenuConstants::TOOL_BUTTON_SIZE() , FloatMenuConstants::TOOL_BUTTON_SIZE() ),
        QSize(FloatMenuConstants::CENTER_BUTTON_SIZE() , FloatMenuConstants::CENTER_BUTTON_SIZE() ),
        QSize(FloatMenuConstants::ARC_CONTENT_SVG_WIDTH() , FloatMenuConstants::ARC_CONTENT_SVG_HEIGHT() ),
        QSize(FloatMenuConstants::GRAPHIC_ICON_SIZE() , FloatMenuConstants::GRAPHIC_ICON_SIZE() )
    };
    
    // 预加载弧形背景
    preloadSvg(FloatMenuConstants::ResourcePaths::ARC_BACKGROUND_SVG, commonSizes, devicePixelRatio);
    
    preloadSvg(FloatMenuConstants::ResourcePaths::CLEAR_CONTROL_SVG, commonSizes, devicePixelRatio);
    
    // 预加载画笔线宽SVG
    preloadSvg(FloatMenuConstants::ResourcePaths::PEN_WIDTH_1_SVG, commonSizes, devicePixelRatio);
    preloadSvg(FloatMenuConstants::ResourcePaths::PEN_WIDTH_2_SVG, commonSizes, devicePixelRatio);
    preloadSvg(FloatMenuConstants::ResourcePaths::PEN_WIDTH_3_SVG, commonSizes, devicePixelRatio);

}

void FloatMenuSvgCache::clearCache(const QString& svgPath)
{
    QMutexLocker locker(&m_mutex);
    
    if (svgPath.isEmpty()) {
        // 清除所有缓存
        m_cache.clear();
    } else {
        // 清除指定SVG的缓存
        auto it = m_cache.begin();
        while (it != m_cache.end()) {
            if (it.key().startsWith(svgPath)) {
                it = m_cache.erase(it);
            } else {
                ++it;
            }
        }
    }
    
}

QString FloatMenuSvgCache::generateCacheKey(const QString& svgPath, 
                                           const QSize& size, 
                                           qreal devicePixelRatio,
                                           const QColor& color) const
{
    QString colorStr = color.isValid() ? color.name() : "none";
    return QString("%1_%2x%3_%4_%5")
           .arg(svgPath)
           .arg(size.width())
           .arg(size.height())
           .arg(devicePixelRatio, 0, 'f', 2)
           .arg(colorStr);
}

QPixmap FloatMenuSvgCache::renderSvgToPixmap(QSvgRenderer* renderer, 
                                            const QSize& size, 
                                            qreal devicePixelRatio,
                                            const QColor& color) const
{
    if (!renderer || !renderer->isValid()) {
        return QPixmap();
    }
    
    QSize actualSize = size * devicePixelRatio;
    QPixmap pixmap(actualSize);
    pixmap.fill(Qt::transparent);
    pixmap.setDevicePixelRatio(devicePixelRatio);
    
    QPainter painter(&pixmap);
    FloatMenuUtils::setupHighQualityRendering(&painter);
    
    // 如果指定了颜色，设置合成模式
    if (color.isValid()) {
        painter.setCompositionMode(QPainter::CompositionMode_SourceIn);
    }
    
    // 渲染SVG
    renderer->render(&painter, QRectF(0, 0, actualSize.width(), actualSize.height()));
    
    // 应用颜色
    if (color.isValid()) {
        painter.setCompositionMode(QPainter::CompositionMode_SourceIn);
        painter.fillRect(pixmap.rect(), color);
    }
    
    return pixmap;
}


