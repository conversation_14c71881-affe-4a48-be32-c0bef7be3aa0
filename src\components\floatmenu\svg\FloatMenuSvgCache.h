#ifndef FLOATMENUSVGCACHE_H
#define FLOATMENUSVGCACHE_H

#include <QObject>
#include <QPixmap>
#include <QSvgRenderer>
#include <QHash>
#include <QSize>
#include <QMutex>

#include "../utils/FloatMenuConstants.h"
#include "../utils/FloatMenuUtils.h"

/**
 * @file FloatMenuSvgCache.h
 * @brief FloatMenu SVG 缓存管理器
 * 
 * 提供高效的SVG渲染缓存机制，减少重复渲染开销，
 * 特别针对高DPI显示和频繁重绘场景进行优化
 */

/**
 * @brief SVG缓存项结构
 */
struct SvgCacheItem {
    QPixmap pixmap;                 // 缓存的像素图

    SvgCacheItem() {}

    SvgCacheItem(const QPixmap& p) : pixmap(p) {}
};

/**
 * @brief SVG缓存配置
 */
struct SvgCacheConfig {
    int maxCacheSize = 50;              // 最大缓存项数量
    bool enablePreloading = true;       // 是否启用预加载
};



/**
 * @brief FloatMenu SVG缓存管理器
 * 
 * 采用单例模式，提供全局的SVG缓存服务
 * 支持多线程安全访问和自动内存管理
 */
class FloatMenuSvgCache : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     * @return 缓存管理器实例
     */
    static FloatMenuSvgCache* instance();

    /**
     * @brief 设置缓存配置
     * @param config 缓存配置
     */
    void setConfig(const SvgCacheConfig& config);
    
    /**
     * @brief 获取当前配置
     * @return 当前缓存配置
     */
    SvgCacheConfig getConfig() const { return m_config; }

    /**
     * @brief 获取缓存的SVG像素图
     * @param svgPath SVG文件路径
     * @param size 目标尺寸
     * @param devicePixelRatio 设备像素比例
     * @param color 可选的颜色替换
     * @return 缓存的像素图
     */
    QPixmap getCachedPixmap(const QString& svgPath, 
                           const QSize& size, 
                           qreal devicePixelRatio = 1.0,
                           const QColor& color = QColor());

    /**
     * @brief 预加载SVG文件
     * @param svgPath SVG文件路径
     * @param sizes 预加载的尺寸列表
     * @param devicePixelRatio 设备像素比例
     */
    void preloadSvg(const QString& svgPath, 
                   const QList<QSize>& sizes, 
                   qreal devicePixelRatio = 1.0);

    /**
     * @brief 预加载FloatMenu常用SVG
     * @param devicePixelRatio 设备像素比例
     */
    void preloadFloatMenuSvgs(qreal devicePixelRatio = 1.0);

    /**
     * @brief 清除指定SVG的缓存
     * @param svgPath SVG文件路径
     */
    void clearCache(const QString& svgPath = QString());





    /**
     * @brief 设置是否启用缓存
     * @param enabled 是否启用
     */
    void setEnabled(bool enabled) { m_enabled = enabled; }
    
    /**
     * @brief 获取缓存是否启用
     * @return 是否启用
     */
    bool isEnabled() const { return m_enabled; }



private:
    /**
     * @brief 私有构造函数（单例模式）
     * @param parent 父对象
     */
    explicit FloatMenuSvgCache(QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~FloatMenuSvgCache();
    
    /**
     * @brief 禁用拷贝构造函数
     */
    FloatMenuSvgCache(const FloatMenuSvgCache&) = delete;
    
    /**
     * @brief 禁用赋值操作符
     */
    FloatMenuSvgCache& operator=(const FloatMenuSvgCache&) = delete;

    /**
     * @brief 生成缓存键
     * @param svgPath SVG文件路径
     * @param size 尺寸
     * @param devicePixelRatio 设备像素比例
     * @param color 颜色
     * @return 缓存键
     */
    QString generateCacheKey(const QString& svgPath, 
                            const QSize& size, 
                            qreal devicePixelRatio,
                            const QColor& color) const;

    /**
     * @brief 渲染SVG到像素图
     * @param renderer SVG渲染器
     * @param size 目标尺寸
     * @param devicePixelRatio 设备像素比例
     * @param color 可选的颜色替换
     * @return 渲染的像素图
     */
    QPixmap renderSvgToPixmap(QSvgRenderer* renderer, 
                             const QSize& size, 
                             qreal devicePixelRatio,
                             const QColor& color = QColor()) const;



private:
    static FloatMenuSvgCache* s_instance;       ///< 单例实例
    
    mutable QMutex m_mutex;                     ///< 线程安全锁
    QHash<QString, SvgCacheItem> m_cache;       ///< 缓存哈希表
    QHash<QString, QSvgRenderer*> m_renderers;  ///< SVG渲染器缓存
    
    SvgCacheConfig m_config;                    ///< 缓存配置
    bool m_enabled;                             ///< 是否启用缓存
};

#endif // FLOATMENUSVGCACHE_H
