#ifndef FLOATMENUCONSTANTS_H
#define FLOATMENUCONSTANTS_H

#include <QColor>
#include <QSizeF>
#include "../../screen_adaptation/ScreenAdaptationManager.h"

/**
 * @file FloatMenuConstants.h
 * @brief FloatMenu 组件相关的常量定义
 * 
 * 统一管理 FloatMenu 组件及其子组件的所有常量，
 * 避免重复定义和魔法数字
 */

namespace FloatMenuConstants {
    
    // ========== 核心圆盘尺寸常量 ==========
    inline int CORE_WIDGET_SIZE() { return ScreenAdaptationConstants::adaptSize(440); }
    inline int BACKGROUND_SIZE() { return ScreenAdaptationConstants::adaptSize(400); }
    inline int CENTER_BUTTON_SIZE() { return ScreenAdaptationConstants::adaptSize(130); }
    inline int TOOL_BUTTON_SIZE() { return ScreenAdaptationConstants::adaptSize(76); }
    inline int TOOL_RADIUS() { return ScreenAdaptationConstants::adaptSize(138); }
    inline int CORE_MARGIN() { return ScreenAdaptationConstants::adaptSize(106); }
    inline QRectF SECTOR_RECT() { return QRectF(-ScreenAdaptationConstants::adaptSize(200), -ScreenAdaptationConstants::adaptSize(172), ScreenAdaptationConstants::adaptSize(200), ScreenAdaptationConstants::adaptSize(172)); }
    
    // ========== 弧形内容尺寸常量 ==========
    inline int ARC_CONTENT_BASE_SIZE() { return ScreenAdaptationConstants::adaptSize(653); }
    inline int ARC_CONTENT_SVG_WIDTH() { return ScreenAdaptationConstants::adaptSize(328); }
    inline int ARC_CONTENT_SVG_HEIGHT() { return ScreenAdaptationConstants::adaptSize(653); }
    
    // ========== 图形工具面板尺寸常量 ==========
    inline int GRAPHIC_PANEL_WIDTH() { return ScreenAdaptationConstants::adaptSize(847); }
    inline int GRAPHIC_PANEL_HEIGHT() { return ScreenAdaptationConstants::adaptSize(336); }
    inline int GRAPHIC_PANEL_RADIUS() { return ScreenAdaptationConstants::adaptSize(40); }
    inline int GRAPHIC_PANEL_PADDING() { return ScreenAdaptationConstants::adaptSize(40); }
    inline int GRAPHIC_ICON_SIZE() { return ScreenAdaptationConstants::adaptSize(68); }
    inline int GRAPHIC_ICON_SPACING_H() { return ScreenAdaptationConstants::adaptSize(30); }
    inline int GRAPHIC_ICON_SPACING_V() { return ScreenAdaptationConstants::adaptSize(40); }
    inline int GRAPHIC_TITLE_HEIGHT() { return ScreenAdaptationConstants::adaptSize(56); }
    const int GRAPHIC_ICONS_PER_ROW_1 = 3;      // 第一排图标数量
    const int GRAPHIC_ICONS_PER_ROW_2 = 6;      // 第二排图标数量
    const int GRAPHIC_ICON_ROWS = 2;            // 图标行数
    inline int GRAPHIC_PANEL_MARGIN() { return ScreenAdaptationConstants::adaptSize(21); }
    
    // ========== 画笔弧形内容常量 ==========
    inline int PEN_CONTENT_PADDING() { return ScreenAdaptationConstants::adaptSize(43); }
    inline int PEN_COLOR_BUTTON_SIZE() { return ScreenAdaptationConstants::adaptSize(51); }
    inline int PEN_COLOR_BUTTON_SPACING() { return ScreenAdaptationConstants::adaptSize(17); }

    // 画笔类型WebP图片区域常量
    inline int PEN_IMAGE_LEFT_OFFSET() { return ScreenAdaptationConstants::adaptSize(43); }
    inline int PEN_IMAGE_TOP_OFFSET() { return ScreenAdaptationConstants::adaptSize(96); }
    inline int PEN_IMAGE_WIDTH() { return ScreenAdaptationConstants::adaptSize(268); }
    inline int PEN_IMAGE_HEIGHT() { return ScreenAdaptationConstants::adaptSize(323); }
    
    // ========== 橡皮擦弧形内容常量 ==========
    inline int ERASER_CONTENT_PADDING() { return ScreenAdaptationConstants::adaptSize(43); }
    const int ERASER_ICON_COUNT = 3;            // 橡皮擦图标数量
    inline int ERASER_LARGE_ICON_SIZE() { return ScreenAdaptationConstants::adaptSize(77); }
    inline int ERASER_MEDIUM_ICON_SIZE() { return ScreenAdaptationConstants::adaptSize(51); }
    inline int ERASER_SMALL_ICON_SIZE() { return ScreenAdaptationConstants::adaptSize(38); }
    inline int ERASER_CLICK_AREA_SIZE() { return ScreenAdaptationConstants::adaptSize(77); }
    inline int ERASER_CLEAR_CONTROL_SIZE() { return ScreenAdaptationConstants::adaptSize(49); }
    inline int ERASER_CLEAR_IMAGE_WIDTH() { return ScreenAdaptationConstants::adaptSize(207); }
    inline int ERASER_CLEAR_IMAGE_HEIGHT() { return ScreenAdaptationConstants::adaptSize(147); }
    inline int ERASER_CLEAR_IMAGE_OFFSETX() { return ScreenAdaptationConstants::adaptSize(100); }
    inline int ERASER_CLEAR_IMAGE_OFFSETY() { return ScreenAdaptationConstants::adaptSize(22); }
    
    // ========== 橡皮擦大小常量 ==========
    inline QSizeF ERASER_SIZE_SMALL() {
        return QSizeF(ScreenAdaptationConstants::adaptSize(123.0),
                      ScreenAdaptationConstants::adaptSize(138.0));
    }
    inline QSizeF ERASER_SIZE_MEDIUM() {
        return QSizeF(ScreenAdaptationConstants::adaptSize(155.0),
                      ScreenAdaptationConstants::adaptSize(174.0));
    }
    inline QSizeF ERASER_SIZE_LARGE() {
        return QSizeF(ScreenAdaptationConstants::adaptSize(170.0),
                      ScreenAdaptationConstants::adaptSize(191.0));
    }
    
    // ========== 角度常量 ==========
    constexpr qreal ERASER_DEFAULT_CLEAR_ANGLE = 44.0;  // 默认清屏控制角度
    constexpr qreal ERASER_MAX_CLEAR_ANGLE = 78.0;      // 最大清屏控制角度
    
    // ========== 定时器常量 ==========
    const int AUTO_COLLAPSE_DELAY = 5000;      // 自动收起延迟时间（毫秒）
    
    // ========== 交互常量 ==========
    inline int DRAG_THRESHOLD() { return ScreenAdaptationConstants::adaptSize(6); }
    
    // ========== 选中状态常量 ==========
    const int NO_SELECTION = -1;               // 无选中状态
    
    // ========== 画笔类型常量 ==========
    const int PEN_TYPE_SOLID = 0;              // 实线笔
    const int PEN_TYPE_DASHED = 1;             // 虚线笔
    const int PEN_TYPE_HIGHLIGHTER = 2;        // 荧光笔
    
    // ========== 默认画笔设置 ==========
    const int DEFAULT_PEN_TYPE = PEN_TYPE_SOLID;    // 默认实线笔
    const QColor DEFAULT_PEN_COLOR = QColor("#ffaa00");       // 默认黄色
    static constexpr qreal DEFAULT_PEN_WIDTH_BASE = 2.0;        // 默认画笔线宽（基础值）
    inline qreal DEFAULT_PEN_WIDTH() { return ScreenAdaptationConstants::adaptSize(4.3); }

    // ========== 画笔线宽映射 ==========
    inline qreal PEN_WIDTH_SMALL() { return ScreenAdaptationConstants::adaptSize(4.3); }
    inline qreal PEN_WIDTH_MEDIUM() { return ScreenAdaptationConstants::adaptSize(8.5); }
    inline qreal PEN_WIDTH_LARGE() { return ScreenAdaptationConstants::adaptSize(12.8); }
    
    // ========== 动画常量 ==========
    const int ANIMATION_DURATION_FAST = 150;   // 快速动画时长（毫秒）
    const int ANIMATION_DURATION_NORMAL = 300; // 正常动画时长（毫秒）
    const int ANIMATION_DURATION_SLOW = 500;   // 慢速动画时长（毫秒）
    
    // ========== 透明度常量 ==========
    const qreal OPACITY_HIDDEN = 0.0;          // 隐藏状态透明度
    const qreal OPACITY_VISIBLE = 1.0;         // 可见状态透明度
    const qreal OPACITY_SEMI_TRANSPARENT = 0.6; // 半透明状态
    
    // ========== 缩放常量 ==========
    const qreal SCALE_HIDDEN = 0.0;            // 隐藏状态缩放
    const qreal SCALE_NORMAL = 1.0;            // 正常状态缩放
    const qreal SCALE_HOVER = 1.1;             // 悬停状态缩放
    
    // ========== 颜色常量 ==========
    const QColor COLOR_WHITE = QColor(255, 255, 255);              // 白色
    const QColor COLOR_BLACK = QColor(0, 0, 0);                    // 黑色
    const QColor COLOR_SHADOW = QColor(0, 0, 0, 32);               // 阴影颜色 #20000000
    const QColor COLOR_GLASS_BG = QColor(236, 235, 243, 153);      // 毛玻璃背景 #ECEBF3 with 0.6 opacity
    const QColor COLOR_GRADIENT_START = QColor(41, 218, 128);      // 渐变起始色 rgba(41, 218, 128, 1)
    const QColor COLOR_GRADIENT_END = QColor(96, 83, 227);         // 渐变结束色 rgba(96, 83, 227, 1)
    
    // ========== 资源路径常量 ==========
    namespace ResourcePaths {
        // SVG 资源路径
        const QString ARC_BACKGROUND_SVG = ":/images/floatmenu/floatmenu-arc-bg.svg";
        const QString ERASER_PNG = ":/images/floatmenu/floatmenu-arc-eraser.png";  // 使用PNG格式的橡皮擦图标
        const QString CLEAR_CONTROL_SVG = ":/images/floatmenu/floatmenu-arc-eraser-clear-control.svg";
        
        // 画笔线宽SVG路径
        const QString PEN_WIDTH_1_SVG = ":/images/floatmenu/floatmenu-arc-pen-width-1.svg";
        const QString PEN_WIDTH_2_SVG = ":/images/floatmenu/floatmenu-arc-pen-width-2.svg";
        const QString PEN_WIDTH_3_SVG = ":/images/floatmenu/floatmenu-arc-pen-width-3.svg";
        
        // WebP 图片资源路径
        const QString PEN_COLOR_SELECT_WEBP = ":/images/floatmenu/floatmenu-arc-pen-color-select.webp";
        const QString ERASER_CLEAR_WEBP = ":/images/floatmenu/floatmenu-arc-eraser-clear.png";
        
        // 画笔类型WebP路径
        const QString PEN_TYPE_DASHED_WEBP = ":/images/floatmenu/floatmenu-arc-pen-type-dashed.webp";
        const QString PEN_TYPE_HIGHLIGHTER_WEBP = ":/images/floatmenu/floatmenu-arc-pen-type-highlighter.webp";
        const QString PEN_TYPE_SOLID_WEBP = ":/images/floatmenu/floatmenu-arc-pen-type-solid.webp";
    }
}

#endif // FLOATMENUCONSTANTS_H
