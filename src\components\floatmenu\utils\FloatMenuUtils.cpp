#include "FloatMenuUtils.h"
#include "../svg/FloatMenuSvgCache.h"
#include <QApplication>

namespace FloatMenuUtils {

QPixmap getCachedSvgPixmap(const QString& svgPath,
                          const QSize& size,
                          qreal devicePixelRatio,
                          const QColor& color,
                          const QString& componentName)
{
    // 使用SVG缓存管理器获取像素图
    FloatMenuSvgCache* cache = FloatMenuSvgCache::instance();
    QPixmap pixmap = cache->getCachedPixmap(svgPath, size, devicePixelRatio, color);

    
    return pixmap;
}

void preloadSvgCache(qreal devicePixelRatio)
{
    // 获取设备像素比例
    if (devicePixelRatio <= 0) {
        // 使用默认值，因为QCoreApplication没有devicePixelRatio方法
        devicePixelRatio = 1.0;
    }
    
    // 预加载FloatMenu常用的SVG
    FloatMenuSvgCache* cache = FloatMenuSvgCache::instance();
    cache->preloadFloatMenuSvgs(devicePixelRatio);

}

} // namespace FloatMenuUtils
