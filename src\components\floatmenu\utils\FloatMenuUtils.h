#ifndef FLOATMENUUTILS_H
#define FLOATMENUUTILS_H

#include <QPainter>
#include <QSvgRenderer>
#include <QObject>
#include <QPropertyAnimation>
#include <QParallelAnimationGroup>
#include <QEasingCurve>
#include <QLinearGradient>
#include <QPointF>
#include <QRectF>
#include <QSizeF>
#include <QColor>
#include <QPixmap>
#include <QSize>
#include <functional>
#include "FloatMenuConstants.h"

/**
 * @file FloatMenuUtils.h
 * @brief FloatMenu 组件的公共工具函数
 * 
 * 提供 FloatMenu 组件及其子组件共用的工具函数，
 * 避免代码重复，提高代码复用性
 */

namespace FloatMenuUtils {



    /**
     * @brief 设置画笔的高质量渲染选项
     * @param painter 画笔对象
     * 
     * 统一设置所有FloatMenu组件的渲染质量，
     * 特别针对4K屏幕优化
     */
    inline void setupHighQualityRendering(QPainter* painter) {
        if (!painter) return;
        
        painter->setRenderHint(QPainter::Antialiasing, true);
        painter->setRenderHint(QPainter::SmoothPixmapTransform, true);
        painter->setRenderHint(QPainter::TextAntialiasing, true);
        painter->setRenderHint(QPainter::VerticalSubpixelPositioning, true);
        painter->setRenderHint(QPainter::LosslessImageRendering, true);
    }
    
    /**
     * @brief 安全加载SVG渲染器
     * @param svgPath SVG文件路径
     * @param parent 父对象
     * @param componentName 组件名称（用于日志）
     * @return SVG渲染器指针，加载失败返回nullptr
     */
    inline QSvgRenderer* loadSvgRenderer(const QString& svgPath,
                                        QObject* parent = nullptr,
                                        const QString& componentName = "Unknown") {
        QSvgRenderer* renderer = new QSvgRenderer(svgPath, parent);

        if (!renderer->isValid()) {
            qWarning() << QString("%1: 无法加载SVG文件 - %2").arg(componentName, svgPath);
            delete renderer;
            return nullptr;
        }

        return renderer;
    }
    
    /**
     * @brief 安全加载WebP图片
     * @param imagePath 图片文件路径
     * @param componentName 组件名称（用于日志）
     * @param devicePixelRatio 设备像素比例（用于高DPI显示）
     * @return QPixmap对象
     */
    inline QPixmap loadWebPImage(const QString& imagePath,
                                const QString& componentName = "Unknown",
                                qreal devicePixelRatio = 1.0) {
        QPixmap pixmap(imagePath);

        if (pixmap.isNull()) {
            qWarning() << QString("%1: 无法加载WebP图片 - %2").arg(componentName, imagePath);
        } else {
            pixmap.setDevicePixelRatio(devicePixelRatio);
        }

        return pixmap;
    }

    /**
     * @brief 获取缓存的SVG像素图（高性能版本）
     * @param svgPath SVG文件路径
     * @param size 目标尺寸
     * @param devicePixelRatio 设备像素比例
     * @param color 可选的颜色替换
     * @param componentName 组件名称（用于日志）
     * @return 缓存的像素图
     */
    QPixmap getCachedSvgPixmap(const QString& svgPath,
                              const QSize& size,
                              qreal devicePixelRatio = 1.0,
                              const QColor& color = QColor(),
                              const QString& componentName = "Unknown");

    /**
     * @brief 预加载SVG缓存
     * @param devicePixelRatio 设备像素比例
     */
    void preloadSvgCache(qreal devicePixelRatio = 1.0);

    /**
     * @brief 创建线性渐变（FloatMenu常用的渐变样式）
     * @param startPoint 起始点
     * @param endPoint 结束点
     * @return 配置好的线性渐变对象
     */
    inline QLinearGradient createFloatMenuGradient(const QPointF& startPoint, 
                                                   const QPointF& endPoint) {
        QLinearGradient gradient(startPoint, endPoint);
        gradient.setColorAt(0.0, QColor(41, 218, 128));  // rgba(41, 218, 128, 1)
        gradient.setColorAt(1.0, QColor(96, 83, 227));   // rgba(96, 83, 227, 1)
        return gradient;
    }
    
    /**
     * @brief 绘制圆形渐变边框
     * @param painter 画笔对象
     * @param rect 绘制区域
     * @param borderWidth 边框宽度
     */
    inline void drawGradientCircleBorder(QPainter* painter, 
                                        const QRectF& rect, 
                                        qreal borderWidth = 4.0) {
        if (!painter) return;
        
        painter->save();
        
        // 计算边框区域
        QRectF borderRect = rect.adjusted(-borderWidth, -borderWidth, borderWidth, borderWidth);
        
        // 创建线性渐变 - 从上到下 (180deg)
        QLinearGradient borderGradient = createFloatMenuGradient(
            borderRect.topLeft(), 
            borderRect.bottomLeft()
        );
        
        // 绘制渐变边框
        painter->setBrush(QBrush(borderGradient));
        painter->setPen(Qt::NoPen);
        painter->drawEllipse(borderRect);
        
        painter->restore();
    }
    
    /**
     * @brief 绘制阴影效果
     * @param painter 画笔对象
     * @param rect 绘制区域
     * @param shadowOffset 阴影偏移
     * @param shadowColor 阴影颜色
     */
    inline void drawShadow(QPainter* painter, 
                          const QRectF& rect, 
                          const QPointF& shadowOffset = QPointF(2, 2),
                          const QColor& shadowColor = QColor(0, 0, 0, 32)) {
        if (!painter) return;
        
        painter->save();
        
        painter->setBrush(shadowColor);
        painter->setPen(Qt::NoPen);
        
        QRectF shadowRect = rect.adjusted(
            shadowOffset.x(), shadowOffset.y(), 
            shadowOffset.x(), shadowOffset.y()
        );
        
        painter->drawEllipse(shadowRect);
        
        painter->restore();
    }
    
    /**
     * @brief 计算旋转后的矩形尺寸
     * @param originalSize 原始尺寸
     * @param rotationAngle 旋转角度（度）
     * @return 旋转后的尺寸
     */
    inline QSize calculateRotatedSize(const QSize& originalSize, qreal rotationAngle) {
        if (qFuzzyIsNull(rotationAngle)) {
            return originalSize;
        }
        
        qreal radians = qDegreesToRadians(qAbs(rotationAngle));
        qreal cosAngle = qCos(radians);
        qreal sinAngle = qSin(radians);
        
        int newWidth = qCeil(originalSize.width() * cosAngle + originalSize.height() * sinAngle);
        int newHeight = qCeil(originalSize.width() * sinAngle + originalSize.height() * cosAngle);
        
        return QSize(newWidth, newHeight);
    }
    
    /**
     * @brief 根据弧形位置获取旋转角度
     * @param position 弧形位置枚举值
     * @return 旋转角度（度）
     */
    inline qreal getRotationAngleForPosition(int position) {
        switch (position) {
            case 1:  // ArcPosition::Right
                return 0.0;     // 右侧：0度（默认方向）
            case 0:  // ArcPosition::Top
                return -90.0;   // 上方：-90度（逆时针旋转90度）
            case 3:  // ArcPosition::Left
                return 180.0;   // 左侧：180度
            case 2:  // ArcPosition::Bottom
                return 90.0;    // 下方：90度（顺时针旋转90度）
            default:
                return 0.0;
        }
    }
    
    /**
     * @brief 检查点是否在圆形区域内
     * @param point 检查的点
     * @param center 圆心
     * @param radius 半径
     * @return 是否在圆形区域内
     */
    inline bool isPointInCircle(const QPointF& point, const QPointF& center, qreal radius) {
        QLineF line(point, center);
        return line.length() <= radius;
    }
    
    /**
     * @brief 限制值在指定范围内
     * @param value 要限制的值
     * @param min 最小值
     * @param max 最大值
     * @return 限制后的值
     */
    template<typename T>
    inline T clamp(const T& value, const T& min, const T& max) {
        return qMax(min, qMin(value, max));
    }
    
    /**
     * @brief 安全的模糊比较（用于qreal类型）
     * @param a 第一个值
     * @param b 第二个值
     * @return 是否近似相等
     */
    inline bool fuzzyCompare(qreal a, qreal b) {
        return qFuzzyCompare(a, b);
    }
    
    /**
     * @brief 安全的零值检查（用于qreal类型）
     * @param value 要检查的值
     * @return 是否近似为零
     */
    inline bool fuzzyIsNull(qreal value) {
        return qFuzzyIsNull(value);
    }

    /**
     * @brief 统一的弧形内容点击信号连接
     * @param arcContent 弧形内容组件
     * @param receiver 接收者对象
     * @param resetTimerFunc 重置定时器的函数
     * @param componentName 组件名称（用于日志）
     */
    template<typename ArcContentType, typename ReceiverType>
    inline void connectArcContentClicked(ArcContentType* arcContent,
                                        ReceiverType* receiver,
                                        std::function<void()> resetTimerFunc,
                                        const QString& componentName) {
        if (!arcContent || !receiver) return;

        QObject::connect(arcContent, &ArcContentType::contentClicked,
                        receiver, [resetTimerFunc, componentName](const QPointF& pos) {
                            resetTimerFunc(); // 重置定时器
                        });
    }

    /**
     * @brief 创建标准的显示/隐藏动画组
     * @param target 目标对象
     * @param showDuration 显示动画时长
     * @param hideDuration 隐藏动画时长
     * @return 包含显示和隐藏动画组的pair
     */
    template<typename TargetType>
    inline QPair<QParallelAnimationGroup*, QParallelAnimationGroup*>
    createStandardAnimations(TargetType* target,
                            int showDuration = FloatMenuConstants::ANIMATION_DURATION_NORMAL,
                            int hideDuration = FloatMenuConstants::ANIMATION_DURATION_NORMAL) {
        if (!target) return {nullptr, nullptr};

        // 创建显示动画组
        QParallelAnimationGroup* showAnimation = new QParallelAnimationGroup(target);

        QPropertyAnimation* showOpacityAnim = new QPropertyAnimation(target, "opacity");
        showOpacityAnim->setDuration(showDuration);
        showOpacityAnim->setStartValue(FloatMenuConstants::OPACITY_HIDDEN);
        showOpacityAnim->setEndValue(FloatMenuConstants::OPACITY_VISIBLE);
        showOpacityAnim->setEasingCurve(QEasingCurve::OutCubic);

        QPropertyAnimation* showScaleAnim = new QPropertyAnimation(target, "scale");
        showScaleAnim->setDuration(showDuration);
        showScaleAnim->setStartValue(FloatMenuConstants::SCALE_HIDDEN);
        showScaleAnim->setEndValue(FloatMenuConstants::SCALE_NORMAL);
        showScaleAnim->setEasingCurve(QEasingCurve::OutBack);

        showAnimation->addAnimation(showOpacityAnim);
        showAnimation->addAnimation(showScaleAnim);

        // 创建隐藏动画组
        QParallelAnimationGroup* hideAnimation = new QParallelAnimationGroup(target);

        QPropertyAnimation* hideOpacityAnim = new QPropertyAnimation(target, "opacity");
        hideOpacityAnim->setDuration(hideDuration);
        hideOpacityAnim->setStartValue(FloatMenuConstants::OPACITY_VISIBLE);
        hideOpacityAnim->setEndValue(FloatMenuConstants::OPACITY_HIDDEN);
        hideOpacityAnim->setEasingCurve(QEasingCurve::InCubic);

        QPropertyAnimation* hideScaleAnim = new QPropertyAnimation(target, "scale");
        hideScaleAnim->setDuration(hideDuration);
        hideScaleAnim->setStartValue(FloatMenuConstants::SCALE_NORMAL);
        hideScaleAnim->setEndValue(FloatMenuConstants::SCALE_HIDDEN);
        hideScaleAnim->setEasingCurve(QEasingCurve::InBack);

        hideAnimation->addAnimation(hideOpacityAnim);
        hideAnimation->addAnimation(hideScaleAnim);

        return {showAnimation, hideAnimation};
    }



    /**
     * @brief 验证对象指针的有效性
     * @param ptr 要验证的指针
     * @param componentName 组件名称
     * @param objectName 对象名称
     * @return 指针是否有效
     */
    template<typename T>
    inline bool validatePointer(T* ptr, const QString& componentName, const QString& objectName) {
        if (!ptr) {
            qWarning() << QString("%1: 无效的%2指针").arg(componentName, objectName);
            return false;
        }
        return true;
    }
}

#endif // FLOATMENUUTILS_H
