#include "iconfontmanager.h"
#include <QApplication>
#include <QDebug>
#include <QFileInfo>
#include <QRegularExpression>

// 静态成员初始化
IconFontManager* IconFontManager::s_instance = nullptr;

IconFontManager* IconFontManager::instance()
{
    if (!s_instance) {
        s_instance = new IconFontManager(qApp);
    }
    return s_instance;
}

IconFontManager::IconFontManager(QObject *parent)
    : QObject(parent)
    , m_cacheEnabled(true)
{
    qDebug() << "IconFontManager initialized";
}

IconFontManager::~IconFontManager()
{
    clearCache();
    qDebug() << "IconFontManager destroyed";
}

bool IconFontManager::loadFont(const QString& fontPath, const QString& fontFamily)
{
    QFileInfo fileInfo(fontPath);
    if (!fileInfo.exists()) {
        qWarning() << "Font file does not exist:" << fontPath;
        return false;
    }

    int fontId = m_fontDatabase.addApplicationFont(fontPath);
    if (fontId == -1) {
        qWarning() << "Failed to load font:" << fontPath;
        return false;
    }

    QStringList fontFamilies = m_fontDatabase.applicationFontFamilies(fontId);
    if (fontFamilies.isEmpty()) {
        qWarning() << "No font families found in:" << fontPath;
        return false;
    }

    QString actualFontFamily = fontFamily.isEmpty() ? fontFamilies.first() : fontFamily;

    // 验证字体族是否存在
    if (!fontFamilies.contains(actualFontFamily)) {
        qWarning() << "Font family" << actualFontFamily << "not found in font file. Available families:" << fontFamilies;
        actualFontFamily = fontFamilies.first();
    }

    if (!m_loadedFontFamilies.contains(actualFontFamily)) {
        m_loadedFontFamilies.append(actualFontFamily);
    }

    // 如果是第一个加载的字体，设为默认字体
    if (m_defaultFontFamily.isEmpty()) {
        m_defaultFontFamily = actualFontFamily;
    }

    qDebug() << "Font loaded successfully:" << actualFontFamily << "from" << fontPath;
    return true;
}

bool IconFontManager::loadDefaultIconFont()
{
    // 尝试加载资源中的字体文件，优先使用TTF格式
    QString fontPath = ":/fonts/iconfont/iconfont.ttf";
    bool success = loadFont(fontPath, "iconfont");

    if (success) {
        qDebug() << "Default iconfont loaded successfully from resources";
    } else {
        qWarning() << "Failed to load default iconfont from resources";
    }

    return success;
}

void IconFontManager::setDefaultFontFamily(const QString& fontFamily)
{
    if (m_loadedFontFamilies.contains(fontFamily)) {
        m_defaultFontFamily = fontFamily;
        qDebug() << "Default font family set to:" << fontFamily;
    } else {
        qWarning() << "Font family not loaded:" << fontFamily;
    }
}

QString IconFontManager::currentFontFamily() const
{
    return m_defaultFontFamily;
}

QIcon IconFontManager::createIcon(int unicode, const QSize& size, const QColor& color, const QString& fontFamily)
{
    QPixmap pixmap = createPixmap(unicode, size, color, fontFamily);
    return QIcon(pixmap);
}

QIcon IconFontManager::createIcon(const QString& unicodeStr, const QSize& size, const QColor& color, const QString& fontFamily)
{
    int unicode = unicodeStringToInt(unicodeStr);
    return createIcon(unicode, size, color, fontFamily);
}

QPixmap IconFontManager::createPixmap(int unicode, const QSize& size, const QColor& color, const QString& fontFamily)
{
    QString actualFontFamily = fontFamily.isEmpty() ? m_defaultFontFamily : fontFamily;

    if (actualFontFamily.isEmpty()) {
        qWarning() << "No font family available for creating icon";
        return QPixmap();
    }

    // 检查缓存
    if (m_cacheEnabled) {
        QString cacheKey = generateCacheKey(unicode, size, color, actualFontFamily);
        if (m_iconCache.contains(cacheKey)) {
            return m_iconCache.value(cacheKey);
        }
    }

    QPixmap pixmap = createPixmapInternal(unicode, size, color, actualFontFamily);

    // 添加到缓存（限制缓存大小，避免内存过度使用）
    if (m_cacheEnabled && !pixmap.isNull()) {
        // 限制缓存条目数量，适合Widget应用
        const int MAX_CACHE_SIZE = 200; // 对于普通Widget应用，200个图标缓存足够
        if (m_iconCache.size() >= MAX_CACHE_SIZE) {
            // 清除最旧的缓存项（简单的LRU策略）
            auto it = m_iconCache.begin();
            m_iconCache.erase(it);
        }

        QString cacheKey = generateCacheKey(unicode, size, color, actualFontFamily);
        m_iconCache.insert(cacheKey, pixmap);
    }

    return pixmap;
}

QFont IconFontManager::getFont(int fontSize, const QString& fontFamily)
{
    QString actualFontFamily = fontFamily.isEmpty() ? m_defaultFontFamily : fontFamily;

    if (actualFontFamily.isEmpty()) {
        qWarning() << "No font family available";
        return QFont();
    }

    QFont font(actualFontFamily);
    font.setPixelSize(fontSize);
    return font;
}

void IconFontManager::clearCache()
{
    m_iconCache.clear();
    qDebug() << "Icon cache cleared";
}

void IconFontManager::setCacheEnabled(bool enabled)
{
    m_cacheEnabled = enabled;
    if (!enabled) {
        clearCache();
    }
    qDebug() << "Cache" << (enabled ? "enabled" : "disabled");
}

QStringList IconFontManager::getLoadedFontFamilies() const
{
    return m_loadedFontFamilies;
}

bool IconFontManager::isFontLoaded(const QString& fontFamily) const
{
    return m_loadedFontFamilies.contains(fontFamily);
}

int IconFontManager::unicodeStringToInt(const QString& unicodeStr)
{
    QString cleanStr = unicodeStr;

    // 移除可能的前缀
    if (cleanStr.startsWith("\\u")) {
        cleanStr = cleanStr.mid(2);
    } else if (cleanStr.startsWith("0x")) {
        cleanStr = cleanStr.mid(2);
    } else if (cleanStr.startsWith("&#x")) {
        cleanStr = cleanStr.mid(3);
        if (cleanStr.endsWith(";")) {
            cleanStr.chop(1);
        }
    }

    bool ok;
    int unicode = cleanStr.toInt(&ok, 16);
    if (!ok) {
        qWarning() << "Failed to parse unicode string:" << unicodeStr;
        return 0;
    }

    return unicode;
}

QString IconFontManager::generateCacheKey(int unicode, const QSize& size, const QColor& color, const QString& fontFamily) const
{
    return QString("%1_%2x%3_%4_%5")
           .arg(unicode, 0, 16)
           .arg(size.width())
           .arg(size.height())
           .arg(color.name())
           .arg(fontFamily);
}

QPixmap IconFontManager::createPixmapInternal(int unicode, const QSize& size, const QColor& color, const QString& fontFamily)
{
    if (unicode <= 0) {
        qWarning() << "Invalid unicode value:" << unicode;
        return QPixmap();
    }

    QPixmap pixmap(size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::TextAntialiasing, true);

    QFont font = getFont(qMin(size.width(), size.height()), fontFamily);
    painter.setFont(font);
    painter.setPen(color);

    QString iconText = QString(QChar(unicode));
    QRect textRect = painter.fontMetrics().boundingRect(iconText);

    // 计算居中位置
    int x = (size.width() - textRect.width()) / 2;
    int y = (size.height() - textRect.height()) / 2 + painter.fontMetrics().ascent();

    painter.drawText(x, y, iconText);
    painter.end();

    return pixmap;
}
