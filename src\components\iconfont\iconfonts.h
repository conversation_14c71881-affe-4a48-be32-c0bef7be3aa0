#ifndef ICONFONTS_H
#define ICONFONTS_H

#include <QIcon>
#include <QSize>
#include <QColor>
#include <QString>

/**
 * @brief IconFont图标定义和便捷工具类
 *
 * 提供常用图标的Unicode定义和快捷创建方法
 * 可以根据实际使用的iconfont项目进行定制
 */
namespace IconFonts {

    // ==================== 智慧黑板图标Unicode定义 ====================
    // 基于实际iconfont项目 (ID: 4853523) 的Unicode映射

    namespace Icons {
        // 基础操作图标
        constexpr int SAVE          = 0xe66d;   // 保存 (baocun)
        constexpr int SAVE_ALT      = 0xe63e;   // 保存 (save)
        constexpr int EXIT          = 0xe6d7;   // 退出 (tuichu)
        constexpr int REFRESH       = 0xe604;   // 刷新 (refresh)
        constexpr int SETTING       = 0xe628;   // 设置 (setting)
        constexpr int SHUTDOWN      = 0xe62a;   // 关机 (shutdown)
        constexpr int SHUTDOWN_ALT  = 0xe641;   // 关机 (guanji)
        constexpr int CLOSE         = 0xe63c;   // 关闭 (close)
        constexpr int WORKSPACE_SETTING = 0xe6da; // 工作台设置 (gongzuotaishezhi)
        constexpr int WORKSPACE_SETTING_UNSELECTED = 0xe6db; // 工作台设置未选中 (gongzuotaishezhiweixuanzhong)
        constexpr int LAYOUT_SETTING = 0xe6dc;  // 布局设置 (bujushezhi)
        constexpr int ADD_TIMER     = 0xe6dd;   // 添加倒计时 (tianjiadaojishi)

        // 编辑操作图标
        constexpr int UNDO          = 0xe647;   // 撤销 (revoke)
        constexpr int DELETE        = 0xe655;   // 删除 (delete)
        constexpr int DELETE_PLAIN  = 0xe661;   // 删除无底 (delete-plain)
        constexpr int DELETE_DIALOG = 0xe660;   // 删除对话框 (dialog-delete)
        constexpr int CLEAR         = 0xe646;   // 清空 (clear)
        constexpr int CLEAR_ALT     = 0xe666;   // 清除 (qingchu)
        constexpr int SELECT        = 0xe637;   // 勾选 (select)
        constexpr int PLUS          = 0xe652;   // 加号 (plus)
        constexpr int PLUS_ALT      = 0xe63f;   // 加号 (jia)
        constexpr int MINUS         = 0xe657;   // 减号 (minus)
        constexpr int SAVE_BOARD    = 0xe66d;   // 保存板书 (minus)

        // 绘图工具图标
        constexpr int SELECT_TOOL   = 0xe648;   // 选择工具 (xuanze)
        constexpr int SELECT_TOOL2  = 0xe662;   // 选择工具2 (xuanze1)
        constexpr int PEN_TOOL      = 0xe645;   // 画笔 (pencil)
        constexpr int PEN_NORMAL    = 0xe665;   // 画笔-初始 (huabi-chushi)
        constexpr int PEN_MARKER    = 0xe663;   // 画笔-马克笔 (huabi-makebi)
        constexpr int PEN_WATERCOLOR= 0xe669;   // 画笔-水彩笔 (huabi-shuicaibi)
        constexpr int PEN_DASHED    = 0xe664;   // 画笔-虚线 (huabi-xuxian)
        constexpr int LINE_TOOL     = 0xe649;   // 直线 (line)
        constexpr int DASHED_LINE   = 0xe64f;   // 虚线 (dashed)
        constexpr int ERASER        = 0xe644;   // 橡皮擦 (eraser)
        constexpr int ERASER_ALT    = 0xe667;   // 橡皮擦2 (xiangpi)

        // 笔刷相关图标 (新增全系列笔刷图标)
        constexpr int WATERCOLOR_BRUSH = 0xe66e;  // 水彩笔 (shuicaibi)
        constexpr int MARKER_BRUSH     = 0xe66f;  // 马克笔 (makebi)
        constexpr int DASHED_BRUSH     = 0xe670;  // 虚线 (xuxian)
        constexpr int MARKER_TIP       = 0xe668;  // 马克笔尖 (makebijian)
        constexpr int WATERCOLOR_TIP   = 0xe66a;  // 水彩笔尖 (shuicaibijian)
        constexpr int DASHED_COLOR     = 0xe66b;  // 虚线颜色 (xuxianyanse)
        
        // 新增笔刷全系列图标
        constexpr int MARKER_BRUSH_FULL = 0xe671; // 马克笔全 (makebiquan)
        constexpr int WATERCOLOR_BRUSH_FULL = 0xe672; // 水彩笔全 (shuicaibiquan)
        constexpr int DASHED_PEN        = 0xe673; // 虚线笔 (xuxianbi)

        // 几何图形图标
        constexpr int CIRCLE        = 0xe64e;   // 圆形 (circle)
        constexpr int ELLIPSE       = 0xe650;   // 椭圆 (ellipse)
        constexpr int RECTANGLE     = 0xe64c;   // 长方形 (rect)
        constexpr int SQUARE        = 0xe64b;   // 正方形 (square)
        constexpr int TRIANGLE      = 0xe64d;   // 三角形 (triangle)
        constexpr int RIGHT_TRIANGLE= 0xe651;   // 直角三角形 (right-triangle)
        constexpr int ARROW         = 0xe64a;   // 箭头 (arrow)
        constexpr int ARROW_ALT     = 0xe674;   // 箭头 (jiantou)
        constexpr int GRAPHIC       = 0xe643;   // 图形 (graphic)

        // 界面控制图标
        constexpr int ARROW_LEFT    = 0xe63a;   // 左箭头 (left)
        constexpr int ARROW_RIGHT   = 0xe63b;   // 右箭头 (right)
        constexpr int ARROW_DOWN    = 0xe634;   // 下箭头 (arrow-down)
        constexpr int ARROW_RIGHT2  = 0xe633;   // 右箭头2 (arrow-right)
        constexpr int FULLSCREEN    = 0xe656;   // 全屏 (fullscreen)
        constexpr int MINIMIZE      = 0xe62d;   // 最小化 (min-size)
        constexpr int MINIMIZE2     = 0xe640;   // 最小化2 (min-size2)
        constexpr int ROTATE        = 0xe658;   // 旋转 (xuanzhuan)
        constexpr int COLLAPSE      = 0xe642;   // 收起 (shouqi)

        // 工具和功能图标
        constexpr int TOOLBOX       = 0xe659;   // 工具箱 (gongjuxiang)
        constexpr int TOOLBOX_SELECT= 0xe65e;   // 工具箱选中 (gongjuxiang-select)
        constexpr int RESOURCE      = 0xe65b;   // 资源库 (ziyuanku)
        constexpr int RESOURCE_SELECT=0xe65d;   // 资源库选中 (ziyuanku-select)
        constexpr int MAGNIFIER     = 0xe65a;   // 放大镜 (magnifier)
        constexpr int TIMER         = 0xe65f;   // 计时器 (timer)
        constexpr int ROLL_NAME     = 0xe65c;   // 随机点名 (roll-name)
        constexpr int TEACHER       = 0xe63d;   // 教师 (teacher)
        constexpr int PHONE         = 0xe62f;   // 手机 (phone)
        constexpr int LOCK          = 0xe631;   // 锁 (lock)

        // 教学相关图标
        constexpr int COURSEWARE    = 0xe638;   // 课件 (kejian)
        constexpr int MATERIAL      = 0xe639;   // 素材 (sucai)
        constexpr int THUMBNAIL     = 0xe66c;   // 缩略 (suolve)

        // 灯光控制图标
        constexpr int LIGHT_ON      = 0xe654;   // 开灯 (kaideng)
        constexpr int LIGHT_OFF     = 0xe653;   // 关灯 (guandeng)

        // 网络和应用图标
        constexpr int NETWORK       = 0xe629;   // 网络 (net)
        constexpr int NO_NETWORK    = 0xe62c;   // 无网络 (no-net)
        constexpr int MORE_APP      = 0xe62b;   // 更多应用 (more-app)
        constexpr int WECHAT        = 0xe630;   // 微信 (wechat)
        constexpr int LARK          = 0xe62e;   // 飞书 (lark)
        constexpr int DINGTALK      = 0x64;     // 钉钉 (dingtalk)

        // 上传功能图标
        constexpr int UPLOAD_VIDEO  = 0xe6d9;   // 黑板上传视频 (heibanshangchuanshipin)
        constexpr int UPLOAD_IMAGE  = 0xe6d8;   // 手机上传图片 (shoujishangchuantupian)

        // 其他功能图标
        constexpr int SWITCH        = 0xe635;   // 切换 (switch)
        constexpr int FISH_SHAPE    = 0xe636;   // 形状结合 (fish)
        constexpr int UNDERSCORE    = 0xe632;   // 下划线 (underscore)
        constexpr int EXCLAMATION   = 0xe605;   // 感叹号 (gantanhao)
    }

    // ==================== 默认样式配置 ====================

    namespace Defaults {
        const QSize SMALL_SIZE(16, 16);         // 小图标尺寸
        const QSize MEDIUM_SIZE(24, 24);        // 中等图标尺寸
        const QSize LARGE_SIZE(32, 32);         // 大图标尺寸
        const QSize TOOLBAR_SIZE(20, 20);       // 工具栏图标尺寸

        const QColor DEFAULT_COLOR(Qt::black);  // 默认颜色
        const QColor ACTIVE_COLOR(0x409EFF);    // 激活状态颜色
        const QColor DISABLED_COLOR(0xC0C4CC);  // 禁用状态颜色
        const QColor HOVER_COLOR(0x66B1FF);     // 悬停状态颜色
    }

    // ==================== 便捷创建方法 ====================

    /**
     * @brief 创建图标（使用默认样式）
     * @param unicode Unicode码点
     * @param size 图标大小（默认为工具栏大小）
     * @param color 图标颜色（默认为黑色）
     * @return QIcon对象
     */
    QIcon createIcon(int unicode,
                     const QSize& size = Defaults::TOOLBAR_SIZE,
                     const QColor& color = Defaults::DEFAULT_COLOR);

    /**
     * @brief 创建工具栏图标
     * @param unicode Unicode码点
     * @param color 图标颜色
     * @return QIcon对象
     */
    QIcon createToolbarIcon(int unicode, const QColor& color = Defaults::DEFAULT_COLOR);

    /**
     * @brief 创建小图标
     * @param unicode Unicode码点
     * @param color 图标颜色
     * @return QIcon对象
     */
    QIcon createSmallIcon(int unicode, const QColor& color = Defaults::DEFAULT_COLOR);

    /**
     * @brief 创建大图标
     * @param unicode Unicode码点
     * @param color 图标颜色
     * @return QIcon对象
     */
    QIcon createLargeIcon(int unicode, const QColor& color = Defaults::DEFAULT_COLOR);

    /**
     * @brief 创建多状态图标（正常、悬停、激活、禁用）
     * @param unicode Unicode码点
     * @param size 图标大小
     * @return QIcon对象
     */
    QIcon createMultiStateIcon(int unicode, const QSize& size = Defaults::TOOLBAR_SIZE);

    /**
     * @brief 初始化IconFont系统
     * @param fontPath 字体文件路径
     * @param fontFamily 字体族名称（可选）
     * @return 是否初始化成功
     */
    bool initialize(const QString& fontPath, const QString& fontFamily = QString());

    /**
     * @brief 检查IconFont系统是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized();

    // ==================== 预定义图标创建方法 ====================

    // 基础操作图标
    QIcon saveIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon exitIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon refreshIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon settingIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon shutdownIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon closeIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon workspaceSettingIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon layoutSettingIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon addTimerIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 编辑操作图标
    QIcon undoIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon deleteIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon deletePlainIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon deleteDialogIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon clearIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon selectIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon plusIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon minusIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 绘图工具图标
    QIcon selectToolIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon penToolIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon penNormalIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon penMarkerIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon penWatercolorIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon penDashedIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon lineToolIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon dashedLineIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon eraserIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 笔刷相关图标
    QIcon watercolorBrushIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon markerBrushIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon dashedBrushIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon markerTipIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon watercolorTipIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon dashedColorIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon markerBrushFullIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon watercolorBrushFullIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon dashedPenIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 几何图形图标
    QIcon circleIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon ellipseIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon rectangleIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon squareIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon triangleIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon rightTriangleIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon arrowIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon graphicIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 界面控制图标
    QIcon arrowLeftIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon arrowRightIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon arrowDownIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon fullscreenIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon minimizeIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon rotateIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 工具和功能图标
    QIcon toolboxIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon toolboxSelectIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon resourceIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon resourceSelectIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon magnifierIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon timerIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon rollNameIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon teacherIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon phoneIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon lockIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 教学相关图标
    QIcon coursewareIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon materialIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon thumbnailIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 灯光控制图标
    QIcon lightOnIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon lightOffIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 网络和应用图标
    QIcon networkIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon noNetworkIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon moreAppIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon wechatIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon larkIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon dingtalkIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

    // 上传功能图标
    QIcon uploadVideoIcon(const QSize& size = Defaults::TOOLBAR_SIZE);
    QIcon uploadImageIcon(const QSize& size = Defaults::TOOLBAR_SIZE);

} // namespace IconFonts

#endif // ICONFONTS_H
