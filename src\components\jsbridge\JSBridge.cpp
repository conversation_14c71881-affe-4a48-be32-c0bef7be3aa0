#include "JSBridge.h"
#include <iostream>
#include <QCoreApplication>
#include <QString>
#include <QTimer>
#include <QElapsedTimer>
#include <QTimer>
#include <QElapsedTimer>

// Serialize to string
std::string JSBridgeMsg::serialize() const {
    json j = json{
                    {"id", this->id},
                    {"method", this->method},
                    {"data", this->data},
                    {"status", this->status}
    };
    return j.dump();
}

// Deserialize from string
JSBridgeMsg JSBridgeMsg::deserialize(const std::string& str) {
    if (str.empty()) {
        throw std::runtime_error("JSBridgeMsg string is empty.");
    }
    try {
        json j = json::parse(str);
        if (!j.is_object()) {
            throw std::runtime_error("JSBridgeMsg is not an object. msg: " + str);
        }

        JSBridgeMsg msg;

        if (!j.contains("id") || j.at("id").is_null()) {
            throw std::runtime_error("JSBridgeMsg id is required. msg: " + str);
        }
        j.at("id").get_to(msg.id);

        if (!j.contains("method") || j.at("method").is_null()) {
            throw std::runtime_error("JSBridgeMsg method is required. msg: " + str);
        }
        j.at("method").get_to(msg.method);

        if (j.contains("data") && !j.at("data").is_null()) {
            j.at("data").get_to(msg.data);
        }

        if (j.contains("status") && !j.at("status").is_null()) {
            j.at("status").get_to(msg.status);
        }
        return msg;
    }
    catch (const std::exception& e) {
        qWarning() << "Error deserializing JSBridgeMsg: " << QString::fromStdString(str) << ", what: " << e.what();
        throw std::runtime_error("Error deserializing JSBridgeMsg: " + str + ", what: " + e.what());
    }
    catch (...) {
        qWarning() << "Unknown error deserializing JSBridgeMsg: " << QString::fromStdString(str);
        throw std::runtime_error("Unknown error deserializing JSBridgeMsg: " + str);
    }
}

void JSBridgeContext::setResult(const JSBridgeMsg &result) const {
    qInfo() << "JSBridge setResult: " << QString::fromStdString(result.serialize());

    if (!hasResult) {
        qInfo() << "JSBridge setResult: hasResult is false";
        return;
    }

    if (cefView == nullptr) {
        qInfo() << "JSBridge setResult: _cefView is nullptr";
        return;
    }

    try {
        cefQuery.setResponseResult(true, QString::fromStdString(result.serialize()));
        cefView->responseQCefQuery(cefQuery);
    } catch (const std::exception &e) {
        qWarning() << "Error setResult: " << e.what();
        cefQuery.setResponseResult(false, QString::fromStdString(JSBridgeMsg::newErrorResponse(
                                       data, e.what()).serialize()));
        cefView->responseQCefQuery(cefQuery);
    }
    catch (...) {
        qWarning() << "Unknown error setResult";
        cefQuery.setResponseResult(false, QString::fromStdString(JSBridgeMsg::newErrorResponse(
                                       data,  "Unknown Error setResult").serialize()));
    }
}

void JSBridge::init(QCefView *cefView, const QString &cefName) {
    // 参数检查
    if (!cefView) {
        qWarning() << "_cefView is null!";
        return;
    }

    // 直接从QCefView中获取browserId返回的值是-1，表示还没有初始化完成
    // 因此放到页面加载时进行初始化
    auto loadCon = QObject::connect(cefView, &QCefView::loadStart,
                                    [cefView, cefName](const int &browserId, const QCefFrameId &frameId, bool isMainFrame,
                                                       int transitionType) {
                                        // 加锁，防止connect多次
                                        std::lock_guard lock(cef_init_mtx_);

                                        if (browserIdCefViewMap.contains(cefView->browserId())) {
                                            qInfo() << "loadStart browserId: " << cefView->browserId() << " 已经存在，无需重复初始化!";
                                            return;
                                        }
                                        auto connection = QObject::connect(cefView,
                                                                           &QCefView::invokeMethod,
                                                                           &JSBridge::onInvokeMethod);
                                        if (connection) {
                                            qInfo() << "invokeMethod browserId: " << cefView->browserId() << " 连接成功!";
                                        } else {
                                            qInfo() << "invokeMethod browserId: " << cefView->browserId() << " 连接失败!";
                                        }

                                        connection = connect(cefView,
                                                             &QCefView::cefQueryRequest,
                                                             &JSBridge::onQCefQueryRequest);
                                        if (connection) {
                                            qInfo() << "cefQueryRequest browserId: " << cefView->browserId() << " 连接成功!";
                                        } else {
                                            qInfo() << "cefQueryRequest browserId: " << cefView->browserId() << " 连接失败!";
                                        }

                                        connection = QObject::connect(cefView,
                                                             &QCefView::destroyed,
                                                             &JSBridge::onCefViewDestroyed);
                                        if (connection) {
                                            qInfo() << "destroyed browserId: " << cefView->browserId() << " 连接成功!";
                                        } else {
                                            qInfo() << "destroyed browserId: " << cefView->browserId() << " 连接失败!";
                                        }
                                        browserIdCefViewMap[cefView->browserId()] = cefView;
                                        if (!cefName.isEmpty()) {
                                            nameCefViewMap[cefName] = cefView;
                                        } else {
                                            qInfo() << "cefName is empty!";
                                        }
                                    }
    );
    if (loadCon) {
        qInfo() << "QCefView loadStart 连接成功!";
    } else {
        qCritical() << "QCefView loadStart 连接失败!";
    }
}

void JSBridge::onInvokeMethod(const QCefBrowserId &browserId,
                              const QCefFrameId &frameId,
                              const QString &method,
                              const QVariantList &arguments) {
    try {
        qInfo() << "browserId: " << browserId << " onInvokeMethodNotify, method: " << method << " , arguments" << arguments;

        if (arguments.length() != 1) {
            qWarning() << "参数错误, 只能有一个参数";
            return;
        }

        QPointer<QCefView> cefView = nullptr;
        {
            std::lock_guard lock(cef_init_mtx_);
            cefView = browserIdCefViewMap[browserId];
        }
        if (!cefView) {
            qWarning() << "未找到对应的 QCefView 实例，browserId: " << browserId;
            return;
        }

        auto data = JSBridgeMsg::deserialize(arguments[0].toString().toStdString());
        if (!data.isValid()) {
            qWarning() << "data is not valid, data: " << arguments[0].toString();
            return;
        }
        JSBridgeContext bridgeContext(browserId, frameId, cefView, data);

        qDebug() << "onInvokeMethod isMainThread: " << isMainThread();
        if (method == JS_CALLBACK_METHOD) {
            std::string uuid = data.getId();

            nlohmann::basic_json<> result = data.getData();

            qDebug() << "uuid: " << uuid << " result: " << QString::fromStdString(result.dump());
            
            std::shared_ptr<JSBridgeTimeoutCallbackData> callbackData = nullptr;
            {
                std::lock_guard lock(pending_js_callbacks_mutex_);
                if (pending_js_callbacks_.find(uuid) != pending_js_callbacks_.end()) {
                    callbackData = pending_js_callbacks_[uuid];
                    pending_js_callbacks_.remove(uuid);
                }
                else {
                    qWarning() << "onInvokeMethod callback uuid: " << uuid << " not found";
                }
            }
            
            if (callbackData && callbackData->callback) {
                // 停止超时定时器
                if (callbackData->timeoutTimer) {
                    callbackData->timeoutTimer->stop();
                    callbackData->timeoutTimer->deleteLater();
                    callbackData->timeoutTimer = nullptr;
                }
                
                callbackData->callback(bridgeContext);
            }
            else {
                qInfo() << "callback is null!";
            }
            return;
        }
        {
            std::lock_guard lock(handlerMutex_);
            if (resultHandlerMap.contains(method)) {
                // 在ui线程中执行
                if (isMainThread()) {
                    resultHandlerMap[method](bridgeContext);
                }
                else {
                    QMetaObject::invokeMethod(
                        cefView,
                        [=]() {
                            try {
                                resultHandlerMap[method](bridgeContext);
                            }
                            catch (const std::exception& e) {
                                qWarning() << "onInvokeMethod 异常：" << e.what();
                            }
                            catch (...) {
                                qWarning() << "onInvokeMethod 未知异常";
                            }
                        },
                        Qt::QueuedConnection);
                }
            } else if (asyncResultHandlerMap.contains(method)) {
                getThreadPool()->start([method, bridgeContext]() {
                    try {
                        asyncResultHandlerMap[method](bridgeContext);
                    }
                    catch (const std::exception& e) {
                        qWarning() << "onInvokeMethod 异常：" << e.what();
                    }
                    catch (...) {
                        qWarning() << "onInvokeMethod 未知异常";
                    }
                });
            } else {
                qWarning() << "invokeMethod 不存在的方法：" << method;
            }
        }
    }
    catch (const std::exception& e) {
        qWarning() << "onInvokeMethod 异常：" << e.what();
    }
    catch (...) {
        qWarning() << "onInvokeMethod 未知异常";
    }
}

void JSBridge::onQCefQueryRequest(const QCefBrowserId &browserId, const QCefFrameId &frameId, const QCefQuery &query) {
    try {
        qInfo() << "browserId: " << browserId << " onQCefQueryRequest called";

        QString request = query.request();

        auto msg = JSBridgeMsg::deserialize(request.toStdString());
        if (!msg.isValid()) {
            qWarning() << "onQCefQueryRequest data is not valid, data: " << request;
            return;
        }

        auto method = msg.getMethod();
        if (method.empty()) {
            qWarning() << "onQCefQueryRequest Invalid Method!, request: " << request;
            return;
        }

        QCefView *cefView = nullptr;
        {
            std::lock_guard lock(cef_init_mtx_);
            cefView = browserIdCefViewMap[browserId];
        }
        if (!cefView) {
            qWarning() << "未找到对应的 QCefView 实例，browserId: " << browserId;
            return;
        }

        auto qMethod = QString::fromStdString(method);
        JSBridgeContext bridgeContext(browserId, frameId, cefView, query, msg);

        {
            std::lock_guard lock(handlerMutex_);
            if (resultHandlerMap.contains(qMethod)) {
                if (isMainThread()) {
                    resultHandlerMap[qMethod](bridgeContext);
                }
                else {
                    QMetaObject::invokeMethod(
                        cefView,
                        [=]() {
                            resultHandlerMap[qMethod](bridgeContext);
                        },
                        Qt::QueuedConnection);
                }
            } else if (asyncResultHandlerMap.contains(qMethod)) {
                getThreadPool()->start([qMethod, bridgeContext]() {
                    try {
                        asyncResultHandlerMap[qMethod](bridgeContext);
                    }
                    catch (const std::exception& e) {
                        qWarning() << "onQCefQueryRequest 异常：" << e.what();
                    }
                    catch (...) {
                        qWarning() << "onQCefQueryRequest 未知异常";
                    }
                });
            } else {
                qDebug() << "cefQueryRequest 不存在方法: " << qMethod;
            }
        }
    }
    catch (const std::exception& e) {
        qWarning() << "onQCefQueryRequest 异常：" << e.what();
    }
    catch (...) {
        qWarning() << "onQCefQueryRequest 未知异常";
    }
}

// 带超时的callJs方法
void JSBridge::callJs(const QString& cefName, const JSBridgeMsg &data, ResultHandlerFunc callback, 
                       int timeoutMs, TimeoutHandlerFunc timeoutCallback) {
    callJsInternal(cefName, data, callback, timeoutMs, timeoutCallback);
}

// 内部callJs实现
void JSBridge::callJsInternal(const QString& cefName, const JSBridgeMsg &data, ResultHandlerFunc callback,
                              int timeoutMs, TimeoutHandlerFunc timeoutCallback) {
    QString requestId;
    if (data.getId().empty()) {
        requestId = QString::fromStdString(JSBridgeMsg::generate_jsbridge_id());
    }
    else {
        requestId = QString::fromStdString(data.getId());
    }
    if (cefName.isEmpty()) {
        qWarning() << "callJs cefName is empty!";
        return;
    }
    if (data.getMethod().empty()) {
        qWarning() << "callJs method is empty!";
        return;
    }

    QCefView *cefView = nullptr;
    {
        std::lock_guard lock(cef_init_mtx_);
        cefView = nameCefViewMap[cefName];
    }
    if (!cefView) {
        qWarning() << "callJs 未找到对应的 QCefView 实例，cefName: " << cefName;
        return;
    }

    std::string uuid;
    if (requestId.isEmpty()) {
        uuid = JSBridgeMsg::generate_jsbridge_id();
    } else {
        uuid = requestId.toStdString();
    }
    
    if (callback) {
        std::lock_guard lock(pending_js_callbacks_mutex_);
        
        // 创建超时回调数据结构
        auto callbackData = std::make_shared<JSBridgeTimeoutCallbackData>(data);
        callbackData->request = data;
        callbackData->callback = callback;
        callbackData->timeoutCallback = timeoutCallback;
        callbackData->timeoutMs = timeoutMs;
        callbackData->elapsedTimer.start();
        
        // 如果设置了超时时间，创建定时器
        if (timeoutMs > 0) {
            callbackData->timeoutTimer = new QTimer();
            callbackData->timeoutTimer->setSingleShot(true);
            callbackData->timeoutTimer->setInterval(timeoutMs);
            
            // 连接超时信号
            QObject::connect(callbackData->timeoutTimer, &QTimer::timeout, [uuid]() {
                onTimeout(uuid);
            });
            
            callbackData->timeoutTimer->start();
        }
        
        pending_js_callbacks_[uuid] = callbackData;
    }

    QCefEvent event(QString::fromStdString(data.getMethod()));

    qDebug() << "callJs cefName: " << cefName << ", method: " << data.getMethod() <<  ", argument[0]: " <<data.serialize().c_str();
    event.arguments().append(QString(data.serialize().c_str()));

    qDebug() << "callJS isMainThread: " << isMainThread();
    // 主线程执行
    if (isMainThread()) {
        cefView->triggerEvent(event);
    }
    else {
        QMetaObject::invokeMethod(
            cefView,
            [=]() {
                try {
                    cefView->triggerEvent(event);
                }
                catch (const std::exception& e) {
                    qWarning() << "callJs 异常：" << e.what();
                }
                catch (...) {
                    qWarning() << "callJs 未知异常";
                }
            },
            Qt::QueuedConnection);
    }
}

// 超时处理槽函数
void JSBridge::onTimeout(const std::string& requestId) {
    qWarning() << "JSBridge callJs timeout for requestId: " << QString::fromStdString(requestId);
    
    std::shared_ptr<JSBridgeTimeoutCallbackData> callbackData = nullptr;
    {
        std::lock_guard lock(pending_js_callbacks_mutex_);
        if (pending_js_callbacks_.find(requestId) != pending_js_callbacks_.end()) {
            callbackData = pending_js_callbacks_[requestId];
            pending_js_callbacks_.remove(requestId);
        }
    }
    
    if (callbackData) {
        // 调用超时回调
        if (callbackData->timeoutCallback) {
            callbackData->timeoutCallback(callbackData->request);
        }
        
        // 清理定时器
        if (callbackData->timeoutTimer) {
            callbackData->timeoutTimer->stop();
            callbackData->timeoutTimer->deleteLater();
            callbackData->timeoutTimer = nullptr;
        }
    }
}

bool JSBridge::isMainThread() {
    return QThread::currentThread() == QCoreApplication::instance()->thread();
}

// 获取线程池实例
QThreadPool* JSBridge::getThreadPool() {
    static QThreadPool threadPool;
    return &threadPool;
}

void JSBridge::onCefViewDestroyed(QObject *cefView) {
    // 当cefview销毁时，从map中移除cefview
    qDebug() << "JSBridge onCefViewDestroyed" << cefView;
    std::lock_guard lock(cef_init_mtx_);
    for (auto it = nameCefViewMap.begin(); it != nameCefViewMap.end(); ++it) {
        if (it.value() == cefView) {
            nameCefViewMap.erase(it);
            break;
        }
    }
    for (auto it = browserIdCefViewMap.begin(); it != browserIdCefViewMap.end(); ++it) {
        if (it.value() == cefView) {
            browserIdCefViewMap.erase(it);
            break;
        }
    }
}
