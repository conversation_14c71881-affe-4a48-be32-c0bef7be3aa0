﻿#ifndef JSBRIDGE_H
#define JSBRIDGE_H

#include "QCefView.h"
#include <qobject.h>
#include <QtConcurrent/QtConcurrentRun>
#include <iostream>
#include <qpointer.h>
#include <random>
#include <sstream>
#include <QTimer>
#include <QElapsedTimer>

#include "nlohmann/json.hpp"
#include "src/utils/Uuid.h"

class TestJSBridge;

// Message structure
class JSBridgeMsg {
    using json = nlohmann::json;

public:
    // 构造函数
    JSBridgeMsg(const std::string& id, const std::string& method, const json& data, const std::string& status)
            : id(id), method(method), data(data), status(status) {}

    // 静态方法，用于创建请求
    static JSBridgeMsg newRequest(const std::string& method, const json& data) {
        return JSBridgeMsg(generate_jsbridge_id(), method, data, "");
    }
    // 静态方法，用于创建请求，可以指定 id
    static JSBridgeMsg newRequest(const std::string& id, const std::string& method, const json& data) {
        return JSBridgeMsg(id, method, data, "");
    }
    // 静态方法，用于创建响应
    static JSBridgeMsg newResponse(const JSBridgeMsg& request, const json& data, const std::string& status) {
        return JSBridgeMsg(request.getId(), "__response__", data, status);
    }
    static JSBridgeMsg newResponse(const JSBridgeMsg& request, const json& data) {
        return JSBridgeMsg(request.getId(), "__response__", data, "200");
    }
    static JSBridgeMsg newErrorResponse(const JSBridgeMsg& request, const std::string& errMsg) {
        return JSBridgeMsg(request.getId(), "__response__", json{{"message", errMsg}}, "500");
    }

    bool isValid() {
        return !id.empty() && !method.empty();
    }

    bool success() const {
        return status == "200";
    }

    std::string getId() const {
        return id;
    }
    std::string getMethod() const {
        return method;
    }
    json getData() const {
        return data;
    }
    std::string getStatus() const {
        return status;
    }

    // Serialize to string
    std::string serialize() const;

    // Deserialize from string
    static JSBridgeMsg deserialize(const std::string& str);

    // 支持 << 运算符
    friend std::ostream& operator<<(std::ostream& os, const JSBridgeMsg& msg) {
        os << "JSBridgeMsg{id=" << msg.id << ", method=" << msg.method << ", data=" << msg.data.dump() << ", status=" << msg.status << "}";
        return os;
    }

    // 生成 id 的函数
    static std::string generate_jsbridge_id() {
        return UuidUtil::generate_v4();
    }
private:
    JSBridgeMsg() = default;

    std::string id;
    std::string method;
    json data;
    std::string status;

};

// JSBridgeContext 类用于传递 JSBridge 的上下文信息
class JSBridgeContext {
public:
    JSBridgeContext(const QCefBrowserId &bid,
                    const QCefFrameId &fid,
                    QCefView *view,
                    const JSBridgeMsg &d)
        : browserId(bid),
          frameId(fid),
          cefView(view), hasResult(false),
          data(d) {
        // do nothing
    }

    JSBridgeContext(const QCefBrowserId &bid,
                    const QCefFrameId &fid,
                    QCefView *view,
                    const QCefQuery query,
                    const JSBridgeMsg &d)
    : browserId(bid),
      frameId(fid),
      cefView(view),
      cefQuery(query), hasResult(true),
      data(d) {
        // do nothing
    }

    // getter 方法
    QCefBrowserId getBrowserId() const {
        return browserId;
    }
    QCefFrameId getFrameId() const {
        return frameId;
    }
    QPointer<QCefView> getCefView() const {
        return cefView;
    }
    QCefQuery getCefQuery() const {
        return cefQuery;
    }

    JSBridgeMsg getData() const {
        return data;
    }

    // 设置返回结果
    void setResult(const JSBridgeMsg &result) const;
private:
    JSBridgeContext() = delete;

    const QCefBrowserId browserId;
    const QCefFrameId frameId;
    QPointer<QCefView> cefView;
    const QCefQuery cefQuery;
    const bool hasResult;

    const JSBridgeMsg data;
};

// 超时回调数据结构
struct JSBridgeTimeoutCallbackData {
    // 请求数据
    JSBridgeMsg request;
    // 记录消耗时间
    QElapsedTimer elapsedTimer;
    // 超时时间（毫秒）
    int timeoutMs;
    // 超时定时器
    QTimer* timeoutTimer;
    // 回调函数
    std::function<void(const JSBridgeContext&)> callback;
    // 超时回调函数
    std::function<void(const JSBridgeMsg&)> timeoutCallback;

    JSBridgeTimeoutCallbackData(const JSBridgeMsg& request) :
        request(request),
        timeoutMs(0), timeoutTimer(nullptr) {}
    
    ~JSBridgeTimeoutCallbackData() {
        if (timeoutTimer) {
            timeoutTimer->stop();
            timeoutTimer->deleteLater();
            timeoutTimer = nullptr;
        }
    }
};

class JSBridge : public QObject {
    Q_OBJECT

public:

    // qt调用js方法后，js回调qt的方法名
    inline static const QString JS_CALLBACK_METHOD = "__js_callback__";

    using ResultHandlerFunc = std::function<void(const JSBridgeContext &)>;
    using TimeoutHandlerFunc = std::function<void(const JSBridgeMsg &)>;

    // 注册同步方法（handler在ui线程执行）
    static void registerHandler(const QString &name, const ResultHandlerFunc &func) {
        std::lock_guard<std::mutex> lock(handlerMutex_);
        resultHandlerMap[name] = func;
        qDebug() << "JSBridge Registered handler:" << name;
    }

    // 注册异步方法（handler在非ui线程执行）
    static void registerAsyncHandler(const QString &name, const ResultHandlerFunc &func) {
        std::lock_guard<std::mutex> lock(handlerMutex_);
        asyncResultHandlerMap[name] = func;
        qDebug() << "JSBridge Registered async handler:" << name;
    }

    // callJs方法（默认10分钟超时）
    static void callJs(const QString &cefName, const JSBridgeMsg &data, ResultHandlerFunc callback, 
                       int timeoutMs = 10 * 60 * 1000, TimeoutHandlerFunc timeoutCallback = nullptr);


    // 初始化cefView
    static void init(QCefView *cefView, const QString &cefName);

    // 获取cefView
    static QPointer<QCefView> getCefView(const QString &cefName) {
        std::lock_guard<std::mutex> lock(cef_init_mtx_);
        return nameCefViewMap[cefName];
    }

signals:

private slots:
    // js 调用 qt slot（无返回值版本）
    static void onInvokeMethod(const QCefBrowserId &browserId, const QCefFrameId &frameId, const QString &method, const QVariantList &arguments);

    // js 调用 qt slot（有返回值版本）
    static void onQCefQueryRequest(const QCefBrowserId &browserId, const QCefFrameId &frameId, const QCefQuery &query);

    // cefView销毁时，移除相关的cefView
    static void onCefViewDestroyed(QObject *cefView = nullptr);

    // 超时处理槽函数
    static void onTimeout(const std::string& requestId);

private:
    using json = nlohmann::json;

    friend TestJSBridge;

    // 是否是主线程
    static bool isMainThread();

    static QThreadPool *getThreadPool();

    JSBridge() = default;

    // 内部callJs实现
    static void callJsInternal(const QString &cefName, const JSBridgeMsg &data, ResultHandlerFunc callback,
                              int timeoutMs = 0, TimeoutHandlerFunc timeoutCallback = nullptr);

    inline static QMap<QString, ResultHandlerFunc> resultHandlerMap;
    inline static QMap<QString, ResultHandlerFunc> asyncResultHandlerMap;
    inline static std::mutex handlerMutex_;

    inline static QMap<QCefBrowserId, QPointer<QCefView>> browserIdCefViewMap;
    inline static QMap<QString, QPointer<QCefView>> nameCefViewMap;
    // cefView初始化锁
    inline static std::mutex cef_init_mtx_;

    // 待处理的JS回调（包含超时信息）
    inline static QMap<std::string, std::shared_ptr<JSBridgeTimeoutCallbackData>> pending_js_callbacks_;
    inline static std::mutex pending_js_callbacks_mutex_;
};


#endif // JSBRIDGE_H
