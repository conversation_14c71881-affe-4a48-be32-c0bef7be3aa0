﻿//
// Created by HLJY on 2025/6/19.
//

#include "JSBridgeHandlerFactory.h"

std::vector<std::function<JSBridgeHandler*()>>& JSBridgeHandlerFactory::getRegistry() {
    static std::vector<std::function<J<PERSON>ridgeHandler*()>> registry;
    return registry;
}

// 注册处理器
void JSBridgeHandlerFactory::registerHandler(std::function<JSBridgeHandler*()> creator) {
    getRegistry().push_back(creator);
}

void JSBridgeHandlerFactory::autoRegister() {
    for (auto& creator : getRegistry()) {
        JSBridgeHandler* instance = creator();
        instance->registerHandler();
        delete instance;
    }
}