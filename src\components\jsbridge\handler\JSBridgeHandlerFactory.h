﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef JSBRIDGEHANDLERFACTORY_H
#define JSBRIDGEHANDLERFACTORY_H

#include <vector>
#include <functional>
#include "JSBridgeHandler.h"


class JSBridgeHandlerFactory {

public:
    // 注册处理器
    static void registerHandler(std::function<JSBridgeHandler*()> creator);

    // 自动调用所有注册方法
    static void autoRegister();

private:
    static std::vector<std::function<JSBridgeHandler*()>>& getRegistry();
};



#endif //JSBRIDGEHANDLERFACTORY_H
