#include <QVBoxLayout>
#include <QLabel>
#include "OctopusBubbleWidget.h"
#include <QPainter>
#include <QTimer>
#include <QDebug>
#include <QThread>


OctopusBubbleWidget::OctopusBubbleWidget(const QString &message, int duration, QWidget *parent)
    : QWidget(parent)
    , m_message(message)
    , m_autoCloseTime(duration) {

    // 无边框、显示在顶部
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    // 设置背景透明
    setAttribute(Qt::WA_TranslucentBackground);

    setupUI();

    // 自动关闭
    QTimer::singleShot(m_autoCloseTime, this, [this]() {
        qDebug() << "Bubble auto close after" << m_autoCloseTime << "ms" << ",main: " << QThread::isMainThread();
        this->deleteLater();
    });
}

void OctopusBubbleWidget::setupUI() {
    // 固定高度
    setFixedHeight(m_fixHeight);

    // 根据文本长度确定宽度
    m_font = QFont("Microsoft YaHei", m_frontSize, QFont::Medium);

    QFontMetrics metrics(m_font);

    int textWidth = metrics.horizontalAdvance(m_message) + m_padding_left + m_padding_right + m_triangle_width;
    int totalWidth = std::max(m_minWidth, textWidth);
    totalWidth = std::min(m_maxWidth, totalWidth);

    setFixedWidth(totalWidth);
}

void OctopusBubbleWidget::paintEvent(QPaintEvent *event) {
    Q_UNUSED(event);
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing, true);

    // 绘制渐变背景
    QLinearGradient gradient(0, 0, width() * 0.1f, height() * 1.0f); // 170度对应大约水平分量0.1倍，垂直分量1.0倍
    gradient.setColorAt(0, QColor("#31D29E"));
    gradient.setColorAt(1, QColor("#6355E4"));
    painter.setBrush(gradient);
    painter.setPen(Qt::NoPen);

    // 绘制圆角矩形
    QRectF rect = QRectF(0, 0, width() - m_triangle_width, height());
    painter.drawRoundedRect(rect, m_borderRadius, m_borderRadius);

    // 绘制三角形
    QPointF points[3] = {
            QPointF(width() - m_triangle_width -1, height() / 2 - m_triangle_height / 2),  // 三角形尖端指向控件右侧中心
            QPointF(width(), height() / 2),
            QPointF(width() - m_triangle_width -1, height() / 2 + m_triangle_height / 2)
    };

    painter.drawPolygon(points, 3);

    // 绘制背景图片
    QImage image = QImage(":/images/octopus/bubble_background_icon.png");
    painter.drawImage(QPointF(width() - m_triangle_width - m_background_padding_right - m_background_width,
                              (height() - m_background_height) / 2), image);

    // 绘制文字, padding
    painter.setFont(m_font);
    painter.setPen(Qt::white);
    QRect textRect = rect.toRect().adjusted(m_padding_left, m_padding_height, -m_padding_right, -m_padding_height);
    painter.drawText(textRect, Qt::AlignLeft | Qt::AlignVCenter, m_message);
}

OctopusBubbleWidget::~OctopusBubbleWidget() {
}
