#ifndef OCTOPUSBUBBLEWIDGET_H
#define OCTOPUSBUBBLEWIDGET_H

#include <QWidget>
#include <QHBoxLayout>
#include <src/screen_adaptation/ScreenAdaptationManager.h>

/**
 * 章鱼气泡
 */
class OctopusBubbleWidget : public QWidget
{
    Q_OBJECT
public:
    OctopusBubbleWidget(const QString &message, int duration = 3000, QWidget *parent = nullptr);
    ~OctopusBubbleWidget();

protected:
    void paintEvent(QPaintEvent *event) override;
    void setupUI();

private:
    // 消息内容
    QString m_message;
    // 字体
    QFont m_font;

    // 字体大小
    const int m_frontSize = ScreenAdaptationConstants::adaptSize(30);

    // 该控件的最小宽度和最大宽度
    const int m_minWidth = ScreenAdaptationConstants::adaptSize(100);
    const int m_maxWidth = ScreenAdaptationConstants::adaptSize(1500);
    // 该控件的高度
    const int m_fixHeight = ScreenAdaptationConstants::adaptSize(92);

    // 文字和边框的padding
    const int m_padding_left = ScreenAdaptationConstants::adaptSize(31);
    const int m_padding_right = ScreenAdaptationConstants::adaptSize(50);
    const int m_padding_height = ScreenAdaptationConstants::adaptSize(25);
    // 箭头宽度和箭头高度
    const int m_triangle_width = ScreenAdaptationConstants::adaptSize(14);
    const int m_triangle_height = ScreenAdaptationConstants::adaptSize(26);
    // 边框圆角半径
    const int m_borderRadius = ScreenAdaptationConstants::adaptSize(20);
    // 背景的padding
    const int m_background_padding_right = ScreenAdaptationConstants::adaptSize(21);
    // 背景的宽高
    const int m_background_width = ScreenAdaptationConstants::adaptSize(60);
    const int m_background_height = ScreenAdaptationConstants::adaptSize(40);

    // 自动关闭时间，毫秒
    const int m_autoCloseTime = 3000;
};


#endif //OCTOPUSBUBBLEWIDGET_H
