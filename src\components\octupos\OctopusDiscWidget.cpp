//
// Created by <PERSON>LJ<PERSON> on 2025/7/17.
//

#include <QLabel>
#include <QPropertyAnimation>
#include "OctopusDiscWidget.h"

OctopusDiscWidget::OctopusDiscWidget(QWidget *parent, ZIndexManager *zIndexManager) : QWidget(parent)
    , m_zIndexManager(zIndexManager)
    , m_outer(nullptr), m_inner(nullptr)
    , m_bubble(nullptr) {

    // 无边框、显示在顶部
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    // 设置背景透明
    setAttribute(Qt::WA_TranslucentBackground);

    setFixedSize(m_fixedSize, m_fixedSize);
    setupUI();

    // 设置层级
    if (m_zIndexManager) {
        m_zIndexManager->registerComponent(this, ZIndexLevel::QT_OCTOPUS_DISC, ComponentType::SIDEBAR,
                                           "OctopusDiscWidget");
    }
}

void OctopusDiscWidget::setupUI() {
  m_outer = new RotatableImageWidget(QPixmap(":/images/octopus/octopus_disc_outer.png"), this);
  m_outer->setFixedSize(m_fixedSize, m_fixedSize);

  m_inner = new QLabel(this);
  QPixmap pixmap = QPixmap(":/images/octopus/octopus_disc_inner.png");
  if (pixmap.isNull()) {
    qWarning() << "Failed to load octopus_disc_inner.png";
    return;
  }
  m_inner->setPixmap(pixmap);
  m_inner->setAlignment(Qt::AlignCenter);
  m_inner->setScaledContents(true);
  m_inner->setGeometry(m_outerThickness, m_outerThickness, m_innerSize, m_innerSize);
}

OctopusDiscWidget::~OctopusDiscWidget() {
    if (m_zIndexManager) {
        m_zIndexManager->unregisterComponent(this);
    }
}

void OctopusDiscWidget::showMessage(const QString &message, int duration) {
    if (message.isEmpty()) {
        qWarning() << "Message is empty";
        return;
    }
    if (m_bubble) {
        m_bubble->hide();
    }
    m_bubble = new OctopusBubbleWidget(message, duration, this);
    // 显示气泡，在当前控件的左侧显示
    int x = this->x() - m_bubble->width() - m_bubbleMargin;
    int y = this->y() + this->height() / 2 - m_bubble->height() / 2;
    QPoint pos = (QPoint(x, y));
    m_bubble->move(pos);
    m_bubble->show();
}

void OctopusDiscWidget::mousePressEvent(QMouseEvent *event) {
    emit clicked();
}

void OctopusDiscWidget::startRotationAnimation(int duration) {
    m_outer->startRotationAnimation(duration);
}

void OctopusDiscWidget::stopRotationAnimation() {
    m_outer->stopRotationAnimation();
}
