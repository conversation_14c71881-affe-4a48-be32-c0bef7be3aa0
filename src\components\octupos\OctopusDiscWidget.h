//
// Created by <PERSON><PERSON>J<PERSON> on 2025/7/17.
//

#ifndef OCTOPUSDISCWIDGET_H
#define OCTOPUSDISCWIDGET_H

#include <QWidget>
#include <QLabel>
#include <QPointer>
#include <src/components/whiteboardview/ZIndexManager.h>
#include "RotatableImageWidget.h"
#include "OctopusBubbleWidget.h"

/**
 * 章鱼旋转小圆盘
 */
class OctopusDiscWidget : public QWidget {
    Q_OBJECT
public:

    explicit OctopusDiscWidget(QWidget *parent = nullptr, ZIndexManager *zIndexManager = nullptr);
    ~OctopusDiscWidget() override;

    void showMessage(const QString &message, int duration = 3000);

    // 开始旋转动画
    // duration : 旋转一圈需要的
    void startRotationAnimation(int duration = 2000);

    // 停止旋转动画
    void stopRotationAnimation();
signals:
    // 点击事件
    void clicked();

protected:
    void mousePressEvent(QMouseEvent *event) override;

private:
    void setupUI();

    RotatableImageWidget *m_outer;
    QLabel *m_inner;

    QPointer<OctopusBubbleWidget> m_bubble = nullptr;

    QPointer<ZIndexManager> m_zIndexManager;

    // 圆盘的大小
    const int m_fixedSize = ScreenAdaptationConstants::adaptSize(140);
    // 内部圆盘的大小
    const int m_innerSize = ScreenAdaptationConstants::adaptSize(130);
    // 外部圆盘的厚度
    const int m_outerThickness = ScreenAdaptationConstants::adaptSize(5);
    // 气泡距离圆盘左侧的距离
    const int m_bubbleMargin = ScreenAdaptationConstants::adaptSize(21);
};


#endif //OCTOPUSDISCWIDGET_H
