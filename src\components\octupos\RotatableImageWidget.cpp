#include "RotatableImageWidget.h"

RotatableImageWidget::RotatableImageWidget(QPixmap pixmap, QWidget *parent):
    QWidget(parent),
    m_angle(0),
    m_pixmap(pixmap)
{
}

RotatableImageWidget::~RotatableImageWidget() {
    if (animation) {
        animation->stop();
        delete animation;
        animation = nullptr;
    }
}
void RotatableImageWidget::setRotationAngle(qreal angle) {
    if (qFuzzyCompare(m_angle, angle)) return;
    m_angle = angle;
    update();  // 触发重绘
    emit rotationChanged();
}

void RotatableImageWidget::paintEvent(QPaintEvent* event) {
    // 检查是否有图像
    if (m_pixmap.isNull()) {
        return;
    }

    QPainter painter(this);
    painter.setRenderHints(QPainter::Antialiasing | QPainter::SmoothPixmapTransform);

    // 获取原始图片
    const QPixmap& originalPix = m_pixmap;
    // 计算图片的缩放比例，保证图片完整显示在 QLabel 内
    qreal scaleFactor = qMin(width() / (qreal)originalPix.width(), height() / (qreal)originalPix.height());
    // 计算缩放后图片的尺寸
    QSize scaledSize = originalPix.size() * scaleFactor;

    // 1. 将坐标原点移动到 QLabel 的中心点
    painter.translate(width() / 2.0, height() / 2.0);
    // 2. 应用旋转
    painter.rotate(m_angle);
    // 3. 将坐标原点移回图片左上角位置，以便绘制图片
    painter.translate(-scaledSize.width() / 2.0, -scaledSize.height() / 2.0);

    // 绘制缩放后的图片
    painter.drawPixmap(0, 0, originalPix.scaled(scaledSize, Qt::KeepAspectRatio, Qt::SmoothTransformation));
}

void RotatableImageWidget::startRotationAnimation(int duration) {
    if (animation) {
        animation->stop();
        delete animation;
    }

    animation = new QPropertyAnimation(this, "rotationAngle");
    animation->setDuration(duration);
    animation->setStartValue(0);
    animation->setEndValue(360); // 360度完整旋转
    animation->setLoopCount(-1); // 无限循环
    animation->start();
}

void RotatableImageWidget::stopRotationAnimation() {
    if (animation) {
        animation->stop();
        delete animation;
        animation = nullptr;

        m_angle = 0;
        update();
    }
}

