#ifndef ROTATABLEIMAGEWIDGET_H
#define ROTATABLEIMAGEWIDGET_H


#include <QLabel>
#include <QPropertyAnimation>
#include <QPainter>

/**
 * 可旋转的图片控件
 */
class RotatableImageWidget : public QWidget {
Q_OBJECT

    Q_PROPERTY(qreal rotationAngle READ rotationAngle WRITE setRotationAngle NOTIFY rotationChanged)
public:
    explicit RotatableImageWidget(QPixmap pixmap, QWidget *parent = nullptr);

    ~RotatableImageWidget();

    // 获取旋转角度
    qreal rotationAngle() const { return m_angle; }

    // 设置旋转角度
    void setRotationAngle(qreal angle);

    // 开始旋转动画
    // duration : 旋转一圈需要的时间，单位毫秒
    void startRotationAnimation(int duration = 2000);

    // 停止旋转动画
    void stopRotationAnimation();

signals:

    void rotationChanged();

protected:
    void paintEvent(QPaintEvent *event) override;

private:
    qreal m_angle;
    QPixmap m_pixmap;
    QPropertyAnimation *animation = nullptr;
};

#endif //ROTATABLEIMAGEWIDGET_H
