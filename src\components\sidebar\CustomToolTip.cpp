#include "CustomToolTip.h"
#include <QPainter>
#include <QPainterPath>
#include "src/screen_adaptation/ScreenAdaptationManager.h"

bool CustomToolTip::s_hoverEnabled = false;

void CustomToolTip::setHoverEnabled(bool enabled) {
    s_hoverEnabled = enabled;
}
bool CustomToolTip::isHoverEnabled() {
    return s_hoverEnabled;
}

CustomToolTip::CustomToolTip(QWidget* parent)
    : QLabel(parent), m_timer(new QTimer(this)) {
    setWindowFlags(Qt::ToolTip | Qt::FramelessWindowHint);
    setAttribute(Qt::WA_ShowWithoutActivating);
    setAttribute(Qt::WA_TransparentForMouseEvents);
    setAttribute(Qt::WA_TranslucentBackground);
    setStyleSheet(
        "QLabel {"
        "  color: #fff;"
        "  font-weight: bold;"
        "  font-size: 21px;"
        "  padding: 14px;"
        "  letter-spacing: 1px;"
        "  background: transparent;"
        "}"
    );
    setAlignment(Qt::AlignCenter);
    m_timer->setSingleShot(true);
    connect(m_timer, &QTimer::timeout, this, &CustomToolTip::hide);
}

void CustomToolTip::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    painter.setCompositionMode(QPainter::CompositionMode_Source);
    painter.fillRect(rect(), Qt::transparent);
    painter.setCompositionMode(QPainter::CompositionMode_SourceOver);

    int radius = ScreenAdaptationConstants::adaptSize(40);
    int iconMargin = ScreenAdaptationConstants::adaptSize(30);
    int iconDiameter = ScreenAdaptationConstants::adaptSize(66);
    int checkIconSize = ScreenAdaptationConstants::adaptSize(32);

    QPainterPath path;
    path.addRoundedRect(rect(), radius, radius);
    painter.fillPath(path, QColor("#5f52e3"));

    // 绿色圆
    QRect iconRect(iconMargin, (height() - iconDiameter) / 2, iconDiameter, iconDiameter);
    painter.setBrush(QColor("#2ed16c"));
    painter.setPen(Qt::NoPen);
    painter.drawEllipse(iconRect);

    // 用iconfont字体画勾（更小字号，向下偏移2像素）
    QFont iconFont("iconfont");
    iconFont.setPixelSize(checkIconSize);
    painter.setFont(iconFont);
    painter.setPen(QColor("#fff"));
    QRect iconTextRect(iconRect.left(), iconRect.top() + ScreenAdaptationConstants::adaptSize(1), iconRect.width(), iconRect.height());
    painter.drawText(iconTextRect, Qt::AlignCenter, QChar(0xe637));

    // 绘制文字
    int textLeft = iconRect.right() + ScreenAdaptationConstants::adaptSize(12);
    int textRightPadding = ScreenAdaptationConstants::adaptSize(30);
    QRect textRect(textLeft, 0, width() - textLeft - textRightPadding, height());
    painter.setFont(font());
    painter.setPen(QColor("#fff"));
    painter.drawText(textRect, Qt::AlignVCenter | Qt::AlignLeft, text());
}

void CustomToolTip::showTip(const QPoint& globalPos, const QString& text, int durationMs) {
    setText(text);
    int iconDiameter = ScreenAdaptationConstants::adaptSize(66); 
    int iconMargin = ScreenAdaptationConstants::adaptSize(30);
    int iconTextGap = ScreenAdaptationConstants::adaptSize(12);
    int textRightPadding = ScreenAdaptationConstants::adaptSize(30);
    int verticalPadding = ScreenAdaptationConstants::adaptSize(8);

    // 计算文字宽度
    QFontMetrics fm(font());
    int textWidth = fm.horizontalAdvance(text);

    // 计算最小宽度：iconMargin + iconDiameter + iconTextGap + textWidth + textRightPadding
    int minWidth = iconMargin + iconDiameter + iconTextGap + textWidth + textRightPadding;
    minWidth = std::max(minWidth, ScreenAdaptationConstants::adaptSize(736)); // 强制最小宽度为736px
    setMinimumWidth(minWidth);

    setMinimumHeight(iconDiameter + verticalPadding * 2);
    adjustSize();
    move(globalPos.x() - width(), globalPos.y() - height() / 2);
    show();
    raise();
    m_timer->start(durationMs);
}

void CustomToolTip::hideTip() {
    m_timer->stop();
    hide();
} 