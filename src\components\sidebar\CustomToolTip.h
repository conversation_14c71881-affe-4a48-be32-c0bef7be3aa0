#pragma once
#include <QLabel>
#include <QTimer>
#include <QPaintEvent>

class CustomToolTip : public QLabel {
    Q_OBJECT
public:
    explicit CustomToolTip(QWidget* parent = nullptr);
    void showTip(const QPoint& globalPos, const QString& text, int durationMs = 5000);
    void hideTip();

    // 静态开关控制
    static void setHoverEnabled(bool enabled);
    static bool isHoverEnabled();

protected:
    void paintEvent(QPaintEvent* event) override;

private:
    QTimer* m_timer;
    static bool s_hoverEnabled;
}; 