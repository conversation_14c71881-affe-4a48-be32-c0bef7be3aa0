#include "SideBarConfigManager.h"
#include "../utils/ScreenUtils.h"

SideBarConfigManager* SideBarConfigManager::s_instance = nullptr;

SideBarConfigManager::SideBarConfigManager()
{
}

SideBarConfigManager::~SideBarConfigManager()
{
}

SideBarConfigManager* SideBarConfigManager::instance()
{
    if (!s_instance) {
        s_instance = new SideBarConfigManager();
    }
    return s_instance;
}

void SideBarConfigManager::destroy()
{
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

SideBarConstants::ToolInfo SideBarConfigManager::getResourcesToolInfo(const QSize& screenSize) const
{
    SideBarConstants::ToolInfo resourcesToolInfo;
    resourcesToolInfo.toolName = "资源";
    resourcesToolInfo.toolIcon = ":/images/side/side_resources.svg";
    resourcesToolInfo.viewType = SideBarConstants::ViewType::Normal;
    resourcesToolInfo.clickType = SideBarConstants::ClickType::Cef;
    resourcesToolInfo.fullscreen = true;
    resourcesToolInfo.showToolBox = false;
    resourcesToolInfo.zlevel = static_cast<int>(ZIndexLevel::CEF_RESOURCE_SELECTOR);
    resourcesToolInfo.url = "http://domainname/src/widget/ResourceDialog/index.html";
    resourcesToolInfo.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    
    return resourcesToolInfo;
}

SideBarConstants::ToolInfo SideBarConfigManager::getResourcesToolInfo() const
{
    return getResourcesToolInfo(ScreenUtils::getScreenSize());
}

SideBarConstants::ToolInfo SideBarConfigManager::getToolboxToolInfo() const
{
    SideBarConstants::ToolInfo toolboxToolInfo;
    toolboxToolInfo.toolName = "工具箱";
    toolboxToolInfo.toolIcon = ":/images/side/side_toolbox.svg";
    toolboxToolInfo.viewType = SideBarConstants::ViewType::Normal;
    toolboxToolInfo.clickType = SideBarConstants::ClickType::ToolBox;
    toolboxToolInfo.showToolBox = false;
    
    return toolboxToolInfo;
}

SideBarConstants::ToolInfo SideBarConfigManager::getExitToolInfo(const QSize& screenSize) const
{
    SideBarConstants::ToolInfo exitToolInfo;
    exitToolInfo.toolName = "退出";
    exitToolInfo.toolIcon = ":/images/side/side_exit.svg";
    exitToolInfo.viewType = SideBarConstants::ViewType::Normal;
    exitToolInfo.clickType = SideBarConstants::ClickType::ElectronFunc;
    exitToolInfo.fullscreen = true;
    exitToolInfo.showToolBox = false;
    exitToolInfo.zlevel = static_cast<int>(ZIndexLevel::CEF_FILE_SAVE);
    exitToolInfo.url = "http://domainname/src/widget/SaveBlackBoardDialog/index.html?showBlackBoardExit=true";
    exitToolInfo.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    
    return exitToolInfo;
}

SideBarConstants::ToolInfo SideBarConfigManager::getExitToolInfo() const
{
    return getExitToolInfo(ScreenUtils::getScreenSize());
}

SideBarConstants::ToolInfo SideBarConfigManager::getDesktopToolInfo() const
{
    SideBarConstants::ToolInfo desktopToolInfo;
    desktopToolInfo.toolName = "桌面";
    desktopToolInfo.toolIcon = ":/images/side/side_desktop.svg";
    desktopToolInfo.viewType = SideBarConstants::ViewType::Grid;
    desktopToolInfo.clickType = SideBarConstants::ClickType::ElectronFunc;
    desktopToolInfo.showToolBox = false;
    
    return desktopToolInfo;
}

SideBarConstants::ToolInfo SideBarConfigManager::getAppMarketToolInfo(const QSize& screenSize) const
{
    SideBarConstants::ToolInfo appMarketToolInfo;
    appMarketToolInfo.toolName = "应用市场";
    appMarketToolInfo.toolIcon = ":/images/side/side_applicationmarket.svg";
    appMarketToolInfo.viewType = SideBarConstants::ViewType::Grid;
    appMarketToolInfo.clickType = SideBarConstants::ClickType::Cef;
    appMarketToolInfo.fullscreen = true;
    appMarketToolInfo.showToolBox = false;
    appMarketToolInfo.zlevel = static_cast<int>(ZIndexLevel::CEF_APP_CENTER_FULLSCREEN);
    appMarketToolInfo.url = "http://domainname/src/widget/AppCenter/index.html";
    appMarketToolInfo.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    
    return appMarketToolInfo;
}

SideBarConstants::ToolInfo SideBarConfigManager::getAppMarketToolInfo() const
{
    return getAppMarketToolInfo(ScreenUtils::getScreenSize());
}

SideBarConstants::ToolInfo SideBarConfigManager::getWifiToolInfo() const
{
    SideBarConstants::ToolInfo wifiToolInfo;
    wifiToolInfo.toolName = "WiFi";
    wifiToolInfo.toolIcon = ":/images/side/side_wifi.svg";
    wifiToolInfo.viewType = SideBarConstants::ViewType::Grid;
    wifiToolInfo.clickType = SideBarConstants::ClickType::ElectronFunc;
    wifiToolInfo.showToolBox = false;
    
    return wifiToolInfo;
}

 