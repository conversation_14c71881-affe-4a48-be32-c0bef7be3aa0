#ifndef SIDEBARCONFIGMANAGER_H
#define SIDEBARCONFIGMANAGER_H

#include "SideBarConstants.h"
#include "../whiteboardview/ZIndexManager.h"
#include <QList>
#include <QSize>

/**
 * @brief 侧边栏配置管理器
 * 
 * 负责管理侧边栏所有工具的配置信息，包括固定按钮和动态工具配置
 */
class SideBarConfigManager
{
public:
    static SideBarConfigManager* instance();
    static void destroy();

    /**
     * @brief 获取资源按钮配置（需要屏幕尺寸）
     * @param screenSize 屏幕尺寸
     * @return 资源按钮配置信息
     */
    SideBarConstants::ToolInfo getResourcesToolInfo(const QSize& screenSize) const;

    /**
     * @brief 获取资源按钮配置（便捷方法，内部获取屏幕尺寸）
     * @return 资源按钮配置信息
     */
    SideBarConstants::ToolInfo getResourcesToolInfo() const;

    /**
     * @brief 获取工具箱按钮配置
     * @return 工具箱按钮配置信息
     */
    SideBarConstants::ToolInfo getToolboxToolInfo() const;

    /**
     * @brief 获取退出按钮配置（需要屏幕尺寸）
     * @param screenSize 屏幕尺寸
     * @return 退出按钮配置信息
     */
    SideBarConstants::ToolInfo getExitToolInfo(const QSize& screenSize) const;

    /**
     * @brief 获取退出按钮配置（便捷方法，内部获取屏幕尺寸）
     * @return 退出按钮配置信息
     */
    SideBarConstants::ToolInfo getExitToolInfo() const;

    /**
     * @brief 获取桌面按钮配置
     * @return 桌面按钮配置信息
     */
    SideBarConstants::ToolInfo getDesktopToolInfo() const;

    /**
     * @brief 获取应用市场按钮配置（需要屏幕尺寸）
     * @param screenSize 屏幕尺寸
     * @return 应用市场按钮配置信息
     */
    SideBarConstants::ToolInfo getAppMarketToolInfo(const QSize& screenSize) const;

    /**
     * @brief 获取应用市场按钮配置（便捷方法，内部获取屏幕尺寸）
     * @return 应用市场按钮配置信息
     */
    SideBarConstants::ToolInfo getAppMarketToolInfo() const;

    /**
     * @brief 获取WiFi按钮配置
     * @return WiFi按钮配置信息
     */
    SideBarConstants::ToolInfo getWifiToolInfo() const;

private:
    explicit SideBarConfigManager();
    ~SideBarConfigManager();

    static SideBarConfigManager* s_instance;
};

#endif // SIDEBARCONFIGMANAGER_H 