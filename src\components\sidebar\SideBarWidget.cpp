#include "SideBarWidget.h"
#include "SideBarConfigManager.h"
#include <QPainter>
#include <QPainterPath>
#include <QLinearGradient>
#include <QApplication>
#include <QScreen>
#include <QDebug>
#include <QEasingCurve>
#include <QDateTime>
#include <QSvgWidget>
#include <QSvgRenderer>
#include <QTimer>
#include <QSpacerItem>
#include <QScrollArea>
#include <exception>
#include <QToolTip>
#include <QFont>
#include <QEvent>
#include "CustomToolTip.h"
#include "src/screen_adaptation/ScreenAdaptationManager.h"
#include <QThread>
#include <QMetaObject>

SideBarWidget::SideBarWidget(QWidget *parent)
    : QWidget(parent)
    , m_isExpanded(true)
    , m_isAnimating(false)
    , m_targetExpanded(true)
    , m_collapseButton(nullptr)
    , m_layout(nullptr)
    , m_timeLabel(nullptr)
    , m_timeTimer(nullptr)
    , m_resourcesButton(nullptr)
    , m_toolboxButton(nullptr)
    , m_topDivider(nullptr)
    , m_bottomDivider(nullptr)
    , m_exitButton(nullptr)
    , m_smallButtonsContainer(nullptr)
    , m_desktopButton(nullptr)
    , m_appMarketButton(nullptr)
    , m_wifiButton(nullptr)

    , m_geometryAnimation(nullptr)
    , m_opacityAnimation(nullptr)
    , m_moveOutAnimation(nullptr)
    , m_moveInAnimation(nullptr)
    , m_mousePressed(false)
    , m_mousePressPos()
    , m_mainWindow(nullptr)
    , m_initialPositionSet(false)
{
    setObjectName("SideBarWidget");

    setAttribute(Qt::WA_TranslucentBackground);

    // 如果有父窗口，建立层级关系但保持独立窗口特性
    if (parent) {
        m_mainWindow = parent;
    }

    // 直接初始化UI和动画
    initializeUI();
    initializeAnimations();

    // 立即设置初始位置，防止动画从(0,0)开始
    setInitialPosition();
    m_initialPositionSet = true;

}

SideBarWidget::~SideBarWidget()
{
}

void SideBarWidget::setExpanded(bool expanded, bool animated)
{
    if (m_isExpanded == expanded || m_isAnimating) {
        return;
    }

    if (animated) {
        m_isAnimating = true;
        m_targetExpanded = expanded;

        // 确保几何状态已正确初始化，防止动画从左上角开始
        ensureValidGeometry();

        QRect currentGeometry = geometry();

        // 再次验证当前几何状态是否有效，如果仍然无效则强制设置正确位置
        QSize screenSize = ScreenUtils::getScreenSize();
        bool isInvalidPosition = currentGeometry.x() < 0 || currentGeometry.y() < 0 ||
                                currentGeometry.x() >= screenSize.width() || currentGeometry.y() >= screenSize.height();
        bool isInvalidSize = currentGeometry.width() <= 0 || currentGeometry.height() <= 0;
        bool isAtOrigin = currentGeometry.x() == 0 && currentGeometry.y() == 0;

        if (!currentGeometry.isValid() || currentGeometry.isEmpty() || isInvalidPosition || isInvalidSize || isAtOrigin) {
            qDebug() << "SideBarWidget: 动画前检测到无效几何状态，使用计算的正确位置";
            // 使用当前状态的正确几何位置作为动画起始点
            currentGeometry = calculateCorrectCurrentGeometry();
            setGeometry(currentGeometry);
            QApplication::processEvents();
        }

        QRect offScreenGeometry = calculateOffScreenGeometry();

        m_moveOutAnimation->setStartValue(currentGeometry);
        m_moveOutAnimation->setEndValue(offScreenGeometry);
        m_moveOutAnimation->start();
    } else {
        m_isExpanded = expanded;

        // 独立窗口模式：使用位置更新方法而不是直接设置几何
        updatePositionRelativeToMainWindow();
        updateWidgetsVisibility(expanded);
        emit expandedChanged(expanded);
    }
}

void SideBarWidget::toggleExpanded(bool animated)
{
    setExpanded(!m_isExpanded, animated);
}

void SideBarWidget::updatePosition()
{
    if (m_isAnimating) {
        return;
    }

    // 独立窗口模式：使用位置更新方法
    updatePositionRelativeToMainWindow();
}

void SideBarWidget::updateContentSize()
{
    if (m_isAnimating) {
        return;
    }

    if (m_isExpanded) {
        // 独立窗口模式：使用位置更新方法
        updatePositionRelativeToMainWindow();
    }
}

void SideBarWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    if (m_isExpanded) {
        drawExpandedBackground(&painter);
    } else {
        drawCollapsedBackground(&painter);
    }
}

void SideBarWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_mousePressed = true;
        m_mousePressPos = event->pos();
    }
    QWidget::mousePressEvent(event);
}

void SideBarWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton && m_mousePressed) {
        m_mousePressed = false;

        if (!m_isExpanded) {
            toggleExpanded(true);
        }
    }
    // 阻止Qt的默认窗口激活行为，防止鼠标释放时自动置顶
    // QWidget::mouseReleaseEvent(event);
}

void SideBarWidget::onExpandAnimationFinished()
{
    m_isAnimating = false;

    if (m_isExpanded) {
        updateWidgetsVisibility(true);
    }

    emit expandedChanged(m_isExpanded);
}

void SideBarWidget::onCollapseAnimationFinished()
{
    m_isAnimating = false;
}

void SideBarWidget::onMoveOutAnimationFinished()
{

    m_isExpanded = m_targetExpanded;
    updateWidgetsVisibility(m_isExpanded);

    // 计算正确的目标位置（绝对屏幕坐标）
    QRect targetGeometry = calculateCorrectTargetGeometry();
    QRect offScreenStartGeometry = calculateOffScreenGeometryForTarget();



    setGeometry(offScreenStartGeometry);
    update();

    m_moveInAnimation->setStartValue(offScreenStartGeometry);
    m_moveInAnimation->setEndValue(targetGeometry);
    m_moveInAnimation->start();
}

void SideBarWidget::onMoveInAnimationFinished()
{
    m_isAnimating = false;
    emit expandedChanged(m_isExpanded);
}

void SideBarWidget::onCollapseButtonClicked()
{
    emit collapseRequested();
    setExpanded(false, true);
}



void SideBarWidget::updateTimeDisplay()
{
    if (m_timeLabel) {
        QDateTime currentTime = QDateTime::currentDateTime();
        QString timeString = currentTime.toString("hh:mm");
        m_timeLabel->setText(timeString);
    }
}

bool SideBarWidget::handleMousePress(QObject *obj, QMouseEvent *mouseEvent)
{
    if (mouseEvent->button() != Qt::LeftButton) {
        return false;
    }

    // 处理收起按钮点击
    if (obj == m_collapseButton) {
        onCollapseButtonClicked();
        return true;
    }

    // 处理工具按钮点击
    QWidget* widget = qobject_cast<QWidget*>(obj);
    if (widget) {
        QVariant toolInfoVariant = widget->property("toolInfo");
        if (toolInfoVariant.isValid()) {
            SideBarConstants::ToolInfo toolInfo = toolInfoVariant.value<SideBarConstants::ToolInfo>();
            emit buttonClicked(toolInfo);
            return true;
        }
    }

    return false;
}

bool SideBarWidget::eventFilter(QObject *obj, QEvent *event)
{
    // 资源按钮：处理自定义tooltip
    if (obj == m_resourcesButton) {
        // 保留原有点击处理逻辑，无需再处理悬浮tooltip
    }

    // 保留原有点击处理逻辑
    if (event->type() == QEvent::MouseButtonPress) {
        QMouseEvent *mouseEvent = static_cast<QMouseEvent*>(event);
        if (handleMousePress(obj, mouseEvent)) {
            return true;
        }
    }

    return QWidget::eventFilter(obj, event);
}

void SideBarWidget::initializeUI()
{
    m_layout = new QVBoxLayout(this);
    m_layout->setContentsMargins(
        0,
        SideBarConstants::getContainerPadding(),
        0,
        SideBarConstants::getContainerPadding()
    );
    m_layout->setSpacing(0);

    m_timeLabel = new QLabel(this);
    m_timeLabel->setAlignment(Qt::AlignCenter);
    QString timeStyle = QString(
        "QLabel {"
        "    font-weight: 900;"
        "    font-size: %1px;"
        "    color: #FFFFFF;"
        "    line-height: %1px;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(SideBarConstants::getTimeDisplayFontSize());

    m_timeLabel->setStyleSheet(timeStyle);

    m_timeTimer = new QTimer(this);
    connect(m_timeTimer, &QTimer::timeout, this, &SideBarWidget::updateTimeDisplay);
    m_timeTimer->start(1000);

    updateTimeDisplay();

    m_collapseButton = createCollapseButton(":/images/side/side_collapsed.svg");
    if (m_collapseButton) {
        m_collapseButton->installEventFilter(this);
    }

    m_topDivider = createDivider();
    m_bottomDivider = createDivider();

    // 添加时间标签
    if (m_timeLabel) {
        m_layout->addWidget(m_timeLabel, 0, Qt::AlignTop | Qt::AlignCenter);
        addSpacing(SideBarConstants::getButtonTopMargin());
    }

    // 创建固定的资源按钮和工具箱按钮
    createFixedTopButtons();

    // 创建中心动态区域
    createCenterDynamicArea();

    // 创建固定的底部按钮和小按钮组
    createFixedBottomButtons();


}

void SideBarWidget::initializeAnimations()
{
    m_geometryAnimation = new QPropertyAnimation(this, "geometry", this);
    m_geometryAnimation->setDuration(SideBarConstants::ANIMATION_DURATION);
    m_geometryAnimation->setEasingCurve(QEasingCurve::OutCubic);

    m_moveOutAnimation = new QPropertyAnimation(this, "geometry", this);
    m_moveOutAnimation->setDuration(SideBarConstants::ANIMATION_DURATION / 2);
    m_moveOutAnimation->setEasingCurve(QEasingCurve::InCubic);

    m_moveInAnimation = new QPropertyAnimation(this, "geometry", this);
    m_moveInAnimation->setDuration(SideBarConstants::ANIMATION_DURATION / 2);
    m_moveInAnimation->setEasingCurve(QEasingCurve::OutCubic);

    connect(m_geometryAnimation, &QPropertyAnimation::finished, this, &SideBarWidget::onExpandAnimationFinished);
    connect(m_moveOutAnimation, &QPropertyAnimation::finished, this, &SideBarWidget::onMoveOutAnimationFinished);
    connect(m_moveInAnimation, &QPropertyAnimation::finished, this, &SideBarWidget::onMoveInAnimationFinished);
}

QWidget* SideBarWidget::getValidParentWidget() const
{
    QWidget* parentWidget = qobject_cast<QWidget*>(parent());
    if (!parentWidget || parentWidget->width() <= 0 || parentWidget->height() <= 0) {
        return nullptr;
    }
    return parentWidget;
}

QRect SideBarWidget::calculateExpandedGeometry() const
{
    // 独立窗口模式：优先使用主窗口作为参考
    QWidget* referenceWidget = m_mainWindow ? m_mainWindow : getValidParentWidget();

    int width = SideBarConstants::getExpandedWidth();
    int height = calculateContentHeight();

    if (width <= 0 || height <= 0) {
        return QRect();
    }

    int maxHeight;
    if (referenceWidget) {
        maxHeight = static_cast<int>(referenceWidget->height() * 0.8);
    } else {
        // 没有参考窗口时，使用屏幕高度的80%
        QSize screenSize = ScreenUtils::getScreenSize();
        maxHeight = static_cast<int>(screenSize.height() * 0.8);
    }

    if (height > maxHeight) {
        height = maxHeight;
    }

    // 独立窗口模式下，只返回大小，位置由updatePositionRelativeToMainWindow处理
    if (m_mainWindow) {
        // 返回相对大小，位置为0（将由独立窗口位置管理方法处理）
        QRect result(0, 0, width, height);
        return result.isValid() ? result : QRect();
    } else if (referenceWidget) {
        // 传统子组件模式
        int x = referenceWidget->width() - width;
        int y = (referenceWidget->height() - height) / 2;

        x = qMax(0, x);
        y = qMax(0, y);

        QRect result(x, y, width, height);
        return result.isValid() ? result : QRect();
    } else {
        // 没有参考窗口时，使用屏幕右侧居中
        QSize screenSize = ScreenUtils::getScreenSize();
        int x = screenSize.width() - width;
        int y = (screenSize.height() - height) / 2;

        QRect result(x, y, width, height);
        return result.isValid() ? result : QRect();
    }
}

QRect SideBarWidget::calculateCollapsedGeometry() const
{
    // 独立窗口模式：优先使用主窗口作为参考
    QWidget* referenceWidget = m_mainWindow ? m_mainWindow : getValidParentWidget();

    int clickWidth = SideBarConstants::getCollapsedClickAreaWidth();
    int height = SideBarConstants::getCollapsedHeight();
    int rightMargin = SideBarConstants::getCollapsedRightMargin();
    int visualWidth = SideBarConstants::getCollapsedWidth();

    // 独立窗口模式下，只返回大小，位置由updatePositionRelativeToMainWindow处理
    if (m_mainWindow) {
        // 返回相对大小，位置为0（将由独立窗口位置管理方法处理）
        return QRect(0, 0, clickWidth, height);
    } else if (referenceWidget) {
        // 传统子组件模式
        int visualCenterX = referenceWidget->width() - visualWidth / 2 - rightMargin;
        int x = visualCenterX - clickWidth / 2;
        int y = (referenceWidget->height() - height) / 2;

        return QRect(x, y, clickWidth, height);
    } else {
        // 没有参考窗口时，使用屏幕右侧位置
        QSize screenSize = ScreenUtils::getScreenSize();
        int visualCenterX = screenSize.width() - visualWidth / 2 - rightMargin;
        int x = visualCenterX - clickWidth / 2;
        int y = (screenSize.height() - height) / 2;

        return QRect(x, y, clickWidth, height);
    }
}

QRect SideBarWidget::calculateOffScreenGeometry() const
{
    // 独立窗口模式：优先使用主窗口作为参考
    QWidget* referenceWidget = m_mainWindow ? m_mainWindow : getValidParentWidget();

    int width = this->width();
    int height = this->height();

    // 如果当前几何状态无效，使用当前状态的预期尺寸
    if (width <= 0 || height <= 0) {
        if (m_isExpanded) {
            width = SideBarConstants::getExpandedWidth();
            height = calculateContentHeight();
            if (height <= 0) {
                height = SideBarConstants::getExpandedHeight();
            }
        } else {
            width = SideBarConstants::getCollapsedClickAreaWidth();
            height = SideBarConstants::getCollapsedHeight();
        }
    }

    if (referenceWidget) {
        // 获取主窗口的屏幕位置和大小
        QPoint mainWindowPos = referenceWidget->mapToGlobal(QPoint(0, 0));
        QSize mainWindowSize = referenceWidget->size();

        // 计算屏幕右侧外的位置（屏幕坐标）
        int x = mainWindowPos.x() + mainWindowSize.width() + 50;  // 主窗口右侧外50像素
        int y = mainWindowPos.y() + (mainWindowSize.height() - height) / 2;  // 垂直居中

        return QRect(x, y, width, height);
    } else {
        // 没有参考窗口时，使用屏幕右侧外的位置
        QSize screenSize = ScreenUtils::getScreenSize();
        int x = screenSize.width() + 50;  // 屏幕右侧外50像素
        int y = (screenSize.height() - height) / 2;  // 垂直居中

        return QRect(x, y, width, height);
    }
}

QRect SideBarWidget::calculateOffScreenGeometryForTarget() const
{
    // 独立窗口模式：优先使用主窗口作为参考
    QWidget* referenceWidget = m_mainWindow ? m_mainWindow : getValidParentWidget();

    int width = m_targetExpanded ? SideBarConstants::getExpandedWidth() : SideBarConstants::getCollapsedClickAreaWidth();
    int height = m_targetExpanded ? calculateContentHeight() : SideBarConstants::getCollapsedHeight();

    if (m_targetExpanded) {
        int maxHeight;
        if (referenceWidget) {
            maxHeight = static_cast<int>(referenceWidget->height() * 0.8);
        } else {
            QSize screenSize = ScreenUtils::getScreenSize();
            maxHeight = static_cast<int>(screenSize.height() * 0.8);
        }
        if (height > maxHeight) {
            height = maxHeight;
        }
    }

    if (referenceWidget) {
        // 获取主窗口的屏幕位置和大小
        QPoint mainWindowPos = referenceWidget->mapToGlobal(QPoint(0, 0));
        QSize mainWindowSize = referenceWidget->size();

        // 计算屏幕右侧外的位置（屏幕坐标）
        int x = mainWindowPos.x() + mainWindowSize.width() + 50;  // 主窗口右侧外50像素
        int y = mainWindowPos.y() + (mainWindowSize.height() - height) / 2;  // 垂直居中

        return QRect(x, y, width, height);
    } else {
        // 没有参考窗口时，使用屏幕右侧外的位置
        QSize screenSize = ScreenUtils::getScreenSize();
        int x = screenSize.width() + 50;  // 屏幕右侧外50像素
        int y = (screenSize.height() - height) / 2;  // 垂直居中

        return QRect(x, y, width, height);
    }
}

QRect SideBarWidget::calculateCorrectTargetGeometry() const
{

    // 获取屏幕尺寸
    QSize screenSize = ScreenUtils::getScreenSize();

    // 获取目标尺寸
    int width, height;
    if (m_isExpanded) {
        width = SideBarConstants::getExpandedWidth();
        height = calculateContentHeight();
        if (height <= 0) {
            height = SideBarConstants::getExpandedHeight();
        }
    } else {
        width = SideBarConstants::getCollapsedClickAreaWidth();
        height = SideBarConstants::getCollapsedHeight();
    }

    // 计算绝对屏幕位置（始终使用屏幕尺寸，避免主窗口尺寸变化的影响）
    int x, y;
    if (m_isExpanded) {
        // 展开状态：紧贴屏幕右边
        x = screenSize.width() - width;
    } else {
        // 收起状态：考虑右边距
        int rightMargin = SideBarConstants::getCollapsedRightMargin();
        int visualWidth = SideBarConstants::getCollapsedWidth();
        int visualCenterFromRight = visualWidth / 2 + rightMargin;
        x = screenSize.width() - visualCenterFromRight - width / 2;
    }
    y = (screenSize.height() - height) / 2;

    QRect result(x, y, width, height);
    return result;
}

QRect SideBarWidget::calculateCorrectCurrentGeometry() const
{
    // 获取屏幕尺寸
    QSize screenSize = ScreenUtils::getScreenSize();

    // 获取当前状态的尺寸
    int width, height;
    if (m_isExpanded) {
        width = SideBarConstants::getExpandedWidth();
        height = calculateContentHeight();
        if (height <= 0) {
            height = SideBarConstants::getExpandedHeight();
        }
    } else {
        width = SideBarConstants::getCollapsedClickAreaWidth();
        height = SideBarConstants::getCollapsedHeight();
    }

    // 计算当前状态的正确位置
    int x, y;
    if (m_isExpanded) {
        // 展开状态：紧贴屏幕右边
        x = screenSize.width() - width;
    } else {
        // 收起状态：考虑右边距
        int rightMargin = SideBarConstants::getCollapsedRightMargin();
        int visualWidth = SideBarConstants::getCollapsedWidth();
        int visualCenterFromRight = visualWidth / 2 + rightMargin;
        x = screenSize.width() - visualCenterFromRight - width / 2;
    }
    y = (screenSize.height() - height) / 2;

    QRect result(x, y, width, height);
    return result;
}

int SideBarWidget::calculateContentHeight() const
{
    if (!m_layout) {
        return SideBarConstants::getExpandedHeight();
    }
    int contentHeight = 0;

    contentHeight += m_layout->contentsMargins().top();
    contentHeight += m_layout->contentsMargins().bottom();

    for (int i = 0; i < m_layout->count(); ++i) {
        QLayoutItem* item = m_layout->itemAt(i);
        if (!item) continue;

        if (item->widget()) {
            QWidget* widget = item->widget();
            if (widget && (widget->isVisible() ||
                widget == m_timeLabel ||
                widget == m_collapseButton ||
                widget == m_resourcesButton ||
                widget == m_toolboxButton ||
                widget == m_topDivider ||
                widget == m_bottomDivider ||
                widget == m_exitButton ||
                widget == m_smallButtonsContainer ||
                widget == m_centerContainer ||
                m_toolButtons.contains(widget))) {
                QSize hint = widget->sizeHint();
                if (hint.height() > 0) {
                    contentHeight += hint.height();
                }
            }
        } else if (item->spacerItem()) {
            QSpacerItem* spacer = item->spacerItem();
            if (spacer) {
                QSize hint = spacer->sizeHint();
                if (hint.height() > 0) {
                    contentHeight += hint.height();
                }
            }
        }
    }

    int minHeight = SideBarConstants::getFunctionButtonSize() * 2 +
                   SideBarConstants::getTimeDisplayFontSize() +
                   SideBarConstants::getCollapseButtonClickSize() +
                   SideBarConstants::getContainerPadding() * 2;

    if (contentHeight < minHeight) {
        contentHeight = minHeight;
    }

    int maxHeight = 2000;
    if (contentHeight > maxHeight) {
        contentHeight = maxHeight;
    }

    return contentHeight;
}

void SideBarWidget::drawExpandedBackground(QPainter *painter)
{
    QRectF rect = this->rect();
    qreal leftRadius = SideBarConstants::getExpandedLeftBorderRadius();

    QPainterPath path;

    path.moveTo(rect.left() + leftRadius, rect.top());
    path.lineTo(rect.right(), rect.top());
    path.lineTo(rect.right(), rect.bottom());
    path.lineTo(rect.left() + leftRadius, rect.bottom());

    path.arcTo(rect.left(), rect.bottom() - 2 * leftRadius,
               2 * leftRadius, 2 * leftRadius, 270, -90);

    path.lineTo(rect.left(), rect.top() + leftRadius);

    path.arcTo(rect.left(), rect.top(),
               2 * leftRadius, 2 * leftRadius, 180, -90);

    QColor bgColor(0, 0, 0, SideBarConstants::EXPANDED_BG_ALPHA);
    painter->setBrush(bgColor);
    painter->setPen(Qt::NoPen);
    painter->drawPath(path);
}

void SideBarWidget::drawCollapsedBackground(QPainter *painter)
{
    QRectF widgetRect = this->rect();
    qreal radius = SideBarConstants::getBorderRadius();

    int visualWidth = SideBarConstants::getCollapsedWidth();
    int clickWidth = SideBarConstants::getCollapsedClickAreaWidth();

    qreal visualX = widgetRect.x() + (clickWidth - visualWidth) / 2.0;
    qreal visualY = widgetRect.y();
    qreal visualHeight = widgetRect.height();

    QRectF visualRect(visualX, visualY, visualWidth, visualHeight);

    QLinearGradient gradient(visualRect.topLeft(), visualRect.bottomLeft());
    gradient.setColorAt(0.0, QColor(41, 218, 128));
    gradient.setColorAt(1.0, QColor(95, 82, 227));

    painter->setBrush(QBrush(gradient));
    painter->setPen(Qt::NoPen);
    painter->drawRoundedRect(visualRect, radius, radius);

    #ifdef DEBUG_CLICK_AREA
    painter->setPen(QPen(QColor(255, 0, 0, 100), 1, Qt::DashLine));
    painter->setBrush(Qt::NoBrush);
    painter->drawRect(widgetRect);
    #endif
}

QWidget* SideBarWidget::createIconWidget(const QString& iconPath, const QSize& size, QWidget* parent)
{
    QSvgWidget* svgWidget = new QSvgWidget(parent);
    svgWidget->setFixedSize(size);

    if (!iconPath.isEmpty()) {
        svgWidget->load(iconPath);
        if (svgWidget->renderer() && svgWidget->renderer()->isValid()) {
            return svgWidget;
        }
    }
    svgWidget->deleteLater();

    // 创建占位符
    QLabel* placeholderLabel = new QLabel("?", parent);
    placeholderLabel->setFixedSize(size);
    placeholderLabel->setAlignment(Qt::AlignCenter);
    placeholderLabel->setStyleSheet(QString(
        "QLabel { "
        "background-color: rgba(255, 0, 0, 0.3); "
        "color: white; "
        "font-size: %1px; "
        "border-radius: 4px; "
        "}"
    ).arg(qMin(size.width(), size.height()) / 3));

    return placeholderLabel;
}

QWidget* SideBarWidget::createFunctionButton(const QString& iconPath, const QString& text, const SideBarConstants::ToolInfo& toolInfo)
{
    QWidget* container = new QWidget(this);
    container->setFixedSize(
        SideBarConstants::getExpandedWidth(),
        SideBarConstants::getFunctionButtonSize() + SideBarConstants::getFunctionButtonTextSize() + 4
    );
    // 强制高度，防止因内容变化塌陷
    container->setFixedHeight(
        SideBarConstants::getFunctionButtonSize() + SideBarConstants::getFunctionButtonTextSize() + 4
    );

    // 设置工具信息属性，用于点击事件识别
    if (!toolInfo.toolName.isEmpty()) {
        container->setProperty("toolInfo", QVariant::fromValue(toolInfo));
        container->installEventFilter(this);
    }

    QVBoxLayout* layout = new QVBoxLayout(container);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(4);
    layout->setAlignment(Qt::AlignCenter);

    // 创建图标组件
    QSize iconSize(SideBarConstants::getFunctionButtonSize(), SideBarConstants::getFunctionButtonSize());
    QWidget* iconWidget = createIconWidget(iconPath, iconSize, container);
    iconWidget->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    layout->addWidget(iconWidget, 0, Qt::AlignCenter);

    // 创建文本标签
    QLabel* textLabel = new QLabel(text, container);
    textLabel->setAlignment(Qt::AlignCenter);
    textLabel->setStyleSheet(QString(
        "QLabel {"
        "    font-weight: 400;"
        "    font-size: %1px;"
        "    color: #FFFFFF;"
        "    line-height: 38px;"
        "    background: transparent;"
        "    border: none;"
        "}"
    ).arg(SideBarConstants::getFunctionButtonTextSize()));
    textLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    layout->addWidget(textLabel, 0, Qt::AlignCenter);

    // 关键：加stretch，防止塌陷
    layout->addStretch(1);

    return container;
}

QFrame* SideBarWidget::createDivider()
{
    QFrame* divider = new QFrame(this);
    divider->setFixedSize(
        SideBarConstants::getDividerWidth(),
        1
    );

    // 设置分割栏为水平线
    divider->setFrameShape(QFrame::HLine);
    divider->setFrameShadow(QFrame::Plain);

    divider->setStyleSheet(QString(
        "QFrame {"
        "    border: none;"
        "    background: transparent;"
        "    color: rgba(255, 255, 255, 0.4);"
        "    width: %1px;"
        "}"
        "QFrame[frameShape=\"4\"] {"  // HLine = 4
        "    border-top: 2px dotted rgba(255, 255, 255, 0.4);"
        "    border-bottom: none;"
        "    border-left: none;"
        "    border-right: none;"
        "}"
    ).arg(SideBarConstants::getDividerWidth()));

    return divider;
}

QWidget* SideBarWidget::createSmallButton(const QString& iconPath, const SideBarConstants::ToolInfo& toolInfo)
{
    int buttonSize = SideBarConstants::getSmallButtonSize();
    QSize size(buttonSize, buttonSize);

    QWidget* iconWidget = createIconWidget(iconPath, size, this);

    if (!toolInfo.toolName.isEmpty()) {
        iconWidget->setProperty("toolInfo", QVariant::fromValue(toolInfo));
        iconWidget->installEventFilter(this);
    }

    return iconWidget;
}

QWidget* SideBarWidget::createCollapseButton(const QString& iconPath)
{
    int clickSize = SideBarConstants::getCollapseButtonClickSize();
    int visualWidth = SideBarConstants::getCollapseButtonVisualWidth();
    int visualHeight = SideBarConstants::getCollapseButtonVisualHeight();

    QWidget* container = new QWidget(this);
    container->setFixedSize(clickSize, clickSize);

    QVBoxLayout* layout = new QVBoxLayout(container);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setAlignment(Qt::AlignCenter);

    // 创建图标组件
    QSize iconSize(visualWidth, visualHeight);
    QWidget* iconWidget = createIconWidget(iconPath, iconSize, container);

    // 如果是占位符，修改显示文本为"X"
    QLabel* label = qobject_cast<QLabel*>(iconWidget);
    if (label && label->text() == "?") {
        label->setText("X");
        label->setStyleSheet(
            "QLabel { "
            "background-color: rgba(255, 0, 0, 0.3); "
            "color: white; "
            "font-size: 12px; "
            "border-radius: 2px; "
            "}"
        );
    }

    layout->addWidget(iconWidget, 0, Qt::AlignCenter);
    return container;
}

QWidget* SideBarWidget::createSmallButtonsContainer()
{
    QWidget* container = new QWidget(this);

    QGridLayout* gridLayout = new QGridLayout(container);
    gridLayout->setContentsMargins(0, 0, 0, 0);
    gridLayout->setSpacing(SideBarConstants::getSmallButtonSpacing());

    // 创建空的占位符，具体内容将通过setToolsConfig方法设置
    // 这里只设置容器的基本尺寸
    int containerWidth = SideBarConstants::getSmallButtonSize() * 2 + SideBarConstants::getSmallButtonSpacing() * 1;
    int containerHeight = SideBarConstants::getSmallButtonSize() * 2 + SideBarConstants::getSmallButtonSpacing() * 1;
    container->setFixedSize(containerWidth, containerHeight);

    return container;
}





void SideBarWidget::createFixedTopButtons()
{
    // 获取屏幕尺寸，供多个配置使用
    QSize screenSize = ScreenUtils::getScreenSize();

    // 使用配置管理器获取资源按钮配置
    SideBarConstants::ToolInfo resourcesToolInfo = SideBarConfigManager::instance()->getResourcesToolInfo();

    // 使用配置管理器获取工具箱按钮配置
    SideBarConstants::ToolInfo toolboxToolInfo = SideBarConfigManager::instance()->getToolboxToolInfo();

    // 创建资源按钮
    m_resourcesButton = createFunctionButton(resourcesToolInfo.toolIcon, resourcesToolInfo.toolName, resourcesToolInfo);
    if (m_resourcesButton) {
        m_layout->addWidget(m_resourcesButton, 0, Qt::AlignTop | Qt::AlignCenter);
        addSpacing(SideBarConstants::getButtonTopMargin());
        m_resourcesButton->installEventFilter(this);
    }

    // 创建工具箱按钮
    m_toolboxButton = createFunctionButton(toolboxToolInfo.toolIcon, toolboxToolInfo.toolName, toolboxToolInfo);
    if (m_toolboxButton) {
        m_layout->addWidget(m_toolboxButton, 0, Qt::AlignTop | Qt::AlignCenter);
    }

    // 添加第一个分割线
    if (m_topDivider) {
        addSpacing(SideBarConstants::getButtonTopMargin());
        m_layout->addWidget(m_topDivider, 0, Qt::AlignTop | Qt::AlignCenter);
        addSpacing(SideBarConstants::getButtonTopMargin());
    }
}

void SideBarWidget::createCenterDynamicArea()
{
    // 创建中心动态区域容器
    m_centerContainer = new QWidget(this);

    m_centerContainer->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);

    m_centerLayout = new QVBoxLayout(m_centerContainer);
    m_centerLayout->setContentsMargins(0, 0, 0, 0);
    m_centerLayout->setSpacing(0);

    m_layout->addWidget(m_centerContainer, 1);
}

void SideBarWidget::createFixedBottomButtons()
{
    // 获取屏幕尺寸，供多个配置使用
    QSize screenSize = ScreenUtils::getScreenSize();

    if (m_bottomDivider) {
        addSpacing(SideBarConstants::getButtonTopMargin());
        m_layout->addWidget(m_bottomDivider, 0, Qt::AlignTop | Qt::AlignCenter);
        addSpacing(SideBarConstants::getButtonTopMargin());
    }

    // 使用配置管理器获取退出按钮配置
    SideBarConstants::ToolInfo exitToolInfo = SideBarConfigManager::instance()->getExitToolInfo();

    // 创建退出按钮
    m_exitButton = createFunctionButton(exitToolInfo.toolIcon, exitToolInfo.toolName, exitToolInfo);
    if (m_exitButton) {
        m_layout->addWidget(m_exitButton, 0, Qt::AlignTop | Qt::AlignCenter);
        addSpacing(SideBarConstants::getButtonTopMargin());
    }

    // 创建小按钮组容器
    m_smallButtonsContainer = new QWidget(this);
    QGridLayout* gridLayout = new QGridLayout(m_smallButtonsContainer);
    gridLayout->setContentsMargins(0, 0, 0, 0);
    gridLayout->setSpacing(SideBarConstants::getSmallButtonSpacing());

    // 使用配置管理器获取各个按钮配置
    SideBarConstants::ToolInfo desktopToolInfo = SideBarConfigManager::instance()->getDesktopToolInfo();
    SideBarConstants::ToolInfo appMarketToolInfo = SideBarConfigManager::instance()->getAppMarketToolInfo();
    SideBarConstants::ToolInfo wifiToolInfo = SideBarConfigManager::instance()->getWifiToolInfo();

    // 创建小按钮并添加到网格布局
    m_desktopButton = createSmallButton(desktopToolInfo.toolIcon, desktopToolInfo);
    m_appMarketButton = createSmallButton(appMarketToolInfo.toolIcon, appMarketToolInfo);
    m_wifiButton = createSmallButton(wifiToolInfo.toolIcon, wifiToolInfo);

    if (m_desktopButton && m_appMarketButton && m_wifiButton) {
        gridLayout->addWidget(m_desktopButton, 0, 0, Qt::AlignCenter);
        gridLayout->addWidget(m_appMarketButton, 0, 1, Qt::AlignCenter);
        gridLayout->addWidget(m_wifiButton, 1, 0, Qt::AlignCenter);
    }

    // 设置小按钮容器尺寸
    int containerWidth = SideBarConstants::getSmallButtonSize() * 2 + SideBarConstants::getSmallButtonSpacing();
    int containerHeight = SideBarConstants::getSmallButtonSize() * 2 + SideBarConstants::getSmallButtonSpacing();
    m_smallButtonsContainer->setFixedSize(containerWidth, containerHeight);

    // 添加小按钮容器到布局
    if (m_smallButtonsContainer) {
        m_layout->addWidget(m_smallButtonsContainer, 0, Qt::AlignTop | Qt::AlignCenter);
        addSpacing(SideBarConstants::getButtonTopMargin());
    }

    // 最后添加收起按钮到最底部
    if (m_collapseButton) {
        m_layout->addWidget(m_collapseButton, 0, Qt::AlignTop | Qt::AlignCenter);
    }
}

void SideBarWidget::addSpacing(int spacing)
{
    if (m_layout && spacing > 0) {
        m_layout->addSpacerItem(new QSpacerItem(0, spacing, QSizePolicy::Minimum, QSizePolicy::Fixed));
    }
}

void SideBarWidget::addCenterSpacing(int spacing)
{
    if (m_centerLayout && spacing > 0) {
        m_centerLayout->addSpacerItem(new QSpacerItem(0, spacing, QSizePolicy::Minimum, QSizePolicy::Fixed));
    }
}

void SideBarWidget::setToolsConfig(const QList<SideBarConstants::ToolInfo>& toolsConfig)
{

    // 保存配置
    m_toolsConfig = toolsConfig;

    if (!m_layout) {
        qWarning() << "SideBarWidget: 布局未初始化";
        return;
    }

    // 清除现有组件
    clearExistingComponents();

    // 重新构建UI
    rebuildUIFromConfig();

    // 强制更新布局（使用QTimer延迟执行，确保布局完全构建完成）
    QTimer::singleShot(0, [this]() {
        if (m_centerLayout) {
            m_centerLayout->update();
            m_centerLayout->activate();
        }
        if (m_layout) {
            m_layout->update();
            m_layout->activate();
        }

        // 强制重新计算和应用几何信息
        adjustSize();
        updateGeometry();

        // 如果是第一次设置工具配置且还没有设置初始位置，现在设置
        if (!m_initialPositionSet) {
            setInitialPosition();
            m_initialPositionSet = true;
        } else if (m_isExpanded) {
            // 重新计算并应用展开状态的几何尺寸
            updatePositionRelativeToMainWindow();
        }

        // 强制重绘
        update();
    });
}

void SideBarWidget::clearExistingComponents()
{
    // 只清除中心动态区域的内容
    if (m_centerLayout) {
        // 清除动态工具按钮
        for (QWidget* button : m_toolButtons) {
            if (button) {
                m_centerLayout->removeWidget(button);
                button->deleteLater();
            }
        }
        m_toolButtons.clear();

        // 清除中心布局中的所有其他项目
        QLayoutItem* item;
        while ((item = m_centerLayout->takeAt(0)) != nullptr) {
            if (item->widget()) {
                item->widget()->deleteLater();
            }
            delete item;
        }
    }
}

void SideBarWidget::updateWidgetsVisibility(bool visible)
{
    if (m_layout) {
        // // 遍历主布局中的所有组件
        for (int i = 0; i < m_layout->count(); ++i) {
            QLayoutItem* item = m_layout->itemAt(i);
            if (item && item->widget()) {
                QWidget* widget = item->widget();

                widget->setVisible(visible);
            }
        }
    }
}

void SideBarWidget::rebuildUIFromConfig()
{
    // 在中心动态区域重新构建UI布局
    if (!m_centerLayout) {
        return;
    }

    // 按照配置顺序处理，将连续的Grid类型工具分组
    QList<SideBarConstants::ToolInfo> currentGridGroup;
    bool isFirstItem = true;

    for (int i = 0; i < m_toolsConfig.size(); ++i) {
        const auto& toolInfo = m_toolsConfig[i];

        if (toolInfo.viewType == SideBarConstants::ViewType::Grid) {
            // 收集连续的Grid类型工具
            currentGridGroup.append(toolInfo);
        } else {
            // 遇到非Grid类型，先处理之前收集的Grid组
            if (!currentGridGroup.isEmpty()) {
                // 如果不是第一个组件，先添加间距
                if (!isFirstItem) {
                    addCenterSpacing(SideBarConstants::getButtonTopMargin());
                }
                
                QWidget* gridContainer = createGridContainer(currentGridGroup);
                if (gridContainer) {
                    m_centerLayout->addWidget(gridContainer, 0, Qt::AlignTop | Qt::AlignCenter);
                    isFirstItem = false;
                }
                currentGridGroup.clear();
            }

            // 处理当前的非Grid类型组件
            QWidget* widget = createButtonByType(toolInfo);
            if (widget) {
                // 如果不是第一个组件，先添加间距
                if (!isFirstItem) {
                    if (toolInfo.viewType != SideBarConstants::ViewType::Divide) {
                        addCenterSpacing(SideBarConstants::getButtonTopMargin());
                    } else {
                        addCenterSpacing(SideBarConstants::getSmallButtonSpacing());
                    }
                }
                
                m_centerLayout->addWidget(widget, 0, Qt::AlignTop | Qt::AlignCenter);

                // 根据类型添加到相应的列表
                if (toolInfo.viewType == SideBarConstants::ViewType::Normal) {
                    m_toolButtons.append(widget);
                }
                
                isFirstItem = false;
            }
        }
    }

    // 处理最后剩余的Grid组（如果配置列表以Grid类型结尾）
    if (!currentGridGroup.isEmpty()) {
        // 如果不是第一个组件，先添加间距
        if (!isFirstItem) {
            addCenterSpacing(SideBarConstants::getButtonTopMargin());
        }
        
        QWidget* gridContainer = createGridContainer(currentGridGroup);
        if (gridContainer) {
            m_centerLayout->addWidget(gridContainer, 0, Qt::AlignTop | Qt::AlignCenter);
        }
    }

    m_centerLayout->addStretch(1);

}

QWidget* SideBarWidget::createButtonByType(const SideBarConstants::ToolInfo& toolInfo)
{
    switch (toolInfo.viewType) {
        case SideBarConstants::ViewType::Normal:
            return createFunctionButton(toolInfo.toolIcon, toolInfo.toolName, toolInfo);

        case SideBarConstants::ViewType::Grid:
            // Grid类型在rebuildUIFromConfig中统一处理，这里不应该被调用
            qWarning() << "SideBarWidget: Grid类型应该在rebuildUIFromConfig中统一处理";
            return nullptr;

        case SideBarConstants::ViewType::Divide:
            return createDivider();

        default:
            qWarning() << "SideBarWidget: 未知的视图类型:" << static_cast<int>(toolInfo.viewType);
            return nullptr;
    }
}

QWidget* SideBarWidget::createGridContainer(const QList<SideBarConstants::ToolInfo>& gridTools)
{
    QWidget* container = new QWidget(this);
    QGridLayout* gridLayout = new QGridLayout(container);
    gridLayout->setContentsMargins(0, 0, 0, 0);
    gridLayout->setSpacing(SideBarConstants::getSmallButtonSpacing());

    int row = 0, col = 0;
    for (const auto& toolInfo : gridTools) {
        QWidget* button = createSmallButton(toolInfo.toolIcon, toolInfo);
        if (button) {
            gridLayout->addWidget(button, row, col, Qt::AlignCenter);

            col++;
            if (col >= 2) { // 一行两个
                col = 0;
                row++;
            }
        }
    }

    // 设置容器尺寸
    int containerWidth = SideBarConstants::getSmallButtonSize() * 2 + SideBarConstants::getSmallButtonSpacing();
    int containerHeight = SideBarConstants::getSmallButtonSize() * ((gridTools.size() + 1) / 2) +
                         SideBarConstants::getSmallButtonSpacing() * (((gridTools.size() + 1) / 2) - 1);
    container->setFixedSize(containerWidth, containerHeight);

    return container;
}

void SideBarWidget::showResourceButtonToolTip(const QString& text, int durationMs) {
    if (QThread::currentThread() != qApp->thread()) {
        QString textCopy = text;
        int durationCopy = durationMs;
        QMetaObject::invokeMethod(this, [this, textCopy, durationCopy]() {
            showResourceButtonToolTip(textCopy, durationCopy);
        }, Qt::QueuedConnection);
        return;
    }
    static CustomToolTip* tip = nullptr;
    if (!tip) tip = new CustomToolTip(nullptr);
    if (!m_resourcesButton) return;

    int btnLeft = m_resourcesButton->mapToGlobal(QPoint(0, 0)).x();
    int btnCenterY = m_resourcesButton->mapToGlobal(m_resourcesButton->rect().center()).y();
    int tooltipOffset = -ScreenAdaptationConstants::adaptSize(8); // 适配8px
    QPoint tooltipAnchor(btnLeft + tooltipOffset, btnCenterY);
    tip->showTip(tooltipAnchor, text, durationMs);
}

void SideBarWidget::setMainWindow(QWidget* mainWindow)
{
    m_mainWindow = mainWindow;
}

void SideBarWidget::updatePositionRelativeToMainWindow()
{
    // 始终使用屏幕尺寸进行计算，避免主窗口尺寸变化的影响
    QSize screenSize = ScreenUtils::getScreenSize();

    // 计算目标尺寸
    int width, height;
    if (m_isExpanded) {
        width = SideBarConstants::getExpandedWidth();
        height = calculateContentHeight();
        if (height <= 0) {
            height = SideBarConstants::getExpandedHeight();
        }
    } else {
        width = SideBarConstants::getCollapsedClickAreaWidth();
        height = SideBarConstants::getCollapsedHeight();
    }

    // 计算位置（始终基于屏幕尺寸）
    int x, y;
    if (m_isExpanded) {
        // 展开状态：紧贴屏幕右边
        x = screenSize.width() - width;
    } else {
        // 收起状态：需要考虑右边距，保持视觉居中
        int rightMargin = SideBarConstants::getCollapsedRightMargin();
        int visualWidth = SideBarConstants::getCollapsedWidth();
        int visualCenterFromRight = visualWidth / 2 + rightMargin;
        x = screenSize.width() - visualCenterFromRight - width / 2;
    }

    // 垂直居中
    y = (screenSize.height() - height) / 2;

    move(x, y);
    resize(width, height);
}

void SideBarWidget::showIndependentWindow()
{
    // 如果还没有设置初始位置，先设置初始位置
    if (!m_initialPositionSet) {
        setInitialPosition();
        m_initialPositionSet = true;
    } else {
        // 独立窗口模式显示，先更新位置再显示
        updatePositionRelativeToMainWindow();
    }

    show();
}

void SideBarWidget::hideIndependentWindow()
{
    hide();
}

void SideBarWidget::setInitialPosition()
{
    // 独立窗口模式，优先相对于主窗口定位
    if (m_mainWindow) {
        updatePositionRelativeToMainWindow();
    } else {
        // 如果没有主窗口引用，使用屏幕右侧居中位置
        QSize screenSize = ScreenUtils::getScreenSize();
        int width = SideBarConstants::getExpandedWidth();
        int height = calculateContentHeight();

        // 确保高度有效
        if (height <= 0) {
            height = SideBarConstants::getExpandedHeight();
        }

        // 位置：屏幕右侧，垂直居中
        int x = screenSize.width() - width;
        int y = (screenSize.height() - height) / 2;

        setGeometry(x, y, width, height);
    }
}

void SideBarWidget::ensureValidGeometry()
{
    // 如果还没有设置初始位置，先设置
    if (!m_initialPositionSet) {
        setInitialPosition();
        m_initialPositionSet = true;
        return;
    }

    // 检查当前几何状态是否有效
    QRect currentGeometry = geometry();
    qDebug() << "SideBarWidget: X - " << currentGeometry.x() << "Y - " << currentGeometry.y() << "WIDTH - " << currentGeometry.width() << "HEIGHT - " << currentGeometry.height();

    // 更严格的几何状态验证
    QSize screenSize = ScreenUtils::getScreenSize();
    bool isInvalidPosition = currentGeometry.x() < 0 || currentGeometry.y() < 0 ||
                            currentGeometry.x() >= screenSize.width() || currentGeometry.y() >= screenSize.height();
    bool isInvalidSize = currentGeometry.width() <= 0 || currentGeometry.height() <= 0;
    bool isAtOrigin = currentGeometry.x() == 0 && currentGeometry.y() == 0;

    if (!currentGeometry.isValid() || currentGeometry.isEmpty() || isInvalidPosition || isInvalidSize || isAtOrigin) {
        qDebug() << "SideBarWidget: 检测到无效几何状态，强制重新初始化";
        updatePositionRelativeToMainWindow();

        // 确保更新立即生效
        QApplication::processEvents();
    }
}

void SideBarWidget::openWidget(const QString& toolName)
{
    // 查找对应的工具配置
    for (const auto& toolInfo : m_toolsConfig) {
        if (toolInfo.toolName == toolName) {
            // 触发按钮点击信号，让外部处理工具打开逻辑
            emit buttonClicked(toolInfo);
            return;
        }
    }
    
    // 如果没有找到对应的工具，尝试查找固定按钮
    if (toolName == "资源") {
        // 获取资源按钮配置并触发信号
        SideBarConstants::ToolInfo resourcesToolInfo = SideBarConfigManager::instance()->getResourcesToolInfo();
        emit buttonClicked(resourcesToolInfo);
    } else if (toolName == "工具箱") {
        // 获取工具箱按钮配置并触发信号
        SideBarConstants::ToolInfo toolboxToolInfo = SideBarConfigManager::instance()->getToolboxToolInfo();
        emit buttonClicked(toolboxToolInfo);
    } else if (toolName == "退出") {
        // 获取退出按钮配置并触发信号
        SideBarConstants::ToolInfo exitToolInfo = SideBarConfigManager::instance()->getExitToolInfo();
        emit buttonClicked(exitToolInfo);
    }
    
    qDebug() << "SideBarWidget: 尝试打开工具" << toolName;
}




