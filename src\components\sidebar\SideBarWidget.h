#ifndef SIDEBARWIDGET_H
#define SIDEBARWIDGET_H

#include <QWidget>
#include <QPushButton>
#include <QPropertyAnimation>
#include <QVBoxLayout>
#include <QPaintEvent>
#include <QMouseEvent>
#include <QLabel>
#include <QTimer>
#include <QFrame>
#include <QSvgWidget>
#include <QGridLayout>
#include <QScrollArea>
#include "SideBarConstants.h"
#include "../../utils/ScreenUtils.h"

/**
 * @brief 侧边栏组件
 * 
 * 提供可展开/收起的侧边栏功能，位于WhiteboardView最右边
 * 特性：
 * - 展开状态：160x1679px，黑色半透明背景
 * - 收起状态：9x234px，渐变背景，距右边84px
 * - 支持点击切换状态，带平滑动画效果
 * - 圆角4.5px，使用屏幕适配
 */
class SideBarWidget : public QWidget
{
    Q_OBJECT
    
    // 动画属性
    Q_PROPERTY(QRect geometry READ geometry WRITE setGeometry)
    Q_PROPERTY(qreal opacity READ windowOpacity WRITE setWindowOpacity)

public:
    /**
     * @brief 构造函数
     * @param parent 父组件
     */
    explicit SideBarWidget(QWidget *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SideBarWidget();
    
    /**
     * @brief 获取是否展开状态
     * @return true表示展开，false表示收起
     */
    bool isExpanded() const { return m_isExpanded; }
    
    /**
     * @brief 设置展开状态
     * @param expanded 是否展开
     * @param animated 是否使用动画
     */
    void setExpanded(bool expanded, bool animated = true);
    
    /**
     * @brief 切换展开/收起状态
     * @param animated 是否使用动画
     */
    void toggleExpanded(bool animated = true);
    
    /**
     * @brief 更新位置（在父组件大小改变时调用）
     */
    void updatePosition();

    /**
     * @brief 更新内容高度（在添加或移除内容时调用）
     */
    void updateContentSize();

    /**
     * @brief 设置工具配置
     * @param toolsConfig 工具配置列表
     */
    void setToolsConfig(const QList<SideBarConstants::ToolInfo>& toolsConfig);

    /**
     * @brief 显示资源按钮提示
     * @param text 提示文本
     * @param durationMs 提示显示时长（毫秒）
     */
    void showResourceButtonToolTip(const QString& text, int durationMs = 5000);

    /**
     * @brief 设置主窗口引用（用于独立窗口定位）
     * @param mainWindow 主窗口指针
     */
    void setMainWindow(QWidget* mainWindow);

    /**
     * @brief 更新相对于主窗口的位置
     */
    void updatePositionRelativeToMainWindow();

    /**
     * @brief 显示独立窗口
     */
    void showIndependentWindow();

    /**
     * @brief 隐藏独立窗口
     */
    void hideIndependentWindow();

    /**
     * @brief 打开指定工具窗口
     * @param toolName 工具名称
     */
    void openWidget(const QString& toolName);

signals:
    /**
     * @brief 展开状态改变信号
     * @param expanded 新的展开状态
     */
    void expandedChanged(bool expanded);

    /**
     * @brief 收起按钮点击信号
     */
    void collapseRequested();

    /**
     * @brief 按钮点击信号
     * @param toolInfo 工具信息（包含视图类型、点击类型、工具名称、配置信息等）
     */
    void buttonClicked(const SideBarConstants::ToolInfo& toolInfo);

protected:
    /**
     * @brief 绘制事件
     */
    void paintEvent(QPaintEvent *event) override;

    /**
     * @brief 鼠标按下事件
     */
    void mousePressEvent(QMouseEvent *event) override;

    /**
     * @brief 鼠标释放事件
     */
    void mouseReleaseEvent(QMouseEvent *event) override;

    /**
     * @brief 事件过滤器（处理收起按钮点击）
     */
    bool eventFilter(QObject *obj, QEvent *event) override;

private slots:
    /**
     * @brief 展开动画完成槽函数
     */
    void onExpandAnimationFinished();

    /**
     * @brief 收起动画完成槽函数
     */
    void onCollapseAnimationFinished();

    /**
     * @brief 移出屏幕动画完成槽函数
     */
    void onMoveOutAnimationFinished();

    /**
     * @brief 移入屏幕动画完成槽函数
     */
    void onMoveInAnimationFinished();

    /**
     * @brief 收起按钮点击槽函数
     */
    void onCollapseButtonClicked();

    /**
     * @brief 更新时间显示槽函数
     */
    void updateTimeDisplay();



private:
    /**
     * @brief 初始化UI
     */
    void initializeUI();
    
    /**
     * @brief 初始化动画
     */
    void initializeAnimations();

    /**
     * @brief 获取有效的父组件
     * @return 有效的父组件指针，如果无效则返回nullptr
     */
    QWidget* getValidParentWidget() const;

    /**
     * @brief 计算展开状态的几何位置
     */
    QRect calculateExpandedGeometry() const;
    
    /**
     * @brief 计算收起状态的几何位置
     */
    QRect calculateCollapsedGeometry() const;

    /**
     * @brief 计算正确的目标几何（绝对屏幕坐标）
     * @return 目标几何尺寸（绝对坐标）
     */
    QRect calculateCorrectTargetGeometry() const;

    /**
     * @brief 计算当前状态的正确几何位置
     * @return 当前状态应该的正确几何位置
     */
    QRect calculateCorrectCurrentGeometry() const;

    /**
     * @brief 设置初始位置
     */
    void setInitialPosition();

    /**
     * @brief 确保几何状态有效，防止动画从无效位置开始
     */
    void ensureValidGeometry();

    /**
     * @brief 计算屏幕外位置（用于动画过渡，保持当前尺寸）
     */
    QRect calculateOffScreenGeometry() const;

    /**
     * @brief 计算屏幕外位置（用于目标状态，使用目标尺寸）
     */
    QRect calculateOffScreenGeometryForTarget() const;

    /**
     * @brief 计算内容所需的高度
     */
    int calculateContentHeight() const;
    
    /**
     * @brief 绘制展开状态背景
     */
    void drawExpandedBackground(QPainter *painter);
    
    /**
     * @brief 绘制收起状态背景
     */
    void drawCollapsedBackground(QPainter *painter);

    /**
     * @brief 创建功能按钮
     * @param iconPath 图标路径
     * @param text 按钮文字
     * @param toolInfo 工具信息（用于点击事件识别）
     * @return 创建的按钮组件
     */
    QWidget* createFunctionButton(const QString& iconPath, const QString& text, const SideBarConstants::ToolInfo& toolInfo = SideBarConstants::ToolInfo());

    /**
     * @brief 创建分割栏
     * @return 创建的分割栏组件
     */
    QFrame* createDivider();

    /**
     * @brief 创建小尺寸按钮（仅图标，无文字）
     * @param iconPath 图标路径
     * @param toolInfo 工具信息（用于点击事件识别）
     * @return 创建的按钮组件
     */
    QWidget* createSmallButton(const QString& iconPath, const SideBarConstants::ToolInfo& toolInfo = SideBarConstants::ToolInfo());

    /**
     * @brief 创建收起按钮（特殊尺寸：视觉22x18，点击区域40x40）
     * @param iconPath 图标路径
     * @return 创建的按钮组件
     */
    QWidget* createCollapseButton(const QString& iconPath);

    /**
     * @brief 创建小按钮容器（grid布局）
     * @return 创建的容器组件
     */
    QWidget* createSmallButtonsContainer();

    /**
     * @brief 添加间距到主布局
     * @param spacing 间距大小
     */
    void addSpacing(int spacing);

    /**
     * @brief 添加间距到中心布局
     * @param spacing 间距大小
     */
    void addCenterSpacing(int spacing);

    /**
     * @brief 创建固定的顶部按钮
     */
    void createFixedTopButtons();

    /**
     * @brief 创建中心动态区域
     */
    void createCenterDynamicArea();

    /**
     * @brief 创建固定的底部按钮
     */
    void createFixedBottomButtons();

    /**
     * @brief 创建图标组件（SVG或占位符）
     * @param iconPath 图标路径
     * @param size 图标尺寸
     * @param parent 父组件
     * @return 创建的图标组件
     */
    QWidget* createIconWidget(const QString& iconPath, const QSize& size, QWidget* parent);

    /**
     * @brief 根据视图类型创建不同的UI元素
     * @param toolInfo 工具信息
     * @return 创建的组件
     */
    QWidget* createButtonByType(const SideBarConstants::ToolInfo& toolInfo);

    /**
     * @brief 创建网格容器
     * @param gridTools 网格工具列表
     * @return 创建的网格容器
     */
    QWidget* createGridContainer(const QList<SideBarConstants::ToolInfo>& gridTools);

    /**
     * @brief 根据配置重新构建UI
     */
    void rebuildUIFromConfig();

    /**
     * @brief 清除现有组件
     */
    void clearExistingComponents();

    /**
     * @brief 更新所有组件的可见性
     * @param visible 是否可见
     */
    void updateWidgetsVisibility(bool visible);

    /**
     * @brief 处理鼠标按下事件
     * @param obj 事件对象
     * @param mouseEvent 鼠标事件
     * @return 是否处理了事件
     */
    bool handleMousePress(QObject *obj, QMouseEvent *mouseEvent);



private:
    // 状态
    bool m_isExpanded;                      ///< 是否展开状态
    bool m_isAnimating;                     ///< 是否正在动画中
    bool m_targetExpanded;                  ///< 动画目标状态
    
    // UI组件
    QWidget* m_collapseButton;              ///< 收起按钮
    QVBoxLayout* m_layout;                  ///< 主布局管理器
    QVBoxLayout* m_centerLayout;            ///< 中心动态区域布局
    QWidget* m_centerContainer;             ///< 中心动态区域容器
    QLabel* m_timeLabel;                    ///< 时间显示标签
    QTimer* m_timeTimer;                    ///< 时间更新定时器
    QWidget* m_resourcesButton;             ///< 资源按钮
    QWidget* m_toolboxButton;               ///< 工具箱按钮
    QFrame* m_topDivider;                      ///< 顶部分割栏
    QList<QWidget*> m_toolButtons;          ///< 动态工具按钮列表
    QFrame* m_bottomDivider;                ///< 底部分割栏
    QWidget* m_exitButton;                  ///< 退出按钮
    QWidget* m_smallButtonsContainer;       ///< 小按钮容器
    QWidget* m_desktopButton;               ///< 桌面按钮
    QWidget* m_appMarketButton;             ///< 应用市场按钮
    QWidget* m_wifiButton;                  ///< WiFi按钮

    // 新的工具配置存储
    QList<SideBarConstants::ToolInfo> m_toolsConfig;  ///< 工具配置列表

    // 动画
    QPropertyAnimation* m_geometryAnimation; ///< 几何位置动画
    QPropertyAnimation* m_opacityAnimation;  ///< 透明度动画
    QPropertyAnimation* m_moveOutAnimation;  ///< 移出屏幕动画
    QPropertyAnimation* m_moveInAnimation;   ///< 移入屏幕动画

    // 鼠标交互
    bool m_mousePressed;                    ///< 鼠标是否按下
    QPoint m_mousePressPos;                 ///< 鼠标按下位置

    // 独立窗口管理
    QWidget* m_mainWindow;                  ///< 主窗口引用（用于定位）
    bool m_initialPositionSet;              ///< 是否已设置初始位置
};

#endif // SIDEBARWIDGET_H
