//
// Created by HLJ<PERSON> on 2025/6/30.
//

#include "MagnifierButtonPanelWidget.h"
#include <QStyleOption>
#include <QIcon>
#include <src/screen_adaptation/ScreenAdaptationManager.h>
#include <QPainterPath>
#include <QPen>
#include "MagnifierIconButton.h"

MagnifierButtonPanelWidget::MagnifierButtonPanelWidget(QWidget *parent) : QWidget(parent)
{
    setAttribute(Qt::WA_TranslucentBackground);
    setWindowFlags(Qt::FramelessWindowHint | Qt::Tool | Qt::WindowStaysOnTopHint);

    setFixedSize(ScreenAdaptationConstants::adaptSize(307), ScreenAdaptationConstants::adaptSize(122));

    // 创建布局
    QHBoxLayout *layout = new QHBoxLayout(this);
//    layout->setContentsMargins(16, 8, 16, 8);
//    layout->setSpacing(20); // 增加按钮之间的间距
//
    // 创建自定义按钮
    m_lightBtn = new MagnifierIconButton(this);
    m_lightBtn->setSvgIcon(":/images/magnifier/close_light.svg");
    m_lightBtn->setText("关灯");

    m_closeBtn = new MagnifierIconButton(this);
    m_closeBtn->setSvgIcon(":/images/magnifier/close_magnifier.svg");
    m_closeBtn->setText("关闭");

    // 添加到布局
    layout->addWidget(m_lightBtn, /*stretch*/1);
    layout->addWidget(m_closeBtn, /*stretch*/1);
    layout->setSpacing(0);
    int marginV = ScreenAdaptationConstants::adaptSize(2); // 间距
    layout->setContentsMargins(marginV, marginV, marginV, marginV); // 四周2px间距
    layout->setAlignment(Qt::AlignCenter);

    // 连接信号
    connect(m_lightBtn, &MagnifierIconButton::clicked, this, &MagnifierButtonPanelWidget::onLightToggle);
    connect(m_closeBtn, &MagnifierIconButton::clicked, this, &MagnifierButtonPanelWidget::closeClicked);

    setLightButtonStyle(true);

    m_lightBtn->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    m_closeBtn->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    m_lightBtn->setCornerType(MagnifierIconButton::Left);
    m_closeBtn->setCornerType(MagnifierIconButton::Right);
}

void MagnifierButtonPanelWidget::setLightButtonStyle(bool isOn) {
    if (isOn) {
        m_lightBtn->setSvgIcon(":/images/magnifier/close_light.svg");
        m_lightBtn->setText("关灯");
        m_lightBtn->setBackgroundColor(Qt::white);
        m_lightBtn->setContentColor(QColor("#707070"));
    } else {
        m_lightBtn->setSvgIcon(":/images/magnifier/open_light.svg");
        m_lightBtn->setText("开灯");
        m_lightBtn->setBackgroundColor(QColor("#5F52E3"));
        m_lightBtn->setContentColor(QColor("#5F52E3"));
    }
    m_closeBtn->setBackgroundColor(Qt::white);
    m_closeBtn->setContentColor(QColor("#707070"));
}

void MagnifierButtonPanelWidget::onLightToggle() {
    m_lightOn = !m_lightOn;
    // 更新按钮文字和图标颜色
    setLightButtonStyle(m_lightOn);

    emit lightToggled(m_lightOn);
}

void MagnifierButtonPanelWidget::paintEvent(QPaintEvent* event) {
    Q_UNUSED(event);

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);  // 抗锯齿

    // 创建圆角矩形路径
    QPainterPath path;
    path.addRoundedRect(rect(), ScreenAdaptationConstants::adaptSize(20), ScreenAdaptationConstants::adaptSize(20));  // 圆角半径

    // 填充白色背景
    painter.fillPath(path, Qt::white);

    // 添加2px #979797边框
    QPen borderPen(QColor("#979797"));
    borderPen.setWidth(ScreenAdaptationConstants::adaptSize(2));
    painter.setPen(borderPen);
    painter.drawPath(path);
}
