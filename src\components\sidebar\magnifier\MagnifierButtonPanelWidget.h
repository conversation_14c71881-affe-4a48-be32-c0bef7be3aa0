//
// Created by <PERSON><PERSON>J<PERSON> on 2025/6/30.
//

#ifndef MAGNIFIERBUTTONPANELWIDGET_H
#define MAGNIFIERBUTTONPANELWIDGET_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QPixmap>
#include <QTimer>
#include <QScreen>
#include <QApplication>
#include <QMouseEvent>
#include <QPainter>
#include <QGraphicsOpacityEffect>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QToolButton>
#include "MagnifierIconButton.h"

class MagnifierButtonPanelWidget : public QWidget {
Q_OBJECT

public:
    explicit MagnifierButtonPanelWidget(QWidget *parent = nullptr);

signals:
    /**
     * 信号：灯的开关状态改变
     */
    void lightToggled(bool isOn);

    /**
     * 信号：关闭按钮被点击
     */
    void closeClicked();

private:
    void paintEvent(QPaintEvent*) override;

    /**
     * 开关灯按钮
     */
    MagnifierIconButton* m_lightBtn;
    /**
     * 关闭按钮
     */
    MagnifierIconButton* m_closeBtn;

    /**
     * 灯的开关状态
     */
    bool m_lightOn = true;

private slots:

    void onLightToggle();

    void setLightButtonStyle(bool isOn);
};


#endif //MAGNIFIERBUTTONPANELWIDGET_H
