﻿//
// Created by HLJ<PERSON> on 2025/7/1.
//

#include "MagnifierFullWidget.h"

MagnifierFullWidget::MagnifierFullWidget(QWidget *parent, ZIndexManager *zIndexManager) : QWidget(parent), m_zIndexManager(zIndexManager) {
    setAttribute(Qt::WA_DeleteOnClose);
    setAttribute(Qt::WA_TranslucentBackground);

    setObjectName("MagnifierFullWidget");

    setupUI();

    setConnections();

    m_magnifierWidget->captureScreen();
}

void MagnifierFullWidget::setupUI() {
    m_maskWidget = new MagnifierScreenMaskWidget(this);
    m_maskWidget->show();
    if (m_zIndexManager) {
        m_zIndexManager->registerComponent(m_maskWidget, ZIndexLevel::QT_MAGNIFIER, ComponentType::SIDEBAR, "MagnifierMaskWidget");
    }

    m_magnifierWidget = new MagnifierWidget(this);
    m_magnifierWidget->show();
    if (m_zIndexManager) {
        m_zIndexManager->registerComponent(m_magnifierWidget, ZIndexLevel::QT_MAGNIFIER, ComponentType::SIDEBAR, "MagnifierMaskWidget");
    }

}

MagnifierFullWidget::~MagnifierFullWidget() {
    if (m_zIndexManager) {
        m_zIndexManager->unregisterComponent(m_maskWidget);
        m_zIndexManager->unregisterComponent(m_magnifierWidget);
    }
}

void MagnifierFullWidget::setConnections() {
    connect(m_magnifierWidget, &MagnifierWidget::lightToggled, this, [this](bool isOn) {
        if (isOn) {
            if (m_maskWidget) {
                m_maskWidget->changeMaskColor(QColor(0, 0, 0, 1));
            }
        }
        else {
            if (m_maskWidget) {
                m_maskWidget->changeMaskColor(QColor(0, 0, 0, 128));
            }
        }
    });

    connect(m_magnifierWidget, &QWidget::destroyed, m_maskWidget, &QWidget::close);
    connect(m_magnifierWidget, &MagnifierWidget::closeClicked, this, [this]() {
        close();
    });

    connect(m_maskWidget, &MagnifierScreenMaskWidget::maskClicked, this, [this]() {
        m_magnifierWidget->showOnTop();
    });

    connect(m_magnifierWidget, &MagnifierWidget::screenCaptured, m_maskWidget, &MagnifierScreenMaskWidget::setScreenCapture);
}

