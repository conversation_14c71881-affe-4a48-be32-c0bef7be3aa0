﻿//
// Created by <PERSON><PERSON>J<PERSON> on 2025/7/1.
//

#ifndef HL_WHITEBOARD_QT_MAGNIFIERFULLWIDGET_H
#define HL_WHITEBOARD_QT_MAGNIFIERFULLWIDGET_H

#include <QWidget>
#include "MagnifierScreenMaskWidget.h"
#include "MagnifierWidget.h"
#include "../../whiteboardview/ZIndexManager.h"

class MagnifierScreenMaskWidget;

class MagnifierFullWidget : public QWidget {
    Q_OBJECT

public:
    explicit MagnifierFullWidget(QWidget *parent = nullptr, ZIndexManager *zIndexManager = nullptr);
    ~MagnifierFullWidget() override;


private:
    MagnifierScreenMaskWidget* m_maskWidget;
    MagnifierWidget* m_magnifierWidget;

    void setupUI();

    void setConnections();

    ZIndexManager* m_zIndexManager;
};


#endif //HL_WHITEBOARD_QT_MAGNIFIERFULLWIDGET_H
