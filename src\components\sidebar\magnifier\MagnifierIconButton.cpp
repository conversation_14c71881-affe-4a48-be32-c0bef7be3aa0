#include "MagnifierIconButton.h"
#include <QMouseEvent>
#include <QFont>
#include <src/screen_adaptation/ScreenAdaptationManager.h>
#include <QPainter>
#include <QPainterPath>
#include <QSvgWidget>

MagnifierIconButton::MagnifierIconButton(QWidget* parent)
    : QWidget(parent)
{
    int iconSize = ScreenAdaptationConstants::adaptSize(43);
    int fontSize = ScreenAdaptationConstants::adaptSize(20);
    int marginV = ScreenAdaptationConstants::adaptSize(14);

    m_iconContainer = new QWidget(this);
    QHBoxLayout* iconLayout = new QHBoxLayout(m_iconContainer);
    iconLayout->setContentsMargins(0, 0, 0, 0);
    iconLayout->setSpacing(0);

    m_iconLabel = new QLabel(this);
    m_iconLabel->setAlignment(Qt::AlignCenter);
    m_iconLabel->setStyleSheet("QLabel { color: #979797; }");

    m_textLabel = new QLabel(this);
    m_textLabel->setAlignment(Qt::AlignCenter);
    QFont font = m_textLabel->font();
    font.setPointSize(fontSize);
    font.setBold(true);
    m_textLabel->setFont(font);
    m_textLabel->setStyleSheet("QLabel { color: #979797; }");

    m_layout = new QVBoxLayout(this);
    m_layout->setSpacing(ScreenAdaptationConstants::adaptSize(6));
    m_layout->setContentsMargins(0, marginV, 0, marginV);
    m_layout->addWidget(m_iconContainer, 0, Qt::AlignHCenter);
    m_layout->addWidget(m_textLabel);

    setCursor(Qt::PointingHandCursor);
}

void MagnifierIconButton::setSvgIcon(const QString& svgPath) {
    int iconSize = ScreenAdaptationConstants::adaptSize(43);
    if (!m_svgWidget) {
        m_svgWidget = new QSvgWidget(this);
        m_svgWidget->setFixedSize(iconSize, iconSize);
        m_svgWidget->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    }
    QHBoxLayout* iconLayout = qobject_cast<QHBoxLayout*>(m_iconContainer->layout());
    if (iconLayout) {
        QLayoutItem* child;
        while ((child = iconLayout->takeAt(0)) != nullptr) {
            if (child->widget()) child->widget()->setParent(nullptr);
            delete child;
        }
        iconLayout->addStretch();
        iconLayout->addWidget(m_svgWidget);
        iconLayout->addStretch();
    }
    m_svgWidget->load(svgPath);
    m_svgWidget->setVisible(true);
    if (m_iconLabel) m_iconLabel->setVisible(false);
}

void MagnifierIconButton::setIcon(const QPixmap& icon) {
    if (m_svgWidget) m_svgWidget->setVisible(false);
    if (m_iconLabel) {
        m_iconLabel->setPixmap(icon.scaled(ScreenAdaptationConstants::adaptSize(43), ScreenAdaptationConstants::adaptSize(43), Qt::KeepAspectRatio, Qt::SmoothTransformation));
        m_iconLabel->setVisible(true);
        QHBoxLayout* iconLayout = qobject_cast<QHBoxLayout*>(m_iconContainer->layout());
        if (iconLayout) {
            QLayoutItem* child;
            while ((child = iconLayout->takeAt(0)) != nullptr) {
                if (child->widget()) child->widget()->setParent(nullptr);
                delete child;
            }
            iconLayout->addStretch();
            iconLayout->addWidget(m_iconLabel);
            iconLayout->addStretch();
        }
    }
}

void MagnifierIconButton::setText(const QString& text) {
    m_textLabel->setText(text);
}

void MagnifierIconButton::setSelected(bool selected) {
    m_selected = selected;
    // 可根据选中状态调整样式
}

void MagnifierIconButton::mousePressEvent(QMouseEvent* event) {
    if (event->button() == Qt::LeftButton) {
        emit clicked();
    }
}

void MagnifierIconButton::setContentColor(const QColor& color) {
    // 如果icon为#5F52E3（开灯状态），则文字为白色，否则为rgba(0,0,0,0.85)
    if (color == QColor("#5F52E3") || color == QColor(95,82,227)) {
        m_textLabel->setStyleSheet("QLabel { color: #ffffff; }");
    } else {
        m_textLabel->setStyleSheet("QLabel { color: rgba(0,0,0,0.85); }");
    }
    // 设置icon颜色（仅SVG有效）
    m_iconLabel->setStyleSheet(QString("QLabel { color: %1; }").arg(color.name()));
}

void MagnifierIconButton::setBackgroundColor(const QColor& color) {
    m_backgroundColor = color;
    update();
}

void MagnifierIconButton::setCornerType(CornerType type) {
    m_cornerType = type;
    update();
}

void MagnifierIconButton::paintEvent(QPaintEvent* event) {
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    QPainterPath path;
    int radius = ScreenAdaptationConstants::adaptSize(18); // 比bar略小2px
    QRect r = rect();
    if (m_cornerType == Left) {
        path.moveTo(r.right(), r.top());
        path.lineTo(r.left() + radius, r.top());
        path.quadTo(r.left(), r.top(), r.left(), r.top() + radius);
        path.lineTo(r.left(), r.bottom() - radius);
        path.quadTo(r.left(), r.bottom(), r.left() + radius, r.bottom());
        path.lineTo(r.right(), r.bottom());
        path.closeSubpath();
    } else if (m_cornerType == Right) {
        path.moveTo(r.left(), r.top());
        path.lineTo(r.right() - radius, r.top());
        path.quadTo(r.right(), r.top(), r.right(), r.top() + radius);
        path.lineTo(r.right(), r.bottom() - radius);
        path.quadTo(r.right(), r.bottom(), r.right() - radius, r.bottom());
        path.lineTo(r.left(), r.bottom());
        path.closeSubpath();
    } else if (m_cornerType == Both) {
        path.addRoundedRect(r, radius, radius);
    } else {
        path.addRect(r);
    }
    painter.fillPath(path, m_backgroundColor);
    QWidget::paintEvent(event);
} 