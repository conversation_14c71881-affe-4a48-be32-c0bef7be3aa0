#pragma once
#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <QSvgWidget>

class MagnifierIconButton : public QWidget {
    Q_OBJECT
public:
    enum CornerType { None, Left, Right, Both };
    explicit MagnifierIconButton(const QPixmap& icon, const QString& text, QWidget* parent = nullptr);
    explicit MagnifierIconButton(QWidget* parent = nullptr);

    void setIcon(const QPixmap& icon);
    void setSvgIcon(const QString& svgPath);
    void setText(const QString& text);
    void setSelected(bool selected);
    void setContentColor(const QColor& color);
    void setBackgroundColor(const QColor& color);
    void setCornerType(CornerType type);

signals:
    void clicked();

protected:
    void mousePressEvent(QMouseEvent* event) override;
    void paintEvent(QPaintEvent* event) override;

private:
    QWidget* m_iconContainer = nullptr;
    QSvgWidget* m_svgWidget = nullptr;
    QLabel* m_iconLabel;
    QLabel* m_textLabel;
    QVBoxLayout* m_layout;
    bool m_selected = false;
    QColor m_backgroundColor = Qt::white;
    CornerType m_cornerType = None;
}; 