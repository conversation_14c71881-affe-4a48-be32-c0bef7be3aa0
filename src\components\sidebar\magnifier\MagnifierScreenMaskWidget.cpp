//
// Created by HLJY on 2025/6/30.
//

#include "MagnifierScreenMaskWidget.h"

MagnifierScreenMaskWidget::MagnifierScreenMaskWidget(QWidget *parent)
        : QWidget(parent)
        , m_maskColor(QColor(0, 0, 0, 1)) // 完全透明会导致鼠标穿透，所以设置一点不透明
{
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    setAttribute(Qt::WA_TranslucentBackground);

    // 获取屏幕尺寸
    QScreen *screen = QApplication::primaryScreen();
    if (screen) {
        setGeometry(screen->geometry());
    }
}

void MagnifierScreenMaskWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);

    qDebug() << "MagnifierScreenMaskWidget::paintEvent - 开始绘制";
    // 绘制屏幕截图
    if (!m_screenCapture.isNull()) {
        qDebug() << "MagnifierScreenMaskWidget::paintEvent - 绘制屏幕截图" << m_screenCapture.size();
        painter.drawPixmap(rect(), m_screenCapture, m_screenCapture.rect());
    }
    qDebug() << "MagnifierScreenMaskWidget::paintEvent - 绘制屏幕截图结束" << rect();

    painter.fillRect(rect(), m_maskColor);
}

void MagnifierScreenMaskWidget::mousePressEvent(QMouseEvent *event)
{
    // 阻止鼠标事件传递到下层窗口
    emit maskClicked();
    event->accept();
}

void MagnifierScreenMaskWidget::mouseMoveEvent(QMouseEvent *event)
{
    // 阻止鼠标事件传递到下层窗口
    event->accept();
}

void MagnifierScreenMaskWidget::mouseReleaseEvent(QMouseEvent *event)
{
    // 阻止鼠标事件传递到下层窗口
    event->accept();
}

void MagnifierScreenMaskWidget::changeMaskColor(QColor color) {
    m_maskColor = color;
    update();
}

void MagnifierScreenMaskWidget::setScreenCapture(QPixmap pixmap) {
    m_screenCapture = pixmap;
}
