//
// Created by HLJY on 2025/6/30.
//

#ifndef MAGNIFIERSCREENMASKWIDGET_H
#define MAGNIFIERSCREENMASKWIDGET_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QPixmap>
#include <QTimer>
#include <QScreen>
#include <QApplication>
#include <QMouseEvent>
#include <QPainter>
#include <QGraphicsOpacityEffect>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QToolButton>

/**
 * 放大镜的遮罩层
 * 用于阻止鼠标事件传递到屏幕上，并且显示一个透明的遮罩层
 */
class MagnifierScreenMaskWidget : public QWidget
{
    Q_OBJECT

public:
    explicit MagnifierScreenMaskWidget(QWidget *parent = nullptr);
    /**
     * 修改遮罩层的颜色
     */
    void changeMaskColor(QColor color);

signals:
    void maskClicked();

    public slots:
    /**
    * 设置屏幕截图
    */
    void setScreenCapture(QPixmap pixmap);
protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private:

    /**
     * 遮罩层的颜色
     */
    QColor m_maskColor;

    /**
     * 屏幕截图
     */
    QPixmap m_screenCapture;
};




#endif //MAGNIFIERSCREENMASKWIDGET_H
