#include "MagnifierWidget.h"
#include <QSlider>
#include <QScreen>
#include <QApplication>
#include <QGuiApplication>
#include <QtGlobal>
#include <QMouseEvent>
#include <QPainter>
#include <QTimer>
#include <QStyleOption>
#include <QIcon>
#include <QDateTime>
#include "src/screen_adaptation/ScreenAdaptationManager.h"
#include <QtConcurrent>
#include <QFutureWatcher>


MagnifierWidget::MagnifierWidget(QWidget *parent)
    : QWidget(parent)
    , m_isDragging(false)
    , m_magnificationFactor(1.3)
    , m_lastCapturePos(-1, -1) // 初始化为无效坐标
    , m_currentDragArea(MagnifierDragArea::None)
    , m_isResizing(false)
    , m_minSize(150) // 设置最小窗口大小为150x150
    , m_screenCaptureDpr(1.0)
    , m_lastWidth(0)
    , m_lastHeight(0)
    , m_lastMoveTime(0)
    , m_resizeStartGlobalPos(0, 0)
    , m_resizeStartGeometry(0, 0, 0, 0)
{
    // 设置窗口属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);

    setAttribute(Qt::WA_DeleteOnClose);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_NoSystemBackground, true);

    // 设置为全屏
    setGeometry(QGuiApplication::primaryScreen()->geometry());

    setMinimumSize(m_minSize, m_minSize);

    // 启用鼠标跟踪，这样即使没有按下鼠标按钮也能接收到鼠标移动事件
    setMouseTracking(true);

    setupUI();

    setConnect();
}

void MagnifierWidget::setConnect() {
    connect(m_buttonPanel, &MagnifierButtonPanelWidget::lightToggled, this, [this](bool isOn) {
        emit lightToggled(isOn);
    });
    connect(m_buttonPanel, &MagnifierButtonPanelWidget::closeClicked, this, [this]() {
        emit closeClicked();
    });
}

MagnifierWidget::~MagnifierWidget()
{
}

void MagnifierWidget::setupUI()
{
    m_label = new QLabel(this);
    // 设置初始位置
    m_label->setGeometry( ScreenAdaptationConstants::adaptSize(2476), ScreenAdaptationConstants::adaptSize(705),
                ScreenAdaptationConstants::adaptSize(880), ScreenAdaptationConstants::adaptSize(880));

    m_label->show();


    // 创建悬浮按钮面板
    m_buttonPanel = new MagnifierButtonPanelWidget(this);
    m_buttonPanel->show();
    syncButtonPanel();
}

// 同步按钮面板始终显示在m_label的下面，并居中显示
void MagnifierWidget::syncButtonPanel()
{
    if (!m_buttonPanel) {
        return;
    }
    QPoint globalBottom = m_label->geometry().bottomLeft();
    int x = globalBottom.x() + (m_label->width() - m_buttonPanel->width()) / 2;
    int y = globalBottom.y() + 12; // 间隔12像素
    m_buttonPanel->move(x, y);
//    m_buttonPanel->raise(); // 保证在最上层
}

void MagnifierWidget::closeEvent(QCloseEvent *event)
{
    if (m_buttonPanel) {
        m_buttonPanel->close();
        m_buttonPanel->deleteLater();
        m_buttonPanel = nullptr;
    }
    event->accept();
}

void MagnifierWidget::captureScreen()
{
    QScreen *screen = QGuiApplication::primaryScreen();
    if (screen) {
        QPixmap screenshot = screen->grabWindow(0);
        m_screenCapture = screenshot;
        m_screenCaptureDpr = screenshot.devicePixelRatio(); // 记录DPR
        emit screenCaptured(m_screenCapture);

        updateMagnifiedContent();
    }
}

bool MagnifierWidget::updateMagnifiedContent()
{
    // 记录耗时
//    qint64 startTime = QDateTime::currentMSecsSinceEpoch();

    if (m_screenCapture.isNull()) {
        captureScreen();
    }

    int borderMargin = m_borderMargin;
    QRect contentRect = m_label->geometry().adjusted(borderMargin, borderMargin, -borderMargin, -borderMargin);
    qreal dpr = m_screenCaptureDpr;

    // 1. 逻辑像素中心点
    QPoint globalCenter = mapToGlobal(contentRect.center());
    // 2. 采集区域逻辑像素大小
    int captureWidth = int(contentRect.width() / m_magnificationFactor);
    int captureHeight = int(contentRect.height() / m_magnificationFactor);
    // 3. 采集区域逻辑像素左上角
    QPoint capturePos = globalCenter - QPoint(captureWidth / 2, captureHeight / 2);
    
    // 获取屏幕尺寸，确保不超出屏幕边界
    QScreen *screen = QGuiApplication::screenAt(globalCenter);
    if (!screen) {
        screen = QGuiApplication::primaryScreen();
    }
    QRect screenGeometry = screen->geometry();
    
    // 确保采集区域不超出屏幕边界
    if (capturePos.x() < screenGeometry.left()) {
        capturePos.setX(screenGeometry.left());
    }
    if (capturePos.y() < screenGeometry.top()) {
        capturePos.setY(screenGeometry.top());
    }
    if (capturePos.x() + captureWidth > screenGeometry.right()) {
        capturePos.setX(screenGeometry.right() - captureWidth);
    }
    if (capturePos.y() + captureHeight > screenGeometry.bottom()) {
        capturePos.setY(screenGeometry.bottom() - captureHeight);
    }
    
    // 4. 转为物理像素
    QRect srcRectPx(
        int(capturePos.x() * dpr),
        int(capturePos.y() * dpr),
        int(captureWidth * dpr),
        int(captureHeight * dpr)
    );

    // 只在窗口或区域变化时才重新裁剪和缩放
    QPoint lastCapturePosPx(int(m_lastCapturePos.x() * dpr), int(m_lastCapturePos.y() * dpr));
    if (srcRectPx.topLeft() == lastCapturePosPx && width() == m_lastWidth && height() == m_lastHeight) {
        m_canMove = 1;
        return false;
    }
    m_lastCapturePos = QPointF(srcRectPx.x() / dpr, srcRectPx.y() / dpr);
    m_lastWidth = width();
    m_lastHeight = height();

    // 确保srcRectPx不超出屏幕截图的边界
    srcRectPx = srcRectPx.intersected(QRect(0, 0, m_screenCapture.width(), m_screenCapture.height()));
    
    QPixmap screenCaptureCopy = m_screenCapture; // 创建副本避免线程安全问题
    QSize contentSizePx(int(contentRect.width() * dpr), int(contentRect.height() * dpr));

    // 采集
    QPixmap capturedRegion = screenCaptureCopy.copy(srcRectPx);

    // 缩放到内容区域物理像素大小
    QPixmap scaledPixmap = capturedRegion.scaled(contentSizePx, Qt::KeepAspectRatio,
                                                 Qt::SmoothTransformation);
    scaledPixmap.setDevicePixelRatio(dpr);

    m_magnifiedPixmap = scaledPixmap;

    m_canMove = 1;

    // 触发重绘
    update();

//    qDebug() << "updateMagnifiedContent setup took" << QDateTime::currentMSecsSinceEpoch() - startTime << "ms";

    return true;
}

MagnifierDragArea MagnifierWidget::getDragArea(const QPoint &pos) const
{
    int borderMargin = m_borderMargin;
    int pointRadius = m_borderMargin + m_borderWidth;
    QRect borderRect = m_label->geometry().adjusted(borderMargin, borderMargin, -borderMargin, -borderMargin);
    
    // 检查八个拖拽点
    QPoint points[8] = {
        borderRect.topLeft(),                    // TopLeft
        QPoint(borderRect.center().x(), borderRect.top()), // Top
        borderRect.topRight(),                   // TopRight
        QPoint(borderRect.right(), borderRect.center().y()), // Right
        borderRect.bottomRight(),                // BottomRight
        QPoint(borderRect.center().x(), borderRect.bottom()), // Bottom
        borderRect.bottomLeft(),                 // BottomLeft
        QPoint(borderRect.left(), borderRect.center().y()) // Left
    };
    
    // 检查鼠标是否在拖拽点附近
    for (int i = 0; i < 8; ++i) {
        if ((pos - points[i]).manhattanLength() <= pointRadius) {
            return static_cast<MagnifierDragArea>(i + 1); // +1 因为枚举从1开始
        }
    }
    
    return MagnifierDragArea::None;
}

void MagnifierWidget::updateCursor(MagnifierDragArea area)
{
    Qt::CursorShape cursor = Qt::ArrowCursor;

    switch (area) {
    case MagnifierDragArea::TopLeft:
        cursor = Qt::SizeFDiagCursor;
        break;
    case MagnifierDragArea::Top:
        cursor = Qt::SizeVerCursor;
        break;
    case MagnifierDragArea::TopRight:
        cursor = Qt::SizeBDiagCursor;
        break;
    case MagnifierDragArea::Right:
        cursor = Qt::SizeHorCursor;
        break;
    case MagnifierDragArea::BottomRight:
        cursor = Qt::SizeFDiagCursor;
        break;
    case MagnifierDragArea::Bottom:
        cursor = Qt::SizeVerCursor;
        break;
    case MagnifierDragArea::BottomLeft:
        cursor = Qt::SizeBDiagCursor;
        break;
    case MagnifierDragArea::Left:
        cursor = Qt::SizeHorCursor;
        break;
    default:
        cursor = Qt::ArrowCursor;
        break;
    }

    if (cursor != lastCursor) {
        setCursor(cursor);
        lastCursor = cursor;
    }
}

void MagnifierWidget::resizeLabelWindow(const QPoint &currentGlobalPos)
{
    QPoint delta = currentGlobalPos - m_resizeStartGlobalPos;
    QRect newGeometry = m_resizeStartGeometry;

    switch (m_currentDragArea) {
    case MagnifierDragArea::TopLeft:
        newGeometry.setTopLeft(m_resizeStartGeometry.topLeft() + delta);
        break;
    case MagnifierDragArea::Top:
        newGeometry.setTop(m_resizeStartGeometry.top() + delta.y());
        break;
    case MagnifierDragArea::TopRight:
        newGeometry.setTopRight(m_resizeStartGeometry.topRight() + delta);
        break;
    case MagnifierDragArea::Right:
        newGeometry.setRight(m_resizeStartGeometry.right() + delta.x());
        break;
    case MagnifierDragArea::BottomRight:
        newGeometry.setBottomRight(m_resizeStartGeometry.bottomRight() + delta);
        break;
    case MagnifierDragArea::Bottom:
        newGeometry.setBottom(m_resizeStartGeometry.bottom() + delta.y());
        break;
    case MagnifierDragArea::BottomLeft:
        newGeometry.setBottomLeft(m_resizeStartGeometry.bottomLeft() + delta);
        break;
    case MagnifierDragArea::Left:
        newGeometry.setLeft(m_resizeStartGeometry.left() + delta.x());
        break;
    default:
        return;
    }

    // 保证最小尺寸
    if (newGeometry.width() >= m_minSize && newGeometry.height() >= m_minSize) {
        m_label->setGeometry(newGeometry);
    }
}

void MagnifierWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        QPoint localPos = event->pos();
        MagnifierDragArea dragArea = getDragArea(localPos);
        
        if (dragArea != MagnifierDragArea::None) {
            m_isResizing = true;
            m_currentDragArea = dragArea;
            m_resizeStartGlobalPos = event->globalPos();
            m_resizeStartGeometry = m_label->geometry();
            event->accept();
        } else {
            if (m_label->geometry().contains(event->pos())) {
                m_isDragging = true;
                m_dragStartPos = event->pos();
            }
            event->accept();
        }
        updateMagnifiedContent();
    }
}

void MagnifierWidget::mouseMoveEvent(QMouseEvent *event)
{
    QPoint localPos = event->pos();
    MagnifierDragArea dragArea = getDragArea(localPos);

    // 更新光标
    updateCursor(dragArea);

    if (m_isResizing && (event->buttons() & Qt::LeftButton)) {
        // 调整窗口大小，使用全局坐标
        resizeLabelWindow(event->globalPos());
        event->accept();

        syncButtonPanel();
        updateMagnifiedContent();

    } else if (m_isDragging && (event->buttons() & Qt::LeftButton)) {
        qint64 now = QDateTime::currentMSecsSinceEpoch();
        if (!m_canMove || now - m_lastMoveTime < m_moveIntervalMs) {
//            if (!m_canMove) {
//                qDebug() << "MagnifierWidget::mouseMoveEvent: not paint";
//            }
//            if (now - m_lastMoveTime < m_moveIntervalMs) {
//                qDebug() << "MagnifierWidget::mouseMoveEvent: too fast";
//            }
            event->accept();
            return;
        }
        m_lastMoveTime = now;
        // 计算鼠标移动的偏移量
        QPoint delta = event->pos() - m_dragStartPos;
        // 计算新位置
        QPoint newPos = m_label->pos() + delta;
        
        // 限制窗口不能拖出屏幕边界
        QScreen* screen = QGuiApplication::primaryScreen();
        if (screen) {
            QRect screenGeometry = screen->geometry();
            QSize widgetSize = m_label->size();
            
            // 限制左右边界，确保整个窗口都在屏幕内
            int minX = screenGeometry.left();
            int maxX = screenGeometry.right() - widgetSize.width();
            newPos.setX(qBound(minX, newPos.x(), maxX));
            
            // 限制上下边界，确保整个窗口都在屏幕内
            int minY = screenGeometry.top();
            int maxY = screenGeometry.bottom() - widgetSize.height();
            newPos.setY(qBound(minY, newPos.y(), maxY));
        }
        
        // 移动 m_label
        m_label->move(newPos);
        // 更新拖动起始位置
        m_dragStartPos = event->pos();

        event->accept();

        updateMagnifiedContent();
        syncButtonPanel();
    }
    else {
        event->ignore();
    }
}

void MagnifierWidget::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_isDragging = false;
        m_isResizing = false;
        m_currentDragArea = MagnifierDragArea::None;
        event->accept();
        updateMagnifiedContent();
    }
}

void MagnifierWidget::paintEvent(QPaintEvent *event)
{
    // 记录耗时
//    qint64 startTime = QDateTime::currentMSecsSinceEpoch();

    // 获取 m_label 在屏幕上的位置和大小
    QRect labelGeo = m_label->geometry();

    // 定义边框区域（逻辑像素）
    int borderMargin = m_borderMargin;
    int borderWidth = m_borderWidth;

    QRect innerRect = m_label->rect().adjusted(borderMargin, borderMargin, -borderMargin, -borderMargin);

    qreal dpr = devicePixelRatioF();
    QSize widgetSizePx(int(labelGeo.width() * dpr), int(labelGeo.height() * dpr));
    QPixmap offscreenPixmap(widgetSizePx);
    offscreenPixmap.setDevicePixelRatio(dpr);
    offscreenPixmap.fill(Qt::transparent); // 透明背景

    QPainter pixmapPainter(&offscreenPixmap);
    pixmapPainter.setRenderHint(QPainter::SmoothPixmapTransform);
    pixmapPainter.setRenderHint(QPainter::Antialiasing); // 开启抗锯齿

    pixmapPainter.drawPixmap(innerRect.x(), innerRect.y(), m_magnifiedPixmap);

    drawBorder(borderMargin, borderWidth, innerRect, pixmapPainter);

    // 最后将离屏Pixmap绘制到界面上
    QPainter screenPainter(this);
    screenPainter.drawPixmap(m_label->geometry().topLeft(), offscreenPixmap);

//    qint64 endTime = QDateTime::currentMSecsSinceEpoch();
//    qDebug() << "PaintEvent耗时：" << endTime - startTime << "ms";
}

void MagnifierWidget::drawBorder(int borderMargin, int borderWidth, const QRect &innerRect,
                                 QPainter &pixmapPainter) const {// 绘制蓝色边框
    QPen borderPen(m_borderColor, borderWidth);
    pixmapPainter.setPen(borderPen);
    pixmapPainter.drawRect(innerRect);

    // 八个点的半径
    int pointRadius = m_pointRadius;
    pixmapPainter.setBrush(Qt::white);
    pixmapPainter.setPen(Qt::NoPen);

    // 计算八个点的位置
    QPoint points[8] = {
            innerRect.topLeft(),
            QPoint(innerRect.center().x(), innerRect.top()), // 顶边中点
            innerRect.topRight(),
            QPoint(innerRect.right(), innerRect.center().y()), // 右边中点
            innerRect.bottomRight(),
            QPoint(innerRect.center().x(), innerRect.bottom()), // 底边中点
            innerRect.bottomLeft(),
            QPoint(innerRect.left(), innerRect.center().y()) // 左边中点
    };

    // 绘制八个白色圆点，并在外部加蓝色圆圈
    for (int i = 0; i < 8; ++i) {
        const QPoint &pt = points[i];
        // 再画白色圆点
        pixmapPainter.setBrush(m_pointColor);
        pixmapPainter.setPen(Qt::NoPen);
        pixmapPainter.drawEllipse(pt, pointRadius, pointRadius);

        // 先画蓝色圆圈
        pixmapPainter.setBrush(Qt::NoBrush);
        pixmapPainter.setPen(QPen(m_borderColor, borderWidth));
        pixmapPainter.drawEllipse(pt, borderMargin - borderWidth + borderWidth / 2, borderMargin - borderWidth + borderWidth / 2);
    }
}

void MagnifierWidget::showOnTop() {
    // raise();
    // m_buttonPanel->raise();
}


