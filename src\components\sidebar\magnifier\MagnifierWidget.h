#ifndef MAGNIFIERWIDGET_H
#define MAGNIFIERWIDGET_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QPixmap>
#include <QTimer>
#include <QScreen>
#include <QApplication>
#include <QMouseEvent>
#include <QPainter>
#include <QGraphicsOpacityEffect>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QToolButton>
#include <QFuture>
#include <QFutureWatcher>

#include "MagnifierButtonPanelWidget.h"
#include "MagnifierScreenMaskWidget.h"

// 定义拖拽区域枚举
enum class MagnifierDragArea {
    None,
    TopLeft,
    Top,
    TopRight,
    Right,
    BottomRight,
    Bottom,
    BottomLeft,
    Left
};

/**
 * 放大镜窗口
 */
class MagnifierWidget : public QWidget
{
    Q_OBJECT


public:
    explicit MagnifierWidget(QWidget *parent = nullptr);
    ~MagnifierWidget();

    void showOnTop();
    /**
     * 捕获屏幕内容
     */
    void captureScreen();

protected:
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void closeEvent(QCloseEvent *event) override;

signals:
    void lightToggled(bool isOn);
    void closeClicked();
    void screenCaptured(QPixmap pixmap);

private slots:
    /**
     * 更新放大镜内容
     */
    bool updateMagnifiedContent();
private:

    /**
     * 初始化UI
     */
    void setupUI();
    /**
     * 获取当前拖拽区域
     */
    MagnifierDragArea getDragArea(const QPoint &pos) const;
    /**
     * 更新光标
     */
    void updateCursor(MagnifierDragArea area);
    // 移动放大镜窗口
    void resizeLabelWindow(const QPoint &newPos);
    // 同步按钮面板
    void syncButtonPanel();


    // 信号槽
    void setConnect();

    // 绘制边框
    void drawBorder(int borderMargin, int borderWidth, const QRect &innerRect, QPainter &pixmapPainter) const;

    QPixmap m_screenCapture;
    qreal m_screenCaptureDpr = 1.0; // 记录截图的devicePixelRatio
    QPixmap m_magnifiedPixmap;
    QPoint m_dragStartPos;
    bool m_isDragging;
    double m_magnificationFactor;
    QPointF m_lastCapturePos; // 记录上一次的捕获位置
    int m_lastWidth = 0;
    int m_lastHeight = 0;

    // 辅助放大镜显示的label，放大镜内容不会现在是label上，它的作用是用作放大镜的定位等
    QLabel *m_label;

    Qt::CursorShape lastCursor; // 记录上次的光标形状


    // 拖拽调整大小相关
    MagnifierDragArea m_currentDragArea;
    QSize m_originalSize;
    QPoint m_originalPos;
    bool m_isResizing;
    int m_minSize; // 最小窗口大小

    // 按钮面板
    MagnifierButtonPanelWidget *m_buttonPanel = nullptr;


    int m_borderWidth = 6; // 边框宽度
    int m_borderMargin = 12; // 边框边距
    QColor m_borderColor = QColor("#6C63FF"); // 边框颜色
    int m_pointRadius = 6; // 内部原点的半径
    QColor m_pointColor = QColor(Qt::white); // 内部原点的颜色

    QAtomicInt m_canMove = true;

    // 节流用：记录上次move时间和最小间隔
    qint64 m_lastMoveTime = 0;
    const int m_moveIntervalMs = 20;

    // resize时记录起始全局坐标和窗口geometry
    QPoint m_resizeStartGlobalPos;
    QRect m_resizeStartGeometry;

};
#endif // MAGNIFIERWIDGET_H
