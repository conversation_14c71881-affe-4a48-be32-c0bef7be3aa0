#include "singleinstancemanager.h"
#include <QDebug>
#include <QCoreApplication>
#include <QStandardPaths>
#include <QDir>
#include <QCryptographicHash>

SingleInstanceManager::SingleInstanceManager(const QString& appKey, QObject *parent)
    : QObject(parent)
    , m_appKey(appKey)
    , m_sharedMemory(nullptr)
    , m_localServer(nullptr)
    , m_isFirstInstance(false)
    , m_messageCallback(nullptr)
    , m_cleanupTimer(nullptr)
{
    // 初始化共享内存
    initializeSharedMemory();
    
    // 创建清理定时器
    m_cleanupTimer = new QTimer(this);
    m_cleanupTimer->setInterval(CLEANUP_INTERVAL);
    connect(m_cleanupTimer, &QTimer::timeout, this, &SingleInstanceManager::cleanupConnections);
}

SingleInstanceManager::~SingleInstanceManager()
{
    cleanup();
}

bool SingleInstanceManager::initializeSharedMemory()
{
    QString sharedMemoryKey = generateSharedMemoryKey();
    
    m_sharedMemory = new QSharedMemory(sharedMemoryKey, this);
    
    // 尝试附加到现有的共享内存
    if (m_sharedMemory->attach()) {
        // 共享内存已存在，说明有其他实例在运行
        m_isFirstInstance = false;
        qDebug() << "SingleInstanceManager: 检测到已有实例在运行";
        return true;
    }
    
    // 尝试创建新的共享内存
    if (m_sharedMemory->create(1)) {
        // 成功创建共享内存，说明这是第一个实例
        m_isFirstInstance = true;
        qDebug() << "SingleInstanceManager: 这是第一个实例";
        return true;
    }
    
    // 创建失败，可能是权限问题或其他错误
    qWarning() << "SingleInstanceManager: 初始化共享内存失败:" << m_sharedMemory->errorString();
    return false;
}

bool SingleInstanceManager::isFirstInstance()
{
    return m_isFirstInstance;
}

bool SingleInstanceManager::startServer()
{
    if (!m_isFirstInstance) {
        qWarning() << "SingleInstanceManager: 只有第一个实例才能启动服务器";
        return false;
    }
    
    if (m_localServer) {
        qWarning() << "SingleInstanceManager: 服务器已经启动";
        return true;
    }
    
    QString serverName = generateServerName();
    
    // 创建本地服务器
    m_localServer = new QLocalServer(this);
    
    // 移除可能存在的旧服务器
    QLocalServer::removeServer(serverName);
    
    // 启动服务器
    if (!m_localServer->listen(serverName)) {
        qWarning() << "SingleInstanceManager: 启动本地服务器失败:" << m_localServer->errorString();
        delete m_localServer;
        m_localServer = nullptr;
        return false;
    }
    
    // 连接信号
    connect(m_localServer, &QLocalServer::newConnection, this, &SingleInstanceManager::onNewConnection);
    
    // 启动清理定时器
    m_cleanupTimer->start();
    
    qDebug() << "SingleInstanceManager: 本地服务器启动成功，监听:" << serverName;
    return true;
}

bool SingleInstanceManager::sendMessageToFirstInstance(const QString& message)
{
    if (m_isFirstInstance) {
        qWarning() << "SingleInstanceManager: 第一个实例不需要发送消息给自己";
        return false;
    }
    
    QString serverName = generateServerName();
    
    QLocalSocket socket;
    socket.connectToServer(serverName);
    
    if (!socket.waitForConnected(CONNECTION_TIMEOUT)) {
        qWarning() << "SingleInstanceManager: 连接到第一个实例失败:" << socket.errorString();
        return false;
    }
    
    // 发送消息
    QByteArray data = message.toUtf8();
    socket.write(data);
    socket.flush();
    
    if (!socket.waitForBytesWritten(CONNECTION_TIMEOUT)) {
        qWarning() << "SingleInstanceManager: 发送消息失败:" << socket.errorString();
        return false;
    }
    
    socket.disconnectFromServer();
    if (socket.state() != QLocalSocket::UnconnectedState) {
        socket.waitForDisconnected(CONNECTION_TIMEOUT);
    }
    
    qDebug() << "SingleInstanceManager: 消息发送成功:" << message;
    return true;
}

void SingleInstanceManager::setMessageReceivedCallback(std::function<void(const QString&)> callback)
{
    m_messageCallback = callback;
}

void SingleInstanceManager::forceCleanupAndRestart()
{
    qDebug() << "SingleInstanceManager: 强制清理残留资源并重新初始化";

    // 1. 清理当前的共享内存连接
    if (m_sharedMemory) {
        if (m_sharedMemory->isAttached()) {
            m_sharedMemory->detach();
        }
        delete m_sharedMemory;
        m_sharedMemory = nullptr;
    }

    // 2. 强制移除可能存在的服务器资源
    QString serverName = generateServerName();
    QLocalServer::removeServer(serverName);

    // 3. 重新初始化共享内存，强制创建新实例
    QString sharedMemoryKey = generateSharedMemoryKey();
    m_sharedMemory = new QSharedMemory(sharedMemoryKey, this);

    // 4. 尝试强制创建共享内存（忽略现有的）
    if (m_sharedMemory->create(1)) {
        m_isFirstInstance = true;
        qDebug() << "SingleInstanceManager: 强制重新初始化成功，现在是第一个实例";
    } else {
        // 如果还是失败，尝试attach然后detach再create
        if (m_sharedMemory->attach()) {
            m_sharedMemory->detach();
        }
        if (m_sharedMemory->create(1)) {
            m_isFirstInstance = true;
            qDebug() << "SingleInstanceManager: 二次尝试重新初始化成功";
        } else {
            qWarning() << "SingleInstanceManager: 强制重新初始化失败:" << m_sharedMemory->errorString();
            // 保持原有状态，不强制设置为第一个实例
            qDebug() << "SingleInstanceManager: 保持当前实例状态，isFirstInstance =" << m_isFirstInstance;
        }
    }
}

void SingleInstanceManager::onNewConnection()
{
    if (!m_localServer) {
        return;
    }
    
    while (QLocalSocket* clientSocket = m_localServer->nextPendingConnection()) {
        qDebug() << "SingleInstanceManager: 新客户端连接";
        
        // 添加到客户端列表
        m_clientSockets.append(clientSocket);
        
        // 连接信号
        connect(clientSocket, &QLocalSocket::readyRead, this, &SingleInstanceManager::onReadyRead);
        connect(clientSocket, &QLocalSocket::disconnected, this, &SingleInstanceManager::onClientDisconnected);
        
        // 发出新实例检测信号
        emit newInstanceDetected();
    }
}

void SingleInstanceManager::onClientDisconnected()
{
    QLocalSocket* clientSocket = qobject_cast<QLocalSocket*>(sender());
    if (clientSocket) {
        qDebug() << "SingleInstanceManager: 客户端断开连接";
        m_clientSockets.removeAll(clientSocket);
        clientSocket->deleteLater();
    }
}

void SingleInstanceManager::onReadyRead()
{
    QLocalSocket* clientSocket = qobject_cast<QLocalSocket*>(sender());
    if (!clientSocket) {
        return;
    }
    
    QByteArray data = clientSocket->readAll();
    QString message = QString::fromUtf8(data);
    
    qDebug() << "SingleInstanceManager: 接收到消息:" << message;
    
    // 发出信号
    emit messageReceived(message);
    
    // 调用回调函数
    if (m_messageCallback) {
        m_messageCallback(message);
    }
}

void SingleInstanceManager::cleanupConnections()
{
    // 清理已断开的连接
    for (auto it = m_clientSockets.begin(); it != m_clientSockets.end();) {
        QLocalSocket* socket = *it;
        if (socket->state() == QLocalSocket::UnconnectedState) {
            qDebug() << "SingleInstanceManager: 清理断开的连接";
            socket->deleteLater();
            it = m_clientSockets.erase(it);
        } else {
            ++it;
        }
    }
}

void SingleInstanceManager::cleanup()
{
    // 停止清理定时器
    if (m_cleanupTimer) {
        m_cleanupTimer->stop();
    }
    
    // 清理客户端连接
    for (QLocalSocket* socket : m_clientSockets) {
        socket->disconnectFromServer();
        socket->deleteLater();
    }
    m_clientSockets.clear();
    
    // 清理服务器
    if (m_localServer) {
        m_localServer->close();
        QLocalServer::removeServer(generateServerName());
        delete m_localServer;
        m_localServer = nullptr;
    }
    
    // 清理共享内存
    if (m_sharedMemory) {
        if (m_sharedMemory->isAttached()) {
            m_sharedMemory->detach();
        }
        delete m_sharedMemory;
        m_sharedMemory = nullptr;
    }
    
    qDebug() << "SingleInstanceManager: 资源清理完成";
}

QString SingleInstanceManager::generateServerName() const
{
    // 使用应用程序名称和用户特定的路径生成唯一的服务器名称
    QString baseName = QString("%1_SingleInstance").arg(m_appKey);
    
    // 添加用户特定的标识符以避免不同用户间的冲突
    QString userPath = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(userPath.toUtf8());
    QString userHash = hash.result().toHex().left(8);
    
    return QString("%1_%2").arg(baseName, userHash);
}

QString SingleInstanceManager::generateSharedMemoryKey() const
{
    // 生成共享内存键，确保在同一用户下唯一
    QString baseName = QString("%1_SharedMemory").arg(m_appKey);
    
    // 添加用户特定的标识符
    QString userPath = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(userPath.toUtf8());
    QString userHash = hash.result().toHex().left(8);
    
    return QString("%1_%2").arg(baseName, userHash);
}
