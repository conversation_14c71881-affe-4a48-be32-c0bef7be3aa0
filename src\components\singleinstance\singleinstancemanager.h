#ifndef SINGLEINSTANCEMANAGER_H
#define SINGLEINSTANCEMANAGER_H

#include <QObject>
#include <QSharedMemory>
#include <QLocalServer>
#include <QLocalSocket>
#include <QString>
#include <QTimer>
#include <functional>

/**
 * @brief 单实例应用程序管理器
 * 
 * 使用QSharedMemory和QLocalServer/QLocalSocket组合实现单实例功能
 * 支持实例间通信，当尝试启动第二个实例时，可以将参数传递给第一个实例
 */
class SingleInstanceManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param appKey 应用程序唯一标识符，用于区分不同的应用程序
     * @param parent 父对象
     */
    explicit SingleInstanceManager(const QString& appKey, QObject *parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SingleInstanceManager();

    /**
     * @brief 检查是否为第一个实例
     * @return true 如果是第一个实例，false 如果已有实例在运行
     */
    bool isFirstInstance();

    /**
     * @brief 启动本地服务器（仅第一个实例调用）
     * @return true 启动成功，false 启动失败
     */
    bool startServer();

    /**
     * @brief 向第一个实例发送消息（仅后续实例调用）
     * @param message 要发送的消息
     * @return true 发送成功，false 发送失败
     */
    bool sendMessageToFirstInstance(const QString& message);

    /**
     * @brief 设置接收到消息时的回调函数
     * @param callback 回调函数，参数为接收到的消息
     */
    void setMessageReceivedCallback(std::function<void(const QString&)> callback);

    /**
     * @brief 强制清理残留资源并重新初始化为第一个实例
     * 当检测到已有实例但无法通信时使用
     */
    void forceCleanupAndRestart();

    /**
     * @brief 获取应用程序唯一标识符
     * @return 应用程序标识符
     */
    QString getAppKey() const { return m_appKey; }

signals:
    /**
     * @brief 接收到来自其他实例的消息时发出的信号
     * @param message 接收到的消息
     */
    void messageReceived(const QString& message);

    /**
     * @brief 有新实例尝试启动时发出的信号
     */
    void newInstanceDetected();

private slots:
    /**
     * @brief 处理新的客户端连接
     */
    void onNewConnection();

    /**
     * @brief 处理客户端断开连接
     */
    void onClientDisconnected();

    /**
     * @brief 读取客户端发送的数据
     */
    void onReadyRead();

    /**
     * @brief 清理断开的连接
     */
    void cleanupConnections();

private:
    /**
     * @brief 初始化共享内存
     * @return true 初始化成功，false 初始化失败
     */
    bool initializeSharedMemory();

    /**
     * @brief 清理资源
     */
    void cleanup();

    /**
     * @brief 生成服务器名称
     * @return 服务器名称
     */
    QString generateServerName() const;

    /**
     * @brief 生成共享内存键
     * @return 共享内存键
     */
    QString generateSharedMemoryKey() const;

private:
    QString m_appKey;                                           ///< 应用程序唯一标识符
    QSharedMemory* m_sharedMemory;                             ///< 共享内存对象
    QLocalServer* m_localServer;                               ///< 本地服务器
    QList<QLocalSocket*> m_clientSockets;                     ///< 客户端连接列表
    bool m_isFirstInstance;                                    ///< 是否为第一个实例
    std::function<void(const QString&)> m_messageCallback;    ///< 消息接收回调函数
    QTimer* m_cleanupTimer;                                    ///< 清理定时器
    
    static const int CLEANUP_INTERVAL = 5000;                 ///< 清理间隔（毫秒）
    static const int CONNECTION_TIMEOUT = 3000;               ///< 连接超时时间（毫秒）
};

#endif // SINGLEINSTANCEMANAGER_H
