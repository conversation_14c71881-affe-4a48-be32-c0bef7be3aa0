#include "SvgManager.h"
#include <QPainter>
#include <QFile>
#include <QDebug>
#include <QDomNodeList>
#include <QMutex>
#include <QMutexLocker>

// 静态成员初始化
SvgManager* SvgManager::s_instance = nullptr;
QMutex SvgManager::s_mutex;

SvgManager::SvgManager(QObject *parent)
    : QObject(parent)
{
    qDebug() << "SvgManager: 创建SVG管理器";
}

SvgManager::~SvgManager()
{
    // 清理所有SVG数据
    for (auto it = m_svgData.begin(); it != m_svgData.end(); ++it) {
        delete it.value();
    }
    m_svgData.clear();
    
    qDebug() << "SvgManager: 销毁SVG管理器";
}

SvgManager* SvgManager::instance()
{
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new SvgManager();
    }
    return s_instance;
}

void SvgManager::destroy()
{
    QMutexLocker locker(&s_mutex);
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

bool SvgManager::loadSvg(const QString& filePath, const QString& svgId)
{
    // 检查是否已经加载
    if (m_svgData.contains(svgId)) {
        qWarning() << "SvgManager: SVG已存在，ID:" << svgId;
        return true;
    }

    // 读取SVG文件
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "SvgManager: 无法打开SVG文件:" << filePath;
        return false;
    }

    QByteArray svgData = file.readAll();
    file.close();

    // 解析SVG文档
    SvgData* data = new SvgData();
    data->filePath = filePath;

    QString errorMsg;
    int errorLine, errorColumn;
    if (!data->originalDoc.setContent(svgData, &errorMsg, &errorLine, &errorColumn)) {
        qWarning() << "SvgManager: SVG解析失败:" << errorMsg 
                   << "行:" << errorLine << "列:" << errorColumn;
        delete data;
        return false;
    }

    // 复制到修改文档
    data->modifiedDoc = data->originalDoc.cloneNode(true).toDocument();

    // 创建渲染器
    data->renderer = new QSvgRenderer(svgData);
    if (!data->renderer->isValid()) {
        qWarning() << "SvgManager: SVG渲染器创建失败:" << filePath;
        delete data;
        return false;
    }

    // 存储数据
    m_svgData[svgId] = data;

    qDebug() << "SvgManager: 成功加载SVG:" << filePath << "ID:" << svgId;
    return true;
}

bool SvgManager::modifyElementColor(const QString& svgId, const QString& elementId, 
                                   const QColor& color, const QString& property)
{
    if (!m_svgData.contains(svgId)) {
        qWarning() << "SvgManager: SVG未找到，ID:" << svgId;
        return false;
    }

    SvgData* data = m_svgData[svgId];
    QDomElement root = data->modifiedDoc.documentElement();

    bool modified = modifyElementRecursive(root, elementId, "", "", color, property);

    if (modified) {
        // 清除缓存
        data->pixmapCache.clear();
        
        // 更新渲染器
        return updateRenderer(svgId);
    }

    return false;
}

bool SvgManager::modifyElementsByTag(const QString& svgId, const QString& tagName,
                                    const QColor& color, const QString& property)
{
    if (!m_svgData.contains(svgId)) {
        qWarning() << "SvgManager: SVG未找到，ID:" << svgId;
        return false;
    }

    SvgData* data = m_svgData[svgId];
    QDomElement root = data->modifiedDoc.documentElement();

    bool modified = modifyElementRecursive(root, "", tagName, "", color, property);

    if (modified) {
        // 清除缓存
        data->pixmapCache.clear();
        
        // 更新渲染器
        return updateRenderer(svgId);
    }

    return false;
}

bool SvgManager::modifyElementsByClass(const QString& svgId, const QString& className,
                                      const QColor& color, const QString& property)
{
    if (!m_svgData.contains(svgId)) {
        qWarning() << "SvgManager: SVG未找到，ID:" << svgId;
        return false;
    }

    SvgData* data = m_svgData[svgId];
    QDomElement root = data->modifiedDoc.documentElement();

    bool modified = modifyElementRecursive(root, "", "", className, color, property);

    if (modified) {
        // 清除缓存
        data->pixmapCache.clear();
        
        // 更新渲染器
        return updateRenderer(svgId);
    }

    return false;
}

bool SvgManager::renderSvg(QPainter* painter, const QString& svgId, const QRectF& rect)
{
    if (!painter || !m_svgData.contains(svgId)) {
        return false;
    }

    SvgData* data = m_svgData[svgId];
    if (!data->renderer || !data->renderer->isValid()) {
        return false;
    }

    data->renderer->render(painter, rect);
    return true;
}

QSizeF SvgManager::getSvgSize(const QString& svgId) const
{
    if (!m_svgData.contains(svgId)) {
        return QSizeF();
    }

    SvgData* data = m_svgData[svgId];
    if (!data->renderer) {
        return QSizeF();
    }

    return data->renderer->defaultSize();
}

QPixmap SvgManager::generatePixmap(const QString& svgId, const QSize& size)
{
    if (!m_svgData.contains(svgId)) {
        return QPixmap();
    }

    SvgData* data = m_svgData[svgId];
    if (!data->renderer || !data->renderer->isValid()) {
        return QPixmap();
    }

    // 检查缓存
    QString cacheKey = QString("%1x%2").arg(size.width()).arg(size.height());
    if (data->pixmapCache.contains(cacheKey)) {
        return data->pixmapCache[cacheKey];
    }

    // 生成新的像素图
    QPixmap pixmap(size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

    data->renderer->render(&painter);

    // 缓存像素图
    data->pixmapCache[cacheKey] = pixmap;

    return pixmap;
}

void SvgManager::clearCache(const QString& svgId)
{
    if (m_svgData.contains(svgId)) {
        m_svgData[svgId]->pixmapCache.clear();
        qDebug() << "SvgManager: 清除SVG缓存，ID:" << svgId;
    }
}

void SvgManager::clearAllCache()
{
    for (auto it = m_svgData.begin(); it != m_svgData.end(); ++it) {
        it.value()->pixmapCache.clear();
    }
    qDebug() << "SvgManager: 清除所有SVG缓存";
}

bool SvgManager::isSvgLoaded(const QString& svgId) const
{
    return m_svgData.contains(svgId);
}

bool SvgManager::resetSvg(const QString& svgId)
{
    if (!m_svgData.contains(svgId)) {
        return false;
    }

    SvgData* data = m_svgData[svgId];
    
    // 重置修改文档为原始文档
    data->modifiedDoc = data->originalDoc.cloneNode(true).toDocument();
    
    // 清除缓存
    data->pixmapCache.clear();
    
    // 更新渲染器
    return updateRenderer(svgId);
}

bool SvgManager::updateRenderer(const QString& svgId)
{
    if (!m_svgData.contains(svgId)) {
        return false;
    }

    SvgData* data = m_svgData[svgId];
    
    // 获取修改后的SVG数据
    QByteArray svgData = data->modifiedDoc.toByteArray();
    
    // 删除旧的渲染器
    if (data->renderer) {
        delete data->renderer;
    }
    
    // 创建新的渲染器
    data->renderer = new QSvgRenderer(svgData);
    
    if (!data->renderer->isValid()) {
        qWarning() << "SvgManager: 更新渲染器失败，ID:" << svgId;
        return false;
    }

    qDebug() << "SvgManager: 成功更新渲染器，ID:" << svgId;
    return true;
}

bool SvgManager::modifyElementRecursive(QDomElement& element, const QString& elementId,
                                       const QString& tagName, const QString& className,
                                       const QColor& color, const QString& property)
{
    bool modified = false;

    // 检查当前元素是否匹配条件
    bool matches = false;
    
    if (!elementId.isEmpty()) {
        // 按ID匹配
        matches = (element.attribute("id") == elementId);
    } else if (!tagName.isEmpty()) {
        // 按标签名匹配
        matches = (element.tagName().toLower() == tagName.toLower());
    } else if (!className.isEmpty()) {
        // 按类名匹配
        QString classAttr = element.attribute("class");
        QStringList classes = classAttr.split(' ', Qt::SkipEmptyParts);
        matches = classes.contains(className);
    } else {
        // 如果没有指定条件，匹配所有有颜色属性的元素
        matches = element.hasAttribute("fill") || element.hasAttribute("stroke");
    }

    if (matches) {
        setElementColor(element, color, property);
        modified = true;
    }

    // 递归处理子元素
    QDomNodeList children = element.childNodes();
    for (int i = 0; i < children.count(); ++i) {
        QDomNode child = children.at(i);
        if (child.isElement()) {
            QDomElement childElement = child.toElement();
            if (modifyElementRecursive(childElement, elementId, tagName, className, color, property)) {
                modified = true;
            }
        }
    }

    return modified;
}

void SvgManager::setElementColor(QDomElement& element, const QColor& color, const QString& property)
{
    QString colorStr = colorToSvgString(color);

    if (property == "fill" || property == "both") {
        element.setAttribute("fill", colorStr);
    }
    
    if (property == "stroke" || property == "both") {
        element.setAttribute("stroke", colorStr);
    }
}

QString SvgManager::colorToSvgString(const QColor& color) const
{
    if (color.alpha() == 255) {
        // 不透明颜色，使用十六进制格式
        return color.name();
    } else {
        // 半透明颜色，使用rgba格式
        return QString("rgba(%1,%2,%3,%4)")
               .arg(color.red())
               .arg(color.green())
               .arg(color.blue())
               .arg(color.alphaF(), 0, 'f', 2);
    }
} 