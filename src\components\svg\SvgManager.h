#ifndef SVGMANAGER_H
#define SVGMANAGER_H

#include <QObject>
#include <QSvgRenderer>
#include <QPixmap>
#include <QColor>
#include <QMap>
#include <QDomDocument>
#include <QDomElement>
#include <QByteArray>
#include <QPainter>

/**
 * @brief SVG管理器类
 * 
 * 提供SVG文件的加载、颜色修改和渲染功能
 * 支持动态修改SVG中特定元素的颜色属性
 */
class SvgManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SvgManager(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~SvgManager();

    /**
     * @brief 加载SVG文件
     * @param filePath SVG文件路径
     * @param svgId SVG的唯一标识符
     * @return 是否加载成功
     */
    bool loadSvg(const QString& filePath, const QString& svgId);

    /**
     * @brief 修改SVG中指定元素的颜色
     * @param svgId SVG标识符
     * @param elementId 元素ID（如果为空，则修改所有fill和stroke属性）
     * @param color 新颜色
     * @param property 要修改的属性（"fill", "stroke", "both"）
     * @return 是否修改成功
     */
    bool modifyElementColor(const QString& svgId, const QString& elementId, 
                           const QColor& color, const QString& property = "fill");

    /**
     * @brief 修改SVG中所有指定类型元素的颜色
     * @param svgId SVG标识符
     * @param tagName 标签名称（如"path", "circle", "rect"等）
     * @param color 新颜色
     * @param property 要修改的属性（"fill", "stroke", "both"）
     * @return 是否修改成功
     */
    bool modifyElementsByTag(const QString& svgId, const QString& tagName,
                            const QColor& color, const QString& property = "fill");

    /**
     * @brief 修改SVG中所有具有指定class的元素颜色
     * @param svgId SVG标识符
     * @param className 类名
     * @param color 新颜色
     * @param property 要修改的属性（"fill", "stroke", "both"）
     * @return 是否修改成功
     */
    bool modifyElementsByClass(const QString& svgId, const QString& className,
                              const QColor& color, const QString& property = "fill");

    /**
     * @brief 渲染SVG到指定矩形
     * @param painter 画笔
     * @param svgId SVG标识符
     * @param rect 目标矩形
     * @return 是否渲染成功
     */
    bool renderSvg(QPainter* painter, const QString& svgId, const QRectF& rect);

    /**
     * @brief 获取SVG的原始尺寸
     * @param svgId SVG标识符
     * @return SVG尺寸
     */
    QSizeF getSvgSize(const QString& svgId) const;

    /**
     * @brief 生成SVG的像素图缓存
     * @param svgId SVG标识符
     * @param size 目标尺寸
     * @return 像素图
     */
    QPixmap generatePixmap(const QString& svgId, const QSize& size);

    /**
     * @brief 清除指定SVG的缓存
     * @param svgId SVG标识符
     */
    void clearCache(const QString& svgId);

    /**
     * @brief 清除所有缓存
     */
    void clearAllCache();

    /**
     * @brief 检查SVG是否已加载
     * @param svgId SVG标识符
     * @return 是否已加载
     */
    bool isSvgLoaded(const QString& svgId) const;

    /**
     * @brief 重置SVG到原始状态
     * @param svgId SVG标识符
     * @return 是否重置成功
     */
    bool resetSvg(const QString& svgId);

    /**
     * @brief 获取单例实例
     * @return SVG管理器实例
     */
    static SvgManager* instance();

    /**
     * @brief 销毁单例实例
     */
    static void destroy();

private:
    /**
     * @brief SVG数据结构
     */
    struct SvgData {
        QDomDocument originalDoc;       ///< 原始SVG文档
        QDomDocument modifiedDoc;       ///< 修改后的SVG文档
        QSvgRenderer* renderer;         ///< SVG渲染器
        QString filePath;               ///< 文件路径
        QMap<QString, QPixmap> pixmapCache; ///< 像素图缓存
        
        SvgData() : renderer(nullptr) {}
        ~SvgData() {
            if (renderer) {
                delete renderer;
                renderer = nullptr;
            }
        }
    };

    /**
     * @brief 更新SVG渲染器
     * @param svgId SVG标识符
     * @return 是否更新成功
     */
    bool updateRenderer(const QString& svgId);

    /**
     * @brief 递归修改DOM元素的颜色属性
     * @param element DOM元素
     * @param elementId 目标元素ID（空表示修改所有）
     * @param tagName 目标标签名（空表示不限制）
     * @param className 目标类名（空表示不限制）
     * @param color 新颜色
     * @param property 要修改的属性
     * @return 是否有修改
     */
    bool modifyElementRecursive(QDomElement& element, const QString& elementId,
                               const QString& tagName, const QString& className,
                               const QColor& color, const QString& property);

    /**
     * @brief 设置元素的颜色属性
     * @param element DOM元素
     * @param color 颜色
     * @param property 属性名
     */
    void setElementColor(QDomElement& element, const QColor& color, const QString& property);

    /**
     * @brief 颜色转换为SVG格式字符串
     * @param color 颜色
     * @return SVG颜色字符串
     */
    QString colorToSvgString(const QColor& color) const;

private:
    QMap<QString, SvgData*> m_svgData;  ///< SVG数据映射
    
    // 单例相关
    static SvgManager* s_instance;      ///< 单例实例
    static QMutex s_mutex;              ///< 线程安全互斥锁
};

#endif // SVGMANAGER_H 