# Toast - Qt全局Toast组件

## 概述

Toast是一个独立、易用的Qt Toast组件，专为跨项目复用而设计。它包含内嵌的SVG图标，无需外部资源文件，只需复制两个文件即可在任何Qt项目中使用。

## 特性

✅ **独立性强** - 内嵌SVG图标，无需外部资源文件  
✅ **易于复用** - 只需复制两个文件即可使用  
✅ **屏幕适配** - 自动适配不同分辨率屏幕
✅ **多种类型** - 支持信息、成功、警告、错误四种类型  
✅ **灵活位置** - 支持7种显示位置  
✅ **队列管理** - 多个Toast智能排列，不会重叠  
✅ **线程安全** - 支持多线程环境  
✅ **动画效果** - 平滑的淡入淡出动画  
✅ **自动关闭** - 可配置自动关闭时间  

## 快速开始

### 1. 复制文件

将以下两个文件复制到你的项目中：
- `Toast.h`
- `Toast.cpp`

### 2. 添加到项目

在你的CMakeLists.txt或.pro文件中添加这两个文件。

### 3. 包含头文件

```cpp
#include "Toast.h"
```

### 4. 基础使用

```cpp
// 显示不同类型的Toast
Toast::showInfo("这是信息提示");
Toast::showSuccess("操作成功！");
Toast::showWarning("请注意");
Toast::showError("发生错误");
```

## 详细用法

### 基础方法

```cpp
// 信息Toast（默认3秒）
Toast::showInfo("信息内容");

// 成功Toast（自定义时长）
Toast::showSuccess("操作成功", 5000);

// 警告Toast
Toast::showWarning("警告信息");

// 错误Toast
Toast::showError("错误信息");
```

### 自定义配置

```cpp
ToastOptions options;
options.duration = 5000;                    // 显示5秒
options.type = ToastType::Success;          // 成功类型
options.position = ToastPosition::TopRight; // 右上角显示
options.gap = 30;                          // Toast间距30px
options.autoClose = true;                  // 自动关闭

Toast::show("自定义Toast", options);
```

### 管理Toast

```cpp
// 清除所有Toast
Toast::clearAll();
```

## 配置选项

### ToastType（Toast类型）

| 类型 | 说明 | 图标 | 颜色 |
|------|------|------|------|
| `ToastType::Info` | 信息提示 | 感叹号 | 灰色 |
| `ToastType::Success` | 成功提示 | 对勾 | 绿色 |
| `ToastType::Warning` | 警告提示 | 三角感叹号 | 黄色 |
| `ToastType::Error` | 错误提示 | X号 | 红色 |

### ToastPosition（显示位置）

| 位置 | 说明 |
|------|------|
| `ToastPosition::Top` | 顶部居中 |
| `ToastPosition::TopLeft` | 左上角 |
| `ToastPosition::TopRight` | 右上角 |
| `ToastPosition::Bottom` | 底部居中（默认） |
| `ToastPosition::BottomLeft` | 左下角 |
| `ToastPosition::BottomRight` | 右下角 |
| `ToastPosition::Center` | 屏幕中央 |

### ToastOptions（配置选项）

```cpp
struct ToastOptions {
    int duration = 3000;                    // 显示时长(毫秒)
    ToastType type = ToastType::Info;       // Toast类型
    ToastPosition position = ToastPosition::Bottom;  // 显示位置
    int gap = 20;                          // Toast间距
    bool autoClose = true;                 // 是否自动关闭
};
```

## 屏幕适配

组件集成了项目的屏幕适配管理器，会自动根据当前屏幕分辨率调整大小：

- Toast高度：140px（适配后）
- 最小宽度：720px（适配后）
- 最大宽度：1800px（适配后）
- 图标尺寸：116x116px（适配后）
- 圆角半径：70px（适配后）
- 字体大小：56px（适配后）

所有尺寸都会根据屏幕分辨率自动缩放，基于4K分辨率(4096x2160)进行适配。

## 高级用法

### 在不同线程中使用

```cpp
// 在工作线程中
QMetaObject::invokeMethod(qApp, []() {
    Toast::showSuccess("后台任务完成");
}, Qt::QueuedConnection);
```

### 自定义样式

如需修改样式，可以编辑`ToastWidget::setupUI()`方法中的样式表。

### 扩展功能

可以通过继承`ToastWidget`类来添加自定义功能，如点击事件、自定义图标等。

## 依赖项

- Qt6 Core
- Qt6 Widgets
- Qt6 Gui
- Qt6 Svg（用于SVG图标渲染）

## 注意事项

1. **Qt版本**：需要Qt 5.12+或Qt 6.x
2. **SVG支持**：确保项目链接了Qt Svg模块
3. **线程安全**：组件是线程安全的，可以在任何线程中调用
4. **内存管理**：Toast会自动管理内存，无需手动删除
5. **屏幕适配**：集成项目屏幕适配管理器，自动适配不同分辨率

## 文件结构

```
src/components/toast/
├── Toast.h               # 头文件
├── Toast.cpp             # 实现文件
├── ToastExample.cpp      # 使用示例
├── README.md             # 说明文档
├── toast.ts              # 原始TypeScript实现（参考）
├── toast.less            # 原始样式文件（参考）
└── base.less             # 原始基础样式（参考）
```

## 示例项目

参考`ToastExample.cpp`文件查看完整的使用示例。

## 项目集成

Toast组件已经集成到项目中，可以通过以下方式测试：

1. **运行项目**：启动hl-whiteboard-qt应用程序
2. **点击测试按钮**：在WhiteboardView左上角有一个"测试Toast"按钮
3. **观察效果**：点击按钮后会依次显示不同类型的Toast

### 在项目中使用

```cpp
#include "src/components/toast/Toast.h"

// 在任何地方调用
Toast::showSuccess("操作成功！");
Toast::showError("发生错误");
Toast::showWarning("警告信息");
Toast::showInfo("提示信息");
```

### 屏幕适配

Toast组件已经集成了项目的屏幕适配管理器，会自动根据当前屏幕分辨率调整大小：

```cpp
// 屏幕适配设计
setFixedHeight(adaptSize(140));           // Toast高度
setMinimumWidth(adaptSize(720));         // 最小宽度
setMaximumWidth(adaptSize(1800));        // 最大宽度
iconSpacer->setFixedSize(adaptSize(116), adaptSize(116));  // 图标尺寸
```

## 许可证

本组件基于项目许可证开源，可自由使用和修改。
