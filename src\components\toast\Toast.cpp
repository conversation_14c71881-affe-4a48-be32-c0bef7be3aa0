#include "Toast.h"
#include <QDebug>
#include <QThread>
#include <QApplication>
#include "nlohmann/json.hpp"

// 类型定义
using json = nlohmann::json;

// 静态成员初始化
QMutex Toast::s_mutex;
Toast* Toast::s_instance = nullptr;

// 内嵌SVG图标定义
namespace ToastIcons {
    // 信息图标
    const QString INFO_ICON = R"(
        <svg t="1750042833405" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4465" width="200" height="200"><path d="M450.602458 665.598073a62.463819 62.463819 0 0 0 122.879645 0L614.441984 102.399704A102.615282 102.615282 0 0 0 512.04228 0 105.256116 105.256116 0 0 0 409.642577 112.639674L450.602458 665.598073z m61.439822 153.599556a102.399704 102.399704 0 1 0 102.399704 102.399703 96.740773 96.740773 0 0 0-102.399704-102.399703z" p-id="4466" fill="#ffffff"></path></svg>
    )";

    // 成功图标
    const QString SUCCESS_ICON = R"(
       <svg t="1750045368604" class="icon" viewBox="0 0 1365 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5030" width="500" height="500"><path d="M1365.333333 121.0368s-218.453333 74.410667-473.2928 372.3264C655.36 763.357867 600.746667 875.042133 509.7472 1024 500.599467 1014.715733 364.066133 744.789333 0 549.205333l191.146667-186.1632s172.919467 121.0368 291.293866 344.4736c0 0 300.373333-474.794667 882.8928-707.515733v121.0368z" p-id="5031" fill="#29DA80"></path></svg>
    )";

    // 警告图标 (和信息图标一样)
    const QString WARNING_ICON = R"(
        <svg t="1750042833405" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4465" width="200" height="200"><path d="M450.602458 665.598073a62.463819 62.463819 0 0 0 122.879645 0L614.441984 102.399704A102.615282 102.615282 0 0 0 512.04228 0 105.256116 105.256116 0 0 0 409.642577 112.639674L450.602458 665.598073z m61.439822 153.599556a102.399704 102.399704 0 1 0 102.399704 102.399703 96.740773 96.740773 0 0 0-102.399704-102.399703z" p-id="4466" fill="#ffffff"></path></svg>
    )";

    // 错误图标
    const QString ERROR_ICON = R"(
        <svg t="1750043020668" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4843" width="200" height="200"><path d="M84.736 0L0.512 70.656c149.942857 111.616 289.645714 239.616 412.379429 367.616C224.914286 627.2 77.531429 813.056 0 887.808l160.219429 133.632c57.014857-117.248 180.772571-294.912 346.642285-481.28C672.731429 727.552 797.019429 906.24 854.016 1024c0 0 156.123429-165.376 169.984-139.776-60.086857-67.584-206.957714-264.192-404.150857-465.408 112.969143-115.712 239.817143-230.4 375.405714-331.264l-36.992-68.608c-153.545143 76.288-299.904 189.44-431.36 309.76C394.386286 203.264 244.425143 83.968 84.717714 0z" p-id="4844" fill="#ffffff"></path></svg>
    )";
}

// ToastWidget 实现
ToastWidget::ToastWidget(const QString& message, const ToastOptions& options, QWidget* parent)
    : QWidget(parent)
    , m_message(message)
    , m_options(options)
    , m_messageLabel(nullptr)
    , m_layout(nullptr)
    , m_fadeInAnimation(nullptr)
    , m_fadeOutAnimation(nullptr)
    , m_opacityEffect(nullptr)
    , m_autoCloseTimer(nullptr)
{
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    setAttribute(Qt::WA_TranslucentBackground);
    setAttribute(Qt::WA_ShowWithoutActivating);
    
    setupUI();
    setupAnimations();
}

ToastWidget::~ToastWidget()
{
    if (m_autoCloseTimer) {
        m_autoCloseTimer->stop();
        delete m_autoCloseTimer;
    }
}

void ToastWidget::setupUI()
{
    // 创建布局 - 使用屏幕适配
    m_layout = new QHBoxLayout(this);
    m_layout->setContentsMargins(adaptSize(12), adaptSize(12), adaptSize(40), adaptSize(12));
    m_layout->setSpacing(adaptSize(12));

    // 创建消息标签 - 手动处理省略号
    m_messageLabel = new QLabel(this);
    m_messageLabel->setStyleSheet(QString(
        "QLabel {"
        "    color: white;"
        "    font-size: %1px;"
        "    font-family: 'Microsoft YaHei', sans-serif;"
        "    font-weight: bold;"
        "    padding: %2px %3px 0px;"
        "    letter-spacing: %4px;"
        "}"
    ).arg(adaptSize(56))
     .arg(adaptSize(4))
     .arg(adaptSize(40))
     .arg(m_message.length() <= 6 ? adaptSize(9) : adaptSize(2)));

    m_messageLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);  // 左对齐，垂直居中

    // 手动计算省略文本
    QFont font("Microsoft YaHei");
    font.setPixelSize(adaptSize(56));
    font.setBold(true);
    QFontMetrics fontMetrics(font);

    QString elidedText = fontMetrics.elidedText(m_message, Qt::ElideRight, adaptSize(1600));
    m_messageLabel->setText(elidedText);

    // 为图标预留空间
    QWidget* iconSpacer = new QWidget(this);
    iconSpacer->setFixedSize(adaptSize(116), adaptSize(116));

    // 添加到布局
    m_layout->addWidget(iconSpacer);
    m_layout->addWidget(m_messageLabel, 1);

    // 设置窗口样式 - 使用屏幕适配
    setFixedHeight(adaptSize(140));

    // 调整大小以适应内容
    adjustSize();
}

void ToastWidget::setupAnimations()
{
    // 创建透明度效果
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    setGraphicsEffect(m_opacityEffect);
    
    // 淡入动画
    m_fadeInAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeInAnimation->setDuration(300);
    m_fadeInAnimation->setStartValue(0.0);
    m_fadeInAnimation->setEndValue(1.0);
    connect(m_fadeInAnimation, &QPropertyAnimation::finished, this, &ToastWidget::onFadeInFinished);
    
    // 淡出动画
    m_fadeOutAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeOutAnimation->setDuration(300);
    m_fadeOutAnimation->setStartValue(1.0);
    m_fadeOutAnimation->setEndValue(0.0);
    connect(m_fadeOutAnimation, &QPropertyAnimation::finished, this, &ToastWidget::onFadeOutFinished);
    
    // 自动关闭定时器
    if (m_options.autoClose && m_options.duration > 0) {
        m_autoCloseTimer = new QTimer(this);
        m_autoCloseTimer->setSingleShot(true);
        connect(m_autoCloseTimer, &QTimer::timeout, this, &ToastWidget::onAutoCloseTimer);
    }
}

void ToastWidget::showToast()
{
    show();
    raise();
    m_fadeInAnimation->start();
}

void ToastWidget::hideToast()
{
    if (m_autoCloseTimer) {
        m_autoCloseTimer->stop();
    }
    m_fadeOutAnimation->start();
}

void ToastWidget::paintEvent(QPaintEvent* event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // 绘制背景
    QColor bgColor = getBackgroundColor(m_options.type);
    painter.setBrush(bgColor);
    painter.setPen(Qt::NoPen);
    painter.drawRoundedRect(rect(), adaptSize(70), adaptSize(70));

    // 直接绘制SVG图标
    drawIcon(painter);

    QWidget::paintEvent(event);
}



void ToastWidget::drawIcon(QPainter& painter)
{
    // 图标位置和尺寸 - 使用屏幕适配
    int iconX = adaptSize(12); // 左边距
    int iconSize = adaptSize(116);
    int svgSize = adaptSize(70);
    int iconY = (height() - iconSize) / 2; // 垂直居中
    QRect iconBackgroundRect(iconX, iconY, iconSize, iconSize);
    QRect iconRect(iconX + (iconSize - svgSize) / 2, iconY + (iconSize - svgSize) / 2, svgSize, svgSize); // 图标在背景中居中

    // 绘制图标背景圆形
    QColor iconBgColor = getIconBackgroundColor(m_options.type);
    painter.setBrush(iconBgColor);
    painter.setPen(Qt::NoPen);
    painter.drawEllipse(iconBackgroundRect);

    // 直接绘制SVG图标
    QString svgData = getIconSvg(m_options.type);
    QSvgRenderer renderer(svgData.toUtf8());

    painter.setRenderHint(QPainter::Antialiasing);
    renderer.render(&painter, iconRect);
}

QPixmap ToastWidget::createIconPixmap(ToastType type, const QSize& size)
{
    QString svgData = getIconSvg(type);
    QSvgRenderer renderer(svgData.toUtf8());

    // 简单直接的渲染，不做复杂处理
    QPixmap pixmap(size);
    pixmap.fill(Qt::transparent);

    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    renderer.render(&painter);

    return pixmap;
}

QString ToastWidget::getIconSvg(ToastType type)
{
    switch (type) {
        case ToastType::Success:
            return ToastIcons::SUCCESS_ICON;
        case ToastType::Warning:
            return ToastIcons::WARNING_ICON;
        case ToastType::Error:
            return ToastIcons::ERROR_ICON;
        case ToastType::Info:
        default:
            return ToastIcons::INFO_ICON;
    }
}

QColor ToastWidget::getBackgroundColor(ToastType type)
{
    switch (type) {
        case ToastType::Success:
            return QColor("#29DA80");  // --color-success
        case ToastType::Warning:
            return QColor("#FFD151");  // --color-warning-light
        case ToastType::Error:
            return QColor("#FF969D");  // --color-error-light
        case ToastType::Info:
        default:
            return QColor("#D1D1D1");  // --color-info-light
    }
}

QColor ToastWidget::getIconBackgroundColor(ToastType type)
{
    switch (type) {
        case ToastType::Success:
            return QColor(255, 255, 255, 217); // rgba(255, 255, 255, 0.85)
        case ToastType::Warning:
            return QColor("#FFAA08");  // --color-warning
        case ToastType::Error:
            return QColor("#DA2934");  // --color-error
        case ToastType::Info:
        default:
            return QColor("#B4B4B4");  // --color-info
    }
}

int ToastWidget::adaptSize(int originalSize) const
{
    return ScreenAdaptationConstants::adaptSize(originalSize);
}

void ToastWidget::onFadeInFinished()
{
    if (m_autoCloseTimer) {
        m_autoCloseTimer->start(m_options.duration);
    }
}

void ToastWidget::onFadeOutFinished()
{
    // 先发射信号，让管理器处理队列
    emit toastClosed();

    // 延迟删除，避免在信号处理过程中删除对象
    QTimer::singleShot(0, this, [this]() {
        deleteLater();
    });
}

void ToastWidget::onAutoCloseTimer()
{
    hideToast();
}

// Toast 管理器实现
Toast::Toast(QObject* parent)
    : QObject(parent)
{
}

Toast::~Toast()
{
    clearAllToasts();
}

Toast* Toast::instance()
{
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new Toast();
    }
    return s_instance;
}

void Toast::showInfo(const QString& message, int duration)
{
    ToastOptions options(duration, ToastType::Info);
    show(message, options);
}

void Toast::showSuccess(const QString& message, int duration)
{
    ToastOptions options(duration, ToastType::Success);
    show(message, options);
}

void Toast::showWarning(const QString& message, int duration)
{
    ToastOptions options(duration, ToastType::Warning);
    show(message, options);
}

void Toast::showError(const QString& message, int duration)
{
    ToastOptions options(duration, ToastType::Error);
    show(message, options);
}

void Toast::show(const QString& message, const ToastOptions& options)
{
    if (QThread::currentThread() == QApplication::instance()->thread()) {
        instance()->showToast(message, options);
    } else {
        QMetaObject::invokeMethod(QApplication::instance(), [message, options]() {
            instance()->showToast(message, options);
        }, Qt::QueuedConnection);
    }
}

void Toast::show(const json & j) {
    if (QThread::currentThread() == QApplication::instance()->thread()) {
        showFromJson(j);
    } else {
        QMetaObject::invokeMethod(QApplication::instance(), [j]() {
            showFromJson(j);
        }, Qt::QueuedConnection);
    }
}

void Toast::showFromJson(const json& j) {
    std::string type = "info";
    std::string message = "";
    std::string position = "bottom";
    int duration = 3000;
    if (j.is_object()) {
        auto j_type = j["type"];
        auto j_message = j["message"];
        auto j_position = j["position"];
        auto j_duration = j["duration"];
        if (j_type.is_string()) {
            type = j_type.get<std::string>();
        }
        if (j_message.is_string()) {
            message = j_message.get<std::string>();
        }
        if (j_position.is_string()) {
            position = j_position.get<std::string>();
        }
        if (j_duration.is_number_integer()) {
            duration = j_duration.get<int>();
        }
    }

    ToastType toastType = ToastType::Info;
    if (type == "info") {
        toastType = ToastType::Info;
    } else if (type == "success") {
        toastType = ToastType::Success;
    } else if (type == "warning") {
        toastType = ToastType::Warning;
    } else if (type == "error") {
        toastType = ToastType::Error;
    }

    ToastPosition toastPosition = ToastPosition::Bottom;
    if (position == "top") {
        toastPosition = ToastPosition::Top;
    } else if (position == "topLeft") {
        toastPosition = ToastPosition::TopLeft;
    } else if (position == "topRight") {
        toastPosition = ToastPosition::TopRight;
    } else if (position == "bottom") {
        toastPosition = ToastPosition::Bottom;
    } else if (position == "bottomLeft") {
        toastPosition = ToastPosition::BottomLeft;
    } else if (position == "bottomRight") {
        toastPosition = ToastPosition::BottomRight;
    } else if (position == "center") {
        toastPosition = ToastPosition::Center;
    }

    ToastOptions options(duration, toastType, toastPosition);
    show(QString::fromStdString(message), options);
}

void Toast::clearAll()
{
    if (QThread::currentThread() == QApplication::instance()->thread()) {

        instance()->clearAllToasts();
    } else {
        QMetaObject::invokeMethod(QApplication::instance(), []() {
            instance()->clearAllToasts();
        }, Qt::QueuedConnection);
    }
}

void Toast::showToast(const QString& message, const ToastOptions& options)
{
    // 创建Toast窗口
    ToastWidget* toast = new ToastWidget(message, options);
    connect(toast, &ToastWidget::toastClosed, this, &Toast::onToastClosed);

    // 先添加到队列，然后计算位置（考虑已存在的Toast）
    {
        QMutexLocker locker(&m_queueMutex);
        m_toastQueue.enqueue(ToastItem(toast, options.position, nullptr));
    }

    // 计算位置（现在会考虑队列中已有的Toast）
    QPoint position = calculateToastPositionWithQueue(toast, options.position);
    toast->move(position);

    // 显示Toast
    toast->showToast();
}

void Toast::clearAllToasts()
{
    QMutexLocker locker(&m_queueMutex);
    while (!m_toastQueue.isEmpty()) {
        ToastItem item = m_toastQueue.dequeue();
        if (item.widget) {
            // 断开信号连接，避免在删除过程中触发onToastClosed
            item.widget->disconnect(this);
            item.widget->hide();
            item.widget->deleteLater();
        }
        if (item.animation) {
            item.animation->stop();
            item.animation->deleteLater();
        }
    }
}

void Toast::onToastClosed()
{
    ToastWidget* toast = qobject_cast<ToastWidget*>(sender());
    if (!toast) return;

    // 延迟处理队列操作，避免在信号处理过程中出现问题
    QTimer::singleShot(0, this, [this, toast]() {
        ToastPosition position = ToastPosition::Bottom;
        QPropertyAnimation* animation = nullptr;
        bool found = false;

        // 从队列中移除
        {
            QMutexLocker locker(&m_queueMutex);
            for (int i = 0; i < m_toastQueue.size(); ++i) {
                if (m_toastQueue[i].widget == toast) {
                    ToastItem item = m_toastQueue[i];
                    position = item.position;
                    animation = item.animation;
                    m_toastQueue.removeAt(i);
                    found = true;
                    break;
                }
            }
        }

        // 清理动画对象
        if (animation) {
            animation->stop();
            animation->deleteLater();
        }

        // 只有找到了Toast才更新位置
        if (found) {
            updateToastPositions(position);
        }
    });
}

void Toast::updateToastPositions(ToastPosition position)
{
    QMutexLocker locker(&m_queueMutex);

    int offset = 0; // 初始偏移量
    for (const ToastItem& item : m_toastQueue) {
        if (item.position == position && item.widget) {
            ToastWidget* toastWidget = qobject_cast<ToastWidget*>(item.widget);
            if (!toastWidget) continue;

            QPoint newPos = calculateToastPosition(toastWidget, position);

            // 根据位置类型调整偏移
            if (position == ToastPosition::Bottom ||
                position == ToastPosition::BottomLeft ||
                position == ToastPosition::BottomRight) {
                newPos.setY(newPos.y() - offset);
            } else if (position == ToastPosition::Top ||
                      position == ToastPosition::TopLeft ||
                      position == ToastPosition::TopRight) {
                newPos.setY(newPos.y() + offset);
            }

            // 直接移动到新位置，不使用动画避免复杂性
            item.widget->move(newPos);

            // 累加偏移量
            offset += item.widget->height() + ScreenAdaptationConstants::adaptSize(20); // 间距
        }
    }
}

QPoint Toast::calculateToastPosition(ToastWidget* toast, ToastPosition position)
{
    if (!toast) return QPoint(0, 0);

    QScreen* screen = QApplication::primaryScreen();
    if (!screen) return QPoint(0, 0);

    QRect screenGeometry = screen->geometry();
    QSize toastSize = toast->size();

    int x = 0, y = 0;
    int margin = ScreenAdaptationConstants::adaptSize(20);

    switch (position) {
        case ToastPosition::Top:
            x = (screenGeometry.width() - toastSize.width()) / 2;
            y = margin;
            break;
        case ToastPosition::TopLeft:
            x = margin;
            y = margin;
            break;
        case ToastPosition::TopRight:
            x = screenGeometry.width() - toastSize.width() - margin;
            y = margin;
            break;
        case ToastPosition::Bottom:
            x = (screenGeometry.width() - toastSize.width()) / 2;
            y = screenGeometry.height() - toastSize.height() - ScreenAdaptationConstants::adaptSize(99); // 底部偏移
            break;
        case ToastPosition::BottomLeft:
            x = margin;
            y = screenGeometry.height() - toastSize.height() - margin;
            break;
        case ToastPosition::BottomRight:
            x = screenGeometry.width() - toastSize.width() - margin;
            y = screenGeometry.height() - toastSize.height() - margin;
            break;
        case ToastPosition::Center:
            x = (screenGeometry.width() - toastSize.width()) / 2;
            y = (screenGeometry.height() - toastSize.height()) / 2;
            break;
    }

    return QPoint(x, y);
}

QPoint Toast::calculateToastPositionWithQueue(ToastWidget* toast, ToastPosition position)
{
    if (!toast) return QPoint(0, 0);

    // 获取基础位置
    QPoint basePos = calculateToastPosition(toast, position);

    // 计算偏移量，考虑已存在的同位置Toast
    int offset = 0;
    QMutexLocker locker(&m_queueMutex);

    for (const ToastItem& item : m_toastQueue) {
        if (item.position == position && item.widget && item.widget != toast) {
            offset += item.widget->height() + ScreenAdaptationConstants::adaptSize(20); // 间距
        }
    }

    // 根据位置类型调整偏移
    if (position == ToastPosition::Bottom ||
        position == ToastPosition::BottomLeft ||
        position == ToastPosition::BottomRight) {
        basePos.setY(basePos.y() - offset);
    } else if (position == ToastPosition::Top ||
              position == ToastPosition::TopLeft ||
              position == ToastPosition::TopRight) {
        basePos.setY(basePos.y() + offset);
    }

    return basePos;
}

#include "Toast.moc"
