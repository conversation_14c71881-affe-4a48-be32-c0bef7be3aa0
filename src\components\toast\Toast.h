#ifndef TOAST_H
#define TOAST_H

#include <QWidget>
#include <QLabel>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>
#include <QTimer>
#include <QApplication>
#include <QScreen>
#include <QPainter>
#include <QSvgRenderer>
#include <QPixmap>
#include <QPaintEvent>
#include <QQueue>
#include <QMutex>
#include <QMutexLocker>
#include <QStyleOption>
#include "nlohmann/json.hpp"

// 屏幕适配支持
#include "../../screen_adaptation/ScreenAdaptationManager.h"

// 类型定义
using json = nlohmann::json;

/**
 * @file Toast.h
 * @brief 全局Toast组件 - 独立可复用的Qt Toast实现
 * 
 * 特性：
 * 1. 内嵌SVG图标，无需外部资源文件
 * 2. 支持多种类型和位置
 * 3. 队列管理，支持多个Toast同时显示
 * 4. 屏幕适配支持
 * 5. 线程安全
 * 6. 易于复制到其他项目
 */

/**
 * @brief Toast类型枚举
 */
enum class ToastType {
    Info,
    Success,
    Warning,
    Error
};

/**
 * @brief Toast位置枚举
 */
enum class ToastPosition {
    Top,
    TopLeft,
    TopRight,
    Bottom,
    BottomLeft,
    BottomRight,
    Center
};

/**
 * @brief Toast配置选项
 */
struct ToastOptions {
    int duration = 3000;                    // 显示时长(毫秒)
    ToastType type = ToastType::Info;       // Toast类型
    ToastPosition position = ToastPosition::Bottom;  // 显示位置
    int gap = 20;                          // Toast间距
    bool autoClose = true;                 // 是否自动关闭
    
    ToastOptions() = default;
    ToastOptions(int d, ToastType t = ToastType::Info, ToastPosition p = ToastPosition::Bottom) 
        : duration(d), type(t), position(p) {}
};

/**
 * @brief Toast项目信息
 */
struct ToastItem {
    QWidget* widget;
    ToastPosition position;
    QPropertyAnimation* animation;
    
    ToastItem(QWidget* w, ToastPosition p, QPropertyAnimation* a) 
        : widget(w), position(p), animation(a) {}
};

/**
 * @brief 单个Toast窗口组件
 */
class ToastWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ToastWidget(const QString& message, const ToastOptions& options, QWidget* parent = nullptr);
    ~ToastWidget();

    void showToast();
    void hideToast();

protected:
    void paintEvent(QPaintEvent* event) override;

private slots:
    void onFadeInFinished();
    void onFadeOutFinished();
    void onAutoCloseTimer();

private:
    void setupUI();
    void setupAnimations();
    void drawIcon(QPainter& painter);
    QPixmap createIconPixmap(ToastType type, const QSize& size);
    QString getIconSvg(ToastType type);
    QColor getBackgroundColor(ToastType type);
    QColor getIconBackgroundColor(ToastType type);
    int adaptSize(int originalSize) const;

    QString m_message;
    ToastOptions m_options;
    QLabel* m_messageLabel;
    QHBoxLayout* m_layout;
    
    QPropertyAnimation* m_fadeInAnimation;
    QPropertyAnimation* m_fadeOutAnimation;
    QGraphicsOpacityEffect* m_opacityEffect;
    QTimer* m_autoCloseTimer;

signals:
    void toastClosed();
};

/**
 * @brief Toast管理器（单例）
 */
class Toast : public QObject
{
    Q_OBJECT

public:
    static Toast* instance();
    
    // 便捷方法
    static void showInfo(const QString& message, int duration = 3000);
    static void showSuccess(const QString& message, int duration = 3000);
    static void showWarning(const QString& message, int duration = 3000);
    static void showError(const QString& message, int duration = 3000);
    
    // 通用方法
    static void show(const QString& message, const ToastOptions& options = ToastOptions());
    static void show(const json& j);

    // 清除所有Toast
    static void clearAll();

private slots:
    void onToastClosed();

private:
    explicit Toast(QObject* parent = nullptr);
    ~Toast();

    void showToast(const QString& message, const ToastOptions& options);
    void clearAllToasts();
    void updateToastPositions(ToastPosition position);
    QPoint calculateToastPosition(ToastWidget* toast, ToastPosition position);
    QPoint calculateToastPositionWithQueue(ToastWidget* toast, ToastPosition position);

    // 线程安全的JSON处理方法
    static void showFromJson(const json& j);
    
    static QMutex s_mutex;
    static Toast* s_instance;
    
    QQueue<ToastItem> m_toastQueue;
    QMutex m_queueMutex;
};

#endif // TOAST_H
