#ifndef IWINDOWMANAGER_H
#define IWINDOWMANAGER_H

// 前向声明，避免包含windows.h
#ifdef Q_OS_WIN
struct HWND__;
typedef struct HWND__* HWND;
#endif

/**
 * @brief 窗口管理器接口
 *
 * 用于解耦WindowActivationBlocker和ZIndexManager之间的直接依赖
 */
class IWindowManager
{
public:
    virtual ~IWindowManager() = default;

    /**
     * @brief 检查窗口是否被管理
     * @param hwnd Windows窗口句柄
     * @return 如果窗口被管理返回true
     */
#ifdef Q_OS_WIN
    virtual bool isWindowManaged(HWND hwnd) const = 0;
#else
    virtual bool isWindowManaged(void* hwnd) const = 0;
#endif
};

#endif // IWINDOWMANAGER_H
