#include "WhiteboardToolConfigManager.h"

SideBarConstants::ToolInfo WhiteboardToolConfigManager::getThumbnailListConfig()
{
    SideBarConstants::ToolInfo thumbnailList;
    thumbnailList.toolName = "缩略图";
    thumbnailList.toolIcon = ":/images/side/side_resources.svg";
    thumbnailList.viewType = SideBarConstants::ViewType::Normal;
    thumbnailList.clickType = SideBarConstants::ClickType::Cef;
    thumbnailList.url = "http://domainname/src/widget/ThumbnailList/index.html?documentName=3%20%E8%AF%B4%E5%92%8C%E5%81%9A.pptx&transThirdName=WY&thumbnailUrl=https%3A%2F%2Fwb.vod.126.net%2Fcourseware%2Fdoc%2F25251963%2F1223412%2Fqy0SsWgA%2Fthumbnail%2F&total=16";
    thumbnailList.geometry = QRect(
        ScreenAdaptationConstants::adaptSize(40.6),
        ScreenAdaptationConstants::adaptSize(627.2),
        ScreenAdaptationConstants::adaptSize(593.5),
        ScreenAdaptationConstants::adaptSize(1525)
    );
    thumbnailList.zlevel = static_cast<int>(ZIndexLevel::CEF_THUMBNAIL_LIST);
    thumbnailList.showToolBox = false;
    return thumbnailList;
}

SideBarConstants::ToolInfo WhiteboardToolConfigManager::getSaveBoardConfig()
{
    SideBarConstants::ToolInfo saveBoard;
    saveBoard.toolName = "保存";
    saveBoard.toolIcon = ":/images/side/side_exit.svg";
    saveBoard.viewType = SideBarConstants::ViewType::Normal;
    saveBoard.clickType = SideBarConstants::ClickType::Cef;
    saveBoard.url = QString("http://domainname/src/widget/SaveBlackBoardDialog/index.html?top=%1&left=%2").arg(0).arg(0);
    QSize screenSize = ScreenUtils::getScreenSize();
    saveBoard.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    saveBoard.zlevel = static_cast<int>(ZIndexLevel::CEF_RESOURCE_SELECTOR);
    saveBoard.showToolBox = false;
    return saveBoard;
}

SideBarConstants::ToolInfo WhiteboardToolConfigManager::getExitDialogConfig()
{
    SideBarConstants::ToolInfo exitDialog;
    exitDialog.toolName = "退出";
    exitDialog.viewType = SideBarConstants::ViewType::Normal;
    exitDialog.clickType = SideBarConstants::ClickType::Cef;
    exitDialog.url = "http://domainname/src/widget/QuitClassDialog/index.html";
    QSize screenSize = ScreenUtils::getScreenSize();
    exitDialog.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    exitDialog.zlevel = static_cast<int>(ZIndexLevel::CEF_EXIT_DIALOG);
    exitDialog.showToolBox = false;
    return exitDialog;
}

SideBarConstants::ToolInfo WhiteboardToolConfigManager::getClassSummaryDialogConfig()
{
    SideBarConstants::ToolInfo summaryDialog;
    summaryDialog.toolName = "课堂小结";
    summaryDialog.viewType = SideBarConstants::ViewType::Normal;
    summaryDialog.clickType = SideBarConstants::ClickType::Cef;
    summaryDialog.url = "http://domainname/src/widget/ClassSummaryDialog/index.html";
    QSize screenSize = ScreenUtils::getScreenSize();
    summaryDialog.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    summaryDialog.zlevel = static_cast<int>(ZIndexLevel::CEF_CLASS_SUMMARY_DIALOG);
    summaryDialog.showToolBox = false;
    return summaryDialog;
}

SideBarConstants::ToolInfo WhiteboardToolConfigManager::getSettingDialogConfig()
{
    SideBarConstants::ToolInfo settingDialog;
    settingDialog.toolName = "设置";
    settingDialog.viewType = SideBarConstants::ViewType::Normal;
    settingDialog.clickType = SideBarConstants::ClickType::Cef;
    settingDialog.url = "http://domainname/src/widget/SettingDialog/index.html";
    QSize screenSize = ScreenUtils::getScreenSize();
    settingDialog.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    settingDialog.zlevel = static_cast<int>(ZIndexLevel::CEF_SETTING_DIALOG);
    settingDialog.showToolBox = false;
    return settingDialog;
}

SideBarConstants::ToolInfo WhiteboardToolConfigManager::getNotOpenOctopusDialogConfig()
{
    SideBarConstants::ToolInfo notOpenOctopusDialog;
    notOpenOctopusDialog.toolName = "未开启小结对话框";
    notOpenOctopusDialog.viewType = SideBarConstants::ViewType::Normal;
    notOpenOctopusDialog.clickType = SideBarConstants::ClickType::Cef;
    notOpenOctopusDialog.url = "http://domainname/src/widget/NotOpenOctopusDialog/index.html";
    QSize screenSize = ScreenUtils::getScreenSize();
    notOpenOctopusDialog.geometry = QRect(0, 0, screenSize.width(), screenSize.height());
    notOpenOctopusDialog.zlevel = static_cast<int>(ZIndexLevel::CEF_SETTING_DIALOG);
    notOpenOctopusDialog.showToolBox = false;
    return notOpenOctopusDialog;
}
