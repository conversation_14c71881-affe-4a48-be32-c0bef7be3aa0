#pragma once
#include "../sidebar/SideBarConstants.h"
#include <QRect>
#include "../utils/ScreenUtils.h"

class WhiteboardToolConfigManager
{
public:
    static SideBarConstants::ToolInfo getThumbnailListConfig();
    static SideBarConstants::ToolInfo getSaveBoardConfig();
    static SideBarConstants::ToolInfo getExitDialogConfig();
    static SideBarConstants::ToolInfo getClassSummaryDialogConfig();
    static SideBarConstants::ToolInfo getSettingDialogConfig();
    static SideBarConstants::ToolInfo getNotOpenOctopusDialogConfig();
    // 可继续添加其他工具的配置获取方法
}; 
