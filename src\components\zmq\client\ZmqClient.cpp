﻿#include "ZmqClient.h"
#include "zhelpers.hpp"
#include "src/utils/Uuid.h"

using json = nlohmann::json;

void ClientZmqMonitor::on_event_connected(const zmq_event_t &event_, const char *addr_) {
    zmqClient_->sendInitRequest();
}

ZmqClient::ZmqClient(const std::string& endpoint, const std::string& identity)
    : ctx_(1),
      running_(false),
      client_zmq_monitor_(this) {

    if (identity.empty()) {
        // 随机生成客户端id
        _identity = UuidUtil::generate_v4();
    } else {
        _identity = identity;
    }

    endpoint_ = endpoint;
    monitor_endpoint_ = "inproc://" + identity + "_monitor_client.sock";
    sender_endpoint_ = "inproc://" + identity + "_sender_client.sock";
}

ZmqClient::~ZmqClient() {
    stop();
}

std::string ZmqClient::getIdentity() const {
    return client_socket_.get(zmq::sockopt::routing_id);
}

void ZmqClient::sendInitRequest() {
    ZmqMsg msg = ZmqMsg::newRequest("init", json{});

    this->sendRequest(msg, [this](const ZmqMsg& response) {
        if (response.success()) {
            qInfo() << "client: " << this->getIdentity() << " init success";
        }
        else {
            qInfo() << "client: " << this->getIdentity() << " init failed, response: " << response.serialize().c_str();
        }
    });
}

void ZmqClient::startMonitor() {
    monitor_thread_ = std::thread([this]() {
        try {
            client_zmq_monitor_.monitor(client_socket_, monitor_endpoint_, ZMQ_EVENT_ALL);
        }
        catch (const std::exception &e) {
            qWarning() << "Error in monitor loop: " << e.what();
        }
        catch (...) {
            qWarning() << "Unknown error occurred in monitor loop.";
        }
    });
}

void ZmqClient::start() {
    qInfo() << "starting client: " << endpoint_;

    worker_thread_ = std::thread(&ZmqClient::clientLoop, this);

    running_ = true;

    qInfo() << "client started: " << endpoint_;
}

void ZmqClient::stop() {
    if (!running_) {
        return;
    }
    qInfo() << "stopping client: " << endpoint_;

    running_ = false;
    if (worker_thread_.joinable()) {
        worker_thread_.join();
    }
    sender_socket_.close();
    client_socket_.close();


    qInfo() << "client stopped: " << endpoint_;
}


void ZmqClient::registerHandler(const std::string& method, ZmqMethodHandler handler) {
    qInfo() << "ZmqClient registerHandler: " << method;

    std::lock_guard<std::mutex> lock(handlers_mutex_);
    method_handlers_[method] = handler;
}

void ZmqClient::sendRequest(const ZmqMsg& message, ZmqResponseCallback callback,
                            int timeoutMs, ZmqTimeoutCallback timeoutCallback) {
    auto data = std::make_shared<ZmqTimeoutCallbackData>(message);
    data->callback = callback;
    data->timeoutCallback = timeoutCallback;
    data->timeoutMs = timeoutMs;

    {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        pending_callbacks_[message.getId()] = data;
    }

    // 启动超时定时器
    {
        std::lock_guard<std::mutex> lock(m_timer_mutex_);
        data->timerId = m_timer_.add(
                std::chrono::milliseconds(timeoutMs),
                [this, msgId = message.getId()] (CppTime::timer_id) {
                    std::shared_ptr<ZmqTimeoutCallbackData> cbData;
                    {
                        std::lock_guard<std::mutex> lock(callback_mutex_);
                        auto it = pending_callbacks_.find(msgId);
                        if (it != pending_callbacks_.end()) {
                            cbData = it->second;
                            pending_callbacks_.erase(it);
                        }
                    }
                    if (cbData && cbData->timeoutCallback) {
                        ZmqMsg timeoutMsg = ZmqMsg::newErrorResponse(cbData->request, "Request timeout");
                        cbData->timeoutCallback(timeoutMsg);
                    }
                }
        );
    }

    qInfo() << "ZmqClient sendRequest: " << message.serialize().c_str();
    sendToServer(message);
}

void ZmqClient::sendToServer(const ZmqMsg &message) {
    // 每个线程一个socket，socket不跨线程使用
    thread_local zmq::socket_t worker = [this]{
        zmq::socket_t so(ctx_, ZMQ_PUSH);
        so.connect(sender_endpoint_);
        return so;
    }();

    worker.send(zmq::buffer(message.serialize()), zmq::send_flags::none);
}

void ZmqClient::sendResponse(const ZmqMsg &message) {
    qDebug() << "ZmqClient sendResponse: " << message.serialize().c_str();

    sendToServer(message);
}

void ZmqClient::processRequest(const ZmqMsg& request) {
    ZmqMethodHandler callback;
    {
        std::lock_guard<std::mutex> lock(handlers_mutex_);
        auto it = method_handlers_.find(request.getMethod());
        if (it != method_handlers_.end()) {
            callback = it->second;
        }
    }
    if (callback != nullptr) {
        try {
            callback(request, [this](const ZmqMsg& response) {
                sendResponse(response);
            });
        }
        catch (const std::exception& e) {
            // 处理异常情况
            qWarning() << "Error processing request: " << e.what();
            ZmqMsg error_response = ZmqMsg::newErrorResponse(request, "Error processing request: " + std::string(e.what()));
            sendResponse(error_response);
        }
        catch (...) {
            // 处理未知异常情况
            qWarning() << "Unknown error occurred while processing request.";
            ZmqMsg error_response = ZmqMsg::newErrorResponse(request, "Unknown error occurred while processing request.");
            sendResponse(error_response);
        }
    }
    else {
        // 默认处理器
        qWarning() << "No handler registered for method: " << request.getMethod();
        ZmqMsg response = ZmqMsg::newErrorResponse(request, "No handler registered for method:  " + std::string(request.getMethod()));
        sendResponse(response);
    }

}
void ZmqClient::handleResponse(ZmqMsg received_msg) {
    try {
        std::shared_ptr<ZmqTimeoutCallbackData> cbData;
        {
            std::lock_guard<std::mutex> lock(callback_mutex_);
            auto it = pending_callbacks_.find(received_msg.getId());
            if (it != pending_callbacks_.end()) {
                cbData = it->second;
                pending_callbacks_.erase(it);
            }
        }
        // 移除定时器
        if (cbData) {
            std::lock_guard<std::mutex> lock(m_timer_mutex_);
            m_timer_.remove(cbData->timerId);
        }
        if (cbData && cbData->callback) {
            cbData->callback(received_msg);
        }
    }
    catch (const std::exception &e) {
        qWarning() << "Error handling response: " << e.what();
    }
    catch (...) {
        qWarning() << "Unknown error occurred while handling response.";
    }
}

void ZmqClient::clientLoop() {
    try {
        client_socket_ = zmq::socket_t(ctx_, ZMQ_DEALER);
        client_socket_.set(zmq::sockopt::routing_id, _identity);

        qInfo() << "ZmqClient identity: " << _identity << ", endpoint: " << endpoint_ << ", sender_endpoint: "
                << sender_endpoint_;

        sender_socket_ = zmq::socket_t(ctx_, ZMQ_PULL);
        sender_socket_.bind(sender_endpoint_);

        startMonitor();

        // 设置心跳参数
        int heartbeat_interval = 2000; // 2秒
        int heartbeat_timeout = 5000; // 5秒
        client_socket_.set(zmq::sockopt::heartbeat_ivl, heartbeat_interval);
        client_socket_.set(zmq::sockopt::heartbeat_timeout, heartbeat_timeout);
        client_socket_.set(zmq::sockopt::heartbeat_ttl, heartbeat_timeout * 2);

        client_socket_.connect(endpoint_);

        while (running_) {
            try {
                zmq::pollitem_t items[] = {
                        {client_socket_, 0, ZMQ_POLLIN, 0},
                        {sender_socket_, 0, ZMQ_POLLIN, 0}
                };

                zmq::poll(items, 2, std::chrono::milliseconds(1000));

                if (items[0].revents & ZMQ_POLLIN) {
                    zmq::message_t message;
                    auto result = client_socket_.recv(message);
                    if (!result.has_value()) {
                        continue;
                    }
                    qDebug() << "ZmqClient receive: " << message.to_string().c_str();

                    ZmqMsg received_msg = ZmqMsg::deserialize(message.to_string());
                    if (!received_msg.isValid()) {
                        qWarning() << "Invalid message received: " << message.to_string().c_str();
                        continue;
                    }

                    if (received_msg.getMethod() != "__response__") {
                        // Add request processing to thread pool
                        (void) server_thread_pool_.submit_task([this, received_msg]() {
                            processRequest(received_msg);
                        });
                    } else {
                        (void) client_thread_pool_.submit_task([this, received_msg]() {
                            handleResponse(received_msg);
                        });
                    }
                }
                if (items[1].revents & ZMQ_POLLIN) {
                    zmq::message_t message;
                    auto result = sender_socket_.recv(message);
                    if (!result.has_value()) {
                        continue;
                    }
                    client_socket_.send(message, zmq::send_flags::none);
                }
            }
            catch (const std::exception &e) {
                qWarning() << "Error in client loop: " << e.what();
            }
            catch (...) {
                qWarning() << "Unknown error occurred in client loop.";
            }
        }
    }
    catch (const std::exception &e) {
        qWarning() << "Error in client loop: " << e.what();
    }
    catch (...) {
        qWarning() << "Unknown error occurred in client loop.";
    }
}

