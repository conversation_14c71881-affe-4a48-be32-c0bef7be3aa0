﻿#ifndef ZMQ_CLIENT_H
#define ZMQ_CLIENT_H

#include <zmq.hpp>
#include <thread>
#include <mutex>
#include <unordered_map>
#include <functional>
#include <atomic>
#include <vector>
#include <queue>
#include <condition_variable>

#include "BS_thread_pool.hpp"
#include "../common/ZmqCommon.h"
#include "cpptime.h"
#include <memory>
#include <chrono>

class ZmqClient;

/**
 * 客户端 zmq 事件监听器
 */
class ClientZmqMonitor: public BaseZmqMonitor {
public:
    explicit ClientZmqMonitor(ZmqClient* zmqClient) : zmqClient_(zmqClient) {}

    void on_event_connected(const zmq_event_t &event_, const char *addr_) override;

private:
    ZmqClient* zmqClient_;
};

/**
 * zmq 客户端，用于发送请求给 zmq 服务器，也可以接收并处理 zmq 服务器的请求
 */
class ZmqClient {
public:
    explicit ZmqClient(const std::string& endpoint = "tcp://localhost:5570", const std::string& identity = "");
    ~ZmqClient();

    /**
     * 获取客户端标识
     * @return 客户端标识
     */
    std::string getIdentity() const;

    /**
     * 启动客户端，建立连接，并可接收并处理 zmq 服务器的请求
     */
    void start();

    /**
     * 停止客户端，断开连接，并停止接收并处理 zmq 服务器的请求
     */
    void stop();

    /**
     * 注册一个方法处理器，用于处理 zmq 服务器的请求
     * @param method 方法名称
     * @param handler 方法处理器
     */
    void registerHandler(const std::string& method, ZmqMethodHandler handler);

    /**
     * 发送请求给 zmq 服务器
     * @param message 要发送的消息
     * @param callback 回调函数，用于处理 zmq 服务器的响应
     */
    void sendRequest(const ZmqMsg& message, ZmqResponseCallback callback,
                     int timeoutMs = 10 * 60 * 1000,
                     ZmqTimeoutCallback timeoutCallback = nullptr);
    /**
     * 发送响应给 zmq 服务器
     */
    void sendResponse(const ZmqMsg& message);

private:
    friend class ClientZmqMonitor;

    /**
     * 发送init请求
     */
    void sendInitRequest();

    /**
     * 启动监控事件
     */
    void startMonitor();

    /**
     * 接收并处理 zmq 服务器的请求 & 响应
     */
    void clientLoop();

    /**
     * 处理服务端发送的请求
     * @param request
     */
    void processRequest(const ZmqMsg& request);

    /**
     * 处理服务端发送的响应
     * @param received_msg 服务端发送的响应
     */
    void handleResponse(ZmqMsg received_msg);

    /**
     * 发送消息给 zmq 服务器
     * @param message 要发送的消息
     */
    void sendToServer(const ZmqMsg &message) ;

    zmq::socket_t client_socket_;
    std::thread worker_thread_;
    std::atomic<bool> running_;

    std::mutex callback_mutex_;
    std::unordered_map<std::string, std::shared_ptr<ZmqTimeoutCallbackData>> pending_callbacks_;
    CppTime::Timer m_timer_;
    std::mutex m_timer_mutex_;

    BS::thread_pool<> server_thread_pool_;
    BS::thread_pool<> client_thread_pool_;

    std::unordered_map<std::string, ZmqMethodHandler> method_handlers_;
    std::mutex handlers_mutex_;

    /**
     * 客户端 zmq 事件监听相关
     */
    ClientZmqMonitor client_zmq_monitor_;
    std::thread monitor_thread_;
    /**
     * 监控地址
     */
    std::string monitor_endpoint_;

    /**
     * 要连接的server地址
     */
    std::string endpoint_;

    /**
     * 客户端标识
     */
    std::string _identity;


    /**
     * sender socket
     */
    zmq::socket_t sender_socket_;
    /**
     * 发送给server的地址
     */
    std::string sender_endpoint_;

    // 在socket后面释放
    zmq::context_t ctx_;
};

#endif // ZMQ_CLIENT_H