﻿
#include "ZmqCommon.h"

std::string ZmqMsg::serialize() const {
    json j = json{
                    {"id", this->id},
                    {"method", this->method},
                    {"data", this->data},
                    {"status", this->status},
                    {"requestMethod", this->requestMethod}
    };
    return j.dump();
}

ZmqMsg ZmqMsg::deserialize(const std::string& str) {
    if (str.empty()) {
        qInfo() << "ZmqMsg string is empty.";
        return ZmqMsg{};
    }
    try {
        json j = json::parse(str);
        if (!j.is_object()) {
            qInfo() << "ZmqMsg is not an object. msg: " + QString::fromStdString(str.c_str());
            return ZmqMsg{};
        }

        ZmqMsg msg;

        if (!j.contains("id") || j.at("id").is_null()) {
            qInfo() << "ZmqMsg id is required. msg: " + QString::fromStdString(str.c_str());
            return ZmqMsg{};
        }
        j.at("id").get_to(msg.id);

        if (!j.contains("method") || j.at("method").is_null()) {
            qInfo() << "ZmqMsg method is required. msg: " + QString::fromStdString(str.c_str());
            return ZmqMsg{};
        }
        j.at("method").get_to(msg.method);

        if (j.contains("data") && !j.at("data").is_null()) {
            j.at("data").get_to(msg.data);
        }

        if (j.contains("status") && !j.at("status").is_null()) {
            j.at("status").get_to(msg.status);
        }

        if (j.contains("requestMethod") && !j.at("requestMethod").is_null()) {
            j.at("requestMethod").get_to(msg.requestMethod);
        }

        return msg;
    }
    catch (const std::exception& e) {
        qWarning() << "Error deserializing ZmqMsg: " << str.c_str() << ", what: " << e.what();
        return ZmqMsg{};
    }
    catch (...) {
        qWarning() << "Unknown error deserializing ZmqMsg: " << str.c_str();
        return ZmqMsg{};
    }
}
