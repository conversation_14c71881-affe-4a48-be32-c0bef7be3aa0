﻿#ifndef ZMQ_COMMON_H
#define ZMQ_COMMON_H

#include <string>
#include <random>
#include <functional>
#include <sstream>
#include <iostream>

#include <nlohmann/json.hpp>

#include "zmq.hpp"
#include "src/utils/Uuid.h"
#include "cpptime.h"

#include <QDebug>

static std::string generate_zmq_id();

// Message structure
class ZmqMsg {
    using json = nlohmann::json;

public:

    // 构造函数
    ZmqMsg() = default;
    ZmqMsg(const std::string& id, const std::string& method, const json& data, const std::string& status, const std::string& requestMethod)
            : id(id), method(method), data(data), status(status), requestMethod(requestMethod) {}

    // 静态方法，用于创建请求
    static ZmqMsg newRequest(const std::string& method, const json& data) {
        return ZmqMsg(generate_zmq_id(), method, data, "", "");
    }
    // 静态方法，用于创建响应
    static ZmqMsg newResponse(const ZmqMsg& request, const json& data, const std::string& status) {
        return ZmqMsg(request.getId(), "__response__", data, status, request.getMethod());
    }
    static ZmqMsg newResponse(const ZmqMsg& request, const json& data) {
        return ZmqMsg(request.getId(), "__response__", data, "200", request.getMethod());
    }
    static ZmqMsg newErrorResponse(const ZmqMsg& request, const std::string &message) {
        return ZmqMsg(request.getId(), "__response__", json{"message", message}, "500", request.getMethod());
    }
    bool success() const {
        return status == "200";
    }

    std::string getId() const {
        return id;
    }
    std::string getMethod() const {
        return method;
    }
    json getData() const {
        return data;
    }
    std::string getStatus() const {
        return status;
    }
    std::string getRequestMethod() const {
        return requestMethod;
    }

    // Serialize to string
    std::string serialize() const;

    // Deserialize from string
    static ZmqMsg deserialize(const std::string& str);

    // 支持 << 运算符
    friend std::ostream& operator<<(std::ostream& os, const ZmqMsg& msg) {
        os << "ZmqMsg{id=" << msg.id << ", method=" << msg.method << ", data=" << msg.data.dump() << ", status=" << msg.status << ", requestMethod=" << msg.requestMethod << "}";
        return os;
    }

    bool isValid() {
        return !getId().empty() && !getMethod().empty();
    }

private:

    std::string id;
    std::string method;
    json data;
    std::string status;
    std::string requestMethod;

};

// Callback function type
using ZmqResponseCallback = std::function<void(const ZmqMsg&)>;
using ZmqMethodHandler = std::function<void(const ZmqMsg&, ZmqResponseCallback)>;
using ZmqTimeoutCallback = std::function<void(const ZmqMsg&)>;

struct ZmqTimeoutCallbackData {
    ZmqMsg request;
    std::chrono::steady_clock::time_point startTime;
    int timeoutMs;
    CppTime::timer_id timerId;
    ZmqResponseCallback callback;
    std::function<void(const ZmqMsg&)> timeoutCallback;

    ZmqTimeoutCallbackData(const ZmqMsg& req)
            : request(req), timeoutMs(0), timerId(0) {
        startTime = std::chrono::steady_clock::now();
    }
};

// 生成zmq id 的函数
static std::string generate_zmq_id() {
    return UuidUtil::generate_v4();
}

class BaseZmqMonitor : public zmq::monitor_t {
public:
    // 实现所有的on_xxx 方法
    void on_monitor_stopped() override {
        qInfo() << "on_event Monitor stopped";
    }
    void on_monitor_started() override {
        qInfo() << "on_event Monitor started";
    }
    void on_event_connected(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Connected to:" << address;
    }
    void on_event_connect_delayed(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Connect delayed to:" << address;
    }
    void on_event_connect_retried(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Connect retried to:" << address;
    }
    void on_event_listening(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Listening on:" << address;
    }
    void on_event_bind_failed(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Bind failed on:" << address;
    }
    void on_event_accepted(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Accepted connection from:" << address;
    }
    void on_event_accept_failed(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Accept failed from:" << address;
    }
    void on_event_closed(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Closed connection to:" << address;
    }
    void on_event_close_failed(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Close failed to:" << address;
    }
    void on_event_disconnected(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Disconnected from:" << address;
    }
    void on_event_handshake_failed_no_detail(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Handshake failed no detail on:" << address;
    }
    void on_event_handshake_failed_protocol(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Handshake failed protocol on:" << address;
    }
    void on_event_handshake_failed_auth(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Handshake failed auth on:" << address;
    }
    void on_event_handshake_succeeded(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Handshake succeeded on:" << address;
    }
    void on_event_unknown(const zmq_event_t &event, const char *address) override {
        qInfo() << "on_event Unknown event on:" << address;
    }
};



#endif // ZMQ_COMMON_H