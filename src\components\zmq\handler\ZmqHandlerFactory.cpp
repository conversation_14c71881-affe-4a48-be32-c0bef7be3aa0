﻿//
// Created by HLJY on 2025/6/19.
//

#include "ZmqHandlerFactory.h"

std::vector<std::function<ZmqHandler*()>>& ZmqHandlerFactory::getRegistry() {
    static std::vector<std::function<ZmqHandler*()>> registry;
    return registry;
}

void ZmqHandlerFactory::registerHandler(std::function<ZmqHandler*()> creator) {
    getRegistry().push_back(creator);
}

void ZmqHandlerFactory::autoRegister() {
    for (auto& creator : getRegistry()) {
        ZmqHandler* instance = creator();
        instance->registerHandler();
        delete instance;
    }
}
