﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef ZMQHANDLERFACTORY_H
#define ZMQHANDLERFACTORY_H
#include "ZmqHandler.h"


class ZmqHandlerFactory {
public:
    // 注册处理器
    static void registerHandler( std::function<ZmqHandler*()> creator );

    // 自动调用所有注册方法
    static void autoRegister();

private:
    static std::vector<std::function<ZmqHandler*()>>& getRegistry();

};



#endif //ZMQHANDLERFACTORY_H
