#include "ZmqServer.h"
#include "zhelpers.hpp"
#include "src/utils/Uuid.h"


ZmqServer::ZmqServer(const std::string &endpoint)
    : ctx_(1),
      running_(false),
      endpoint_(endpoint) {

    std::string uuid = UuidUtil::generate_v4();
    monitor_endpoint_ = "inproc://" + uuid + "_monitor_server.sock";
    backend_endpoint_ = "inproc://" + uuid + "_backend.sock";
    sender_endpoint_ = "inproc://" + uuid + "_sender.sock";

    instance_.store(this);
}

ZmqServer::~ZmqServer() {
    stop();
}

void ZmqServer::startMonitor() {
    monitor_thread_ = std::thread([this]() {
        try {
            server_zmq_monitor_.monitor(front_socket_, monitor_endpoint_, ZMQ_EVENT_ALL);
        }
        catch (const std::exception &e) {
            qWarning() << "Error in monitor loop: " << e.what();
        }
        catch (...) {
            qWarning() << "Unknown error occurred in monitor loop.";
        }
    });
}

void ZmqServer::start() {
    qInfo() << "starting server: " << endpoint_;

    registerInitHandler();

    server_loop_thread_ = std::thread(&ZmqServer::serverLoop, this);


    running_ = true;

    qInfo() << "server started: " << endpoint_;
}

void ZmqServer::proxyLoop() {
    try {
        zmq::proxy(static_cast<void*>(front_socket_),
                   static_cast<void*>(backend_socket_),
                   nullptr);
    }
    catch (const std::exception& e) {
        // Handle proxy error
        qWarning() << "Proxy error: " << e.what();
    }
    catch (...) {
        // Handle other exceptions
        qWarning() << "Unknown error occurred in proxy loop.";
    }
}

void ZmqServer::registerInitHandler() {
    qInfo() << "register init handler";
    registerHandler("init", [this](const ZmqMsg &request, ZmqResponseCallback callback) {
        auto response = ZmqMsg::newResponse(request,
                                            json{
                                                {"message", "init success"}
                                            }
        );

        std::function<void(const std::string &)> listener;
        {
            std::lock_guard<std::mutex> lock(init_listener_mutex_);
            listener = init_listener_;
        }
        if (listener) {
            std::string identity;
            {
                std::lock_guard<std::mutex> lock(request_identity_map_mutex_);
                identity = request_identity_map_[request.getId()];
            }
            listener(identity);
        }

        callback(response);
    });
}

void ZmqServer::stop() {
    if (!running_) {
        return;
    }
    qInfo() << "stopping server: " << endpoint_;
    running_ = false;
    if (server_loop_thread_.joinable()) {
        server_loop_thread_.join();
    }
    front_socket_.close();
    backend_socket_.close();
    ctx_.close();
    qInfo() << "server stopped: " << endpoint_;
}

void ZmqServer::sendRequest(const std::string &identity, const ZmqMsg &message, ZmqResponseCallback callback,
                            int timeoutMs, std::function<void(const ZmqMsg&)> timeoutCallback) {
    auto data = std::make_shared<ZmqTimeoutCallbackData>(message);
    data->callback = callback;
    data->timeoutCallback = timeoutCallback;
    data->timeoutMs = timeoutMs;

    {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        pending_callbacks_[message.getId()] = data;
    }

    // 启动超时定时器
    {
        std::lock_guard<std::mutex> lock(m_timer_mutex_);
        data->timerId = m_timer.add(
            std::chrono::milliseconds(timeoutMs),
            [this, msgId = message.getId()] (CppTime::timer_id) {
                std::shared_ptr<ZmqTimeoutCallbackData> cbData;
                {
                    std::lock_guard<std::mutex> lock(callback_mutex_);
                    auto it = pending_callbacks_.find(msgId);
                    if (it != pending_callbacks_.end()) {
                        cbData = it->second;
                        pending_callbacks_.erase(it);
                    }
                }
                if (cbData && cbData->timeoutCallback) {
                    // 构造超时响应
                    ZmqMsg timeoutMsg = ZmqMsg::newErrorResponse(cbData->request, "Request timeout");
                    cbData->timeoutCallback(timeoutMsg);
                }
            }
        );
    }

    qInfo() << "sending identity: " << identity << ", msg: " << message.serialize().c_str();
    sendToServer(identity, message);
}


void ZmqServer::sendToServer(const std::string &identity, ZmqMsg message) {

    thread_local zmq::socket_t worker = [this]{
        zmq::socket_t so(ctx_, ZMQ_PUSH);
        so.connect(sender_endpoint_);
        return so;
    }();

    worker.send(zmq::buffer(identity), zmq::send_flags::sndmore);
    worker.send(zmq::buffer(message.serialize()));
}

void ZmqServer::sendResponse(const std::string &identity, ZmqMsg error_response) {
    qInfo() << "sending identity: " << identity << ", msg: " <<error_response.serialize().c_str();

    sendToServer(identity, error_response);
}

void ZmqServer::handleResponse(ZmqMsg received_msg) {
    std::shared_ptr<ZmqTimeoutCallbackData> cbData;
    {
        std::lock_guard<std::mutex> lock(callback_mutex_);
        auto it = pending_callbacks_.find(received_msg.getId());
        if (it != pending_callbacks_.end()) {
            cbData = it->second;
            pending_callbacks_.erase(it);
        }
    }
    // 移除定时器
    if (cbData) {
        std::lock_guard<std::mutex> lock(m_timer_mutex_);
        m_timer.remove(cbData->timerId);
    }
    if (cbData && cbData->callback) {
        cbData->callback(received_msg);
    }
}

void ZmqServer::serverLoop() {
    front_socket_ = zmq::socket_t(ctx_, ZMQ_ROUTER);
    backend_socket_ = zmq::socket_t(ctx_, ZMQ_DEALER);

    front_socket_.bind(endpoint_);
    backend_socket_.bind(backend_endpoint_);

    proxy_thread_ = std::thread(&ZmqServer::proxyLoop, this);

    startMonitor();

    zmq::socket_t worker(ctx_, ZMQ_DEALER);
    worker.connect(backend_endpoint_);

    sender_socket_ = zmq::socket_t(ctx_, ZMQ_PULL);
    sender_socket_.bind(sender_endpoint_);

    while (running_) {
        try {
            // 使用zmq::poll来检查socket是否有数据可读
            zmq::pollitem_t items[] = {
                    {worker, 0, ZMQ_POLLIN, 0},
                    {sender_socket_, 0, ZMQ_POLLIN, 0}
            };
            int ret = zmq::poll(items, 2, std::chrono::milliseconds(1000));

            if (ret == 0) {
                // 超时，没有数据可读，继续循环
                continue;
            } else if ((!items[0].revents && !items[1].revents) || !running_) {
                // 没有收到事件或者服务器停止，继续循环
                continue;
            }

            if (items[0].revents & ZMQ_POLLIN) {
                std::vector<zmq::message_t> messages;
                while (true) {
                    zmq::message_t message;
                    auto result = worker.recv(message, zmq::recv_flags::none);
                    if (result.has_value()) {
                        messages.push_back(std::move(message));
                    } else {
                        // 如果没有返回值，说明读取超时或socket中没有数据，这里选择跳过本次接收
                        break;
                    }
                    // 接收两个消息后才能处理，因为第一个消息是客户端identity，第二个是内容
                    if (messages.size() == 2) break;
                }

                if (messages.size() != 2) {
                    // 如果没接收到两个完整的消息，继续等待下一次轮询
                    continue;
                }

                std::string identity = messages[0].to_string();
                std::string content = messages[1].to_string();

                qInfo() << "Received from identity: " << identity << ", msg: " << content.c_str();

                ZmqMsg received_msg = ZmqMsg::deserialize(content);
                if (!received_msg.isValid()) {
                    qWarning() << "Invalid message received: " << identity << ", msg: " << content.c_str();
                    continue;
                }
                if (received_msg.getMethod() == "__response__") {
                    // 忽略线程池提交任务的返回值
                    (void) client_thread_pool_.submit_task([this, received_msg]() {
                        handleResponse(received_msg);
                    });
                } else {
                    (void) server_thread_pool_.submit_task([this, identity, received_msg]() {
                        processRequest(identity, received_msg);
                    });
                }
            }
            if (items[1].revents & ZMQ_POLLIN) {
                zmq::message_t message1;
                auto result = sender_socket_.recv(message1);
                if (!result.has_value()) {
                    continue;
                }
                zmq::message_t message2;
                result = sender_socket_.recv(message2);
                if (!result.has_value()) {
                    continue;
                }
                worker.send(message1, zmq::send_flags::sndmore);
                worker.send(message2, zmq::send_flags::none);
            }
        } catch (const std::exception &e) {
            qWarning() << "Error in server loop: " << e.what();
        }
        catch (...) {
            qWarning() << "Unknown error occurred in server loop.";
        }
    }
}


void ZmqServer::registerHandler(const std::string &method, ZmqMethodHandler handler) {
    qInfo() << "ZmqServer registerHandler: " << method;
    std::lock_guard<std::mutex> lock(handlers_mutex_);
    method_handlers_[method] = handler;
}


void ZmqServer::registerInitListener(std::function<void(const std::string&)> listener) {
    std::lock_guard<std::mutex> lock(init_listener_mutex_);
    init_listener_ = listener;
}

void ZmqServer::processRequest(const std::string &identity, const ZmqMsg &request) {
    try {
        // 保存请求和标识
        {
            std::lock_guard<std::mutex> lock(request_identity_map_mutex_);
            request_identity_map_[request.getId()] = identity;
        }
        ZmqMethodHandler handler = nullptr;
        {
            std::lock_guard<std::mutex> lock(handlers_mutex_);
            auto it = method_handlers_.find(request.getMethod());
            if (it != method_handlers_.end()) {
                handler = it->second;
            }
        }
        if (handler != nullptr) {
            handler(request, [this, identity](const ZmqMsg response) {
                // 处理响应: 发送响应给客户端
                sendResponse(identity, response);
            });
        } else {
            ZmqMsg error_response = ZmqMsg::newErrorResponse(request, "Unknown method: " + request.getMethod());
            sendResponse(identity, error_response);
        }

        // 移除请求和标识
        {
            std::lock_guard<std::mutex> lock(request_identity_map_mutex_);
            request_identity_map_.erase(request.getId());
        }
    } catch (const std::exception &e) {
        // 处理异常: 发送错误响应给客户端
        ZmqMsg error_response = ZmqMsg::newErrorResponse(request, "Internal server error: " + std::string(e.what()));
        sendResponse(identity, error_response);

        // 移除请求和标识
        {
            std::lock_guard<std::mutex> lock(request_identity_map_mutex_);
            request_identity_map_.erase(request.getId());
        }
    }
    catch (...) {
        // 处理未知异常: 发送错误响应给客户端
        ZmqMsg error_response = ZmqMsg::newErrorResponse(request, "Internal server error");
        sendResponse(identity, error_response);

        // 移除请求和标识
        {
            std::lock_guard<std::mutex> lock(request_identity_map_mutex_);
            request_identity_map_.erase(request.getId());
        }
    }
}

void ZmqServer::removeInitListener() {
    std::lock_guard<std::mutex> lock(init_listener_mutex_);
    init_listener_ = nullptr;
}

