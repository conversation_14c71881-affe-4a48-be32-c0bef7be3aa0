﻿#ifndef ZMQ_SERVER_H
#define ZMQ_SERVER_H

#include <zmq.hpp>
#include <thread>
#include <mutex>
#include <unordered_map>
#include <functional>
#include <atomic>
#include <vector>
#include <memory>
#include <queue>
#include <condition_variable>
#include "../common/ZmqCommon.h"
#include "BS_thread_pool.hpp"
#include "cpptime.h"
#include <chrono>

class ServerZmqMonitor: public BaseZmqMonitor {

};

/**
 * 用于处理客户端请求，并且支持发送请求给客户端
 */
class ZmqServer {
public:
    /**
     * @param endpoint zmq 服务器的 endpoint，默认为 tcp://*:5570, 也可使用ipc通信，例如 ipc:///zmq_server.ipc
     */
    explicit ZmqServer(const std::string& endpoint = "tcp://*:5570");
    ~ZmqServer();

    /**
     * 启动 zmq 服务器，并且开始监听客户端请求
     */
    void start();
    /**
     * 停止 zmq 服务器，并且停止监听客户端请求
     */
    void stop();

    /**
     * 注册一个方法处理器，用于处理客户端请求
     * @param method 方法名称
     * @param handler 方法处理器
     */
    void registerHandler(const std::string& method, ZmqMethodHandler handler);
    
    /**
     * 注册一个init消息的监听器，当收到客户端的init消息后会回调此函数
     * @param listener 监听器函数，参数为客户端标识
     */
    void registerInitListener(std::function<void(const std::string&)> listener);

    /**
     * 移除init消息的监听器
     */
    void removeInitListener();

    /**
     * 发送请求给客户端
     * @param identity 客户端的 identity
     * @param message 要发送的消息
     * @param callback 回调函数，用于处理客户端响应
     */
    void sendRequest(const std::string& identity, const ZmqMsg& message, ZmqResponseCallback callback,
                     int timeoutMs = 10 * 60 * 1000,
                     ZmqTimeoutCallback timeoutCallback = nullptr);

    /**
     * 发送响应给客户端
     */
    void sendResponse(const std::string &identity, ZmqMsg error_response);

    /**
     * 获取 zmq 服务器的实例
     * @return zmq 服务器的实例
     */
    static ZmqServer *instance() {
        return instance_.load();
    }

private:
    using json = nlohmann::json;

    /**
     * 启动 zmq 服务器的监控线程，用于监听zmq事件
     */
    void startMonitor();

    /**
     * server的主循环，用于监听客户端请求
     */
    void serverLoop();

    /**
     * 处理客户端请求
     * @param identity 客户端标识
     * @param request 请求内容
     */
    void processRequest(const std::string& identity, const ZmqMsg& request);

    /**
     * 处理客户端响应
     * @param received_msg 收到的消息
     */
    void handleResponse(ZmqMsg received_msg);

    /**
     * 注册 init handler，用于客户端注册（客户端建立连接后就应该发送 init 消息）
     */
    void registerInitHandler();

    /**
     * 发送请求给服务端
     */
    void sendToServer(const std::string &identity, ZmqMsg message);

    // server socket 相关
    zmq::socket_t front_socket_;
    zmq::socket_t backend_socket_;
    std::atomic<bool> running_;
    std::string endpoint_;
    std::thread server_loop_thread_;

    // 请求客户端的callback
    std::mutex callback_mutex_;
    std::unordered_map<std::string, std::shared_ptr<ZmqTimeoutCallbackData>> pending_callbacks_;

    // 用于处理客户端请求的线程池
    // 数量暂时设置为1，确保请求是顺序处理的
    BS::thread_pool<> server_thread_pool_ = BS::thread_pool<>(1);
    // 用于处理客户端响应的线程池
    BS::thread_pool<> client_thread_pool_ = BS::thread_pool<>(1);

    // server handler 相关
    std::unordered_map<std::string, ZmqMethodHandler> method_handlers_;
    std::mutex handlers_mutex_;

    // zmq 事件相关
    ServerZmqMonitor server_zmq_monitor_;
    std::thread monitor_thread_;

    inline static std::atomic<ZmqServer*> instance_;

    std::thread proxy_thread_;

    void proxyLoop();

    std::string monitor_endpoint_;
    std::string backend_endpoint_;
    std::string sender_endpoint_;
    zmq::socket_t sender_socket_;

    zmq::context_t ctx_;

    CppTime::Timer m_timer;
    std::mutex m_timer_mutex_;
    
    // init消息监听器
    std::function<void(const std::string&)> init_listener_;
    std::mutex init_listener_mutex_;

    // 请求和标识
    std::unordered_map<std::string, std::string> request_identity_map_;
    std::mutex request_identity_map_mutex_;

};
#endif // ZMQ_SERVER_H
