﻿//
// Created by HLJY on 2025/6/26.
//

#include <QStandardPaths>
#include <src/constants/AppConstants.h>
#include "CrashReport.h"
#include "win/CrashReportWin.h"
#include <iostream>

void CrashReport::init()
{
    QString logPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) +  "/" + AppConstants::APP_NAME + "/crashes/";
    // 转换为std::string
    std::string logFileStdString = logPath.toStdString();
    std::cout << "logPath: " << logFileStdString;
#if defined(Q_OS_WIN)
    CrashReportWin::init(logPath);
#endif
}
