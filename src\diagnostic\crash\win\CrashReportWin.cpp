﻿#ifdef Q_OS_WIN

#pragma comment(lib, "dbghelp.lib")

#include "CrashReportWin.h"
#include <QDir>
#include <QDateTime>
#include <QDebug>
#include <spdlog/spdlog.h>

QString CrashReportWin::dmpPath;

void CrashReportWin::init(QString path) {
    dmpPath = path;
    QLocale::setDefault(QLocale(QLocale::system()));
    SetUnhandledExceptionFilter((LPTOP_LEVEL_EXCEPTION_FILTER) ApplicationCrashHandler);
    qDebug() << "init win crash report";
}


long ApplicationCrashHandler(EXCEPTION_POINTERS *pException) {
    qDebug() << "crash report win";
    // 触发spdlog flush
    qWarning() << "close spdlog";
    spdlog::shutdown();

    // dmp文件夹
    QDir dmp;
    QString dirPath = CrashReportWin::getDmpPath();

    qDebug() << "dirPath: " << dirPath;

    if (!dmp.exists(dirPath)) {
        qDebug() << "dmp path not exists";
        dmp.mkpath(dirPath);
    }
    qDebug() << "dmp path exists";

    QDateTime current_date_time = QDateTime::currentDateTime();

    QString current_date = current_date_time.toString("yyyy_MM_dd_hh_mm_ss");

    QString time = current_date + ".dmp";

    EXCEPTION_RECORD *record = pException->ExceptionRecord;

    QString errCode(QString::number(record->ExceptionCode, 16));

    QString errAddr(QString::number((uint) record->ExceptionAddress, 16));

    QString errFlag(QString::number(record->ExceptionFlags, 16));

    QString errPara(QString::number(record->NumberParameters, 16));

    qDebug() << "errCode: " << errCode;

    qDebug() << "errAddr: " << errAddr;

    qDebug() << "errFlag: " << errFlag;

    qDebug() << "errPara: " << errPara;

    qDebug() << "dump file" << QString(dirPath + time);
    HANDLE hDumpFile = CreateFile((LPCWSTR) QString(dirPath + time).utf16(),

                                  GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);

    if (hDumpFile != INVALID_HANDLE_VALUE) {

        MINIDUMP_EXCEPTION_INFORMATION dumpInfo;

        dumpInfo.ExceptionPointers = pException;

        dumpInfo.ThreadId = GetCurrentThreadId();

        dumpInfo.ClientPointers = TRUE;

        MiniDumpWriteDump(GetCurrentProcess(), GetCurrentProcessId(), hDumpFile, MiniDumpNormal, &dumpInfo, NULL,
                          NULL);

        CloseHandle(hDumpFile);

    } else {

        qDebug() << "hDumpFile == null";

    }

    return EXCEPTION_EXECUTE_HANDLER;
}

#endif // Q_OS_WIN


