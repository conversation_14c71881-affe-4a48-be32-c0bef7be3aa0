﻿#ifdef Q_OS_WIN

#ifndef CRASHREPORTWIN_H
#define CRASHREPORTWIN_H

#define NOMINMAX

#include <windows.h>
#include <dbghelp.h>
#include <QString>

long ApplicationCrashHandler(EXCEPTION_POINTERS *pException);

class CrashReportWin
{
public:
    static void init(QString path);
    static QString getDmpPath() {
        return CrashReportWin::dmpPath;
    }

private:
    static QString dmpPath;
};

#endif // CRASHREPORTWIN_H

#endif // Q_OS_WIN
