﻿#include "DevToolsWindow.h"
#include <QTextEdit>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QHBoxLayout>
#include <QPushButton>
#include <src/utils/UrlCache.h>
#include <QFileDialog>
#include <QCoreApplication>
#include <src/components/cefview/CefViewWidget.h>
#include <QInputDialog>
#include "QCefView.h"
#include "QCefSetting.h"

DevToolsWindow::DevToolsWindow(QWidget *parent)
        : QMainWindow(parent)
{
    setWindowTitle("开发者工具");

    // 白色背景
    setStyleSheet("background-color: white;");

    resize(800, 600);

    setupUI();

    // close时销毁
    setAttribute(Qt::WA_DeleteOnClose);
}
void DevToolsWindow::setupUI()
{
    // 创建一个主部件来容纳按钮
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    m_mainLayout = new QGridLayout(centralWidget);

    addCrashButton();
    addExitButton();
    addCacheUrlButton();
    addSelectDirButton();
    addCefViewLoadErrorButton();
    addCefViewSaveFileDialogButton();
}

void DevToolsWindow::addCrashButton() {
    QPushButton* crashButton = new QPushButton("触发Crash", this);

    connect(crashButton, &QPushButton::clicked, this, [this]() {
        // 测试代码
        qDebug() << "触发Crash";
        throw std::runtime_error("触发Crash");
    });

    m_mainLayout->addWidget(crashButton);
}

void DevToolsWindow::addExitButton() {
    // 退出应用
    QPushButton* exitButton = new QPushButton("退出应用", this);
    connect(exitButton, &QPushButton::clicked, this, [this]() {
        // 测试代码
        qDebug() << "退出应用";
        QCoreApplication::quit();
    });

    m_mainLayout->addWidget(exitButton);
}


void DevToolsWindow::addCacheUrlButton() {
    QPushButton* cacheUrlButton = new QPushButton("缓存URL", this);

    connect(cacheUrlButton, &QPushButton::clicked, this, [this]() {
        // 测试代码
        qDebug() << "缓存URL";
        auto path = UrlCache::cacheUrl("https://pic.cnblogs.com/face/1552804/20200221225631.png");
        qDebug() << "缓存到的路径：" << path;
    });

    m_mainLayout->addWidget(cacheUrlButton);
}

void DevToolsWindow::addSelectDirButton() {
    QPushButton* selectDirButton = new QPushButton("选择目录", this);
    connect(selectDirButton, &QPushButton::clicked, this, [this]() {
        // 测试代码
        qDebug() << "选择目录";
        // 创建目录选择对话框
        QString selectedDir = QFileDialog::getExistingDirectory(
                nullptr,                    // 父窗口
                "test",                 // 对话框标题
                "",               // 默认显示桌面目录
                QFileDialog::ShowDirsOnly  // 只显示目录（隐藏文件）
                | QFileDialog::DontResolveSymlinks // 不解析符号链接
        );
        qDebug() << "选择的目录：" << selectedDir;
    });

    m_mainLayout->addWidget(selectDirButton);
}

void DevToolsWindow::addCefViewLoadErrorButton() {
    QPushButton* cefViewLoadErrorButton = new QPushButton("CefView加载错误", this);
    connect(cefViewLoadErrorButton, &QPushButton::clicked, this, [this]() {
        bool ok;
        QString url = QInputDialog::getText(this, "输入网址", "请输入要加载的网址:", QLineEdit::Normal, "", &ok);
        if (ok && !url.isEmpty()) {
            CefViewWidget* loadErrorCefView = new CefViewWidget(this, "自定义加载cef", true, true, true);
            loadErrorCefView->setUrl(url);
            m_mainLayout->addWidget(loadErrorCefView);
        }

    });
    m_mainLayout->addWidget(cefViewLoadErrorButton);
}

void DevToolsWindow::addCefViewSaveFileDialogButton() {
    QPushButton* cefViewLoadErrorButton = new QPushButton("CefView打开文件对话框", this);
    connect(cefViewLoadErrorButton, &QPushButton::clicked, this, [this]() {
        QString url = QCoreApplication::applicationDirPath() + "/webView/QCefViewPage.html";
        if (!url.isEmpty()) {
            CefViewWidget* loadErrorCefView = new CefViewWidget(this, "CefView打开文件对话框", true, true, true);
            loadErrorCefView->setUrl(url);
            m_mainLayout->addWidget(loadErrorCefView);
        }

    });
    m_mainLayout->addWidget(cefViewLoadErrorButton);
}

