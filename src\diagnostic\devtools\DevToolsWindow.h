﻿#ifndef DEVTOOLSWINDOW_H
#define DEVTOOLSWINDOW_H

#include <QMainWindow>
#include <QHBoxLayout>
class DevToolsWindow : public QMainWindow {
Q_OBJECT

public:
    explicit DevToolsWindow(QWidget *parent = nullptr);

private:
    void setupUI();

    QGridLayout *m_mainLayout;

    void addCrashButton();

    void addCacheUrlButton();

    void addSelectDirButton();

    void addCefViewLoadErrorButton();

    void addCefViewSaveFileDialogButton();

    void addExitButton();
};

#endif // DEVTOOLSWINDOW_H
