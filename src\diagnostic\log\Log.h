﻿//
// Created by HLJY on 2025/6/26.
//

#ifndef LOG_H
#define LOG_H

#include <spdlog/spdlog.h>
#include <QtLogging>
#include <QCoreApplication>


class Log {
public:
    /**
     * 初始化日志
     */
    static void init();

    /**
     * 设置connect，应用退出时刷新日志
     */
    static void flushOnExit(QCoreApplication *app);

    /**
     * 设置日志级别
     */
    static void setLogLevel(QtMsgType);
private:
    static void messageHandler(QtMsgType, const QMessageLogContext &, const QString &);

    static spdlog::level::level_enum toSpdlogLevel(QtMsgType);

    inline static QtMsgType m_logLevel{};

    inline static std::shared_ptr<spdlog::logger> m_logger{};
};


#endif //LOG_H
