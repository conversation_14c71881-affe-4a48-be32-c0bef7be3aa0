#ifndef IBASEMODEL_H
#define IBASEMODEL_H

#include <QString>

/**
 * @class IBaseModel
 * @brief 基础模型接口类，定义所有模型对象的公共行为
 */
class IBaseModel
{
public:
    /**
     * @brief 获取模块名称（纯虚函数）
     * @return 当前模块名称的常量引用
     */
    virtual const QString &getModuleName() = 0;

    /**
     * @brief 释放资源（纯虚函数）
     * @note 用于清理非内存资源（如文件句柄、网络连接等）
     */
    virtual void disponse() = 0;

    /**
     * @brief 销毁对象（纯虚函数）
     * @note 负责对象内存的释放，通常调用delete this
     */
    virtual void destroy() = 0;

    /**
     * @brief 动态类型转换模板
     * @tparam ObjectType 目标类型（必须是指针类型）
     * @return 转换后的对象指针，失败返回nullptr
     * @example auto derived = baseObj->cast<DerivedType*>();
     */
    template <typename ObjectType>
    ObjectType cast()
    {
        return dynamic_cast<ObjectType>(this);
    }
};

#endif // IBASEMODEL_H