﻿//
// Created by HLJY on 2025/6/19.
//

#include "CaptureScreenJSBridgeHandler.h"

#include "src/components/jsbridge/JSBridge.h"
#include "src/utils/CaptureScreen.h"

using json = nlohmann::json;

void CaptureScreenJSBridgeHandler::registerHandler() {
    JSBridge::registerHandler("captureScreen", &captureScreen);
}

void CaptureScreenJSBridgeHandler::captureScreen(const JSBridgeContext &con) {
    auto snapshotPath = capturePhysicalScreen();
    QFileInfo fileInfo(snapshotPath);

    json result;
    result["snapshotPath"] = "http://tempfileserver/" + fileInfo.fileName().toStdString();
    result["format"] = "png";

    con.setResult(JSBridgeMsg::newResponse(con.getData(), result));
}
