﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef CAPTURESCREENJSBRIDGEHANDLER_H
#define CAPTURESCREENJSBRIDGEHANDLER_H
#include "src/components/jsbridge/JSBridge.h"
#include "src/components/jsbridge/handler/JSBridgeAutoRegister.h"
#include "src/components/jsbridge/handler/JSBridgeHandler.h"


class CaptureScreenJSBridgeHandler: public JSBridgeHandler {
public:
    void registerHandler();
    // 截屏
    static void captureScreen(const JSBridgeContext &con);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static JSBridgeAutoRegister<CaptureScreenJSBridgeHandler> registrar;
};



#endif //CAPTURESCREENJSBRIDGEHANDLER_H
