﻿//
// Created by HLJY on 2025/6/19.
//

#include "CefViewWidgetJSBridgeHandler.h"
#include "src/components/cefview/CefViewWidget.h"

using json = nlohmann::json;

void CefViewWidgetJSBridgeHandler::registerHandler() {
    JSBridge::registerHandler("enterFullScreen", &enterFullScreen);
    JSBridge::register<PERSON>and<PERSON>("exitFullScreen", &exitFullScreen);
    JSBridge::registerHandler("showFullscreenButton", &showFullscreenButton);
    JSBridge::registerHandler("hideFullscreenButton", &hideFullscreenButton);
}

void CefViewWidgetJSBridgeHandler::enterFullScreen(const JSBridgeContext &con) {
    auto cefView = con.getCefView();
    if (cefView) {
        CefViewWidget* parent = qobject_cast<CefViewWidget*>(cefView->parentWidget());
        if (parent) {
            parent->enterFullScreen(false);
        }
    }
}

void CefViewWidgetJSBridgeHandler::exitFullScreen(const JSBridgeContext &con) {
    auto cefView = con.getCefView();
    if (cefView) {
        CefViewWidget* parent = qobject_cast<CefViewWidget*>(cefView->parentWidget());
        if (parent) {
            parent->exitFullScreen();
        }
    }
}

void CefViewWidgetJSBridgeHandler::showFullscreenButton(const JSBridgeContext &con) {
    auto cefView = con.getCefView();
    if (cefView) {
        CefViewWidget* parent = qobject_cast<CefViewWidget*>(cefView->parentWidget());
        if (parent) {
            QMetaObject::invokeMethod(parent, [parent]() {
                parent->setFullscreenButtonVisible(true);
            }, Qt::QueuedConnection);
        }
    }
}

void CefViewWidgetJSBridgeHandler::hideFullscreenButton(const JSBridgeContext &con) {
    auto cefView = con.getCefView();
    if (cefView) {
        CefViewWidget* parent = qobject_cast<CefViewWidget*>(cefView->parentWidget());
        if (parent) {
            QMetaObject::invokeMethod(parent, [parent]() {
                parent->setFullscreenButtonVisible(false);
            }, Qt::QueuedConnection);
        }
    }
}
