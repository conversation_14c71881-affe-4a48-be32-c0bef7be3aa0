﻿//
// Created by HLJY on 2025/6/24.
//

#ifndef QCEFWIDGETJSBRIDGEHANDLER_H
#define QCEFWIDGETJSBRIDGEHANDLER_H


#include <src/components/jsbridge/JSBridge.h>
#include <src/components/jsbridge/handler/JSBridgeHandler.h>
#include <src/components/jsbridge/handler/JSBridgeAutoRegister.h>

class CefViewWidgetJSBridgeHandler : public JSBridgeHandler {
public:
    void registerHandler();
    static void enterFullScreen(const JSBridgeContext& con);
    static void exitFullScreen(const JSBridgeContext& con);
    static void showFullscreenButton(const JSBridgeContext& con);
    static void hideFullscreenButton(const JSBridgeContext& con);

private:
    inline static JSBridgeAutoRegister<CefViewWidgetJSBridgeHandler> registrar;
};


#endif //QCEFWIDGETJSBRIDGEHANDLER_H
