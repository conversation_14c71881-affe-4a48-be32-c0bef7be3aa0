﻿//
// Created by HLJY on 2025/6/19.
//


#include "DefaultJSBridgeHandler.h"

#include "src/components/cefview/CefViewWidget.h"

void DefaultJSBridgeHandler::registerHandler() {
    JSBridge::registerHandler("showDevTools", &showDevTools);
    JSBridge::register<PERSON>and<PERSON>("reload", &reload);
    JSBridge::registerHandler("close", &close);
    JSBridge::registerHandler("hide", &hide);
    JSBridge::registerHandler("show", &show);
}

void DefaultJSBridgeHandler::showDevTools(const JSBridgeContext &con) {
    con.getCefView()->showDevTools();
}

void DefaultJSBridgeHandler::reload(const JSBridgeContext &con) {
    con.getCefView()->browserReload();
}

void DefaultJSBridgeHandler::close(const JSBridgeContext &con) {
    QWidget *parent = con.getCefView()->parentWidget();
    CefViewWidget *cefViewWidget = qobject_cast<CefViewWidget *>(parent);

    if (cefViewWidget) {
        QMetaObject::invokeMethod(cefViewWidget, [cefViewWidget]() {
            cefViewWidget->onCloseButtonClicked();
        }, Qt::QueuedConnection);
    } else {
        qDebug() << "当前cefView的父组件不是CefViewWidget，无法执行关闭操作";
    }
}

void DefaultJSBridgeHandler::hide(const JSBridgeContext &con) {
    con.getCefView()->parentWidget()->hide();
}


void DefaultJSBridgeHandler::show(const JSBridgeContext &con) {
    con.getCefView()->parentWidget()->show();
}
