﻿#ifndef BASEJSBRIDGEHANDLER_H
#define BASEJSBRIDGEHANDLER_H
#include "src/components/jsbridge/JSBridge.h"
#include "src/components/jsbridge/handler/JSBridgeAutoRegister.h"
#include "src/components/jsbridge/handler/JSBridgeHandler.h"


// 基础的jsbridge handler
class DefaultJSBridgeHandler: public JSBridgeHandler {
public:
    // 注册
    void registerHandler();

    // 显示开发者工具
    static void showDevTools(const JSBridgeContext &con);
    // 重新加载页面
    static void reload(const JSBridgeContext &con);
    // 关闭页面
    static void close(const JSBridgeContext &con);
    // 隐藏
    static void hide(const JSBridgeContext &con);
    // 显示
    static void show(const JSBridgeContext &con);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static JSBridgeAutoRegister<DefaultJSBridgeHandler> registrar;
};



#endif //BASEJSBRIDGEHANDLER_H
