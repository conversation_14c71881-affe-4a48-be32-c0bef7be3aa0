﻿//
// Created by HLJY on 2025/6/19.
//


#include "ElectronJSBridgeHandler.h"

#include "src/components/jsbridge/JSBridge.h"
#include "src/components/zmq/server/ZmqServer.h"

void ElectronJSBridgeHandler::register<PERSON>and<PERSON>() {
    JSBridge::register<PERSON><PERSON><PERSON>("callElectronRenderer", &callElectronRenderer);
    JSBridge::register<PERSON>and<PERSON>("callElectron", &callElectron);
}

void ElectronJSBridgeHandler::callElectronRenderer(const JSBridgeContext &con) {


    auto id = con.getData().getId();
    ZmqMsg msg = ZmqMsg(id, "callElectronRenderer", con.getData().getData(), "", "");

    ZmqServer::instance()->sendRequest(con.getData().getData()["identity"], msg, [con](const ZmqMsg &response) {
        con.setResult(JSBridgeMsg::newResponse(con.getData(), response.getData(), response.getStatus()));
    });
}

void ElectronJSBridgeHandler::callElectron(const JSBridgeContext &con) {
    auto id = con.getData().getId();
    auto electronMethod = con.getData().getData()["electronMethod"].get<std::string>();
    ZmqMsg msg = ZmqMsg(id, electronMethod, con.getData().getData()["electronData"], "", "");

    ZmqServer::instance()->sendRequest(con.getData().getData()["identity"], msg, [con](const ZmqMsg &response) {
        con.setResult(JSBridgeMsg::newResponse(con.getData(),response.getData(), response.getStatus()));
    });
}
