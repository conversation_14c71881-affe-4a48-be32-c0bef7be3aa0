﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef ELECTRONJSBRIDGEHANDLER_H
#define ELECTRONJSBRIDGEHANDLER_H
#include "src/components/jsbridge/JSBridge.h"
#include "src/components/jsbridge/handler/JSBridgeAutoRegister.h"


class ElectronJSBridgeHandler : public JSBridgeHandler {
public:
    void registerHandler();
    // 调用Electron Renderer
    static void callElectronRenderer(const JSBridgeContext &con);
    // 调用Electron
    static void callElectron(const JSBridgeContext &con);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static JSBridgeAutoRegister<ElectronJSBridgeHandler> registrar;
};



#endif //ELECTRONJSBRIDGEHANDLER_H
