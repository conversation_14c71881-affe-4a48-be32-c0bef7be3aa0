﻿//
// Created by HLJY on 2025/6/19.
//

#include "FileJSBridgeHandler.h"
#include <fstream>
#include <QCryptographicHash>
#include <QDir>
#include <QFileDialog>
#include <QStandardPaths>
#include <codecvt>


using json = nlohmann::json;

void FileJSBridgeHandler::registerHandler() {
    JSBridge::registerAsyncHandler("saveFileChunk", &saveFileChunk);
    JSBridge::registerHandler("showSaveFileDialog", &showSaveFileDialog);
}

void FileJSBridgeHandler::showSaveFileDialog(const JSBridgeContext &con) {

    qDebug() << "showSaveFileDialog start ";

    QString dialogTitle = "选择目录";
    auto jTitle = con.getData().getData().contains("dialogTitle") ? con.getData().getData()["dialogTitle"] : json{};
    if (!jTitle.is_null()) {
        dialogTitle = QString::fromStdString(jTitle.get<std::string>());
    }

    // 设置桌面为当前目录
    QString desktopPath = QStandardPaths::writableLocation(QStandardPaths::DesktopLocation);

    // 创建目录选择对话框
    QString selectedDir = QFileDialog::getExistingDirectory(
        con.getCefView(),                    // 父窗口
        dialogTitle,                 // 对话框标题
        desktopPath,               // 默认显示桌面目录
        QFileDialog::ShowDirsOnly  // 只显示目录（隐藏文件）
        | QFileDialog::DontResolveSymlinks // 不解析符号链接
    );

    qDebug() << "showSaveFileDialog selectedDir: " << selectedDir;

    json j = json{
            {"selectedDir", selectedDir.toStdString()}
    };
    con.setResult(JSBridgeMsg::newResponse(con.getData(), j));
}

std::wstring s2ws_filepath(const std::string& str) {
    using convert_typeX = std::codecvt_utf8<wchar_t>;
    std::wstring_convert<convert_typeX, wchar_t> converterX;
    return converterX.from_bytes(str);
}

void FileJSBridgeHandler::saveFileChunk(const JSBridgeContext &con) {
    /**
    * 参数是json格式
    * filePath 字段 表示文件存储路径
    * chunkIndex 字段 表示文件块的索引（从1开始递增）
    * chunkContent 字段 标识文件块内容，base64编码
    * totalChunks 字段 表示文件总块数。该字段用于判断是否所有分片都已接收。不是所有的chunk都需要包括该字段，但至少一个chunk需要包含该字段。
    */

    /**
     * 接收到chunk之后，创建临时文件，临时文件命名如下：
     * md5(filePath) + fileName + "-" + chunkIndex + ".tmp"
     * 文件存放路径：TMP + “/” + md5(filePath) + fileName
     * 把chunkContent内容使用base64解码后写入临时文件中
     * 如果包括totalChunks字段，则在相同目录下，创建一个名为 totalChunks.tmp 的文件，写入totalChunks的值
     *
     * 写入完成后，检查是否所有chunk都已收到，检查方法如下：
     * 1. 检查是否存在 totalChunks.tmp 文件
     * 2. 如果不存在，说明chunk接收不全，直接返回
     * 3. 如果存在，则读取 totalChunks.tmp 文件中的值
     * 4. 如果total Chunks.tmp的值 和 其他tmp文件的数量相同
     * 5. 按照chunkIndex对文件排序，合并所有文件，得到完整文件，并写入到目标目录
     * 6. 删除所有tmp文件
     *
     */
    auto j = con.getData().getData();
    auto filePath = j["filePath"].get<std::string>();
    auto chunkIndex = j["chunkIndex"].get<int>();
    auto chunkContent = j["chunkContent"].get<std::string>();

    static qint64 fileTransferStartTime = -1;
    // 如果是第一次传输，或距离上次超过1分钟，则重置开始时间
    if (fileTransferStartTime == -1 || QDateTime::currentMSecsSinceEpoch() - fileTransferStartTime > 60 * 1000) {
        fileTransferStartTime = QDateTime::currentMSecsSinceEpoch();
        qDebug() << "start transfer file, path: " << filePath;
    }

    // 计算文件路径
    std::wstring tempDir = QDir::tempPath().toStdWString();
    std::wstring fileName = QFileInfo(QString::fromStdString(filePath)).fileName().toStdWString();

    std::string pathMd5 = QCryptographicHash::hash(QString::fromStdString(filePath).toUtf8(),
                                                   QCryptographicHash::Md5).toHex().toStdString();
    // 创建临时目录
    QString dir = QString::fromStdWString(tempDir + L"/" + s2ws_filepath(pathMd5) + L"-" + fileName);
    QDir().mkpath(dir);

    // 解码base64内容
    QByteArray decodedContent;
    try {
        decodedContent = QByteArray::fromBase64(QString::fromStdString(chunkContent).toUtf8());
    } catch (...) {
        con.setResult(JSBridgeMsg::newErrorResponse(con.getData(), "Base64 decode failed"));
        return;
    }

    // 写入临时文件
    std::wstring tempFilePath = dir.toStdWString() + L"/" + std::to_wstring(chunkIndex) + L".tmp";
    std::ofstream outFile(tempFilePath, std::ios::binary);
    if (outFile.is_open()) {
        outFile.write(decodedContent.data(), decodedContent.size());
        outFile.close();
    }

    std::wstring totalChunksPath = dir.toStdWString() + L"/" + L"totalChunks.tmp";
    // 如果包含totalChunks字段，保存它
    if (j.contains("totalChunks")) {
        int totalChunks = j["totalChunks"].get<int>();
        std::ofstream totalChunksFile(totalChunksPath);
        if (totalChunksFile.is_open()) {
            totalChunksFile << totalChunks;
            totalChunksFile.close();
        }
    }
    if (!QFile::exists(QString::fromStdWString(totalChunksPath))) {
        qDebug() << "totalChunks file does not exist";
        con.setResult(JSBridgeMsg::newResponse(con.getData(), json{}));
        return;
    }

    std::ifstream totalChunksFileRead(totalChunksPath);
    int totalChunks = 0;
    if (totalChunksFileRead.is_open()) {
        totalChunksFileRead >> totalChunks;
        totalChunksFileRead.close();
    } else {
        con.setResult(JSBridgeMsg::newErrorResponse(con.getData(), "Failed to read totalChunks file"));
        qDebug() << "Failed to read totalChunks file";
        return;
    }

    // 检查是否所有分片都已接收
    std::vector<std::wstring> tmpFiles;
    for (int i = 1; i <= totalChunks; ++i) {
        std::wstring checkPath = dir.toStdWString() + L"/" + std::to_wstring(i) + L".tmp";
        if (QFile::exists(QString::fromStdWString(checkPath))) {
            tmpFiles.push_back(checkPath);
        } else {
            qDebug() << "Not all chunks received yet";
            con.setResult(JSBridgeMsg::newResponse(con.getData(), json{"msg", "Not all chunks received yet"}));
            return;
        }
    }

    qDebug() << "All chunks received";

    static std::mutex mutex;

    std::lock_guard<std::mutex> lock(mutex);

    // 再次检查所有分片是否已被接收
    for (int i = 0; i < tmpFiles.size(); ++i) {
        if (!QFile::exists(QString::fromStdWString(tmpFiles[i]))) {
            // 说明文件分片已经被merge
            qDebug() << "File chunks have been merged";
            con.setResult(JSBridgeMsg::newResponse(con.getData(), json{"msg", "File chunks have been merged"}));
            return;
        }
    }

    // 按照文件名对文件排序
    std::sort(tmpFiles.begin(), tmpFiles.end(), [](const std::wstring &a, const std::wstring &b) {
        // 提取文件名
        std::wstring filenameA = a.substr(a.find_last_of(L'/') + 1);
        std::wstring filenameB = b.substr(b.find_last_of(L'/') + 1);

        // 提取数字部分进行比较
        int indexA = std::stoi(filenameA.substr(0, filenameA.find(L'.')));
        int indexB = std::stoi(filenameB.substr(0, filenameB.find(L'.')));

        return indexA < indexB;
    });

    // 合并文件
    // 如果文件不存在，则创建，包括目录
    if (!QFile::exists(QString::fromStdString(filePath))) {
        QDir().mkpath(QFileInfo(QString::fromStdString(filePath)).absolutePath());
    }
    std::ofstream mergedFile(s2ws_filepath(filePath), std::ios::binary | std::ios::trunc);
    if (mergedFile.is_open()) {
        for (const auto &tmpFile: tmpFiles) {
            std::ifstream inFile(tmpFile, std::ios::binary);
            if (inFile.is_open()) {
                mergedFile << inFile.rdbuf();
                inFile.close();
            }
        }
        mergedFile.close();

        // 删除临时文件
        for (const auto &tmpFile: tmpFiles) {
            QFile::remove(QString::fromStdWString(tmpFile));
        }
        if (!QFile::remove(QString::fromStdWString(totalChunksPath))) {
            qDebug() << "Failed to delete totalChunks.tmp file";
        }

        con.setResult(JSBridgeMsg::newResponse(con.getData(), json{}));
    } else {
        con.setResult(JSBridgeMsg::newErrorResponse(con.getData(), "Failed to create merged file"));
    }

    qDebug() << "File transfer completed, cost: " << QDateTime::currentMSecsSinceEpoch() - fileTransferStartTime << " ms";
    fileTransferStartTime = -1;
}
