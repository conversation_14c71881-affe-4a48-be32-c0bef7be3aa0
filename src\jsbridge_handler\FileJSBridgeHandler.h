﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef FILEJSBRIDGEHANDLER_H
#define FILEJSBRIDGEHANDLER_H

#include <src/components/jsbridge/handler/JSBridgeAutoRegister.h>
#include "src/components/jsbridge/JSBridge.h"
#include "src/components/jsbridge/handler/JSBridgeHandler.h"


class FileJSBridgeHandler: public JSBridgeHandler {
public:
    // 注册
    void registerHandler();
    // 保存文件的对话框
    static void showSaveFileDialog(const JSBridgeContext &con);
    // 传输文件(分块）
    static void saveFileChunk(const JSBridgeContext &con);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static JSBridgeAutoRegister<FileJSBridgeHandler> registrar;
};



#endif //FILEJSBRIDGEHANDLER_H
