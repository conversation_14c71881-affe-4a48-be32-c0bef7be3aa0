﻿
# 说明

实现JSBridge Handler

# 使用方法

1. 创建一个类，继承JSBridgeHandler，一个类中可以有多个handler
```
class DefaultJSBridgeHandler: public JSBridgeHandler 
```

2. 声明并实现registerHandler方法
```angular2html
public:
    // 注册
    void registerHandler();
```

3. 声明并实现 handler方法
```angular2html
    // 显示开发者工具
    static void showDevTools(const JSBridgeContext &con);
```

4. 在registerHandler中注册handler方法
```angular2html
void DefaultJSBridgeHandler::registerHandler() {
    JSBridge::registerHandler("showDevTools", &showDevTools);
}
```

5. 声明并定义 JSBridgeAutoRegister， 目的是自动注册Handler（在应用启动时，registerHandler方法会被调用）
```angular2html
private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static JSBridgeAutoRegister<DefaultJSBridgeHandler> registrar;
```