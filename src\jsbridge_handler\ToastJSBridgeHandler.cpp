﻿//
// Created by HLJY on 2025/6/19.
//


#include "ToastJSBridgeHandler.h"

#include "nlohmann/json.hpp"
#include "src/components/jsbridge/JSBridge.h"
#include "src/components/toast/Toast.h"

using json = nlohmann::json;

void ToastJSBridgeHandler::registerHandler() {
    JSBridge::registerHandler("toast", &toast);
}

void ToastJSBridgeHandler::toast(const JSBridgeContext &con) {
    json j =con.getData().getData();
    Toast::show(j);
}

