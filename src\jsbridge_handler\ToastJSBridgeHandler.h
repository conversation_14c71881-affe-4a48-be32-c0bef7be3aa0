﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef TOASTJSBRIDGEHANDLER_H
#define TOASTJSBRIDGEHANDLER_H
#include "src/components/jsbridge/JSBridge.h"
#include "src/components/jsbridge/handler/JSBridgeAutoRegister.h"
#include "src/components/jsbridge/handler/JSBridgeHandler.h"


class ToastJSBridgeHandler: public JSBridgeHandler {
public:
    void registerHandler();
    // 显示toast
    static void toast(const JSBridgeContext &con);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static JSBridgeAutoRegister<ToastJSBridgeHandler> registrar;
};



#endif //TOASTJSBRIDGEHANDLER_H
