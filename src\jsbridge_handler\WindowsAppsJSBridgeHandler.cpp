﻿//
// Created by HLJY on 2025/6/19.
//


#include "WindowsAppsJSBridgeHandler.h"

#include "src/components/jsbridge/JSBridge.h"
#include "src/utils/WindowsApp.h"

using json = nlohmann::json;

void WindowsAppsJSBridgeHandler::registerHandler() {
    JSBridge::registerHandler("getWindowsApps", &getWindowsApps);
}

void WindowsAppsJSBridgeHandler::getWindowsApps(const JSBridgeContext &con) {
    std::vector<WindowsAppInfo> apps;

    if (con.getData().getData().empty()) {
        apps = GetWindowsApps();
    }
    else {
        auto j = con.getData().getData();

        if (j.is_object()) {
            bool hasExcludedFolders = j.contains("excludedFolders") && j["excludedFolders"].is_array();
            bool hasExcludedFiles = j.contains("excludedFiles") && j["excludedFiles"].is_array();
            bool hasFilterKeywords = j.contains("filterKeywords") && j["filterKeywords"].is_array();
            auto excludedFolders = hasExcludedFolders ? j["excludedFolders"].get<std::unordered_set<std::string>>() : std::unordered_set<std::string>();
            auto excludedFiles = hasExcludedFiles ? j["excludedFiles"].get<std::unordered_set<std::string>>() : std::unordered_set<std::string>();
            auto filterKeywords = hasFilterKeywords ? j["filterKeywords"].get<std::unordered_set<std::string>>() : std::unordered_set<std::string>();
            apps = GetWindowsApps(excludedFolders, excludedFiles, filterKeywords);
        }
        else {
            apps = GetWindowsApps();
        }
    }

    json appsJson = json::array();
    for (const auto& app : apps) {
        json appJson;
        appJson["name"] = QString::fromStdWString(app.name).toStdString();
        appJson["path"] = QString::fromStdWString(app.path).toStdString();
        appJson["shortcut"] = QString::fromStdWString(app.shortcut).toStdString();
        appJson["iconBase64"] = app.iconBase64;

        appsJson.push_back(appJson);
    }
    con.setResult(JSBridgeMsg::newResponse(con.getData(), appsJson));
}

