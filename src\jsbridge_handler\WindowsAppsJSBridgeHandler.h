﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef WINDOWSAPPSJSBRIDGEHANDLER_H
#define WINDOWSAPPSJSBRIDGEHANDLER_H
#include "src/components/jsbridge/JSBridge.h"
#include "src/components/jsbridge/handler/JSBridgeAutoRegister.h"
#include "src/components/jsbridge/handler/JSBridgeHandler.h"


class WindowsAppsJSBridgeHandler: public JSBridgeHandler {
public:
    void registerHandler();
    // 获取应用列表
    static void getWindowsApps(const JSBridgeContext &con);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static JSBridgeAutoRegister<WindowsAppsJSBridgeHandler> registrar;
};



#endif //WINDOWSAPPSJSBRIDGEHANDLER_H
