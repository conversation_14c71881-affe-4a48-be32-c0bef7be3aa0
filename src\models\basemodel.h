#ifndef BASEMODEL_H
#define BASEMODEL_H

#include <QObject>
#include "../interface/ibasemodel.h"
class BaseModel : public QObject, public IBaseModel
{
public:
    BaseModel(const QString &moduleName);

    virtual ~BaseModel();

    virtual const QString &getModuleName() override;

    virtual void disponse() override;

    virtual void destroy() override;

protected:

    QString     moduleName;

};

#endif // BASEMODEL_H
