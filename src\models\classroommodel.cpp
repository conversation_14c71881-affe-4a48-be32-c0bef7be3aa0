#include "classroommodel.h"


ClassroomModel::ClassroomModel(const QString &moduleName)
    : BaseModel(moduleName)
{
}

ClassroomModel::ClassroomModel(const QString &moduleName, ClassroomInfo &info)
    : BaseModel(moduleName)
{
    this->info = info;
}

ClassroomModel::~ClassroomModel() {
}

ClassroomInfo ClassroomModel::getInfo() const
{
    return info;
}

void ClassroomModel::setInfo(ClassroomInfo& info) {
    this->info = info;
}

ResourceItem ClassroomModel::getCurrentResource() const
{
  return this->currentResource;
}

void ClassroomModel::setCurrentResource(ResourceItem &currentResource)
{
    this->currentResource = currentResource;
}