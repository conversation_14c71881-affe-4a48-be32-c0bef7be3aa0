#ifndef CLASSROOM_H
#define CLASSROOM_H

#include <QObject>
#include "basemodel.h"

// 模型结构
struct ClassroomInfo
{
    // 组织信息
    QString saasSchoolId;
    QString saasCampusId;
    QString saasClassId;
    QString saasClassName;

    // 用户信息
    QString saasUserId;
    QString userName;

    // 科目信息
    QString saasSubjectId;
    QString saasSubjectCode;
    QString saasSubjectName;
};

// 资源模型结构
struct ResourceItem
{
    QString resourceId;
    QString documentId;
    QString resourceType;
};

class ClassroomModel : public BaseModel
{
public:
    ClassroomModel(const QString &moduleName);
    ClassroomModel(const QString &moduleName, ClassroomInfo &info);
    virtual ~ClassroomModel();
    ClassroomInfo getInfo() const;
    void setInfo(ClassroomInfo &info);
    void setCurrentResource(ResourceItem &currentResource);
    ResourceItem getCurrentResource() const;

private:
    ClassroomInfo info;
    ResourceItem currentResource;
};

#endif // CLASSROOM_H
