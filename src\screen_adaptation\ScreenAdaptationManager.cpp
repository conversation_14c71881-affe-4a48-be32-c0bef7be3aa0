#include "ScreenAdaptationManager.h"
#include <QDebug>
#include <QRegularExpressionMatchIterator>

namespace ScreenAdaptationConstants {

void ScreenAdaptationManager::initialize()
{
    updateScreenInfo();
    
    // 输出初始化信息
    qDebug() << "=== 屏幕适配管理器初始化 ===";
    qDebug() << "屏幕尺寸:" << m_screenSize;
    qDebug() << "屏幕类型:" << getScreenTypeString();
    qDebug() << "设备像素比例:" << m_devicePixelRatio;
    qDebug() << "逻辑DPI:" << m_logicalDpi;
    qDebug() << "物理DPI:" << m_physicalDpi;
    qDebug() << "计算的缩放因子:" << m_scaleFactor;
    qDebug() << "是否高DPI:" << isHighDPI();
    qDebug() << "========================";
}

void ScreenAdaptationManager::updateScreenInfo()
{
    QScreen* screen = QApplication::primaryScreen();
    if (!screen) {
        qWarning() << "无法获取主屏幕信息，使用默认值";
        return;
    }
    
    m_screenSize = screen->size();
    m_screenGeometry = screen->geometry();
    m_devicePixelRatio = screen->devicePixelRatio();
    m_logicalDpi = screen->logicalDotsPerInch();
    m_physicalDpi = screen->physicalDotsPerInch();
    
    // 计算缩放比例
    calculateScaleFactor();
    
    // 确定屏幕类型
    determineScreenType();
}

int ScreenAdaptationManager::getAdaptedSize(int originalSize) const
{
    int adaptedSize = static_cast<int>(originalSize * m_scaleFactor);
    return qMax(1, adaptedSize); // 确保最小值为1
}

qreal ScreenAdaptationManager::getAdaptedSize(qreal originalSize) const
{
    qreal adaptedSize = originalSize * m_scaleFactor;
    return qMax(1.0, adaptedSize); // 确保最小值为1.0
}

QSize ScreenAdaptationManager::getAdaptedSize(const QSize& originalSize) const
{
    return QSize(
        getAdaptedSize(originalSize.width()),
        getAdaptedSize(originalSize.height())
    );
}

QString ScreenAdaptationManager::getScreenTypeString() const
{
    switch (m_screenType) {
        case ScreenType::HD_1080P:
            return "1080p (1920x1080)";
        case ScreenType::QHD_1440P:
            return "1440p (2560x1440)";
        case ScreenType::UHD_4K:
            return "4K (支持3840x2160和4096x2160)";
        case ScreenType::UHD_5K:
            return "5K (5120x2880)";
        case ScreenType::CUSTOM:
            return QString("自定义 (%1x%2)").arg(m_screenSize.width()).arg(m_screenSize.height());
        default:
            return "未知";
    }
}

void ScreenAdaptationManager::calculateScaleFactor()
{
    // 基于屏幕宽度计算缩放因子
    qreal widthRatio = static_cast<qreal>(m_screenSize.width()) / BASE_SCREEN_WIDTH;
    qreal heightRatio = static_cast<qreal>(m_screenSize.height()) / BASE_SCREEN_HEIGHT;
    
    // 使用较小的比例以确保UI不会超出屏幕
    qreal baseScale = qMin(widthRatio, heightRatio);
    
    // 结合DPI进行调整
    qreal dpiScale = m_logicalDpi / BASE_DPI;
    
    // 综合计算最终缩放因子
    m_scaleFactor = baseScale * dpiScale;
    
    // 限制缩放范围，避免过小或过大
    m_scaleFactor = qBound(0.3, m_scaleFactor, 3.0);
    
    qDebug() << "缩放计算详情:";
    qDebug() << "  宽度比例:" << widthRatio;
    qDebug() << "  高度比例:" << heightRatio;
    qDebug() << "  基础缩放:" << baseScale;
    qDebug() << "  DPI缩放:" << dpiScale;
    qDebug() << "  最终缩放因子:" << m_scaleFactor;
}

void ScreenAdaptationManager::determineScreenType()
{
    int width = m_screenSize.width();
    int height = m_screenSize.height();
    
    if (width >= 5120 && height >= 2880) {
        m_screenType = ScreenType::UHD_5K;
    } else if (width >= 3840 && height >= 2160) {
        // 支持标准UHD 4K (3840x2160) 和 Cinema 4K (4096x2160)
        m_screenType = ScreenType::UHD_4K;
    } else if (width >= 2560 && height >= 1440) {
        m_screenType = ScreenType::QHD_1440P;
    } else if (width >= 1920 && height >= 1080) {
        m_screenType = ScreenType::HD_1080P;
    } else {
        m_screenType = ScreenType::CUSTOM;
    }
}

QString ScreenAdaptationManager::getAdaptedCss(const QString &css) const {
    // 把css里 px 替换成 自适应的px
    QString adaptedCss = css;
    QRegularExpression regex("(\\d+)px");
    QRegularExpressionMatchIterator i = regex.globalMatch(css);
    while (i.hasNext()) {
        QRegularExpressionMatch match = i.next();
        int originalSize = match.captured(1).toInt();
        int adaptedSize = getAdaptedSize(originalSize);
        adaptedCss.replace(match.capturedStart(), match.capturedLength(), QString::number(adaptedSize) + "px");
    }
    return adaptedCss;
}

} // namespace ScreenAdaptationConstants
