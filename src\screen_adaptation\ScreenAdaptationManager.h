#ifndef SCREENADAPTATIONMANAGER_H
#define SCREENADAPTATIONMANAGER_H

#include <QObject>
#include <QScreen>
#include <QApplication>
#include <QSize>
#include <QRect>

/**
 * @file ScreenAdaptationManager.h
 * @brief 屏幕适配管理器
 * 
 * 提供统一的多屏幕尺寸适配解决方案，支持1080p、4K等不同分辨率的自动适配
 */

namespace ScreenAdaptationConstants {
    
    // 基准分辨率（设计基准：根据FloatMenu针对4K优化的注释，使用Cinema 4K作为设计基准）
    static const int BASE_SCREEN_WIDTH = 4096;        ///< 基准屏幕宽度（Cinema 4K）
    static const int BASE_SCREEN_HEIGHT = 2160;       ///< 基准屏幕高度（Cinema 4K）
    static const qreal BASE_DPI = 96.0;               ///< 基准DPI
    
    // 屏幕分类
    enum class ScreenType {
        HD_1080P,      ///< 1920x1080 (1080p)
        QHD_1440P,     ///< 2560x1440 (1440p)
        UHD_4K,        ///< 3840x2160 (4K)
        UHD_5K,        ///< 5120x2880 (5K)
        CUSTOM         ///< 自定义分辨率
    };
    
    // 缩放级别
    static const qreal SCALE_SMALL = 0.8;             ///< 小屏幕缩放
    static const qreal SCALE_NORMAL = 1.0;            ///< 标准缩放
    static const qreal SCALE_LARGE = 1.25;            ///< 大屏幕缩放
    static const qreal SCALE_EXTRA_LARGE = 1.5;       ///< 超大屏幕缩放
    static const qreal SCALE_ULTRA_LARGE = 2.0;       ///< 4K屏幕缩放
    
    /**
     * @brief 屏幕适配管理器（单例）
     */
    class ScreenAdaptationManager {
    public:
        static ScreenAdaptationManager* instance() {
            static ScreenAdaptationManager instance;
            return &instance;
        }
        
        /**
         * @brief 初始化屏幕适配
         */
        void initialize();
        
        /**
         * @brief 更新屏幕信息
         */
        void updateScreenInfo();
        
        /**
         * @brief 获取适配后的尺寸
         * @param originalSize 原始尺寸
         * @return 适配后的尺寸
         */
        int getAdaptedSize(int originalSize) const;
        
        /**
         * @brief 获取适配后的尺寸（浮点数）
         * @param originalSize 原始尺寸
         * @return 适配后的尺寸
         */
        qreal getAdaptedSize(qreal originalSize) const;
        
        /**
         * @brief 获取适配后的QSize
         * @param originalSize 原始尺寸
         * @return 适配后的尺寸
         */
        QSize getAdaptedSize(const QSize& originalSize) const;

        /**
         * @brief 获取适配后的css 样式（把css样式里面的px单位替换为适配后的px单位）
         * @return 适配后的css 样式
         */
        QString getAdaptedCss(const QString &css) const;
        
        /**
         * @brief 获取当前缩放因子
         * @return 缩放因子
         */
        qreal getScaleFactor() const { return m_scaleFactor; }
        
        /**
         * @brief 获取设备像素比例
         * @return 设备像素比例
         */
        qreal getDevicePixelRatio() const { return m_devicePixelRatio; }
        
        /**
         * @brief 获取屏幕类型
         * @return 屏幕类型
         */
        ScreenType getScreenType() const { return m_screenType; }
        
        /**
         * @brief 获取屏幕尺寸
         * @return 屏幕尺寸
         */
        QSize getScreenSize() const { return m_screenSize; }
        
        /**
         * @brief 是否为高DPI屏幕
         * @return 是否为高DPI
         */
        bool isHighDPI() const { return m_devicePixelRatio > 1.5; }
        
        /**
         * @brief 是否为4K屏幕
         * @return 是否为4K
         */
        bool is4K() const { return m_screenType == ScreenType::UHD_4K; }
        
        /**
         * @brief 获取屏幕类型的字符串描述
         * @return 屏幕类型描述
         */
        QString getScreenTypeString() const;
        
    private:
        ScreenAdaptationManager() = default;
        
        void calculateScaleFactor();
        void determineScreenType();
        
        QSize m_screenSize;
        QRect m_screenGeometry;
        qreal m_devicePixelRatio = 1.0;
        qreal m_logicalDpi = 96.0;
        qreal m_physicalDpi = 96.0;
        qreal m_scaleFactor = 1.0;
        ScreenType m_screenType = ScreenType::HD_1080P;
    };
    
    /**
     * @brief 便捷函数：获取适配后的尺寸
     * @param originalSize 原始尺寸
     * @return 适配后的尺寸
     */
    inline int adaptSize(int originalSize) {
        return ScreenAdaptationManager::instance()->getAdaptedSize(originalSize);
    }
    
    /**
     * @brief 便捷函数：获取适配后的尺寸（浮点数）
     * @param originalSize 原始尺寸
     * @return 适配后的尺寸
     */
    inline qreal adaptSize(qreal originalSize) {
        return ScreenAdaptationManager::instance()->getAdaptedSize(originalSize);
    }
    
    /**
     * @brief 便捷函数：获取适配后的QSize
     * @param originalSize 原始尺寸
     * @return 适配后的尺寸
     */
    inline QSize adaptSize(const QSize& originalSize) {
        return ScreenAdaptationManager::instance()->getAdaptedSize(originalSize);
    }

    /**
     * @brief 便捷函数：获取适配后的css 样式（把css样式里面的px单位替换为适配后的px单位）
     * @return 适配后的css 样式
     */
    inline QString adaptCss(const QString &css) {
        auto adaptedCss = ScreenAdaptationManager::instance()->getAdaptedCss(css);
        qDebug() << "adaptedCss: " << adaptedCss << " originalCss: " << css;
        return adaptedCss;
    }
}

#endif // SCREENADAPTATIONMANAGER_H
