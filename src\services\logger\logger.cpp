#include "logger.h"
#include <QDir>
#include <QStandardPaths>
#include <QMutexLocker>
#include <QElapsedTimer>


// AsyncLogWriter 实现
AsyncLogWriter::AsyncLogWriter(QObject* parent)
    : QThread(parent), m_logFile(nullptr)
{
}

AsyncLogWriter::~AsyncLogWriter()
{
    stop();
    wait();
}

void AsyncLogWriter::addLogEntry(const LogEntry& entry)
{
    QMutexLocker locker(&m_bufferMutex);
    m_logBuffer.enqueue(entry);

    // 如果缓冲区满了，触发立即写入
    if (m_logBuffer.size() >= BUFFER_SIZE) {
        m_shouldFlush.store(true);
    }

    m_bufferCondition.wakeOne();
}

void AsyncLogWriter::setLogFile(QFile* logFile)
{
    QMutexLocker locker(&m_bufferMutex);
    m_logFile = logFile;
    if (m_logFile && m_logFile->isOpen()) {
        m_logStream.setDevice(m_logFile);
    }
}

void AsyncLogWriter::flush()
{
    m_shouldFlush.store(true);
    m_bufferCondition.wakeOne();
}

void AsyncLogWriter::stop()
{
    m_shouldStop.store(true);
    m_bufferCondition.wakeAll();
}

void AsyncLogWriter::run()
{
    QElapsedTimer flushTimer;
    flushTimer.start();

    while (!m_shouldStop.load()) {
        QQueue<LogEntry> localBuffer;

        // 获取待写入的日志条目
        {
            QMutexLocker locker(&m_bufferMutex);

            // 等待条件：有数据、需要刷新、或者超时
            if (m_logBuffer.isEmpty() && !m_shouldFlush.load()) {
                m_bufferCondition.wait(&m_bufferMutex, FLUSH_INTERVAL_MS);
            }

            // 交换缓冲区，减少锁持有时间
            if (!m_logBuffer.isEmpty()) {
                localBuffer.swap(m_logBuffer);
            }
        }

        // 写入日志条目（在锁外执行I/O操作）
        while (!localBuffer.isEmpty()) {
            LogEntry entry = localBuffer.dequeue();
            writeEntry(entry);
        }

        // 定期刷新或强制刷新
        if (m_shouldFlush.load() || flushTimer.elapsed() > FLUSH_INTERVAL_MS) {
            if (m_logFile && m_logFile->isOpen()) {
                m_logStream.flush();
                m_logFile->flush();
            }
            m_shouldFlush.store(false);
            flushTimer.restart();
        }
    }

    // 退出前写入所有剩余的日志
    QMutexLocker locker(&m_bufferMutex);
    while (!m_logBuffer.isEmpty()) {
        LogEntry entry = m_logBuffer.dequeue();
        writeEntry(entry);
    }
    if (m_logFile && m_logFile->isOpen()) {
        m_logStream.flush();
        m_logFile->flush();
    }
}

void AsyncLogWriter::writeEntry(const LogEntry& entry)
{
    QString formattedMessage = formatLogEntry(entry);

    // 输出到控制台
    qDebug().noquote() << formattedMessage;

    // 写入文件
    if (m_logFile && m_logFile->isOpen()) {
        m_logStream << formattedMessage << Qt::endl;
    }
}

QString AsyncLogWriter::formatLogEntry(const LogEntry& entry)
{
    // 委托给Logger的静态方法，避免重复实现
    return Logger::formatLogEntry(entry);
}

QString AsyncLogWriter::getLevelString(LogLevel level)
{
    // 委托给Logger的实现，避免重复
    return Logger::getLevelString(level);
}

// 初始化静态成员
Logger* Logger::s_instance = nullptr;

Logger* Logger::instance()
{
    if (!s_instance) {
        s_instance = new Logger();
    }
    return s_instance;
}

void Logger::destroy()
{
    if (s_instance) {
        delete s_instance;
        s_instance = nullptr;
    }
}

Logger::Logger()
{
    // 原子变量已在头文件中初始化
    // 创建异步写入器
    m_asyncWriter = std::make_unique<AsyncLogWriter>();
}

Logger::~Logger()
{
    // 刷新所有缓冲的日志
    flush();

    // 停止异步写入器
    if (m_asyncWriter) {
        m_asyncWriter->stop();
        m_asyncWriter->wait();
    }

    if (m_logFile.isOpen()) {
        m_logFile.close();
    }
}

void Logger::initialize(const QString& logFilePath, LogLevel level)
{
    QMutexLocker locker(&m_mutex);

    m_logLevel.store(level);

    // 如果提供了日志文件路径，则打开文件
    if (!logFilePath.isEmpty()) {
        // 确保目录存在
        QDir dir = QFileInfo(logFilePath).dir();
        if (!dir.exists()) {
            dir.mkpath(".");
        }

        m_logFile.setFileName(logFilePath);
        if (m_logFile.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
            // 设置异步写入器的日志文件
            if (m_asyncWriter) {
                m_asyncWriter->setLogFile(&m_logFile);
                // 启动异步写入线程
                if (!m_asyncWriter->isRunning()) {
                    m_asyncWriter->start();
                }
            }
        } else {
            qDebug() << "无法打开日志文件:" << logFilePath;
        }
    } else {
        // 即使没有文件，也启动异步写入器用于控制台输出
        if (m_asyncWriter && !m_asyncWriter->isRunning()) {
            m_asyncWriter->start();
        }
    }

    m_initialized.store(true);

    // 释放锁后再记录初始化日志，避免死锁
    locker.unlock();
    info("日志系统初始化完成");
}

void Logger::debug(const QString& message)
{
    log(LogLevel::DEBUG, message);
}

void Logger::info(const QString& message)
{
    log(LogLevel::INFO, message);
}

void Logger::warning(const QString& message)
{
    log(LogLevel::WARNING, message);
}

void Logger::error(const QString& message)
{
    log(LogLevel::ERROR, message);
}

void Logger::critical(const QString& message)
{
    log(LogLevel::CRITICAL, message);
}

void Logger::setLogLevel(LogLevel level)
{
    m_logLevel.store(level);
}

LogLevel Logger::getLogLevel() const
{
    // 优化：原子变量读取无需锁保护
    return m_logLevel.load();
}

bool Logger::isLevelEnabled(LogLevel level) const
{
    // 优化：原子变量读取无需锁保护
    return level >= m_logLevel.load();
}

void Logger::log(LogLevel level, const QString& message)
{
    // 检查日志级别（使用原子变量，无需加锁）
    if (level < m_logLevel.load()) {
        return;
    }

    if (!m_initialized.load()) {
        return;
    }

    // 创建日志条目
    LogEntry entry(level, message, QDateTime::currentMSecsSinceEpoch());

    // 根据配置选择同步或异步处理
    if (m_asyncEnabled.load() && m_asyncWriter) {
        // 异步处理：直接添加到缓冲区，无需加锁
        m_asyncWriter->addLogEntry(entry);
    } else {
        // 同步处理：立即写入（保留原有行为作为后备）
        logSync(entry);
    }
}

void Logger::logSync(const LogEntry& entry)
{
    QMutexLocker locker(&m_mutex);

    // 使用公共的格式化方法
    QString formattedMessage = formatLogEntry(entry);

    // 输出到控制台
    qDebug().noquote() << formattedMessage;

    // 写入文件
    if (m_logFile.isOpen()) {
        QTextStream stream(&m_logFile);
        stream << formattedMessage << Qt::endl;
        stream.flush();
    }
}

void Logger::setAsyncEnabled(bool enabled)
{
    m_asyncEnabled.store(enabled);

    if (enabled && m_asyncWriter && !m_asyncWriter->isRunning()) {
        // 启用异步模式，启动写入线程
        m_asyncWriter->start();
    } else if (!enabled && m_asyncWriter && m_asyncWriter->isRunning()) {
        // 禁用异步模式，刷新并等待线程结束
        m_asyncWriter->flush();
    }
}

bool Logger::isAsyncEnabled() const
{
    // 优化：原子变量读取无需锁保护
    return m_asyncEnabled.load();
}

void Logger::flush()
{
    if (m_asyncWriter) {
        m_asyncWriter->flush();
    }

    // 同时刷新同步日志
    QMutexLocker locker(&m_mutex);
    if (m_logFile.isOpen()) {
        QTextStream stream(&m_logFile);
        stream.flush();
        m_logFile.flush();
    }
}

QString Logger::getLevelString(LogLevel level)
{
    switch (level) {
        case LogLevel::DEBUG:
            return "调试";
        case LogLevel::INFO:
            return "信息";
        case LogLevel::WARNING:
            return "警告";
        case LogLevel::ERROR:
            return "错误";
        case LogLevel::CRITICAL:
            return "严重错误";
        default:
            return "未知";
    }
}

QString Logger::formatLogEntry(const LogEntry& entry)
{
    QString timestamp = QDateTime::fromMSecsSinceEpoch(entry.timestamp)
                           .toString("yyyy-MM-dd hh:mm:ss.zzz");

    return QString("[%1] [%2] %3")
              .arg(timestamp)
              .arg(getLevelString(entry.level))
              .arg(entry.message);
}



