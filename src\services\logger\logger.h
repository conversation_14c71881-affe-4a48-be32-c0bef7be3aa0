#ifndef LOGGER_H
#define LOGGER_H

#include <QString>
#include <QDateTime>
#include <QFile>
#include <QTextStream>
#include <QMutex>
#include <QDebug>
#include <QThread>
#include <QQueue>
#include <QWaitCondition>
#include <atomic>
#include <memory>


/**
 * @brief 日志级别枚举
 */
enum class LogLevel {
    DEBUG,
    INFO,
    WARNING,
    ERROR,
    CRITICAL
};

/**
 * @brief 日志条目结构
 */
struct LogEntry {
    LogLevel level;
    QString message;
    qint64 timestamp;

    LogEntry() = default;
    LogEntry(LogLevel l, const QString& msg, qint64 ts)
        : level(l), message(msg), timestamp(ts) {}
};

/**
 * @brief 异步日志写入线程
 */
class AsyncLogWriter : public QThread {
    Q_OBJECT

public:
    explicit AsyncLogWriter(QObject* parent = nullptr);
    ~AsyncLogWriter();

    void addLogEntry(const LogEntry& entry);
    void setLogFile(QFile* logFile);
    void flush();
    void stop();

protected:
    void run() override;

private:
    void writeEntry(const LogEntry& entry);
    QString formatLogEntry(const LogEntry& entry);
    QString getLevelString(LogLevel level);

    QQueue<LogEntry> m_logBuffer;
    QMutex m_bufferMutex;
    QWaitCondition m_bufferCondition;
    QFile* m_logFile;
    QTextStream m_logStream;
    std::atomic<bool> m_shouldStop{false};
    std::atomic<bool> m_shouldFlush{false};

    // 常量定义
    static constexpr int BUFFER_SIZE = 1000;
    static constexpr int FLUSH_INTERVAL_MS = 100;
};

/**
 * @brief 日志服务类
 *
 * 提供应用程序日志记录功能，支持不同级别的日志和文件输出
 */
class Logger {
public:
    /**
     * @brief 获取Logger单例实例
     * @return Logger* 单例实例指针
     */
    static Logger* instance();

    /**
     * @brief 销毁Logger单例实例
     */
    static void destroy();

    /**
     * @brief 初始化日志系统
     * @param logFilePath 日志文件路径，默认为空（仅控制台输出）
     * @param level 最低日志级别，默认为DEBUG
     */
    void initialize(const QString& logFilePath = QString(), LogLevel level = LogLevel::DEBUG);

    /**
     * @brief 记录调试级别日志
     * @param message 日志消息
     */
    void debug(const QString& message);

    /**
     * @brief 记录信息级别日志
     * @param message 日志消息
     */
    void info(const QString& message);

    /**
     * @brief 记录警告级别日志
     * @param message 日志消息
     */
    void warning(const QString& message);

    /**
     * @brief 记录错误级别日志
     * @param message 日志消息
     */
    void error(const QString& message);

    /**
     * @brief 记录严重错误级别日志
     * @param message 日志消息
     */
    void critical(const QString& message);

    /**
     * @brief 获取当前日志级别
     * @return LogLevel 当前日志级别
     */
    LogLevel getLogLevel() const;

    /**
     * @brief 检查指定日志级别是否启用
     * @param level 要检查的日志级别
     * @return bool 如果指定级别启用则返回true
     */
    bool isLevelEnabled(LogLevel level) const;

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     */
    void setLogLevel(LogLevel level);

    /**
     * @brief 启用或禁用异步日志
     * @param enabled 是否启用异步日志
     */
    void setAsyncEnabled(bool enabled);

    /**
     * @brief 检查异步日志是否启用
     * @return bool 异步日志是否启用
     */
    bool isAsyncEnabled() const;

    /**
     * @brief 刷新所有缓冲的日志
     */
    void flush();

    /**
     * @brief 获取日志级别字符串表示
     * @param level 日志级别
     * @return QString 日志级别字符串
     */
    static QString getLevelString(LogLevel level);

    /**
     * @brief 格式化日志条目
     * @param entry 日志条目
     * @return QString 格式化后的日志消息
     */
    static QString formatLogEntry(const LogEntry& entry);

private:
    /**
     * @brief 构造函数
     */
    Logger();

    /**
     * @brief 析构函数
     */
    ~Logger();

    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void log(LogLevel level, const QString& message);

    /**
     * @brief 同步写入日志条目（后备方案）
     * @param entry 日志条目
     */
    void logSync(const LogEntry& entry);

    static Logger* s_instance; ///< 单例实例
    std::atomic<LogLevel> m_logLevel{LogLevel::DEBUG};       ///< 当前日志级别（原子变量）
    QFile m_logFile;           ///< 日志文件
    QMutex m_mutex;            ///< 互斥锁，仅用于初始化等关键操作
    std::atomic<bool> m_initialized{false};        ///< 是否已初始化（原子变量）
    std::atomic<bool> m_asyncEnabled{true};        ///< 是否启用异步日志（原子变量）

    // 异步日志相关
    std::unique_ptr<AsyncLogWriter> m_asyncWriter; ///< 异步日志写入器

    // 同步日志缓冲区（用于控制台输出）
    QQueue<LogEntry> m_consoleBuffer;
    QMutex m_consoleMutex;
    static constexpr int CONSOLE_BUFFER_SIZE = 100;
};


#endif // LOGGER_H
