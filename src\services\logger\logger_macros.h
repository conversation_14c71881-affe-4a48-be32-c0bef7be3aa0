#ifndef LOGGER_MACROS_H
#define LOGGER_MACROS_H

#include "logger.h"
#include <QString>
#include <QFileInfo>

/**
 * @file logger_macros.h
 * @brief 提供全局日志宏定义，方便在项目中任何位置使用日志功能
 */

// 获取当前文件名（不包含路径）
#define LOG_CURRENT_FILE (QFileInfo(__FILE__).fileName())

// 获取当前函数名
#define LOG_CURRENT_FUNCTION (QString(__FUNCTION__))

// 获取当前行号
#define LOG_CURRENT_LINE (QString::number(__LINE__))

// 获取位置信息
#define LOG_LOCATION (QString("[%1:%2:%3]").arg(LOG_CURRENT_FILE).arg(LOG_CURRENT_FUNCTION).arg(LOG_CURRENT_LINE))

/**
 * @brief 调试级别日志宏
 * @param message 日志消息
 * @note 在Release模式下（定义了NDEBUG）不产生任何代码，提升性能
 */
#ifdef NDEBUG
#define LOG_DEBUG(message) ((void)0)
#else
#define LOG_DEBUG(message) \
    Logger::instance()->debug(LOG_LOCATION + " " + QString(message))
#endif

/**
 * @brief 信息级别日志宏
 * @param message 日志消息
 */
#define LOG_INFO(message) \
    Logger::instance()->info(LOG_LOCATION + " " + QString(message))

/**
 * @brief 警告级别日志宏
 * @param message 日志消息
 */
#define LOG_WARNING(message) \
    Logger::instance()->warning(LOG_LOCATION + " " + QString(message))

/**
 * @brief 错误级别日志宏
 * @param message 日志消息
 */
#define LOG_ERROR(message) \
    Logger::instance()->error(LOG_LOCATION + " " + QString(message))

/**
 * @brief 严重错误级别日志宏
 * @param message 日志消息
 */
#define LOG_CRITICAL(message) \
    Logger::instance()->critical(LOG_LOCATION + " " + QString(message))

/**
 * @brief 格式化调试级别日志宏
 * @param format 格式字符串
 * @param ... 可变参数
 * @note 在Release模式下（定义了NDEBUG）不产生任何代码，提升性能
 */
#ifdef NDEBUG
#define LOG_DEBUG_FORMAT(format, ...) ((void)0)
#else
#define LOG_DEBUG_FORMAT(format, ...) \
    Logger::instance()->debug(LOG_LOCATION + " " + QString::asprintf(format, __VA_ARGS__))
#endif

/**
 * @brief 格式化信息级别日志宏
 * @param format 格式字符串
 * @param ... 可变参数
 */
#define LOG_INFO_FORMAT(format, ...) \
    Logger::instance()->info(LOG_LOCATION + " " + QString::asprintf(format, __VA_ARGS__))

/**
 * @brief 格式化警告级别日志宏
 * @param format 格式字符串
 * @param ... 可变参数
 */
#define LOG_WARNING_FORMAT(format, ...) \
    Logger::instance()->warning(LOG_LOCATION + " " + QString::asprintf(format, __VA_ARGS__))

/**
 * @brief 格式化错误级别日志宏
 * @param format 格式字符串
 * @param ... 可变参数
 */
#define LOG_ERROR_FORMAT(format, ...) \
    Logger::instance()->error(LOG_LOCATION + " " + QString::asprintf(format, __VA_ARGS__))

/**
 * @brief 格式化严重错误级别日志宏
 * @param format 格式字符串
 * @param ... 可变参数
 */
#define LOG_CRITICAL_FORMAT(format, ...) \
    Logger::instance()->critical(LOG_LOCATION + " " + QString::asprintf(format, __VA_ARGS__))

/**
 * @brief 条件调试日志宏，仅在条件为真时记录日志
 * @param condition 条件表达式
 * @param message 日志消息
 * @note 在Release模式下（定义了NDEBUG）不产生任何代码，提升性能
 */
#ifdef NDEBUG
#define LOG_DEBUG_IF(condition, message) ((void)0)
#else
#define LOG_DEBUG_IF(condition, message) \
    do { if (condition) LOG_DEBUG(message); } while(0)
#endif

/**
 * @brief 条件信息日志宏，仅在条件为真时记录日志
 * @param condition 条件表达式
 * @param message 日志消息
 */
#define LOG_INFO_IF(condition, message) \
    do { if (condition) LOG_INFO(message); } while(0)

/**
 * @brief 条件警告日志宏，仅在条件为真时记录日志
 * @param condition 条件表达式
 * @param message 日志消息
 */
#define LOG_WARNING_IF(condition, message) \
    do { if (condition) LOG_WARNING(message); } while(0)

/**
 * @brief 条件错误日志宏，仅在条件为真时记录日志
 * @param condition 条件表达式
 * @param message 日志消息
 */
#define LOG_ERROR_IF(condition, message) \
    do { if (condition) LOG_ERROR(message); } while(0)

/**
 * @brief 条件严重错误日志宏，仅在条件为真时记录日志
 * @param condition 条件表达式
 * @param message 日志消息
 */
#define LOG_CRITICAL_IF(condition, message) \
    do { if (condition) LOG_CRITICAL(message); } while(0)

#endif // LOGGER_MACROS_H
