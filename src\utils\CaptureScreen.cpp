﻿# include "CaptureScreen.h"
#include <QGuiApplication>
#include <QScreen>
#include <QPixmap>
#include <QPainter>
#include <QDateTime>
#include <QDir>
#include <QDebug>
#include <QRect>


/**
 * 获取物理分辨率截图并保存
 * @return 截图文件路径（如果失败返回空字符串）
 */
QString capturePhysicalScreen() {
    // 确保应用已初始化
    if (!qApp) {
        qFatal("必须创建QGuiApplication实例");
        return QString();
    }

    QScreen *primaryScreen = QGuiApplication::primaryScreen();
    if (!primaryScreen) {
        qWarning("无法获取主屏幕对象");
        return QString();
    }

    // 获取设备像素比（DPR）
    qreal dpr = primaryScreen->devicePixelRatio();
    qDebug() << "设备像素比(DPR):" << dpr;

    // 获取逻辑分辨率
    QRect screenGeometry = primaryScreen->geometry();
    qDebug() << "逻辑分辨率:" << screenGeometry.size();

    // 获取屏幕实际可用区域（解决缩放导致的偏差）
    QRect screenVirtualGeometry = primaryScreen->virtualGeometry();
    qDebug() << "有效虚拟区域:" << screenVirtualGeometry.size();

    // 获取原始屏幕像素图
    QPixmap originalPixmap = primaryScreen->grabWindow(
        0,
        screenGeometry.x(),
        screenGeometry.y(),
        screenGeometry.width(),
        screenGeometry.height()
    );

    // 缩放操作前确保有有效图像
    if (originalPixmap.isNull()) {
        qWarning("原始屏幕捕获失败");
        return QString();
    }

    // 创建目标图像（按物理分辨率尺寸）
    QPixmap scaledPixmap(originalPixmap.size().width() * dpr,
                         originalPixmap.size().height() * dpr);
    scaledPixmap.setDevicePixelRatio(dpr);
    scaledPixmap.fill(Qt::transparent); // 透明背景防止黑边

    // 使用高质量缩放绘制
    QPainter painter(&scaledPixmap);
    painter.setRenderHint(QPainter::SmoothPixmapTransform);
    painter.scale(dpr, dpr);
    painter.drawPixmap(0, 0, originalPixmap);
    painter.end();

    // 生成文件路径
    QString fileName =  "screenshot_" +
                      QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") +
                      ".png";
    QString filePath = QDir::tempPath() + "/" + fileName ;

    // 使用100%质量保存（避免压缩伪影）
    if (!scaledPixmap.save(filePath, "PNG")) {
        qWarning("文件保存失败: %s", qUtf8Printable(filePath));
        return QString();
    }

    qDebug() << "物理分辨率截图保存成功:" << filePath
             << "尺寸:" << scaledPixmap.size()
             << "DPR:" << scaledPixmap.devicePixelRatio();

    return QFileInfo(filePath).absoluteFilePath();
}