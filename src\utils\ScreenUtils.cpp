#include "ScreenUtils.h"
#include <QDebug>

// 静态成员变量定义
QSize ScreenUtils::s_cachedScreenSize = QSize();
QRect ScreenUtils::s_cachedScreenGeometry = QRect();
bool ScreenUtils::s_initialized = false;

QSize ScreenUtils::getScreenSize()
{
    if (!s_initialized) {
        qDebug() << "【ScreenUtils】首次调用，初始化屏幕信息";
        initializeScreenInfo();
    }
    qDebug() << "【ScreenUtils】返回屏幕尺寸:" << s_cachedScreenSize;
    return s_cachedScreenSize;
}

QRect ScreenUtils::getScreenGeometry()
{
    if (!s_initialized) {
        initializeScreenInfo();
    }
    return s_cachedScreenGeometry;
}

int ScreenUtils::getScreenWidth()
{
    return getScreenSize().width();
}

int ScreenUtils::getScreenHeight()
{
    return getScreenSize().height();
}

void ScreenUtils::refreshScreenInfo()
{
    s_initialized = false;
    initializeScreenInfo();
}

void ScreenUtils::initializeScreenInfo()
{
    QScreen* screen = QApplication::primaryScreen();
    if (screen) {
        s_cachedScreenSize = screen->size();
        s_cachedScreenGeometry = screen->geometry();
        s_initialized = true;
        
        qDebug() << "【ScreenUtils】屏幕信息初始化完成:";
        qDebug() << "  屏幕尺寸:" << s_cachedScreenSize;
        qDebug() << "  屏幕几何:" << s_cachedScreenGeometry;
    } else {
        // 如果无法获取屏幕信息，使用默认值
        s_cachedScreenSize = QSize(1920, 1080);
        s_cachedScreenGeometry = QRect(0, 0, 1920, 1080);
        s_initialized = true;
        
        qWarning() << "【ScreenUtils】无法获取主屏幕信息，使用默认值:" << s_cachedScreenSize;
    }
}
