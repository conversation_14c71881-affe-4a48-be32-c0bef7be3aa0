#ifndef SCREENUTILS_H
#define SCREENUTILS_H

#include <QSize>
#include <QRect>
#include <QScreen>
#include <QApplication>

/**
 * @brief 屏幕工具类
 * 
 * 提供全局的屏幕尺寸获取功能，避免组件延迟初始化
 * 在第一次调用时获取屏幕大小，后续调用直接返回缓存值
 */
class ScreenUtils
{
public:
    /**
     * @brief 获取主屏幕尺寸
     * @return 主屏幕尺寸
     */
    static QSize getScreenSize();
    
    /**
     * @brief 获取主屏幕几何信息
     * @return 主屏幕几何信息
     */
    static QRect getScreenGeometry();
    
    /**
     * @brief 获取主屏幕宽度
     * @return 主屏幕宽度
     */
    static int getScreenWidth();
    
    /**
     * @brief 获取主屏幕高度
     * @return 主屏幕高度
     */
    static int getScreenHeight();
    
    /**
     * @brief 强制刷新屏幕信息缓存
     * 当屏幕分辨率改变时可调用此方法
     */
    static void refreshScreenInfo();

private:
    static QSize s_cachedScreenSize;
    static QRect s_cachedScreenGeometry;
    static bool s_initialized;
    
    /**
     * @brief 初始化屏幕信息
     */
    static void initializeScreenInfo();
};

#endif // SCREENUTILS_H
