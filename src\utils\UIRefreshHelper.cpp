#include "UIRefreshHelper.h"
#include <QApplication>
#include <QCoreApplication>
#include <QElapsedTimer>
#include <QDebug>

UIRefreshHelper* UIRefreshHelper::s_instance = nullptr;

UIRefreshHelper* UIRefreshHelper::instance()
{
    if (!s_instance) {
        s_instance = new UIRefreshHelper();
    }
    return s_instance;
}

UIRefreshHelper::UIRefreshHelper(QObject* parent)
    : QObject(parent)
    , m_refreshTimer(new QTimer(this))
    , m_processingEvents(false)
    , m_lastProcessEventsTime(0)
    , m_processEventsInterval(16) // 默认60fps，约16ms间隔
{
    m_refreshTimer->setSingleShot(true);
    m_refreshTimer->setInterval(0);
    connect(m_refreshTimer, &QTimer::timeout, this, &UIRefreshHelper::processPendingRefresh);
}

UIRefreshHelper::~UIRefreshHelper()
{
    s_instance = nullptr;
}

void UIRefreshHelper::requestRefresh(QWidget* widget, bool force)
{
    if (!widget) {
        return;
    }

    // 检查widget是否有效
    if (!widget->isVisible() || widget->isHidden()) {
        return;
    }

    if (force) {
        // 避免在事件处理过程中直接调用repaint，改为延迟处理
        QTimer::singleShot(0, [widget]() {
            if (widget && widget->isVisible()) {
                widget->update();
                // 使用processEvents的安全版本
                QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents, 10);
            }
        });
        return;
    }

    // 添加到待刷新列表
    QMutexLocker locker(&m_mutex);
    m_pendingWidgets.insert(widget);

    if (!m_refreshTimer->isActive()) {
        m_refreshTimer->start();
    }
}

void UIRefreshHelper::requestDelayedRefresh(QWidget* widget, int delayMs)
{
    if (!widget) {
        return;
    }
    
    QTimer::singleShot(delayMs, [this, widget]() {
        requestRefresh(widget, false);
    });
}

void UIRefreshHelper::requestBatchRefresh(const QList<QWidget*>& widgets, bool force)
{
    if (widgets.isEmpty()) {
        return;
    }

    if (force) {
        // 使用延迟处理避免重入问题
        QTimer::singleShot(0, [widgets]() {
            for (QWidget* widget : widgets) {
                if (widget && widget->isVisible()) {
                    widget->update();
                }
            }
            // 批量处理后统一刷新事件
            QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents, 10);
        });
        return;
    }

    // 添加到待刷新列表
    QMutexLocker locker(&m_mutex);
    for (QWidget* widget : widgets) {
        if (widget && widget->isVisible()) {
            m_pendingWidgets.insert(widget);
        }
    }

    if (!m_refreshTimer->isActive()) {
        m_refreshTimer->start();
    }
}

void UIRefreshHelper::safeProcessEvents()
{
    // 重入保护
    if (m_processingEvents) {
        qDebug() << "UIRefreshHelper: processEvents already in progress, skipping";
        return;
    }
    
    // 频率限制
    static QElapsedTimer timer;
    if (!timer.isValid()) {
        timer.start();
    }
    
    qint64 currentTime = timer.elapsed();
    if (currentTime - m_lastProcessEventsTime < m_processEventsInterval) {
        qDebug() << "UIRefreshHelper: processEvents called too frequently, skipping";
        return;
    }
    
    m_processingEvents = true;
    m_lastProcessEventsTime = currentTime;
    
    try {
        QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents);
    } catch (...) {
        qWarning() << "UIRefreshHelper: Exception in processEvents";
    }
    
    m_processingEvents = false;
}

void UIRefreshHelper::setProcessEventsInterval(int intervalMs)
{
    m_processEventsInterval = qMax(1, intervalMs);
}

void UIRefreshHelper::safeForceRefresh(QWidget* widget)
{
    if (!widget || !widget->isVisible()) {
        return;
    }

    // 使用延迟调用避免重入问题
    QTimer::singleShot(0, [widget]() {
        if (widget && widget->isVisible()) {
            widget->update();
            // 限制processEvents的处理时间
            QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents, 5);
        }
    });
}

void UIRefreshHelper::processPendingRefresh()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_pendingWidgets.isEmpty()) {
        return;
    }
    
    // 清理无效的widget
    cleanupInvalidWidgets();
    
    // 刷新所有待处理的widget
    for (QWidget* widget : m_pendingWidgets) {
        if (widget && widget->isVisible()) {
            widget->update();
        }
    }
    
    m_pendingWidgets.clear();
    
    // 安全地处理事件
    locker.unlock();
    safeProcessEvents();
}

void UIRefreshHelper::cleanupInvalidWidgets()
{
    auto it = m_pendingWidgets.begin();
    while (it != m_pendingWidgets.end()) {
        QWidget* widget = *it;
        // 更严格的widget有效性检查
        if (!widget ||
            widget->isHidden() ||
            !widget->isVisible() ||
            widget->visibleRegion().isEmpty()) {
            it = m_pendingWidgets.erase(it);
        } else {
            ++it;
        }
    }
}
