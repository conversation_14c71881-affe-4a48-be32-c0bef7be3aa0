#pragma once

#include <QObject>
#include <QWidget>
#include <QTimer>
#include <QSet>
#include <QMutex>

/**
 * @brief UI刷新助手类
 * 
 * 提供安全的UI刷新机制，避免频繁调用processEvents()带来的问题
 */
class UIRefreshHelper : public QObject
{
    Q_OBJECT

public:
    static UIRefreshHelper* instance();
    
    /**
     * @brief 请求立即刷新指定widget
     * @param widget 需要刷新的widget
     * @param force 是否强制立即刷新（使用repaint）
     */
    void requestRefresh(QWidget* widget, bool force = false);
    
    /**
     * @brief 请求延迟刷新指定widget
     * @param widget 需要刷新的widget
     * @param delayMs 延迟时间（毫秒）
     */
    void requestDelayedRefresh(QWidget* widget, int delayMs = 0);
    
    /**
     * @brief 批量刷新多个widget
     * @param widgets 需要刷新的widget列表
     * @param force 是否强制立即刷新
     */
    void requestBatchRefresh(const QList<QWidget*>& widgets, bool force = false);
    
    /**
     * @brief 安全的processEvents调用
     * 
     * 带有重入保护和频率限制
     */
    void safeProcessEvents();
    
    /**
     * @brief 设置processEvents的最小间隔
     * @param intervalMs 最小间隔（毫秒）
     */
    void setProcessEventsInterval(int intervalMs);

    /**
     * @brief 安全的强制刷新方法
     * @param widget 需要刷新的widget
     */
    void safeForceRefresh(QWidget* widget);

private slots:
    void processPendingRefresh();

private:
    explicit UIRefreshHelper(QObject* parent = nullptr);
    ~UIRefreshHelper();
    
    static UIRefreshHelper* s_instance;
    
    QTimer* m_refreshTimer;
    QSet<QWidget*> m_pendingWidgets;
    QMutex m_mutex;
    
    // processEvents保护
    bool m_processingEvents;
    qint64 m_lastProcessEventsTime;
    int m_processEventsInterval; // 最小间隔（毫秒）
    
    void cleanupInvalidWidgets();
};
