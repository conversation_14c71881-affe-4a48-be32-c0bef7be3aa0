﻿//
// Created by HLJY on 2025/6/27.
//

#include "UrlCache.h"
#include <QUrl>
#include <QFile>
#include <QDir>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QEventLoop>
#include <QCryptographicHash>
#include <QDebug>

QString UrlCache::cacheUrl(const QString& url) {
    QUrl parsedUrl(url);
    if (!parsedUrl.isValid()) {
        qWarning() << "Invalid URL:" << url;
        return QString();
    }

    // 使用 URL 的 MD5 哈希作为文件名，避免特殊字符问题
    QByteArray hash = QCryptographicHash::hash(url.toUtf8(), QCryptographicHash::Md5);
    QString filename = QString(hash.toHex());
    QString fileExtension = parsedUrl.path().section('.', -1);
    if (!fileExtension.isEmpty()) {
        filename += "." + fileExtension;
    }
    QString fileDir = QDir::tempPath() + "/" + "QT";
    QDir dir(fileDir);
    if (!dir.exists()) {
        dir.mkpath(fileDir);
    }
    QString filePath = fileDir + "/" + filename;


    // 检查文件是否已经存在
    if (QFile::exists(filePath)) {
        return filePath;
    }

    // 创建网络管理器（局部静态或每次新建）
    static QNetworkAccessManager networkManager;

    // 下载文件
    QNetworkReply* reply = networkManager.get(QNetworkRequest(parsedUrl));

    // 等待下载完成（同步方式）
    QEventLoop loop;
    QObject::connect(reply, &QNetworkReply::finished, &loop, &QEventLoop::quit);
    loop.exec();

    if (reply->error() != QNetworkReply::NoError) {
        qWarning() << "Download failed:" << reply->errorString();
        reply->deleteLater();
        return QString();
    }

    // 读取数据并保存到本地文件
    QByteArray data = reply->readAll();
    reply->deleteLater();

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open file for writing:" << filePath;
        return QString();
    }

    file.write(data);
    file.close();

    return filePath;
}
