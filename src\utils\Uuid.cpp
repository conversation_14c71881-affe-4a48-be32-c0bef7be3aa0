﻿//
// Created by HLJY on 2025/6/20.
//

#include "Uuid.h"

std::string UuidUtil::generate_v4() {
    // 使用静态引擎，只初始化一次
    static std::random_device rd;
    static std::mt19937 gen(rd());

    // 分布定义可以保留在函数内部
    static std::uniform_int_distribution<> dis(0, 15);
    static std::uniform_int_distribution<> dis2(8, 11);

    // UUID版本4的标准格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx
    std::stringstream ss;

    for (int i = 0; i < 8; i++) ss << std::hex << dis(gen);
    ss << '-';

    for (int i = 0; i < 4; i++) ss << std::hex << dis(gen);
    ss << "-4";

    for (int i = 0; i < 3; i++) ss << std::hex << dis(gen);
    ss << "-" << std::hex << dis2(gen);

    for (int i = 0; i < 3; i++) ss << std::hex << dis(gen);
    ss << '-';

    for (int i = 0; i < 12; i++) ss << std::hex << dis(gen);

    return ss.str();
}