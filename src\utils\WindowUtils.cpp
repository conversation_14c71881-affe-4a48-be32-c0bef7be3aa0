//
// Created by HLJY on 2025/7/10.
//

#include "WindowUtils.h"
#include <QGuiApplication>
#include <QWindow>

void WindowUtils::minimizeAllWindow() {
    // 确保在主线程中调用
    if (!QThread::currentThread()->isMainThread()) {
        QMetaObject::invokeMethod(
                QGuiApplication::instance(),
                [=]() {
                    WindowUtils::minimizeAllWindow();
                },
                Qt::QueuedConnection);
        return;
    }
    QWindowList windows = QGuiApplication::topLevelWindows();
    for (QWindow *item: windows) {
        auto objectName = item->objectName();

        if (!(item->windowState() & Qt::WindowMinimized) && item->isVisible()) {
            qDebug() << "minimize window name: " << objectName;
            item->setProperty("isMinimizedByMinimizeAllWindow", true);
            item->hide();
        } else {
            qDebug() << "窗口已最小化或不可见，跳过最小化: " << objectName;
        }
    }
}

void WindowUtils::restoreMinimizeWindow() {
    if (!QThread::currentThread()->isMainThread()) {
        QMetaObject::invokeMethod(
                QGuiApplication::instance(),
                [=]() {
                    WindowUtils::restoreMinimizeWindow();
                },
                Qt::QueuedConnection);
        return;
    }
    auto windows = QGuiApplication::topLevelWindows();
    for (auto item: windows) {
        auto objectName = item->objectName();

        if (item->property("isMinimizedByMinimizeAllWindow").toBool()) {
            qDebug() << "restore minimize window name: " << objectName;
            item->setProperty("isMinimizedByMinimizeAllWindow", false);
            item->show();
        } else {
            qDebug() << "窗口不是由minimizeAllWindow最小化，跳过: " << objectName;
        }
    }
}