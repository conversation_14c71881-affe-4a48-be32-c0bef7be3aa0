﻿#include <windows.h>
#include <shlobj.h>
#include <shlwapi.h>
#include <gdiplus.h>
#include <iostream>
#include <vector>
#include <string>
#include <unordered_set>
#include <memory>
#include <algorithm>
#include <wincodec.h>  // WIC编解码器
#include "WindowsApp.h"

#include <codecvt>

#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "ole32.lib")
#pragma comment(lib, "shell32.lib")
#pragma comment(lib, "gdiplus.lib")
#pragma comment(lib, "version.lib")
#pragma comment(lib, "windowscodecs.lib")

using namespace std;
using namespace Gdiplus;



// 初始化GDI+
class GdiplusInitializer {
private:
    ULONG_PTR gdiplusToken;
public:
    GdiplusInitializer() {
        GdiplusStartupInput gdiplusStartupInput;
        GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, NULL);
    }
    ~GdiplusInitializer() {
        GdiplusShutdown(gdiplusToken);
    }
};
// 扩展环境变量
wstring ExpandEnvironmentVariables(const wstring& input) {
    DWORD size = ExpandEnvironmentStringsW(input.c_str(), NULL, 0);
    wstring result(size, L'\0');
    ExpandEnvironmentStringsW(input.c_str(), &result[0], size);
    result.resize(size - 1);
    return result;
}

// 将宽字符串转换为小写
wstring ToLower(const wstring& str) {
    wstring result = str;
    transform(result.begin(), result.end(), result.begin(), towlower);
    return result;
}

// 检查名称是否包含过滤关键字
bool ContainsFilterKeyword(const wstring& name, const unordered_set<wstring>& filterKeywords) {
    wstring lowerName = ToLower(name);
    for (const auto& keyword : filterKeywords) {
        if (lowerName.find(keyword) != wstring::npos) {
            return true;
        }
    }
    return false;
}

// 列出快捷方式，限制为只扫描目录及其直接子目录
void ListShortcuts(const wstring& directory, vector<wstring>& shortcuts,
    const unordered_set<wstring>& excludedFolders,
    const unordered_set<wstring>& excludedFiles,
    int currentDepth = 0) {

    // 仅处理根目录和直接子目录（深度0、1和2）
    if (currentDepth > 2) return;

    WIN32_FIND_DATAW findFileData;
    HANDLE hFind = FindFirstFileW((directory + L"\\*").c_str(), &findFileData);

    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            if (wcscmp(findFileData.cFileName, L".") == 0 || wcscmp(findFileData.cFileName, L"..") == 0) {
                continue;
            }

            wstring fileOrDir = directory + L"\\" + findFileData.cFileName;

            if (findFileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) {
                if (excludedFolders.find(findFileData.cFileName) == excludedFolders.end()) {
                    // 递归处理子目录，增加深度计数
                    ListShortcuts(fileOrDir, shortcuts, excludedFolders, excludedFiles, currentDepth + 1);
                }
            }
            else if (PathMatchSpecW(findFileData.cFileName, L"*.lnk")) {
                if (excludedFiles.find(findFileData.cFileName) == excludedFiles.end()) {
                    shortcuts.push_back(fileOrDir);
                }
            }
        } while (FindNextFileW(hFind, &findFileData) != 0);
        FindClose(hFind);
    }
}

// Base64编码
string Base64Encode(const vector<BYTE>& data) {
    static const char table[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    string result;
    int val = 0, valb = -6;
    for (BYTE c : data) {
        val = (val << 8) + c;
        valb += 8;
        while (valb >= 0) {
            result.push_back(table[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }
    if (valb > -6) result.push_back(table[((val << 8) >> (valb + 8)) & 0x3F]);
    while (result.size() % 4) result.push_back('=');
    return result;
}

// 获取指定MIME类型的编码器CLSID
int GetEncoderClsid(const WCHAR* format, CLSID* pClsid) {
    UINT num = 0;
    UINT size = 0;

    GetImageEncodersSize(&num, &size);
    if (size == 0) return -1;

    ImageCodecInfo* pImageCodecInfo = (ImageCodecInfo*)malloc(size);
    if (pImageCodecInfo == NULL) return -1;

    GetImageEncoders(num, size, pImageCodecInfo);

    for (UINT i = 0; i < num; ++i) {
        if (wcscmp(pImageCodecInfo[i].MimeType, format) == 0) {
            *pClsid = pImageCodecInfo[i].Clsid;
            free(pImageCodecInfo);
            return i;
        }
    }

    free(pImageCodecInfo);
    return -1;
}

string IconToPngBase64(HICON hIcon) {
    if (!hIcon) return "";

    // 初始化COM
    CoInitialize(NULL);

    // 获取图标尺寸
    ICONINFO iconInfo;
    GetIconInfo(hIcon, &iconInfo);
    BITMAP bmp;
    GetObject(iconInfo.hbmColor, sizeof(BITMAP), &bmp);
    int width = bmp.bmWidth;
    int height = bmp.bmHeight;

    DeleteObject(iconInfo.hbmColor);
    DeleteObject(iconInfo.hbmMask);

    // 创建兼容DC和内存位图
    HDC hScreenDC = GetDC(NULL);
    HDC hMemDC = CreateCompatibleDC(hScreenDC);

    // 创建32位位图（支持Alpha通道）
    BITMAPV5HEADER bi = { 0 };
    bi.bV5Size = sizeof(BITMAPV5HEADER);
    bi.bV5Width = width;
    bi.bV5Height = -height; // 上下翻转
    bi.bV5Planes = 1;
    bi.bV5BitCount = 32;
    bi.bV5Compression = BI_BITFIELDS;
    // 关键：设置透明通道掩码
    bi.bV5RedMask = 0x00FF0000;
    bi.bV5GreenMask = 0x0000FF00;
    bi.bV5BlueMask = 0x000000FF;
    bi.bV5AlphaMask = 0xFF000000;
    bi.bV5CSType = LCS_WINDOWS_COLOR_SPACE;
    bi.bV5Intent = LCS_GM_IMAGES;

    void* pBits = NULL;
    HBITMAP hBitmap = CreateDIBSection(hMemDC, (BITMAPINFO*)&bi, DIB_RGB_COLORS, &pBits, NULL, 0);

    if (!hBitmap) {
        DeleteDC(hMemDC);
        ReleaseDC(NULL, hScreenDC);
        return "";
    }

    // 选择位图到DC
    HBITMAP hOldBitmap = (HBITMAP)SelectObject(hMemDC, hBitmap);

    // 清除位图内容（透明黑色）
    RECT rc = { 0, 0, width, height };
    // 使用GDI+的透明画刷
    HBRUSH hBrush = CreateSolidBrush(RGB(0, 0, 0));
    FillRect(hMemDC, &rc, hBrush);
    DeleteObject(hBrush);

    // 使用DrawIconEx绘制图标（带透明通道）
    DrawIconEx(hMemDC, 0, 0, hIcon, width, height, 0, NULL, DI_NORMAL);

    // 使用WIC保存为PNG（更可靠地保留透明度）
    IStream* pStream = NULL;
    CreateStreamOnHGlobal(NULL, TRUE, &pStream);

    // 使用WIC创建PNG编码器
    IWICImagingFactory* pFactory = NULL;
    CoCreateInstance(CLSID_WICImagingFactory, NULL, CLSCTX_INPROC_SERVER, IID_IWICImagingFactory, (LPVOID*)&pFactory);

    if (pFactory && pStream) {
        IWICBitmap* pWICBitmap = NULL;
        pFactory->CreateBitmapFromHBITMAP(hBitmap, NULL, WICBitmapUsePremultipliedAlpha, &pWICBitmap);

        if (pWICBitmap) {
            IWICBitmapEncoder* pEncoder = NULL;
            pFactory->CreateEncoder(GUID_ContainerFormatPng, NULL, &pEncoder);

            if (pEncoder) {
                pEncoder->Initialize(pStream, WICBitmapEncoderNoCache);

                IWICBitmapFrameEncode* pFrame = NULL;
                pEncoder->CreateNewFrame(&pFrame, NULL);

                if (pFrame) {
                    pFrame->Initialize(NULL);
                    pFrame->SetSize(width, height);

                    WICPixelFormatGUID format = GUID_WICPixelFormat32bppPBGRA;
                    pFrame->SetPixelFormat(&format);
                    pFrame->WriteSource(pWICBitmap, NULL);
                    pFrame->Commit();
                    pFrame->Release();
                }

                pEncoder->Commit();
                pEncoder->Release();
            }

            pWICBitmap->Release();
        }

        pFactory->Release();

        // 读取流数据
        STATSTG stat;
        pStream->Stat(&stat, STATFLAG_NONAME);
        ULONG size = stat.cbSize.LowPart;

        vector<BYTE> buffer(size);
        LARGE_INTEGER li = { 0 };
        pStream->Seek(li, STREAM_SEEK_SET, NULL);
        pStream->Read(buffer.data(), size, NULL);

        // Base64编码
        string base64 = "data:image/png;base64," + Base64Encode(buffer);

        pStream->Release();

        // 清理资源
        SelectObject(hMemDC, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hMemDC);
        ReleaseDC(NULL, hScreenDC);

        CoUninitialize();
        return base64;
    }

    // 清理资源
    if (pStream) pStream->Release();
    if (pFactory) pFactory->Release();

    SelectObject(hMemDC, hOldBitmap);
    DeleteObject(hBitmap);
    DeleteDC(hMemDC);
    ReleaseDC(NULL, hScreenDC);

    CoUninitialize();
    return "";
}

// 从文件路径提取图标 - 优先提取高分辨率图标
string ExtractIconFromFile(const wstring& filePath) {
    HICON hIcon = NULL;

    // 方法1: 尝试使用IExtractIcon接口获取高分辨率图标
    IShellItemImageFactory* pImageFactory = NULL;
    HRESULT hr = SHCreateItemFromParsingName(filePath.c_str(), NULL, IID_IShellItemImageFactory, (void**)&pImageFactory);

    if (SUCCEEDED(hr)) {
        // 尝试获取最大尺寸的图标
        const SIZE size = { 256, 256 }; // 尝试获取256x256的图标
        HBITMAP hBitmap = NULL;
        hr = pImageFactory->GetImage(size, SIIGBF_BIGGERSIZEOK, &hBitmap);

        if (SUCCEEDED(hr) && hBitmap) {
            // 将HBITMAP转换为HICON
            ICONINFO ii = { 0 };
            ii.fIcon = TRUE;
            ii.hbmColor = hBitmap;
            ii.hbmMask = CreateBitmap(256, 256, 1, 1, NULL);

            hIcon = CreateIconIndirect(&ii);

            // 清理
            DeleteObject(ii.hbmMask);
            DeleteObject(hBitmap);
            pImageFactory->Release();
        }
        else {
            pImageFactory->Release();
        }
    }

    // 方法2: 如果上述方法失败，尝试使用SHGetFileInfoW获取图标
    if (!hIcon) {
        SHFILEINFOW sfi = { 0 };
        if (SUCCEEDED(SHGetFileInfoW(filePath.c_str(), 0, &sfi, sizeof(sfi), SHGFI_ICON | SHGFI_LARGEICON))) {
            hIcon = sfi.hIcon;
        }
    }

    // 方法3: 如果上述方法仍然失败，尝试使用ExtractIconExW或默认图标
    if (!hIcon) {
        if (!ExtractIconExW(filePath.c_str(), 0, &hIcon, NULL, 1)) {
            // 所有方法都失败，使用默认图标
            ExtractIconExW(L"shell32.dll", 0, &hIcon, NULL, 1);
        }
    }

    // 转换为Base64 PNG
    string result = IconToPngBase64(hIcon);

    // 清理
    if (hIcon) DestroyIcon(hIcon);

    return result;
}

// 获取快捷方式信息
bool GetShortcutInfo(const wstring& shortcutPath, wstring& appName, wstring& targetPath, string& iconBase64) {
    // 初始化COM
    CoInitialize(NULL);

    IShellLinkW* pShellLink = NULL;
    HRESULT hr = CoCreateInstance(CLSID_ShellLink, NULL, CLSCTX_INPROC_SERVER, IID_IShellLinkW, (LPVOID*)&pShellLink);
    bool success = false;

    if (SUCCEEDED(hr)) {
        IPersistFile* pPersistFile = NULL;
        hr = pShellLink->QueryInterface(IID_IPersistFile, (LPVOID*)&pPersistFile);

        if (SUCCEEDED(hr)) {
            hr = pPersistFile->Load(shortcutPath.c_str(), STGM_READ);

            if (SUCCEEDED(hr)) {
                WIN32_FIND_DATAW wfd;
                WCHAR target[MAX_PATH] = { 0 };
                WCHAR description[MAX_PATH] = { 0 };

                // 获取目标路径
                if (SUCCEEDED(pShellLink->GetPath(target, MAX_PATH, &wfd, SLGP_UNCPRIORITY))) {
                    targetPath = target;
                    success = true;
                }

                // 方法1: 从目标文件的版本信息获取应用名称
                if (!targetPath.empty()) {
                    DWORD versionSize = GetFileVersionInfoSizeW(targetPath.c_str(), NULL);
                    if (versionSize > 0) {
                        vector<BYTE> versionData(versionSize);
                        if (GetFileVersionInfoW(targetPath.c_str(), 0, versionSize, versionData.data())) {
                            UINT len = 0;
                            LPWSTR productName = NULL;

                            // 尝试获取产品名称或文件描述（英文或中文）
                            if (VerQueryValueW(versionData.data(), L"\\StringFileInfo\\040904B0\\ProductName",
                                (LPVOID*)&productName, &len) && len > 0 && productName) {
                                appName = productName;
                            }
                            else if (VerQueryValueW(versionData.data(), L"\\StringFileInfo\\040904B0\\FileDescription",
                                (LPVOID*)&productName, &len) && len > 0 && productName) {
                                appName = productName;
                            }
                            else if (VerQueryValueW(versionData.data(), L"\\StringFileInfo\\080404B0\\ProductName",
                                (LPVOID*)&productName, &len) && len > 0 && productName) {
                                appName = productName;
                            }
                            else if (VerQueryValueW(versionData.data(), L"\\StringFileInfo\\080404B0\\FileDescription",
                                (LPVOID*)&productName, &len) && len > 0 && productName) {
                                appName = productName;
                            }
                        }
                    }
                }

                // 方法2: 如果从版本信息获取失败，使用快捷方式描述
                if (appName.empty() && SUCCEEDED(pShellLink->GetDescription(description, MAX_PATH)) && wcslen(description) > 0) {
                    appName = description;
                }

                // 方法3: 如果描述为空，尝试使用目标文件名（不含路径和扩展名）
                if (appName.empty() && !targetPath.empty()) {
                    wstring fileName = targetPath.substr(targetPath.find_last_of(L"\\") + 1);
                    // 移除扩展名
                    size_t dotPos = fileName.find_last_of(L".");
                    if (dotPos != wstring::npos) {
                        fileName = fileName.substr(0, dotPos);
                    }
                    appName = fileName;
                }

                // 提取图标
                if (!targetPath.empty()) {
                    iconBase64 = ExtractIconFromFile(targetPath);
                }
                else {
                    iconBase64 = ExtractIconFromFile(shortcutPath);
                }
            }
            pPersistFile->Release();
        }
        pShellLink->Release();
    }
    CoUninitialize();  // 清理资源

    return success;
}

// 宽字符转换为多字节字符
string WStringToString(const wstring& wstr) {
    if (wstr.empty()) return "";
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    string str(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &str[0], size_needed, NULL, NULL);
    return str;
}



// 获取开始菜单应用列表
vector<WindowsAppInfo> GetWindowsApps(
    const unordered_set<wstring>& excludedFolders,
    const unordered_set<wstring>& excludedFiles,
    const unordered_set<wstring>& filterKeywords) {

    // 初始化GDI+
    static GdiplusInitializer gdiplusInit;
    // 扫描路径 - 包含开始菜单和桌面
    vector<wstring> startMenuPaths = {
        // L"C:\\ProgramData\\Microsoft\\Windows\\Start Menu\\Programs",
        // ExpandEnvironmentVariables(L"%APPDATA%\\Microsoft\\Windows\\Start Menu\\Programs"),
        // 添加桌面路径
        L"C:\\Users\\<USER>\\Desktop",
        ExpandEnvironmentVariables(L"%USERPROFILE%\\Desktop")
    };

    unordered_set<wstring> processedTargets;
    vector<WindowsAppInfo> apps;

    for (auto& path : startMenuPaths) {
        vector<wstring> shortcuts;
        ListShortcuts(path, shortcuts, excludedFolders, excludedFiles);

        for (const auto& shortcut : shortcuts) {
            // 提取快捷方式文件名
            wstring shortcutFileName = shortcut.substr(shortcut.find_last_of(L"\\") + 1);
            // 移除.lnk扩展名后检查
            if (shortcutFileName.size() > 4 && shortcutFileName.substr(shortcutFileName.size() - 4) == L".lnk") {
                shortcutFileName = shortcutFileName.substr(0, shortcutFileName.size() - 4);
            }

            // 检查文件名是否包含过滤关键词
            if (ContainsFilterKeyword(shortcutFileName, filterKeywords)) {
                continue;  // 跳过包含过滤关键词的快捷方式
            }

            wstring appName, targetPath;
            string iconBase64;

            if (GetShortcutInfo(shortcut, appName, targetPath, iconBase64) && !targetPath.empty()) {
                if (processedTargets.find(targetPath) == processedTargets.end()) {
                    processedTargets.insert(targetPath);

                    WindowsAppInfo app;
                    app.name = shortcutFileName;
                    app.shortcut = shortcut;
                    app.path = targetPath;
                    app.iconBase64 = iconBase64;
                    apps.push_back(app);
                }
            }
        }
    }

    return apps;
}

vector<WindowsAppInfo> GetWindowsApps() {
    // 设置默认过滤条件

    unordered_set<wstring> excludedFolders = {
        L"7-Zip", L"Git", L"HUAWEI", L"Maintenance", L"NVIDIA Corporation",
        L"Windows Kits", L"Windows PowerShell", L"Accessibility", L"Accessories",
        L"Administrative Tools", L"Startup", L"文印管理客户端"
    };

    unordered_set<wstring> excludedFiles = {
        L"OneDrive.lnk", L"文件资源管理器.lnk", L"Windows 工具"
    };

    unordered_set<wstring> filterKeywords = {
        L"uninstall", L"卸载", L"help", L"帮助", L"readme", L"read me",
        L"manual", L"手册", L"document", L"文档", L"update", L"更新",
        L"setup", L"安装", L"remove", L"删除", L"repair", L"修复",
        L"license", L"许可"
    };

    return GetWindowsApps(excludedFolders, excludedFiles, filterKeywords);
}

std::wstring s2ws(const std::string& str) {
    using convert_typeX = std::codecvt_utf8<wchar_t>;
    std::wstring_convert<convert_typeX, wchar_t> converterX;
    return converterX.from_bytes(str);
}

std::string ws2s(const std::wstring& wstr) {
    using convert_typeX = std::codecvt_utf8<wchar_t>;
    std::wstring_convert<convert_typeX, wchar_t> converterX;
    return converterX.to_bytes(wstr);
}

std::vector<WindowsAppInfo> GetWindowsApps(
    const std::unordered_set<std::string>& excludedFolders,
    const std::unordered_set<std::string>& excludedFiles,
    const std::unordered_set<std::string>& filterKeywords) {

    std::unordered_set<std::wstring> excludedFoldersW;
    std::unordered_set<std::wstring> excludedFilesW;
    std::unordered_set<std::wstring> filterKeywordsW;

    for (const auto& folder : excludedFolders) {
        excludedFoldersW.insert(s2ws(folder));
    }
    for (const auto& file : excludedFiles) {
        excludedFilesW.insert(s2ws(file));
    }
    for (const auto& keyword : filterKeywords) {
        filterKeywordsW.insert(s2ws(keyword));
    }
    auto appsW = GetWindowsApps(excludedFoldersW, excludedFilesW, filterKeywordsW);
    return appsW;
}

