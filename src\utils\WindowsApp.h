﻿//
// Created by HLJY on 2025/6/17.
//

#ifndef WINDOWSAPP_H
#define WINDOWSAPP_H

#include <vector>
#include <string>
#include <unordered_set>
#include "nlohmann/json.hpp"

// 应用信息结构
struct WindowsAppInfo {
    std::wstring name;      // 应用名称
    std::wstring shortcut;  // 快捷方式路径
    std::wstring path;      // 目标路径
    std::string iconBase64; // 图标Base64

    std::string to_json() const {
        nlohmann::json j = {
            {"name", name},
            {"shortcut", shortcut},
            {"path", path},
            {"iconBase64", iconBase64}
        };
        return j.dump();
    }
};

// 获取windows应用
std::vector<WindowsAppInfo> GetWindowsApps();
// 获取windows应用
std::vector<WindowsAppInfo> GetWindowsApps(
    const std::unordered_set<std::string>& excludedFolders,
    const std::unordered_set<std::string>& excludedFiles,
    const std::unordered_set<std::string>& filterKeywords);

#endif //WINDOWSAPP_H
