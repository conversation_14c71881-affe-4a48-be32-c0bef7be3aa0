#include "AbstractCommand.h"
#include "../core/WhiteBoardScene.h"
#include "CommandManager.h"
#include <QDebug>
#include <QDateTime>

// 静态成员初始化
int AbstractCommand::s_commandCounter = 0;

AbstractCommand::AbstractCommand(const QString& text, CommandCategory category)
    : QUndoCommand(text)
    , m_category(category)
    , m_undoable(true)
    , m_firstExecution(true)
    , m_executed(false)
    , m_commandId(generateCommandId())
{
}

AbstractCommand::~AbstractCommand()
{
}

void AbstractCommand::redo()
{
    if (m_firstExecution) {
        execute();
        m_firstExecution = false;
    } else {
        execute();
    }

    m_executed = true;
}

CommandCategory AbstractCommand::category() const
{
    return m_category;
}

bool AbstractCommand::isUndoable() const
{
    return m_undoable;
}

void AbstractCommand::setUndoable(bool undoable)
{
    m_undoable = undoable;
}

void AbstractCommand::setExecuted(bool executed)
{
    m_executed = executed;
}

QString AbstractCommand::commandId() const
{
    return m_commandId;
}

// 受保护的辅助方法实现
WhiteBoardScene* AbstractCommand::scene() const
{
    return CommandManager::instance()->getScene();
}


void AbstractCommand::addItemToActiveLayer(QGraphicsItem* item) const
{
    if (!item) {
        qWarning() << "[COMMAND] Attempted to add null item to active layer";
        return;
    }

    WhiteBoardScene* scenePtr = scene();
    if (scenePtr) {
        scenePtr->addGraphicsItem(item);
        item->setZValue(1000);
    } else {
        qWarning() << "[COMMAND] Scene not available";
    }
}

void AbstractCommand::addItemToHistoryLayer(QGraphicsItem* item) const
{
    if (!item) {
        qWarning() << "[COMMAND] Attempted to add null item to history layer";
        return;
    }

    WhiteBoardScene* scenePtr = scene();
    if (scenePtr) {
        scenePtr->addGraphicsItem(item);
        item->setZValue(0);
    } else {
        qWarning() << "[COMMAND] Scene not available";
    }
}

void AbstractCommand::removeItemFromScene(QGraphicsItem* item) const
{
    if (!item) {
        qWarning() << "[COMMAND] Attempted to remove null item from scene";
        return;
    }

    WhiteBoardScene* scenePtr = scene();
    if (scenePtr) {
        scenePtr->removeGraphicsItem(item);
    } else {
        qWarning() << "[COMMAND] Scene not available";
    }
}

void AbstractCommand::addItemsToActiveLayer(const QList<QGraphicsItem*>& items) const
{
    WhiteBoardScene* scenePtr = scene();
    if (!scenePtr) {
        qWarning() << "[COMMAND] Scene not available for batch operation";
        return;
    }

    for (QGraphicsItem* item : items) {
        if (item) {
            scenePtr->addGraphicsItem(item);
            item->setZValue(1000);
        }
    }
}

void AbstractCommand::addItemsToHistoryLayer(const QList<QGraphicsItem*>& items) const
{
    WhiteBoardScene* scenePtr = scene();
    if (!scenePtr) {
        qWarning() << "[COMMAND] Scene not available for batch operation";
        return;
    }

    for (QGraphicsItem* item : items) {
        if (item) {
            scenePtr->addGraphicsItem(item);
            item->setZValue(0);
        }
    }
}

void AbstractCommand::removeItemsFromScene(const QList<QGraphicsItem*>& items) const
{
    WhiteBoardScene* scenePtr = scene();
    if (!scenePtr) {
        qWarning() << "[COMMAND] Scene not available for batch operation";
        return;
    }

    for (QGraphicsItem* item : items) {
        if (item) {
            scenePtr->removeGraphicsItem(item);
        }
    }
}



QString AbstractCommand::generateCommandId()
{
    return QString("CMD_%1_%2")
        .arg(QDateTime::currentMSecsSinceEpoch())
        .arg(++s_commandCounter);
}
