#ifndef ABSTRACTCOMMAND_H
#define ABSTRACTCOMMAND_H

#include <QUndoCommand>
#include <QSharedPointer>
#include <QList>
#include <QString>
#include <QGraphicsItem>

class WhiteBoardScene;

/**
 * @brief 命令类别枚举
 */
enum class CommandCategory {
    Draw,       ///< 绘制操作
    Edit,       ///< 编辑操作
    Transform,  ///< 变换操作
    Delete,     ///< 删除操作
    All         ///< 所有操作
};

/**
 * @brief 命令基类
 *
 * 所有命令的基类，定义命令的通用接口
 * 适配 whiteboard 文件夹的架构设计
 */
class AbstractCommand : public QUndoCommand
{
public:
    /**
     * @brief 构造函数
     * @param text 命令描述文本
     * @param category 命令类别
     */
    AbstractCommand(const QString& text, CommandCategory category);

    /**
     * @brief 析构函数
     */
    virtual ~AbstractCommand();

    /**
     * @brief 执行命令
     * 子类必须实现此方法
     */
    virtual void execute() = 0;

    /**
     * @brief 重做命令
     * QUndoCommand接口实现
     */
    void redo() override;

    /**
     * @brief 撤销命令
     * 子类必须实现此方法
     */
    virtual void undo() override = 0;

    /**
     * @brief 获取命令类别
     * @return CommandCategory 命令类别
     */
    CommandCategory category() const;

    /**
     * @brief 是否可撤销
     * @return bool 是否可撤销
     */
    bool isUndoable() const;

    /**
     * @brief 设置是否可撤销
     * @param undoable 是否可撤销
     */
    void setUndoable(bool undoable);

    /**
     * @brief 设置命令已执行标志
     * @param executed 是否已执行
     */
    void setExecuted(bool executed);

    /**
     * @brief 获取命令ID（用于调试和日志）
     * @return QString 命令ID
     */
    QString commandId() const;

protected:
    /**
     * @brief 获取场景实例
     * @return WhiteBoardScene* 场景实例
     */
    WhiteBoardScene* scene() const;

    /**
     * @brief 通用方法：添加图形项到活动层
     * @param item 图形项
     */
    void addItemToActiveLayer(QGraphicsItem* item) const;

    /**
     * @brief 通用方法：添加图形项到历史层
     * @param item 图形项
     */
    void addItemToHistoryLayer(QGraphicsItem* item) const;

    /**
     * @brief 通用方法：从场景移除图形项
     * @param item 图形项
     */
    void removeItemFromScene(QGraphicsItem* item) const;

    /**
     * @brief 通用方法：批量添加图形项到活动层
     * @param items 图形项列表
     */
    void addItemsToActiveLayer(const QList<QGraphicsItem*>& items) const;

    /**
     * @brief 通用方法：批量添加图形项到历史层
     * @param items 图形项列表
     */
    void addItemsToHistoryLayer(const QList<QGraphicsItem*>& items) const;

    /**
     * @brief 通用方法：批量从场景移除图形项
     * @param items 图形项列表
     */
    void removeItemsFromScene(const QList<QGraphicsItem*>& items) const;



    /**
     * @brief 生成唯一的命令ID
     * @return QString 唯一ID
     */
    static QString generateCommandId();

protected:
    CommandCategory m_category;   ///< 命令类别
    bool m_undoable;              ///< 是否可撤销
    bool m_firstExecution;        ///< 是否首次执行
    bool m_executed;              ///< 是否已执行
    QString m_commandId;          ///< 命令ID

private:
    static int s_commandCounter;  ///< 命令计数器（用于生成唯一ID）
};

#endif // ABSTRACTCOMMAND_H
