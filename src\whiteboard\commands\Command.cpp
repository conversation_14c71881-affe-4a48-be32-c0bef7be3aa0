#include "Command.h"
#include "../graphics/DrawItem.h"
#include "../core/WhiteBoardScene.h"
#include "CommandManager.h"
#include <QDebug>
#include <QJsonArray>
#include <QJsonDocument>

// Command 实现

Command::Command(OperationType operationType,
                 const QList<GraphicsItemState>& beforeStates,
                 const QList<GraphicsItemState>& afterStates,
                 const QString& description)
    : AbstractCommand(description.isEmpty() ? generateDescription() : description, CommandCategory::Draw)
    , m_operationType(operationType)
    , m_beforeStates(beforeStates)
    , m_afterStates(afterStates)
{
    // 根据操作类型设置命令类别
    switch (operationType) {
    case Add:
        m_category = CommandCategory::Draw;
        break;
    case Delete:
        m_category = CommandCategory::Delete;
        break;
    case Transform:
        m_category = CommandCategory::Transform;
        break;
    case Modify:
        m_category = CommandCategory::Edit;
        break;
    case Clear:
    case Batch:
        m_category = CommandCategory::Edit;
        break;
    }
}

Command::~Command()
{
    // 析构函数
}

void Command::execute()
{
    applyStates(m_afterStates);
}

void Command::undo()
{
    // 对于橡皮擦操作，需要先清除切割后的图形，再恢复原始图形
    if (text().contains("橡皮擦")) {
        WhiteBoardScene* scenePtr = scene();
        if (scenePtr) {
            QList<QGraphicsItem*> currentItems = scenePtr->items();
            QHash<QString, QGraphicsItem*> itemMap;

            for (QGraphicsItem* item : currentItems) {
                DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
                if (drawItem) {
                    itemMap[drawItem->itemId()] = item;
                }
            }

            for (const GraphicsItemState& state : m_afterStates) {
                if (state.exists) {
                    QGraphicsItem* existingItem = itemMap.value(state.itemId);
                    if (existingItem) {
                        scenePtr->removeGraphicsItem(existingItem);
                        delete existingItem;
                        qDebug() << "[COMMAND] Removed after-state item for eraser undo:" << state.itemId;
                    }
                }
            }
        }
    }

    applyStates(m_beforeStates);
}

void Command::applyStates(const QList<GraphicsItemState>& states)
{
    WhiteBoardScene* scenePtr = scene();
    if (!scenePtr) {
        qWarning() << "[COMMAND] Scene not available for applying states";
        return;
    }

    GraphicsStateManager::applySceneState(states, scenePtr);
}

QString Command::generateDescription() const
{
    switch (m_operationType) {
    case Add:
        if (m_afterStates.size() == 1) {
            return "添加图形";
        } else {
            return QString("添加 %1 个图形").arg(m_afterStates.size());
        }
    case Delete:
        if (m_beforeStates.size() == 1) {
            return "删除图形";
        } else {
            return QString("删除 %1 个图形").arg(m_beforeStates.size());
        }
    case Transform:
        if (m_afterStates.size() == 1) {
            return "变换图形";
        } else {
            return QString("变换 %1 个图形").arg(m_afterStates.size());
        }
    case Modify:
        if (m_afterStates.size() == 1) {
            return "修改图形";
        } else {
            return QString("修改 %1 个图形").arg(m_afterStates.size());
        }
    case Clear:
        return "清空画布";
    case Batch:
        return QString("批量操作 (%1 个图形)").arg(m_afterStates.size());
    default:
        return "未知操作";
    }
}

QString Command::debugInfo() const
{
    return QString("Command(type=%1, before=%2, after=%3, desc='%4')")
           .arg(static_cast<int>(m_operationType))
           .arg(m_beforeStates.size())
           .arg(m_afterStates.size())
           .arg(text());
}

QJsonObject Command::toJson() const
{
    QJsonObject json;
    json["operationType"] = static_cast<int>(m_operationType);
    json["description"] = text();
    json["commandId"] = commandId();
    json["beforeStates"] = GraphicsStateManager::serializeStates(m_beforeStates);
    json["afterStates"] = GraphicsStateManager::serializeStates(m_afterStates);
    return json;
}

void Command::fromJson(const QJsonObject& json)
{
    m_operationType = static_cast<OperationType>(json["operationType"].toInt());
    setText(json["description"].toString());
    m_beforeStates = GraphicsStateManager::deserializeStates(json["beforeStates"].toArray());
    m_afterStates = GraphicsStateManager::deserializeStates(json["afterStates"].toArray());
}

// 静态工厂方法

Command* Command::createAddCommand(QGraphicsItem* item, const QString& description)
{
    if (!item) {
        return nullptr;
    }

    // 获取图形的ID
    DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
    QString itemId = drawItem ? drawItem->itemId() : QString::number(reinterpret_cast<quintptr>(item), 16);

    QList<GraphicsItemState> beforeStates;
    // 添加一个"不存在"的状态，表示添加前图形不存在
    beforeStates.append(GraphicsItemState(itemId, false));

    QList<GraphicsItemState> afterStates;
    afterStates.append(GraphicsItemState::fromGraphicsItem(item));

    return new Command(Add, beforeStates, afterStates, description);
}

Command* Command::createDeleteCommand(QGraphicsItem* item, const QString& description)
{
    if (!item) {
        return nullptr;
    }

    // 获取图形的ID
    DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
    QString itemId = drawItem ? drawItem->itemId() : QString::number(reinterpret_cast<quintptr>(item), 16);

    QList<GraphicsItemState> beforeStates;
    beforeStates.append(GraphicsItemState::fromGraphicsItem(item));

    QList<GraphicsItemState> afterStates;
    // 添加一个"不存在"的状态，表示删除后图形不存在
    afterStates.append(GraphicsItemState(itemId, false));

    return new Command(Delete, beforeStates, afterStates, description);
}

Command* Command::createBatchAddCommand(const QList<QGraphicsItem*>& items, const QString& description)
{
    if (items.isEmpty()) {
        return nullptr;
    }

    QList<GraphicsItemState> beforeStates;
    QList<GraphicsItemState> afterStates;

    for (QGraphicsItem* item : items) {
        if (item) {
            // 获取图形的ID
            DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
            QString itemId = drawItem ? drawItem->itemId() : QString::number(reinterpret_cast<quintptr>(item), 16);

            // 添加前状态：图形不存在
            beforeStates.append(GraphicsItemState(itemId, false));

            // 添加后状态：图形存在
            afterStates.append(GraphicsItemState::fromGraphicsItem(item));
        }
    }

    return new Command(Add, beforeStates, afterStates, description);
}

Command* Command::createBatchDeleteCommand(const QList<QGraphicsItem*>& items, const QString& description)
{
    if (items.isEmpty()) {
        return nullptr;
    }

    QList<GraphicsItemState> beforeStates;
    QList<GraphicsItemState> afterStates;

    for (QGraphicsItem* item : items) {
        if (item) {
            // 获取图形的ID
            DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
            QString itemId = drawItem ? drawItem->itemId() : QString::number(reinterpret_cast<quintptr>(item), 16);

            // 删除前状态：图形存在
            beforeStates.append(GraphicsItemState::fromGraphicsItem(item));

            // 删除后状态：图形不存在
            afterStates.append(GraphicsItemState(itemId, false));
        }
    }

    return new Command(Delete, beforeStates, afterStates, description);
}

Command* Command::createTransformCommand(const QList<QGraphicsItem*>& items,
                                        const QList<GraphicsItemState>& beforeStates,
                                        const QList<GraphicsItemState>& afterStates,
                                        const QString& description)
{
    if (items.isEmpty() || beforeStates.isEmpty() || afterStates.isEmpty()) {
        return nullptr;
    }

    return new Command(Transform, beforeStates, afterStates, description);
}

Command* Command::createClearCommand(const QList<QGraphicsItem*>& allItems, const QString& description)
{
    if (allItems.isEmpty()) {
        return nullptr;
    }

    QList<GraphicsItemState> beforeStates;
    QList<GraphicsItemState> afterStates;

    for (QGraphicsItem* item : allItems) {
        if (item) {
            // 获取图形的ID
            DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
            QString itemId = drawItem ? drawItem->itemId() : QString::number(reinterpret_cast<quintptr>(item), 16);

            // 清空前状态：图形存在
            beforeStates.append(GraphicsItemState::fromGraphicsItem(item));

            // 清空后状态：图形不存在
            afterStates.append(GraphicsItemState(itemId, false));
        }
    }

    return new Command(Clear, beforeStates, afterStates, description);
}

// CommandFactory 实现

Command* CommandFactory::addItem(QGraphicsItem* item)
{
    return Command::createAddCommand(item);
}

Command* CommandFactory::deleteItem(QGraphicsItem* item)
{
    return Command::createDeleteCommand(item);
}

Command* CommandFactory::addItems(const QList<QGraphicsItem*>& items)
{
    return Command::createBatchAddCommand(items);
}

Command* CommandFactory::deleteItems(const QList<QGraphicsItem*>& items)
{
    return Command::createBatchDeleteCommand(items);
}

Command* CommandFactory::clearScene(const QList<QGraphicsItem*>& allItems)
{
    return Command::createClearCommand(allItems);
}

Command* CommandFactory::fromStateDifference(const QList<GraphicsItemState>& beforeStates,
                                            const QList<GraphicsItemState>& afterStates,
                                            const QString& description)
{
    // 分析状态差异来确定操作类型
    Command::OperationType opType = Command::Modify;
    
    if (beforeStates.isEmpty() && !afterStates.isEmpty()) {
        opType = Command::Add;
    } else if (!beforeStates.isEmpty() && afterStates.isEmpty()) {
        opType = Command::Delete;
    } else if (beforeStates.size() != afterStates.size()) {
        opType = Command::Batch;
    }

    return new Command(opType, beforeStates, afterStates, description);
}
