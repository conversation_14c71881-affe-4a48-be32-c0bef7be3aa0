#ifndef COMMAND_H
#define COMMAND_H

#include "AbstractCommand.h"
#include "GraphicsItemState.h"
#include <QList>
#include <QString>

/**
 * @brief 统一命令类
 * 
 * 替代所有现有的命令类型，使用前后状态对比的方式实现撤销重做
 * 支持所有操作类型：添加、删除、修改、变换、清空等
 */
class Command : public AbstractCommand
{
public:
    /**
     * @brief 操作类型枚举
     */
    enum OperationType {
        Add,        // 添加图形
        Delete,     // 删除图形
        Modify,     // 修改图形
        Transform,  // 变换图形
        Clear,      // 清空场景
        Batch       // 批量操作
    };

    /**
     * @brief 构造函数 - 基于状态差异
     * @param operationType 操作类型
     * @param beforeStates 操作前的图形状态列表
     * @param afterStates 操作后的图形状态列表
     * @param description 操作描述
     */
    Command(OperationType operationType,
            const QList<GraphicsItemState>& beforeStates,
            const QList<GraphicsItemState>& afterStates,
            const QString& description = QString());

    /**
     * @brief 构造函数 - 单个图形添加
     * @param item 要添加的图形项
     * @param description 操作描述
     */
    static Command* createAddCommand(QGraphicsItem* item, const QString& description = "添加图形");

    /**
     * @brief 构造函数 - 单个图形删除
     * @param item 要删除的图形项
     * @param description 操作描述
     */
    static Command* createDeleteCommand(QGraphicsItem* item, const QString& description = "删除图形");

    /**
     * @brief 构造函数 - 批量图形添加
     * @param items 要添加的图形项列表
     * @param description 操作描述
     */
    static Command* createBatchAddCommand(const QList<QGraphicsItem*>& items, const QString& description = "批量添加图形");

    /**
     * @brief 构造函数 - 批量图形删除
     * @param items 要删除的图形项列表
     * @param description 操作描述
     */
    static Command* createBatchDeleteCommand(const QList<QGraphicsItem*>& items, const QString& description = "批量删除图形");

    /**
     * @brief 构造函数 - 图形变换
     * @param items 要变换的图形项列表
     * @param beforeStates 变换前状态
     * @param afterStates 变换后状态
     * @param description 操作描述
     */
    static Command* createTransformCommand(const QList<QGraphicsItem*>& items,
                                          const QList<GraphicsItemState>& beforeStates,
                                          const QList<GraphicsItemState>& afterStates,
                                          const QString& description = "图形变换");

    /**
     * @brief 构造函数 - 清空场景
     * @param allItems 场景中的所有图形项
     * @param description 操作描述
     */
    static Command* createClearCommand(const QList<QGraphicsItem*>& allItems, const QString& description = "清空画布");

    /**
     * @brief 析构函数
     */
    ~Command();

    // AbstractCommand接口实现
    void execute() override;
    void undo() override;

    // 属性访问
    OperationType operationType() const { return m_operationType; }
    const QList<GraphicsItemState>& beforeStates() const { return m_beforeStates; }
    const QList<GraphicsItemState>& afterStates() const { return m_afterStates; }

    // 调试信息
    QString debugInfo() const;

    // 序列化支持（用于导入导出）
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);

protected:
    /**
     * @brief 应用状态到场景
     * @param states 要应用的状态列表
     */
    void applyStates(const QList<GraphicsItemState>& states);

    /**
     * @brief 生成操作描述文本
     * @return 描述文本
     */
    QString generateDescription() const;

private:
    OperationType m_operationType;              // 操作类型
    QList<GraphicsItemState> m_beforeStates;   // 操作前状态
    QList<GraphicsItemState> m_afterStates;    // 操作后状态
};

/**
 * @brief 命令工厂类
 * 
 * 提供便捷的命令创建方法
 */
class CommandFactory
{
public:
    // 快速创建常用命令
    static Command* addItem(QGraphicsItem* item);
    static Command* deleteItem(QGraphicsItem* item);
    static Command* addItems(const QList<QGraphicsItem*>& items);
    static Command* deleteItems(const QList<QGraphicsItem*>& items);
    static Command* clearScene(const QList<QGraphicsItem*>& allItems);
    
    // 基于状态差异创建命令
    static Command* fromStateDifference(const QList<GraphicsItemState>& beforeStates,
                                       const QList<GraphicsItemState>& afterStates,
                                       const QString& description = QString());
};

#endif // COMMAND_H
