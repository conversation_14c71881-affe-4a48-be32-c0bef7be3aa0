#include "CommandManager.h"
#include "AbstractCommand.h"
#include "Command.h"
#include "GraphicsItemState.h"
#include "../core/WhiteBoardScene.h"
#include <QDebug>
#include <QJsonArray>
#include <QJsonObject>
#include <QDateTime>

// 静态成员初始化
CommandManager* CommandManager::s_instance = nullptr;

CommandManager* CommandManager::instance()
{
    if (!s_instance) {
        s_instance = new CommandManager();
    }
    return s_instance;
}

void CommandManager::destroy()
{
    if (s_instance) {
        s_instance->m_initialized = false;
        s_instance->disconnect();

        if (s_instance->m_undoStack) {
            s_instance->m_undoStack->clear();
        }

        s_instance->m_scene = nullptr;

        CommandManager* temp = s_instance;
        s_instance = nullptr;
        delete temp;
    }
}

CommandManager::CommandManager()
    : QObject(nullptr)
    , m_undoStack(new QUndoStack(this))
    , m_commandFilter(CommandCategory::All)
    , m_scene(nullptr)
    , m_settings()
    , m_initialized(false)
    , m_totalCommandCount(0)
{
    m_undoStack->setUndoLimit(m_settings.maxCommands);
    setupUndoStackConnections();
}

CommandManager::~CommandManager()
{
    if (m_undoStack) {
        m_undoStack->clear();
    }
    m_scene = nullptr;
}

void CommandManager::initialize(WhiteBoardScene* scene)
{
    if (m_initialized) {
        qWarning() << "[COMMAND_MANAGER] Already initialized";
        return;
    }

    if (!scene) {
        qWarning() << "[COMMAND_MANAGER] Invalid scene parameter for initialization";
        return;
    }

    m_scene = scene;
    m_initialized = true;
}

bool CommandManager::isInitialized() const
{
    return m_initialized && !m_scene.isNull();
}

void CommandManager::executeCommand(AbstractCommand* command, bool execute)
{
    if (!command) {
        qWarning() << "[COMMAND_MANAGER] Attempted to execute null command";
        return;
    }

    if (!isInitialized()) {
        qWarning() << "[COMMAND_MANAGER] CommandManager not initialized, deleting command";
        delete command;
        return;
    }

    if (!m_undoStack) {
        qWarning() << "[COMMAND_MANAGER] UndoStack not available";
        delete command;
        return;
    }
    
    if (m_commandFilter != CommandCategory::All && command->category() != m_commandFilter) {
        delete command;
        return;
    }

    prepareForUndoRedo();

    if (execute) {
        command->setExecuted(false);
    } else {
        command->setExecuted(true);
    }

    m_undoStack->push(command);
    m_totalCommandCount++;

    emit commandExecuted(command);
    emit needsRepaint();
    emit operationChanged("执行", command->text());
}

void CommandManager::executeCommands(const QList<AbstractCommand*>& commands, bool execute)
{
    if (commands.isEmpty()) {
        return;
    }
    
    if (!isInitialized()) {
        qWarning() << "[COMMAND_MANAGER] CommandManager not initialized";
        for (AbstractCommand* cmd : commands) {
            delete cmd;
        }
        return;
    }

    prepareForUndoRedo();

    QString macroText = QString("批量操作 (%1 个命令)").arg(commands.size());
    m_undoStack->beginMacro(macroText);

    for (AbstractCommand* command : commands) {
        if (command) {
            if (m_commandFilter != CommandCategory::All && command->category() != m_commandFilter) {
                delete command;
                continue;
            }

            if (execute) {
                command->setExecuted(false);
            } else {
                command->setExecuted(true);
            }

            m_undoStack->push(command);
            m_totalCommandCount++;
        }
    }

    m_undoStack->endMacro();
    emit needsRepaint();
}

bool CommandManager::canUndo() const
{
    return m_undoStack && m_undoStack->canUndo();
}

bool CommandManager::canRedo() const
{
    return m_undoStack && m_undoStack->canRedo();
}

bool CommandManager::undo()
{
    if (!canUndo()) {
        return false;
    }

    prepareForUndoRedo();
    m_undoStack->undo();
    emit needsRepaint();
    emit operationChanged("撤销", "撤销上一步操作");
    return true;
}

bool CommandManager::redo()
{
    if (!canRedo()) {
        return false;
    }

    prepareForUndoRedo();
    m_undoStack->redo();
    emit needsRepaint();
    emit operationChanged("重做", "重做上一步操作");
    return true;
}

void CommandManager::clear()
{
    if (m_undoStack) {
        m_undoStack->clear();
        m_totalCommandCount = 0;
        emit stackCleared();
        emit operationChanged("清空", "清空所有操作历史");
    }
}

void CommandManager::setCommandFilter(CommandCategory category)
{
    m_commandFilter = category;
}

CommandCategory CommandManager::commandFilter() const
{
    return m_commandFilter;
}

WhiteBoardScene* CommandManager::getScene() const
{
    return m_scene.data();
}


void CommandManager::setMaxCommands(int maxCommands)
{
    m_settings.maxCommands = maxCommands;
    checkCommandLimits();
}

int CommandManager::getMaxCommands() const
{
    return m_settings.maxCommands;
}

int CommandManager::getUndoCount() const
{
    return m_undoStack ? m_undoStack->count() - m_undoStack->index() : 0;
}

int CommandManager::getRedoCount() const
{
    return m_undoStack ? m_undoStack->index() : 0;
}

int CommandManager::getTotalCommandCount() const
{
    return m_totalCommandCount;
}


void CommandManager::onUndoStackIndexChanged(int idx)
{
    Q_UNUSED(idx)
    emit canUndoChanged(canUndo());
    emit canRedoChanged(canRedo());
}

void CommandManager::prepareForUndoRedo()
{
    clearSelection();
}

void CommandManager::clearSelection()
{
    if (m_scene) {
        m_scene->clearSelection();
    }
}

void CommandManager::checkCommandLimits()
{
    if (!m_undoStack) {
        return;
    }

    m_undoStack->setUndoLimit(m_settings.maxCommands);
}

void CommandManager::setupUndoStackConnections()
{
    if (!m_undoStack) {
        return;
    }

    connect(m_undoStack, &QUndoStack::indexChanged,
            this, &CommandManager::onUndoStackIndexChanged);
    connect(m_undoStack, &QUndoStack::canUndoChanged,
            this, &CommandManager::canUndoChanged);
    connect(m_undoStack, &QUndoStack::canRedoChanged,
            this, &CommandManager::canRedoChanged);
}

// 统一命令系统接口实现

void CommandManager::addItem(QGraphicsItem* item)
{
    if (!item) {
        qWarning() << "[COMMAND_MANAGER] Cannot add null item";
        return;
    }

    Command* command = CommandFactory::addItem(item);
    if (command) {
        executeCommand(command, true);
    }
}

void CommandManager::deleteItem(QGraphicsItem* item)
{
    if (!item) {
        qWarning() << "[COMMAND_MANAGER] Cannot delete null item";
        return;
    }

    Command* command = CommandFactory::deleteItem(item);
    if (command) {
        executeCommand(command, true);
    }
}

void CommandManager::addItems(const QList<QGraphicsItem*>& items)
{
    if (items.isEmpty()) {
        qWarning() << "[COMMAND_MANAGER] Cannot add empty item list";
        return;
    }

    Command* command = CommandFactory::addItems(items);
    if (command) {
        executeCommand(command, true);
    }
}

void CommandManager::deleteItems(const QList<QGraphicsItem*>& items)
{
    if (items.isEmpty()) {
        qWarning() << "[COMMAND_MANAGER] Cannot delete empty item list";
        return;
    }

    Command* command = CommandFactory::deleteItems(items);
    if (command) {
        executeCommand(command, true);
    }
}

void CommandManager::clearScene()
{
    if (!isInitialized()) {
        qWarning() << "[COMMAND_MANAGER] CommandManager not initialized";
        return;
    }

    QList<QGraphicsItem*> allItems = m_scene->items();
    if (allItems.isEmpty()) {
        qWarning() << "[COMMAND_MANAGER] Scene is already empty";
        return;
    }

    Command* command = CommandFactory::clearScene(allItems);
    if (command) {
        executeCommand(command, true);
    }
}

void CommandManager::transformItems(const QList<QGraphicsItem*>& items,
                                   const QList<GraphicsItemState>& beforeStates,
                                   const QList<GraphicsItemState>& afterStates,
                                   const QString& description)
{
    if (items.isEmpty() || beforeStates.isEmpty() || afterStates.isEmpty()) {
        qWarning() << "[COMMAND_MANAGER] Invalid parameters for transform command";
        return;
    }

    Command* command = Command::createTransformCommand(items, beforeStates, afterStates, description);
    if (command) {
        executeCommand(command, true);
    }
}

// 操作栈序列化实现

QJsonObject CommandManager::exportCommandStack(bool includeExecutedOnly) const
{
    QJsonObject stackJson;

    if (!isInitialized()) {
        qWarning() << "[COMMAND_MANAGER] CommandManager not initialized";
        return stackJson;
    }

    // 基本信息
    stackJson["version"] = 1;
    stackJson["type"] = "CommandStack";
    stackJson["timestamp"] = QDateTime::currentMSecsSinceEpoch();
    stackJson["maxCommands"] = m_undoStack->undoLimit();
    stackJson["currentIndex"] = m_undoStack->index();
    stackJson["canUndo"] = m_undoStack->canUndo();
    stackJson["canRedo"] = m_undoStack->canRedo();

    // 导出命令列表
    QJsonArray commandsArray;
    int commandCount = m_undoStack->count();
    int currentIndex = m_undoStack->index();

    for (int i = 0; i < commandCount; ++i) {
        const QUndoCommand* undoCmd = m_undoStack->command(i);
        if (undoCmd) {
            // 尝试转换为我们的Command类
            const AbstractCommand* abstractCmd = dynamic_cast<const AbstractCommand*>(undoCmd);
            if (abstractCmd) {
                const Command* cmd = dynamic_cast<const Command*>(abstractCmd);
                if (cmd) {
                    // 检查是否只导出已执行的命令
                    bool isExecuted = (i < currentIndex);
                    if (!includeExecutedOnly || isExecuted) {
                        QJsonObject cmdJson = cmd->toJson();
                        cmdJson["index"] = i;
                        cmdJson["isExecuted"] = isExecuted;
                        commandsArray.append(cmdJson);
                    }
                }
            }
        }
    }

    stackJson["commands"] = commandsArray;
    stackJson["commandCount"] = commandsArray.size();

    return stackJson;
}

bool CommandManager::importCommandStack(const QJsonObject& commandStackJson, bool mergeMode)
{
    if (!isInitialized()) {
        qWarning() << "[COMMAND_MANAGER] CommandManager not initialized";
        return false;
    }

    // 验证JSON格式
    if (commandStackJson["type"].toString() != "CommandStack") {
        qWarning() << "[COMMAND_MANAGER] Invalid command stack JSON format";
        return false;
    }

    // 如果不是合并模式，清空当前操作栈
    if (!mergeMode) {
        m_undoStack->clear();
    }

    // 导入命令
    QJsonArray commandsArray = commandStackJson["commands"].toArray();
    int importedCount = 0;

    for (const QJsonValue& cmdValue : commandsArray) {
        if (cmdValue.isObject()) {
            QJsonObject cmdJson = cmdValue.toObject();

            // 创建新的Command对象
            Command* command = new Command(Command::Modify, {}, {}, "导入的命令");
            command->fromJson(cmdJson);

            // 检查命令是否已执行
            bool wasExecuted = cmdJson["isExecuted"].toBool();

            // 直接添加到操作栈，不执行命令（因为场景状态已经正确）
            m_undoStack->push(command);

            // 如果命令在导出时未执行，我们需要撤销它以保持正确的状态
            if (!wasExecuted && m_undoStack->canUndo()) {
                m_undoStack->undo();
            }

            importedCount++;
        }
    }

    qDebug() << "[COMMAND_MANAGER] Imported" << importedCount << "commands in"
             << (mergeMode ? "merge" : "replace") << "mode";

    return importedCount > 0;
}

QJsonObject CommandManager::getCommandStackInfo() const
{
    QJsonObject info;

    if (!isInitialized()) {
        info["initialized"] = false;
        return info;
    }

    info["initialized"] = true;
    info["commandCount"] = m_undoStack->count();
    info["currentIndex"] = m_undoStack->index();
    info["maxCommands"] = m_undoStack->undoLimit();
    info["canUndo"] = m_undoStack->canUndo();
    info["canRedo"] = m_undoStack->canRedo();
    info["undoText"] = m_undoStack->undoText();
    info["redoText"] = m_undoStack->redoText();

    return info;
}
