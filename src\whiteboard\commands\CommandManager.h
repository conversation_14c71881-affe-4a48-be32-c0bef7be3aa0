#ifndef COMMANDMANAGER_H
#define COMMANDMANAGER_H

#include <QObject>
#include <QUndoStack>
#include <QSharedPointer>
#include <QPointer>
#include <QTimer>
#include "AbstractCommand.h"

class WhiteBoardScene;
class CommandMemoryManager;

/**
 * @brief 命令系统管理器
 *
 * 中央控制器，管理命令的创建、执行和撤销/重做
 * 适配 whiteboard 文件夹的架构设计
 */
class CommandManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 简化的设置
     */
    struct Settings {
        int maxCommands = 10;          ///< 最大命令数量
    };

public:
    /**
     * @brief 获取单例实例
     * @return CommandManager* 单例实例
     */
    static CommandManager* instance();

    /**
     * @brief 销毁单例实例
     */
    static void destroy();

    /**
     * @brief 初始化命令管理器
     * @param scene 白板场景
     */
    void initialize(WhiteBoardScene* scene);

    /**
     * @brief 是否已初始化
     * @return bool 是否已初始化
     */
    bool isInitialized() const;

    /**
     * @brief 执行命令
     * @param command 要执行的命令
     * @param execute 是否需要执行（false表示命令已执行，只需加入栈）
     */
    void executeCommand(AbstractCommand* command, bool execute = true);

    /**
     * @brief 批量执行命令
     * @param commands 要执行的命令列表
     * @param execute 是否需要执行
     */
    void executeCommands(const QList<AbstractCommand*>& commands, bool execute = true);

    // 统一命令系统接口
    /**
     * @brief 执行添加图形命令
     * @param item 要添加的图形项
     */
    void addItem(QGraphicsItem* item);

    /**
     * @brief 执行删除图形命令
     * @param item 要删除的图形项
     */
    void deleteItem(QGraphicsItem* item);

    /**
     * @brief 执行批量添加图形命令
     * @param items 要添加的图形项列表
     */
    void addItems(const QList<QGraphicsItem*>& items);

    /**
     * @brief 执行批量删除图形命令
     * @param items 要删除的图形项列表
     */
    void deleteItems(const QList<QGraphicsItem*>& items);

    /**
     * @brief 执行清空场景命令
     */
    void clearScene();

    /**
     * @brief 执行图形变换命令
     * @param items 要变换的图形项列表
     * @param beforeStates 变换前状态
     * @param afterStates 变换后状态
     * @param description 操作描述
     */
    void transformItems(const QList<QGraphicsItem*>& items,
                       const QList<struct GraphicsItemState>& beforeStates,
                       const QList<struct GraphicsItemState>& afterStates,
                       const QString& description = "图形变换");

    // 操作栈序列化接口
    /**
     * @brief 导出操作栈到JSON
     * @param includeExecutedOnly 是否只包含已执行的命令
     * @return 操作栈的JSON表示
     */
    QJsonObject exportCommandStack(bool includeExecutedOnly = true) const;

    /**
     * @brief 从JSON导入操作栈
     * @param commandStackJson 操作栈的JSON数据
     * @param mergeMode 是否合并模式（true=合并，false=替换）
     * @return 是否导入成功
     */
    bool importCommandStack(const QJsonObject& commandStackJson, bool mergeMode = true);

    /**
     * @brief 获取当前操作栈信息
     * @return 包含操作栈统计信息的JSON对象
     */
    QJsonObject getCommandStackInfo() const;

    /**
     * @brief 是否可以撤销
     * @return bool 是否可以撤销
     */
    bool canUndo() const;

    /**
     * @brief 是否可以重做
     * @return bool 是否可以重做
     */
    bool canRedo() const;

    /**
     * @brief 撤销上一个命令
     * @return bool 是否成功撤销
     */
    bool undo();

    /**
     * @brief 重做上一个命令
     * @return bool 是否成功重做
     */
    bool redo();

    /**
     * @brief 清空命令历史
     */
    void clear();

    /**
     * @brief 设置命令过滤器
     * @param category 命令类别
     */
    void setCommandFilter(CommandCategory category);

    /**
     * @brief 获取当前命令过滤器
     * @return CommandCategory 当前命令过滤器
     */
    CommandCategory commandFilter() const;

    /**
     * @brief 获取场景实例
     * @return WhiteBoardScene* 场景实例
     */
    WhiteBoardScene* getScene() const;



    /**
     * @brief 设置最大命令数量
     * @param maxCommands 最大命令数量
     */
    void setMaxCommands(int maxCommands);

    /**
     * @brief 获取最大命令数量
     * @return int 最大命令数量
     */
    int getMaxCommands() const;

    /**
     * @brief 获取统计信息
     */
    int getUndoCount() const;
    int getRedoCount() const;
    int getTotalCommandCount() const;



signals:
    /**
     * @brief 命令执行信号
     * @param command 执行的命令
     */
    void commandExecuted(AbstractCommand* command);

    /**
     * @brief 可撤销状态改变信号
     * @param canUndo 是否可撤销
     */
    void canUndoChanged(bool canUndo);

    /**
     * @brief 可重做状态改变信号
     * @param canRedo 是否可重做
     */
    void canRedoChanged(bool canRedo);

    /**
     * @brief 撤销栈清空信号
     */
    void stackCleared();

    /**
     * @brief 需要重绘信号
     */
    void needsRepaint();

    /**
     * @brief 操作变更信号
     * @param operationType 操作类型（如"添加"、"删除"、"撤销"、"重做"等）
     * @param description 操作描述
     */
    void operationChanged(const QString& operationType, const QString& description);

private slots:
    /**
     * @brief 撤销栈索引改变处理
     * @param idx 新索引
     */
    void onUndoStackIndexChanged(int idx);

private:
    /**
     * @brief 构造函数（私有）
     */
    CommandManager();

    /**
     * @brief 析构函数（私有）
     */
    ~CommandManager();

    /**
     * @brief 准备撤销/重做操作
     * 确保所有图形都在合适的图层，并清除选中状态
     */
    void prepareForUndoRedo();

    /**
     * @brief 清除选中状态
     */
    void clearSelection();

    /**
     * @brief 检查命令数量限制
     */
    void checkCommandLimits();

    /**
     * @brief 设置撤销栈连接
     */
    void setupUndoStackConnections();

private:
    QUndoStack* m_undoStack;              ///< 撤销栈
    CommandCategory m_commandFilter;      ///< 命令过滤器
    
    // 核心组件引用
    QPointer<WhiteBoardScene> m_scene;           ///< 场景引用

    // 简化设置
    Settings m_settings;                  ///< 设置

    // 状态管理
    bool m_initialized;                   ///< 是否已初始化
    int m_totalCommandCount;              ///< 总命令计数

    static CommandManager* s_instance;    ///< 单例实例
};

#endif // COMMANDMANAGER_H
