#ifndef GRAPHICSITEMSTATE_H
#define GRAPHICSITEMSTATE_H

#include <QString>
#include <QJsonObject>
#include <QPointF>
#include <QTransform>
#include <QGraphicsItem>

class DrawItem;

/**
 * @brief 统一的图形状态结构
 * 
 * 用于保存图形的完整状态，支持序列化和反序列化
 * 支持所有类型的图形操作的前后状态保存
 */
struct GraphicsItemState
{
    // 基本标识
    QString itemId;              // 图形唯一ID
    bool exists;                 // 图形是否存在于场景中
    
    // 完整的图形数据（序列化形式）
    QJsonObject itemData;        // 图形的完整序列化数据
    
    // 场景属性
    QPointF position;            // 位置
    QTransform transform;        // 变换矩阵
    qreal zValue;                // Z值（层级）
    qreal opacity;               // 透明度
    bool visible;                // 可见性
    bool enabled;                // 启用状态
    
    // 构造函数
    GraphicsItemState();
    GraphicsItemState(const QString& id, bool itemExists = true);
    
    // 从图形项创建状态
    static GraphicsItemState fromGraphicsItem(QGraphicsItem* item);
    
    // 应用状态到图形项
    void applyToGraphicsItem(QGraphicsItem* item) const;
    
    // 序列化支持
    QJsonObject toJson() const;
    void fromJson(const QJsonObject& json);
    
    // 比较操作
    bool operator==(const GraphicsItemState& other) const;
    bool operator!=(const GraphicsItemState& other) const;
    
    // 调试输出
    QString toString() const;
};

/**
 * @brief 图形状态管理器
 * 
 * 提供图形状态的创建、应用、序列化等功能
 */
class GraphicsStateManager
{
public:
    // 从场景中的图形项创建状态列表
    static QList<GraphicsItemState> captureSceneState(const QList<QGraphicsItem*>& items);
    
    // 应用状态列表到场景
    static void applySceneState(const QList<GraphicsItemState>& states, class WhiteBoardScene* scene);
    
    // 创建空状态（用于表示删除的图形）
    static GraphicsItemState createEmptyState(const QString& itemId);
    
    // 从DrawItem创建完整状态
    static GraphicsItemState createFullState(DrawItem* drawItem);
    
    // 比较两个状态列表的差异
    static QList<QString> findDifferences(const QList<GraphicsItemState>& before, 
                                         const QList<GraphicsItemState>& after);
    
    // 序列化状态列表
    static QJsonArray serializeStates(const QList<GraphicsItemState>& states);
    
    // 反序列化状态列表
    static QList<GraphicsItemState> deserializeStates(const QJsonArray& jsonArray);
};

#endif // GRAPHICSITEMSTATE_H
