#include "DrawingConstants.h"
#include <QDebug>

namespace DrawingConstants {

    // 是否在橡皮擦擦除中
    bool isErasing = false;

    // 全局擦除路径
    QPainterPath g_currentEraserPath;

    // 未完成的切割路径数组
    QList<QPainterPath> g_clipPaths;

    // 上一个矩形
    QRectF g_lastRect;

    // 是否清除完毕
    bool isDrawClear = false;

    void addEraserRect(const QRectF& rect)
    {
        // 打印g_currentEraserPath和rect
        isDrawClear = false;
        if (g_currentEraserPath.isEmpty()) {
            // 如果当前路径为空，直接创建矩形路径
            g_currentEraserPath.addRect(rect);
        } else {
            // 获取当前矩形的四个顶点
            QPointF newTopLeft = rect.topLeft();
            QPointF newTopRight = rect.topRight();
            QPointF newBottomRight = rect.bottomRight();
            QPointF newBottomLeft = rect.bottomLeft();
            QList<QPointF> newRectPoints = {newTopLeft, newTopRight, newBottomRight, newBottomLeft};

            // 获取上次矩形的四个顶点
            QPointF lastTopLeft = g_lastRect.topLeft();
            QPointF lastTopRight = g_lastRect.topRight();
            QPointF lastBottomRight = g_lastRect.bottomRight();
            QPointF lastBottomLeft = g_lastRect.bottomLeft();
            QList<QPointF> lastRectPoints = {lastTopLeft, lastTopRight, lastBottomRight, lastBottomLeft};

            // 创建连接两个矩形的闭合路径
            QPainterPath connectedPath;

            // 计算两个矩形中心点的移动方向
            QPointF lastCenter = g_lastRect.center();
            QPointF newCenter = rect.center();

            qreal newX = newCenter.x();
            qreal newY = newCenter.y();
            qreal lastX = lastCenter.x();
            qreal lastY = lastCenter.y();

            if (newX >= lastX && newY <= lastY) {
                // 右上角
                connectedPath.moveTo(lastTopLeft);
                connectedPath.lineTo(lastBottomLeft);
                connectedPath.lineTo(lastBottomRight);
                connectedPath.lineTo(newBottomRight);
                connectedPath.lineTo(newTopRight);
                connectedPath.lineTo(newTopLeft);
            }else if (newX >= lastX && newY > lastY) {
                // 右下角
                connectedPath.moveTo(lastTopRight);
                connectedPath.lineTo(lastTopLeft);
                connectedPath.lineTo(lastBottomLeft);
                connectedPath.lineTo(newBottomLeft);
                connectedPath.lineTo(newBottomRight);
                connectedPath.lineTo(newTopRight);
            }else if (newX < lastX && newY <= lastY) {
                // 左上角
                connectedPath.moveTo(lastBottomLeft);
                connectedPath.lineTo(lastBottomRight);
                connectedPath.lineTo(lastTopRight);
                connectedPath.lineTo(newTopRight);
                connectedPath.lineTo(newTopLeft);
                connectedPath.lineTo(newBottomLeft);
            }else {
                // 左下角
                connectedPath.moveTo(lastBottomRight);
                connectedPath.lineTo(lastTopRight);
                connectedPath.lineTo(lastTopLeft);
                connectedPath.lineTo(newTopLeft);
                connectedPath.lineTo(newBottomLeft);
                connectedPath.lineTo(newBottomRight);
            }
            connectedPath.closeSubpath(); // 闭合路径
            
            // 更新当前路径
            if (!isDrawClear) {
                // 还没有绘制就合并两条路径
                g_currentEraserPath = g_currentEraserPath.united(connectedPath);
            }else {
                g_currentEraserPath = connectedPath;
            }
        }
        g_clipPaths.append(g_currentEraserPath);
        g_lastRect = rect;
    }

    QPainterPath getCurrentEraserPath()
    {
        return g_currentEraserPath;
    }

    QList<QPainterPath> getClipPaths()
    {
        return g_clipPaths;
    }

    QPainterPath takeFirstClipPath()
    {
        if (g_clipPaths.isEmpty()) {
            return QPainterPath(); // 返回空路径
        }

        QPainterPath firstPath = g_clipPaths.first();
        g_clipPaths.removeFirst(); // 删除首条数据
        return firstPath;
    }

    bool hasClipPaths()
    {
        return !g_clipPaths.isEmpty();
    }

    void setDrawClear(bool clear)
    {
        isDrawClear = clear;
    }

    void clearAllEraserData()
    {
        g_currentEraserPath = QPainterPath();
        g_clipPaths.clear();
    }

    bool getEraseStatus()
    {
        return isErasing;
    }

    void setEraseStatus(bool status)
    {
        isErasing = status;
    }

} // namespace DrawingConstants
