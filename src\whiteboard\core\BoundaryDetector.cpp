#include "BoundaryDetector.h"
#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QDebug>
#include <QtMath>
#include <algorithm>

const int BoundaryDetector::DEFAULT_BSP_DEPTH = 10;

BoundaryDetector::BoundaryDetector(QObject* parent)
    : QObject(parent)
    , m_useBSPIndex(true)
    , m_bspTreeDepth(DEFAULT_BSP_DEPTH)
{
}

BoundaryDetector::~BoundaryDetector()
{
}

void BoundaryDetector::initialize(QGraphicsScene* scene)
{
    setScene(scene);
    
    if (m_scene) {
        // 配置BSP索引
//        if (m_useBSPIndex) {
//            m_scene->setItemIndexMethod(QGraphicsScene::BspTreeIndex);
//            m_scene->setBspTreeDepth(m_bspTreeDepth);
//        }

        connect(m_scene, &QGraphicsScene::changed, this, &BoundaryDetector::onSceneChanged);
    }
}

void BoundaryDetector::setScene(QGraphicsScene* scene)
{
    if (m_scene) {
        disconnect(m_scene, nullptr, this, nullptr);
    }
    
    m_scene = scene;
    
    if (m_scene) {
        connect(m_scene, &QGraphicsScene::changed, this, &BoundaryDetector::onSceneChanged);
    }
}

// 核心检测功能实现
BoundaryDetector::DetectionResult BoundaryDetector::detectIntersections(const QRectF& eraserRect)
{
    if (!m_scene) {
        return DetectionResult{};
    }

    DetectionResult result;

    if (m_useBSPIndex) {
        result = performBSPDetection(eraserRect);
    } else {
        result = performBruteForceDetection(eraserRect);
    }

    emit detectionCompleted(result);

    return result;
}

QList<QGraphicsItem*> BoundaryDetector::getIntersectedItems(const QRectF& eraserRect)
{
    DetectionResult result = detectIntersections(eraserRect);
    return result.intersectedItems;
}

bool BoundaryDetector::hasIntersections(const QRectF& eraserRect)
{
    if (!m_scene) return false;

    QList<QGraphicsItem*> candidates = m_scene->items(eraserRect, Qt::IntersectsItemShape);
    
    for (QGraphicsItem* item : candidates) {
        if (isItemIntersecting(item, eraserRect)) {
            return true;
        }
    }
    

    
    return false;
}

QList<BoundaryDetector::DetectionResult> BoundaryDetector::detectBatchIntersections(
    const QList<QRectF>& eraserRects)
{
    QList<DetectionResult> results;
    results.reserve(eraserRects.size());
    
    for (const QRectF& rect : eraserRects) {
        results.append(detectIntersections(rect));
    }
    
    return results;
}







// 优化配置实现
void BoundaryDetector::setUseBSPIndex(bool useBSP)
{
    m_useBSPIndex = useBSP;
    
    if (m_scene) {
//        if (useBSP) {
//            m_scene->setItemIndexMethod(QGraphicsScene::BspTreeIndex);
//            m_scene->setBspTreeDepth(m_bspTreeDepth);
//        } else {
            m_scene->setItemIndexMethod(QGraphicsScene::NoIndex);
//        }
    }
}

bool BoundaryDetector::isUsingBSPIndex() const
{
    return m_useBSPIndex;
}

void BoundaryDetector::setBSPTreeDepth(int depth)
{
    if (depth > 0 && depth <= 20) {
        m_bspTreeDepth = depth;
        
        if (m_scene && m_useBSPIndex) {
            m_scene->setBspTreeDepth(depth);
        }
    }
}

int BoundaryDetector::getBSPTreeDepth() const
{
    return m_bspTreeDepth;
}

// 私有槽函数实现
void BoundaryDetector::onSceneChanged()
{
    // 场景变化时可以进行一些优化
    if (m_useBSPIndex && m_scene) {
        // BSP索引会自动重建，无需手动操作
    }
}

// 核心检测算法实现
BoundaryDetector::DetectionResult BoundaryDetector::performBSPDetection(const QRectF& eraserRect)
{
    DetectionResult result;

    if (!eraserRect.isValid() || eraserRect.isEmpty()) {
        return result;
    }

    if (qIsInf(eraserRect.x()) || qIsInf(eraserRect.y()) ||
        qIsInf(eraserRect.width()) || qIsInf(eraserRect.height()) ||
        qIsNaN(eraserRect.x()) || qIsNaN(eraserRect.y()) ||
        qIsNaN(eraserRect.width()) || qIsNaN(eraserRect.height())) {
        return result;
    }

    try {
        // 使用BSP索引进行快速空间查询
        QList<QGraphicsItem*> candidates = m_scene->items(eraserRect, Qt::IntersectsItemBoundingRect);
        result.candidateCount = candidates.size();

        for (QGraphicsItem* item : candidates) {
            if (isItemIntersecting(item, eraserRect)) {
                result.intersectedItems.append(item);
            }
        }

        result.actualIntersectionCount = result.intersectedItems.size();
    } catch (...) {
        return performBruteForceDetection(eraserRect);
    }

    return result;
}

BoundaryDetector::DetectionResult BoundaryDetector::performBruteForceDetection(const QRectF& eraserRect)
{
    DetectionResult result;

    // 暴力检测：遍历所有项目
    QList<QGraphicsItem*> allItems = m_scene->items();
    result.candidateCount = allItems.size();

    for (QGraphicsItem* item : allItems) {
        if (rectIntersectsItemBounds(eraserRect, item) && isItemIntersecting(item, eraserRect)) {
            result.intersectedItems.append(item);
        }
    }

    result.actualIntersectionCount = result.intersectedItems.size();

    return result;
}

// 精确相交检测实现
bool BoundaryDetector::isItemIntersecting(QGraphicsItem* item, const QRectF& eraserRect)
{
    if (!item) return false;

    // 首先检查边界框相交
    if (!rectIntersectsItemBounds(eraserRect, item)) {
        return false;
    }

    // 对于简单图形，边界框检测已经足够
    // 对于复杂图形，可以进一步检测形状相交
    QRectF itemBounds = item->boundingRect();
    QRectF itemSceneBounds = item->mapRectToScene(itemBounds);

    return eraserRect.intersects(itemSceneBounds);
}

bool BoundaryDetector::rectIntersectsItemBounds(const QRectF& rect, QGraphicsItem* item)
{
    if (!item) return false;

    QRectF itemBounds = item->boundingRect();
    QRectF itemSceneBounds = item->mapRectToScene(itemBounds);

    return rect.intersects(itemSceneBounds);
}





// 优化方法实现
void BoundaryDetector::optimizeSceneIndex()
{
    if (m_scene && m_useBSPIndex) {
        m_scene->update();
    }
}

QList<QGraphicsItem*> BoundaryDetector::filterCandidates(const QList<QGraphicsItem*>& candidates,
                                                        const QRectF& eraserRect)
{
    QList<QGraphicsItem*> filtered;
    filtered.reserve(candidates.size());

    for (QGraphicsItem* item : candidates) {
        if (isItemIntersecting(item, eraserRect)) {
            filtered.append(item);
        }
    }

    return filtered;
}
