#ifndef BOUNDARYDETECTOR_H
#define BOUNDARYDETECTOR_H

#include <QObject>
#include <QRectF>
#include <QPointF>
#include <QList>

#include <QGraphicsItem>
#include <QGraphicsScene>
#include <QPointer>

/**
 * @brief 边界检测器 - 专为橡皮擦矩形相交检测优化
 *
 * 核心功能：
 * 1. 矩形相交检测
 * 2. 利用QGraphicsView的BSP索引进行空间查询
 * 3. 针对长方形橡皮擦的优化算法
 *
 * 设计原则：
 * - 专注于矩形相交检测，避免复杂的几何算法
 * - 充分利用Qt的BSP索引优势
 */
class BoundaryDetector : public QObject
{
    Q_OBJECT

public:
    struct DetectionResult {
        QList<QGraphicsItem*> intersectedItems;
        int candidateCount;
        int actualIntersectionCount;
    };



public:
    explicit BoundaryDetector(QObject* parent = nullptr);
    ~BoundaryDetector();

    // 初始化
    void initialize(QGraphicsScene* scene);
    void setScene(QGraphicsScene* scene);

    // 核心检测功能
    DetectionResult detectIntersections(const QRectF& eraserRect);
    QList<QGraphicsItem*> getIntersectedItems(const QRectF& eraserRect);
    
    // 快速检测（只返回是否有相交，不返回具体项）
    bool hasIntersections(const QRectF& eraserRect);
    
    // 批量检测（用于擦除轨迹）
    QList<DetectionResult> detectBatchIntersections(const QList<QRectF>& eraserRects);

    // 优化配置
    void setUseBSPIndex(bool useBSP);
    bool isUsingBSPIndex() const;
    void setBSPTreeDepth(int depth);
    int getBSPTreeDepth() const;

signals:
    void detectionCompleted(const DetectionResult& result);

private slots:
    void onSceneChanged();

private:
    // 核心检测算法
    DetectionResult performBSPDetection(const QRectF& eraserRect);
    DetectionResult performBruteForceDetection(const QRectF& eraserRect);
    
    // 精确相交检测
    bool isItemIntersecting(QGraphicsItem* item, const QRectF& eraserRect);
    bool rectIntersectsItemBounds(const QRectF& rect, QGraphicsItem* item);
    
    // 优化方法
    void optimizeSceneIndex();
    QList<QGraphicsItem*> filterCandidates(const QList<QGraphicsItem*>& candidates, 
                                          const QRectF& eraserRect);

private:
    QPointer<QGraphicsScene> m_scene;
    bool m_useBSPIndex;
    int m_bspTreeDepth;

    static const int DEFAULT_BSP_DEPTH;
};

#endif // BOUNDARYDETECTOR_H
