#include "WhiteBoardScene.h"
#include "WhiteboardResourceManager.h"
#include "../graphics/DrawItem.h"

#include <QPainter>
#include <QGraphicsItem>
#include <QDebug>
#include <QMutexLocker>



WhiteBoardScene::WhiteBoardScene(QObject* parent)
    : QGraphicsScene(parent)
    // QPointer 自动初始化为 nullptr
    , m_inTransaction(false)
    , m_batchUpdatesEnabled(false)
    , m_boundingRectValid(false)
    , m_totalItemCount(0)
    , m_autoOptimizeEnabled(false)
    , m_maxItemsBeforeOptimization(1000)
    , m_sceneMargin(100.0)
    , m_initialized(false)
{
    this->setItemIndexMethod(QGraphicsScene::NoIndex);
}

WhiteBoardScene::~WhiteBoardScene()
{
    m_allItems.clear();
}

void WhiteBoardScene::initialize()
{
    if (m_initialized) {
        return;
    }

    if (!WhiteboardResourceManager::instance().initializeResources()) {
    }

    setupSceneOptimization();
    setupItemIndexing();
    setSceneRect(0, 0, 800, 600);
    m_initialized = true;
    
}

void WhiteBoardScene::setupSceneOptimization()
{
//    setItemIndexMethod(QGraphicsScene::BspTreeIndex);
//    setBspTreeDepth(10);
}

void WhiteBoardScene::setupItemIndexing()
{
}



// 图形管理实现 - 专注于QGraphicsScene功能
void WhiteBoardScene::addGraphicsItem(QGraphicsItem* item)
{
    if (!item) return;

    m_itemsMutex.lock();

    // 添加到场景
    addItem(item);

    m_allItems.append(item);
    m_totalItemCount++;
    m_boundingRectValid = false;

    emit itemAdded(item);

    if (!m_batchUpdatesEnabled) {
        emit sceneChanged();
        emit historyLayerChanged();
    }

    if (m_autoOptimizeEnabled && m_totalItemCount % m_maxItemsBeforeOptimization == 0) {
        optimizeScene();
    }
    m_itemsMutex.unlock();
}

void WhiteBoardScene::removeGraphicsItem(QGraphicsItem* item)
{
    if (!item) {
        qWarning() << "[SCENE] Cannot remove null item";
        return;
    }

    m_itemsMutex.lock();

    if (item->scene() != this) {
        qWarning() << "[SCENE] Item is not in this scene, skipping removal";
        m_itemsMutex.unlock();
        return;
    }

    if (!m_allItems.contains(item)) {
        qWarning() << "[SCENE] Item not found in scene item list, but removing from Qt scene";
        removeItem(item);
        m_itemsMutex.unlock();
        return;
    }

    try {
        removeItem(item);
        m_allItems.removeAll(item);
        m_totalItemCount--;
        m_boundingRectValid = false;

        emit itemRemoved(item);

        if (!m_batchUpdatesEnabled) {
            emit sceneChanged();
            emit historyLayerChanged();
        }
    } catch (...) {
        qCritical() << "[SCENE] Exception occurred while removing item";
    }

    m_itemsMutex.unlock();
}



void WhiteBoardScene::clearAllItems()
{
    QMutexLocker locker(&m_itemsMutex);

    // 发送清理开始信号，让工具类有机会清理引用
    emit aboutToClearItems();

    m_allItems.clear();
    m_totalItemCount = 0;
    m_boundingRectValid = false;

    emit sceneChanged();
    emit historyLayerChanged();
}

// 批量操作实现
void WhiteBoardScene::beginTransaction()
{
    m_inTransaction = true;
    m_transactionStates.clear();
    
    emit transactionStarted();
}

void WhiteBoardScene::endTransaction()
{
    m_inTransaction = false;
    m_transactionStates.clear();
    
    emit transactionEnded();
}

void WhiteBoardScene::rollbackTransaction()
{
    if (!m_inTransaction) return;
    
    // 恢复所有项目状态
    for (auto it = m_transactionStates.begin(); it != m_transactionStates.end(); ++it) {
        restoreItemState(it.key());
    }
    
    m_inTransaction = false;
    m_transactionStates.clear();
    
    emit sceneChanged();
}



// 性能优化实现
void WhiteBoardScene::optimizeScene()
{
    cleanupInvisibleItems();
    updateSceneRect();
    update();
}

void WhiteBoardScene::updateSceneRect()
{
    if (!m_boundingRectValid) {
        updateBoundingRect();
    }
    
    // 添加边距
    QRectF newRect = m_cachedBoundingRect.adjusted(-m_sceneMargin, -m_sceneMargin, 
                                                   m_sceneMargin, m_sceneMargin);
    
    if (newRect != sceneRect()) {
        setSceneRect(newRect);
    }
}

void WhiteBoardScene::enableBatchUpdates(bool enable)
{
    m_batchUpdatesEnabled = enable;
}

// 查询功能实现
QList<QGraphicsItem*> WhiteBoardScene::getItemsInRect(const QRectF& rect) const
{
    return items(rect, Qt::IntersectsItemBoundingRect);
}

QList<QGraphicsItem*> WhiteBoardScene::getItemsAtPoint(const QPointF& point) const
{
    return items(point);
}

// 统计信息实现 - 简化的场景信息
int WhiteBoardScene::getTotalItemCount() const
{
    return m_totalItemCount;
}

// QGraphicsScene重写
void WhiteBoardScene::drawBackground(QPainter* painter, const QRectF& rect)
{
    // 背景绘制由WhiteBoardWidget处理
    Q_UNUSED(painter)
    Q_UNUSED(rect)
}

void WhiteBoardScene::drawForeground(QPainter* painter, const QRectF& rect)
{
    // 前景绘制由WhiteBoardWidget处理
    Q_UNUSED(painter)
    Q_UNUSED(rect)
}

// 私有方法实现

void WhiteBoardScene::updateBoundingRect()
{
    QRectF boundingRect;

    m_itemsMutex.lock();
    for (QGraphicsItem* item : m_allItems) {
        if (item && item->isVisible()) {
            boundingRect = boundingRect.united(item->sceneBoundingRect());
        }
    }
    m_itemsMutex.unlock();

    m_cachedBoundingRect = boundingRect;
    m_boundingRectValid = true;
}

void WhiteBoardScene::cleanupInvisibleItems()
{
    QMutexLocker locker(&m_itemsMutex);

    QList<QGraphicsItem*> itemsToRemove;

    for (QGraphicsItem* item : m_allItems) {
        if (item && !item->isVisible() && item->opacity() <= 0.0) {
            itemsToRemove.append(item);
        }
    }

    for (QGraphicsItem* item : itemsToRemove) {
        removeGraphicsItem(item);
        // 安全删除：检查item是否仍然有效且没有父项管理
        if (item && !item->parentItem() && !item->scene()) {
            qDebug() << "Deleting item with no parent and no scene";
            delete item;
        }
        // 如果item有父项或仍在场景中，让Qt的父子关系自动管理内存
    }


}

void WhiteBoardScene::saveItemState(QGraphicsItem* item)
{
    if (!item || !m_inTransaction) return;

    // 保存项目状态（位置、变换等）
    QVariantMap state;
    state["pos"] = item->pos();
    state["rotation"] = item->rotation();
    state["scale"] = item->scale();
    state["visible"] = item->isVisible();
    state["opacity"] = item->opacity();

    m_transactionStates[item] = state;
}

void WhiteBoardScene::restoreItemState(QGraphicsItem* item)
{
    if (!item || !m_transactionStates.contains(item)) return;

    QVariantMap state = m_transactionStates[item].toMap();

    item->setPos(state["pos"].toPointF());
    item->setRotation(state["rotation"].toReal());
    item->setScale(state["scale"].toReal());
    item->setVisible(state["visible"].toBool());
    item->setOpacity(state["opacity"].toReal());
}

void WhiteBoardScene::onItemChanged()
{
    // 项目变化时的处理
    m_boundingRectValid = false;

    if (!m_batchUpdatesEnabled) {
        emit sceneChanged();
    }
}

QGraphicsItem* WhiteBoardScene::findItemById(const QString& itemId) const
{
    if (itemId.isEmpty()) {
        return nullptr;
    }

    // 遍历所有图形项查找匹配的ID
    QList<QGraphicsItem*> allItems = items();
    for (QGraphicsItem* item : allItems) {
        // 尝试转换为DrawItem
        DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
        if (drawItem && drawItem->itemId() == itemId) {
            return item;
        }
    }

    return nullptr;
}
