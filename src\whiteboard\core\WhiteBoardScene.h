#ifndef WHITEBOARDSCENE_H
#define WHITEBOARDSCENE_H

#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QMutex>
#include <QElapsedTimer>
#include <QHash>
#include <QList>

/**
 * @brief 简化的白板场景类 - 管理所有图形元素
 *
 * 核心功能：
 * 1. 管理所有图形元素的添加、删除、修改
 * 2. 优化场景更新和重绘区域
 * 3. 支持图形的批量操作和事务处理
 */
class WhiteBoardScene : public QGraphicsScene
{
    Q_OBJECT

public:
    explicit WhiteBoardScene(QObject* parent = nullptr);
    ~WhiteBoardScene();

    // 初始化
    void initialize();

    // 图形管理 - 专注于QGraphicsScene功能
    void addGraphicsItem(QGraphicsItem* item);
    void removeGraphicsItem(QGraphicsItem* item);
    void clearAllItems();

    // 批量操作
    void beginTransaction();
    void endTransaction();
    void rollbackTransaction();

    // 性能优化
    void optimizeScene();
    void updateSceneRect();
    void enableBatchUpdates(bool enable);

    // 查询功能
    QList<QGraphicsItem*> getItemsInRect(const QRectF& rect) const;
    QList<QGraphicsItem*> getItemsAtPoint(const QPointF& point) const;
    QGraphicsItem* findItemById(const QString& itemId) const;

    // 统计信息 - 简化的场景信息
    int getTotalItemCount() const;

protected:
    // QGraphicsScene重写
    void drawBackground(QPainter* painter, const QRectF& rect) override;
    void drawForeground(QPainter* painter, const QRectF& rect) override;

private slots:
    void onItemChanged();

private:
    // 初始化方法
    void setupSceneOptimization();
    void setupItemIndexing();



    // 性能优化
    void updateBoundingRect();
    void cleanupInvisibleItems();

    // 事务处理
    void saveItemState(QGraphicsItem* item);
    void restoreItemState(QGraphicsItem* item);

signals:
    void itemAdded(QGraphicsItem* item);
    void itemRemoved(QGraphicsItem* item);
    void sceneChanged();
    void historyLayerChanged(); // 新增：历史层变化信号
    void aboutToClearItems();  // 清理前通知信号
    void transactionStarted();
    void transactionEnded();

private:
    // 图形管理 - 简化的场景管理
    QList<QGraphicsItem*> m_allItems;
    mutable QMutex m_itemsMutex;

    // 事务处理
    bool m_inTransaction;
    QHash<QGraphicsItem*, QVariant> m_transactionStates;

    // 性能优化
    bool m_batchUpdatesEnabled;
    QElapsedTimer m_updateTimer;
    QRectF m_cachedBoundingRect;
    bool m_boundingRectValid;

    // 统计信息 - 只保留总数
    int m_totalItemCount;

    // 配置参数
    bool m_autoOptimizeEnabled;
    int m_maxItemsBeforeOptimization;
    qreal m_sceneMargin;

    // 初始化状态
    bool m_initialized;
};

#endif // WHITEBOARDSCENE_H
