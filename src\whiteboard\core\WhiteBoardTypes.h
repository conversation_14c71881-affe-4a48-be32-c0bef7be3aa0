#ifndef WHITEBOARDTYPES_H
#define WHITEBOARDTYPES_H

/**
 * @brief 白板系统的公共类型定义
 * 
 * 这个文件包含了白板系统中各个组件共享的类型定义，
 * 避免循环依赖问题。
 */

// 工具类型枚举 - 使用enum class避免与Windows API冲突
enum class ToolType {
    FreeDraw = 0,    // 自由绘制
    FreeDrawDashed,  // 自由绘制 - 虚线
    FreeDrawHighlighter, // 自由绘制 - 荧光笔
    Line,            // 直线
    DashedLine,      // 虚线
    Rectangle,       // 矩形
    Square,          // 正方形
    Ellipse,         // 椭圆
    Circle,          // 圆形
    Triangle,        // 三角形
    RightTriangle,   // 直角三角形
    Arrow,           // 箭头
    Image,           // 图片
    Eraser,          // 橡皮擦
    Lasso,           // 套索选择
    PassThrough      // 穿透模式
};

// AnchorType定义在SelectionUITypes.h中

/**
 * @brief 变换类型枚举
 */
enum class TransformType {
    None,           ///< 无变换
    Move,           ///< 移动
    Scale,          ///< 缩放
    Rotate,         ///< 旋转
    ScaleKeepRatio, ///< 缩放（保持比例）
    Custom          ///< 自定义变换
};

/**
 * @brief 判断是否是自由绘制类型的工具
 */
inline bool isFreeDrawTool(ToolType toolType) {
    return toolType == ToolType::FreeDraw ||
           toolType == ToolType::FreeDrawDashed ||
           toolType == ToolType::FreeDrawHighlighter;
}

#endif // WHITEBOARDTYPES_H
