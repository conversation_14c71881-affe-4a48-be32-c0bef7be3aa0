#include "WhiteboardResourceManager.h"
#include <QDebug>
#include <QDir>
#include <QResource>

WhiteboardResourceManager& WhiteboardResourceManager::instance()
{
    static WhiteboardResourceManager instance;
    return instance;
}

WhiteboardResourceManager::WhiteboardResourceManager(QObject *parent)
    : QObject(parent)
{
    // 构造函数中不进行资源初始化，由显式调用 initializeResources() 完成
}

WhiteboardResourceManager::~WhiteboardResourceManager()
{
    cleanupResources();
}

bool WhiteboardResourceManager::initializeResources()
{
    if (m_initialized) {
        return true;
    }

    // 初始化主要资源文件
    Q_INIT_RESOURCE(whiteboard_resources);
    
    // 验证关键资源是否可用
    QStringList criticalResources = {
        ":/whiteboard/images/eraser.svg",
        ":/whiteboard/images/select-tool-delete.svg",
        ":/whiteboard/images/select-rotate.svg"
    };

    bool allResourcesAvailable = true;

    for (const QString& resourcePath : criticalResources) {
        QResource resource(resourcePath);
        if (!resource.isValid() || resource.size() == 0) {
            allResourcesAvailable = false;
        }
    }

    if (allResourcesAvailable) {
        m_initialized = true;
    }

    return m_initialized;
}

void WhiteboardResourceManager::cleanupResources()
{
    if (!m_initialized) {
        return;
    }

    Q_CLEANUP_RESOURCE(resources);
    m_initialized = false;
}

QStringList WhiteboardResourceManager::getAvailableResources() const
{
    QStringList resources;
    
    // 列出所有已知的资源路径
    QStringList knownResources = {
        ":/whiteboard/images/eraser.svg",
        ":/whiteboard/images/select-tool-delete.svg",
        ":/whiteboard/images/select-rotate.svg"
    };

    for (const QString& resourcePath : knownResources) {
        QResource resource(resourcePath);
        if (resource.isValid()) {
            resources << QString("%1 (%2 bytes)").arg(resourcePath).arg(resource.size());
        } else {
            resources << QString("%1 (MISSING)").arg(resourcePath);
        }
    }

    return resources;
}
