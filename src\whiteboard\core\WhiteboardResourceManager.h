#pragma once

#include <QObject>

/**
 * @brief 白板资源管理器
 * 
 * 负责管理白板库的所有资源文件，包括：
 * - 图标资源（橡皮擦、工具等）
 * - 着色器资源
 * - 其他静态资源
 * 
 * 采用单例模式，确保资源只初始化一次
 */
class WhiteboardResourceManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 获取单例实例
     */
    static WhiteboardResourceManager& instance();

    /**
     * @brief 初始化所有资源
     * @return 是否初始化成功
     */
    bool initializeResources();

    /**
     * @brief 清理所有资源
     */
    void cleanupResources();

    /**
     * @brief 检查资源是否已初始化
     */
    bool isInitialized() const { return m_initialized; }

    /**
     * @brief 获取资源路径（用于调试）
     */
    QStringList getAvailableResources() const;

private:
    explicit WhiteboardResourceManager(QObject *parent = nullptr);
    ~WhiteboardResourceManager();

    // 禁用拷贝构造和赋值
    WhiteboardResourceManager(const WhiteboardResourceManager&) = delete;
    WhiteboardResourceManager& operator=(const WhiteboardResourceManager&) = delete;

    bool m_initialized = false;
};
