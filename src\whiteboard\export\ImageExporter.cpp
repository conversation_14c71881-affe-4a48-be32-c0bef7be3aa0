#include "ImageExporter.h"
#include "../core/WhiteBoardScene.h"
#include "../core/WhiteBoardWidget.h"
#include "../graphics/DrawItem.h"

#include <QPainter>
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QFileInfo>
#include <QDir>
#include <QSvgGenerator>
#include <QPdfWriter>
#include <QImageWriter>
#include <QApplication>

#include <cmath>

ImageExporter::ImageExporter(QObject* parent)
    : QObject(parent)
    , m_scene(nullptr)
    , m_view(nullptr)
    , m_progressCallback(nullptr)
    , m_contentBoundsValid(false)
{
}

ImageExporter::~ImageExporter()
{
}

// 设置源
void ImageExporter::setScene(WhiteBoardScene* scene)
{
    m_scene = scene;
    m_contentBoundsValid = false;
}

void ImageExporter::setView(WhiteBoardWidget* view)
{
    m_view = view;
    if (view && view->scene()) {
        setScene(qobject_cast<WhiteBoardScene*>(view->scene()));
    }
}

WhiteBoardScene* ImageExporter::getScene() const
{
    return m_scene;
}

WhiteBoardWidget* ImageExporter::getView() const
{
    return m_view;
}

// 基础导出功能
bool ImageExporter::exportToFile(const QString& filePath, ImageFormat format, const ExportOptions& options)
{
    if (!m_scene) {
        setLastError("No scene set for export");
        return false;
    }

    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.dir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            setLastError("Cannot create directory: " + dir.path());
            return false;
        }
    }

    emit exportStarted(filePath);
    updateProgress(0, "开始导出...");

    bool success = false;
    
    switch (format) {
    case PNG:
        success = exportToPng(filePath, options);
        break;
    case JPG:
        success = exportToJpg(filePath, options);
        break;
    case SVG:
        success = exportToSvg(filePath, options);
        break;
    case PDF:
        success = exportToPdf(filePath, options);
        break;
    case BMP:
    case TIFF:
        success = saveAsGeneric(filePath, format, options);
        break;
    default:
        setLastError("Unsupported format");
        return false;
    }

    if (success) {
        qint64 fileSize = QFileInfo(filePath).size();
        updateProgress(100, "导出完成");
        emit exportCompleted(filePath, fileSize);
    } else {
        emit exportFailed(getLastError());
    }

    return success;
}

QPixmap ImageExporter::exportToPixmap(const ExportOptions& options)
{
    if (!m_scene) {
        setLastError("No scene set for export");
        return QPixmap();
    }

    updateProgress(0, "生成Pixmap...");
    
    QPixmap pixmap = renderToPixmap(options);
    
    updateProgress(100, "Pixmap生成完成");
    return pixmap;
}

QImage ImageExporter::exportToImage(const ExportOptions& options)
{
    if (!m_scene) {
        setLastError("No scene set for export");
        return QImage();
    }

    updateProgress(0, "生成Image...");
    
    QImage image = renderToImage(options);
    
    updateProgress(100, "Image生成完成");
    return image;
}

// 格式特定导出
bool ImageExporter::exportToPng(const QString& filePath, const ExportOptions& options)
{
    updateProgress(20, "渲染PNG图像...");
    
    QImage image = renderToImage(options);
    if (image.isNull()) {
        setLastError("Failed to render image");
        return false;
    }

    updateProgress(80, "保存PNG文件...");
    
    return saveAsPng(image, filePath, options);
}

bool ImageExporter::exportToJpg(const QString& filePath, const ExportOptions& options)
{
    updateProgress(20, "渲染JPG图像...");
    
    QImage image = renderToImage(options);
    if (image.isNull()) {
        setLastError("Failed to render image");
        return false;
    }

    updateProgress(80, "保存JPG文件...");
    
    return saveAsJpg(image, filePath, options);
}

bool ImageExporter::exportToSvg(const QString& filePath, const ExportOptions& options)
{
    updateProgress(20, "生成SVG文件...");
    
    return saveAsSvg(filePath, options);
}

bool ImageExporter::exportToPdf(const QString& filePath, const ExportOptions& options)
{
    updateProgress(20, "生成PDF文件...");
    
    return saveAsPdf(filePath, options);
}

// 预览功能
QPixmap ImageExporter::generatePreview(const QSize& previewSize)
{
    if (!m_scene) {
        return QPixmap();
    }

    ExportOptions previewOptions;
    previewOptions.imageSize = previewSize;
    previewOptions.quality = Medium;
    previewOptions.antialiasing = true;
    previewOptions.highQualityScaling = false; // 预览不需要高质量缩放
    
    return renderToPixmap(previewOptions);
}

QRectF ImageExporter::getContentBounds() const
{
    if (!m_scene) {
        return QRectF();
    }

    if (!m_contentBoundsValid) {
        QRectF bounds;
        QList<QGraphicsItem*> items = m_scene->items();

        bool hasValidBounds = false;
        for (QGraphicsItem* item : items) {
            if (item->isVisible()) {
                QRectF itemBounds = item->sceneBoundingRect();

                if (itemBounds.width() > 1 && itemBounds.height() > 1) {
                    if (!hasValidBounds) {
                        bounds = itemBounds;
                        hasValidBounds = true;
                    } else {
                        bounds = bounds.united(itemBounds);
                    }
                }
            }
        }

        if (bounds.isEmpty()) {
            bounds = m_scene->sceneRect();
        }

        m_cachedContentBounds = bounds;
        m_contentBoundsValid = true;
    }

    return m_cachedContentBounds;
}

QSize ImageExporter::getOptimalSize(int maxWidth, int maxHeight) const
{
    QRectF contentBounds = getContentBounds();
    if (contentBounds.isEmpty()) {
        return QSize(800, 600); // 默认尺寸
    }

    qreal aspectRatio = contentBounds.width() / contentBounds.height();
    
    int width = maxWidth;
    int height = qRound(width / aspectRatio);
    
    if (height > maxHeight) {
        height = maxHeight;
        width = qRound(height * aspectRatio);
    }
    
    return QSize(width, height);
}

// 格式检测和验证
ImageExporter::ImageFormat ImageExporter::detectFormat(const QString& filePath)
{
    QString suffix = QFileInfo(filePath).suffix().toLower();
    
    if (suffix == "png") return PNG;
    if (suffix == "jpg" || suffix == "jpeg") return JPG;
    if (suffix == "bmp") return BMP;
    if (suffix == "tiff" || suffix == "tif") return TIFF;
    if (suffix == "svg") return SVG;
    if (suffix == "pdf") return PDF;
    
    return PNG; // 默认格式
}

QString ImageExporter::formatToExtension(ImageFormat format)
{
    switch (format) {
    case PNG: return "png";
    case JPG: return "jpg";
    case BMP: return "bmp";
    case TIFF: return "tiff";
    case SVG: return "svg";
    case PDF: return "pdf";
    default: return "png";
    }
}

QString ImageExporter::formatToMimeType(ImageFormat format)
{
    switch (format) {
    case PNG: return "image/png";
    case JPG: return "image/jpeg";
    case BMP: return "image/bmp";
    case TIFF: return "image/tiff";
    case SVG: return "image/svg+xml";
    case PDF: return "application/pdf";
    default: return "image/png";
    }
}

bool ImageExporter::isFormatSupported(ImageFormat format)
{
    QList<QByteArray> supportedFormats = QImageWriter::supportedImageFormats();
    QString formatStr = formatToExtension(format);
    
    return supportedFormats.contains(formatStr.toUtf8()) || 
           format == SVG || format == PDF;
}

// 错误处理
QString ImageExporter::getLastError() const
{
    return m_lastError;
}

void ImageExporter::clearLastError()
{
    m_lastError.clear();
}

void ImageExporter::setProgressCallback(ProgressCallback callback)
{
    m_progressCallback = callback;
}

// 私有方法
void ImageExporter::updateProgress(int percentage, const QString& status)
{
    if (m_progressCallback) {
        m_progressCallback(percentage, status);
    }
    emit exportProgress(percentage, status);
}

void ImageExporter::setLastError(const QString& error)
{
    m_lastError = error;
    qWarning() << "ImageExporter Error:" << error;
}

// 内部渲染方法实现
QImage ImageExporter::renderToImage(const ExportOptions& options)
{
    if (!m_scene) {
        setLastError("No scene available for rendering");
        return QImage();
    }

    updateProgress(10, "计算渲染区域...");

    QRectF sourceRect = calculateExportRegion(options);
    QSize targetSize = calculateOptimalSize(sourceRect, options);

    updateProgress(20, "创建渲染目标...");

    // 创建高质量图像
    QImage::Format format = (options.backgroundMode == Transparent) ?
                            QImage::Format_ARGB32_Premultiplied :
                            QImage::Format_RGB32;

    QImage image(targetSize, format);

    if (options.backgroundMode == Transparent) {
        image.fill(Qt::transparent);
    } else {
        QColor bgColor = (options.backgroundMode == Custom) ?
                        options.customBackgroundColor :
                        (options.backgroundMode == White ? Qt::white : Qt::black);
        image.fill(bgColor);
    }

    updateProgress(40, "设置渲染器...");

    QPainter painter(&image);
    setupHighQualityRendering(&painter, options);

    updateProgress(60, "渲染场景内容...");

    // 渲染背景
    if (options.backgroundMode != Transparent) {
        renderBackground(&painter, QRectF(QPointF(0, 0), targetSize), options);
    }

    m_scene->render(&painter, QRectF(QPointF(0, 0), targetSize), sourceRect);

    painter.end();

    updateProgress(80, "应用后处理...");

    // 应用高质量处理
    if (options.enableMultisampling && options.multisampleCount > 1) {
        image = applyAntialiasing(image, options.multisampleCount);
    }

    updateProgress(90, "完成渲染...");

    return image;
}

QPixmap ImageExporter::renderToPixmap(const ExportOptions& options)
{
    QImage image = renderToImage(options);
    return QPixmap::fromImage(image);
}

void ImageExporter::setupHighQualityRendering(QPainter* painter, const ExportOptions& options)
{
    if (!painter) return;

    // 设置高质量渲染选项
    if (options.antialiasing) {
        painter->setRenderHint(QPainter::Antialiasing, true);
        painter->setRenderHint(QPainter::TextAntialiasing, true);
    }

    if (options.highQualityScaling) {
        painter->setRenderHint(QPainter::SmoothPixmapTransform, true);
    }

    // 设置合成模式
    painter->setCompositionMode(QPainter::CompositionMode_SourceOver);
}

QImage ImageExporter::applyAntialiasing(const QImage& source, int samples)
{
    if (samples <= 1) return source;

    // 简单的超采样抗锯齿
    QSize supersampleSize = source.size() * samples;
    QImage supersample(supersampleSize, source.format());
    supersample.fill(Qt::transparent);

    QPainter painter(&supersample);
    painter.setRenderHint(QPainter::Antialiasing, true);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);

    painter.scale(samples, samples);
    painter.drawImage(0, 0, source);
    painter.end();

    // 缩放回原始尺寸
    return supersample.scaled(source.size(), Qt::IgnoreAspectRatio, Qt::SmoothTransformation);
}

void ImageExporter::renderBackground(QPainter* painter, const QRectF& rect, const ExportOptions& options)
{
    if (!painter || options.backgroundMode == Transparent) return;

    QColor bgColor;
    switch (options.backgroundMode) {
    case White:
        bgColor = Qt::white;
        break;
    case Black:
        bgColor = Qt::black;
        break;
    case Custom:
        bgColor = options.customBackgroundColor;
        break;
    default:
        return;
    }

    painter->fillRect(rect, bgColor);
}

QRectF ImageExporter::calculateExportRegion(const ExportOptions& options) const
{
    if (!options.exportRegion.isEmpty()) {
        return options.exportRegion;
    }

    if (m_view) {
        QRectF viewportRect = m_view->mapToScene(m_view->viewport()->rect()).boundingRect();
        return viewportRect;
    }

    if (m_scene) {
        QRectF sceneRect = m_scene->sceneRect();
        return sceneRect;
    }

    // 最后回退到内容边界
    return getContentBounds();
}

QSize ImageExporter::calculateOptimalSize(const QRectF& contentRect, const ExportOptions& options) const
{
    if (!options.imageSize.isEmpty()) {
        return options.imageSize;
    }

    // 如果内容区域为空，返回默认尺寸
    if (contentRect.isEmpty()) {
        return QSize(800, 600);
    }

    // 根据DPI计算尺寸
    qreal dpiScale = options.dpi / 96.0; // 96 DPI是标准
    QSize size = contentRect.size().toSize() * dpiScale * options.scaleFactor;

    // 确保最小尺寸
    size.setWidth(qMax(size.width(), 100));
    size.setHeight(qMax(size.height(), 100));

    // 限制最大尺寸
    const int maxDimension = 8192;
    if (size.width() > maxDimension || size.height() > maxDimension) {
        size = size.scaled(maxDimension, maxDimension, Qt::KeepAspectRatio);
    }

    return size;
}

// 格式特定保存方法
bool ImageExporter::saveAsPng(const QImage& image, const QString& filePath, const ExportOptions& options)
{
    QImageWriter writer(filePath, "PNG");
    writer.setQuality(options.quality);
    writer.setCompression(9); // 最大压缩

    if (!writer.write(image)) {
        setLastError("Failed to save PNG: " + writer.errorString());
        return false;
    }

    return true;
}

bool ImageExporter::saveAsJpg(const QImage& image, const QString& filePath, const ExportOptions& options)
{
    // JPG不支持透明度，需要合成背景
    QImage jpgImage = image;
    if (image.hasAlphaChannel()) {
        QColor bgColor = (options.backgroundMode == Custom) ?
                        options.customBackgroundColor : Qt::white;

        QImage composite(image.size(), QImage::Format_RGB32);
        composite.fill(bgColor);

        QPainter painter(&composite);
        painter.drawImage(0, 0, image);
        painter.end();

        jpgImage = composite;
    }

    QImageWriter writer(filePath, "JPG");
    writer.setQuality(options.quality);
    writer.setOptimizedWrite(true);
    writer.setProgressiveScanWrite(true);

    if (!writer.write(jpgImage)) {
        setLastError("Failed to save JPG: " + writer.errorString());
        return false;
    }

    return true;
}

bool ImageExporter::saveAsSvg(const QString& filePath, const ExportOptions& options)
{
    if (!m_scene) {
        setLastError("No scene available for SVG export");
        return false;
    }

    QRectF sourceRect = calculateExportRegion(options);
    QSize targetSize = calculateOptimalSize(sourceRect, options);

    QSvgGenerator generator;
    generator.setFileName(filePath);
    generator.setSize(targetSize);
    generator.setViewBox(QRect(QPoint(0, 0), targetSize));
    generator.setTitle("WhiteBoard Export");
    generator.setDescription("Exported from WhiteBoard Application");

    QPainter painter(&generator);
    setupHighQualityRendering(&painter, options);

    // 渲染背景
    if (options.backgroundMode != Transparent) {
        renderBackground(&painter, QRectF(QPointF(0, 0), targetSize), options);
    }

    // 渲染场景
    m_scene->render(&painter, QRectF(QPointF(0, 0), targetSize), sourceRect);

    painter.end();

    return true;
}

bool ImageExporter::saveAsPdf(const QString& filePath, const ExportOptions& options)
{
    if (!m_scene) {
        setLastError("No scene available for PDF export");
        return false;
    }

    QRectF sourceRect = calculateExportRegion(options);

    QPdfWriter writer(filePath);
    writer.setPageSize(QPageSize(sourceRect.size(), QPageSize::Point));
    writer.setPageMargins(QMarginsF(0, 0, 0, 0));
    writer.setResolution(options.dpi);
    writer.setTitle("WhiteBoard Export");
    writer.setCreator("WhiteBoard Application");

    QPainter painter(&writer);
    setupHighQualityRendering(&painter, options);

    // 渲染背景
    if (options.backgroundMode != Transparent) {
        renderBackground(&painter, QRectF(QPointF(0, 0), sourceRect.size()), options);
    }

    // 渲染场景
    m_scene->render(&painter, QRectF(QPointF(0, 0), sourceRect.size()), sourceRect);

    painter.end();

    return true;
}

bool ImageExporter::saveAsGeneric(const QString& filePath, ImageFormat format, const ExportOptions& options)
{
    QImage image = renderToImage(options);
    if (image.isNull()) {
        setLastError("Failed to render image");
        return false;
    }

    QString formatStr = formatToExtension(format).toUpper();
    QImageWriter writer(filePath, formatStr.toUtf8());
    writer.setQuality(options.quality);

    if (!writer.write(image)) {
        setLastError("Failed to save " + formatStr + ": " + writer.errorString());
        return false;
    }

    return true;
}

// 批量导出和高级功能
bool ImageExporter::exportMultipleFormats(const QString& baseFilePath,
                                         const QList<ImageFormat>& formats,
                                         const ExportOptions& options)
{
    if (formats.isEmpty()) {
        setLastError("No formats specified for export");
        return false;
    }

    QFileInfo fileInfo(baseFilePath);
    QString baseName = fileInfo.completeBaseName();
    QString dirPath = fileInfo.dir().path();

    bool allSuccess = true;
    int totalFormats = formats.size();

    for (int i = 0; i < totalFormats; ++i) {
        ImageFormat format = formats[i];
        QString extension = formatToExtension(format);
        QString filePath = QDir(dirPath).filePath(baseName + "." + extension);

        updateProgress((i * 100) / totalFormats,
                      QString("导出 %1 格式...").arg(extension.toUpper()));

        if (!exportToFile(filePath, format, options)) {
            allSuccess = false;
            qWarning() << "Failed to export format:" << extension;
        }
    }

    return allSuccess;
}

bool ImageExporter::exportRegion(const QString& filePath, const QRectF& region,
                                ImageFormat format, const ExportOptions& options)
{
    ExportOptions regionOptions = options;
    regionOptions.exportRegion = region;

    return exportToFile(filePath, format, regionOptions);
}

bool ImageExporter::exportTiled(const QString& baseFilePath, const QSize& tileSize,
                               ImageFormat format, const ExportOptions& options)
{
    if (!m_scene || tileSize.isEmpty()) {
        setLastError("Invalid parameters for tiled export");
        return false;
    }

    QRectF contentBounds = getContentBounds();
    if (contentBounds.isEmpty()) {
        setLastError("No content to export");
        return false;
    }

    QFileInfo fileInfo(baseFilePath);
    QString baseName = fileInfo.completeBaseName();
    QString extension = formatToExtension(format);
    QString dirPath = fileInfo.dir().path();

    int tilesX = qCeil(contentBounds.width() / tileSize.width());
    int tilesY = qCeil(contentBounds.height() / tileSize.height());
    int totalTiles = tilesX * tilesY;

    bool allSuccess = true;
    int currentTile = 0;

    for (int y = 0; y < tilesY; ++y) {
        for (int x = 0; x < tilesX; ++x) {
            QRectF tileRect(
                contentBounds.x() + x * tileSize.width(),
                contentBounds.y() + y * tileSize.height(),
                tileSize.width(),
                tileSize.height()
            );

            // 确保瓦片不超出内容边界
            tileRect = tileRect.intersected(contentBounds);

            QString tilePath = QDir(dirPath).filePath(
                QString("%1_tile_%2_%3.%4").arg(baseName).arg(x).arg(y).arg(extension)
            );

            updateProgress((currentTile * 100) / totalTiles,
                          QString("导出瓦片 %1/%2...").arg(currentTile + 1).arg(totalTiles));

            if (!exportRegion(tilePath, tileRect, format, options)) {
                allSuccess = false;
                qWarning() << "Failed to export tile:" << tilePath;
            }

            currentTile++;
        }
    }

    return allSuccess;
}

// 选项预设
ImageExporter::ExportOptions ImageExporter::getPresetOptions(const QString& presetName)
{
    ExportOptions options;

    if (presetName == "web") {
        options.imageSize = QSize(1920, 1080);
        options.quality = Medium;
        options.backgroundMode = White;
        options.antialiasing = true;
        options.dpi = 96;
    } else if (presetName == "print") {
        options.imageSize = QSize(3508, 2480); // A4 at 300 DPI
        options.quality = Maximum;
        options.backgroundMode = White;
        options.antialiasing = true;
        options.dpi = 300;
        options.optimizeForPrint = true;
    } else if (presetName == "thumbnail") {
        options.imageSize = QSize(400, 300);
        options.quality = Medium;
        options.backgroundMode = White;
        options.antialiasing = true;
        options.dpi = 96;
        options.highQualityScaling = false;
    } else if (presetName == "transparent") {
        options.imageSize = QSize(1920, 1080);
        options.quality = High;
        options.backgroundMode = Transparent;
        options.antialiasing = true;
        options.dpi = 96;
    } else if (presetName == "high_quality") {
        options.imageSize = QSize(4096, 4096);
        options.quality = Maximum;
        options.backgroundMode = White;
        options.antialiasing = true;
        options.dpi = 300;
        options.enableMultisampling = true;
        options.multisampleCount = 8;
        options.highQualityScaling = true;
    }

    return options;
}

QStringList ImageExporter::getAvailablePresets()
{
    return QStringList() << "web" << "print" << "thumbnail" << "transparent" << "high_quality";
}
