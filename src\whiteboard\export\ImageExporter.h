#ifndef IMAGEEXPORTER_H
#define IMAGEEXPORTER_H

#include <QObject>
#include <QPixmap>
#include <QImage>
#include <QSize>
#include <QColor>
#include <QRectF>
#include <QString>
#include <QVariantMap>

class WhiteBoardScene;
class WhiteBoardWidget;

/**
 * @brief 白板图片导出器 - 高质量图片导出系统
 * 
 * 核心功能：
 * 1. 多格式图片导出（PNG、JPG、BMP、TIFF）
 * 2. 自定义分辨率和DPI设置
 * 3. 透明背景和实体背景支持
 * 4. 高质量抗锯齿渲染
 * 5. 批量导出和区域导出
 * 6. 性能优化的大尺寸导出
 */
class ImageExporter : public QObject
{
    Q_OBJECT

public:
    // 支持的图片格式
    enum ImageFormat {
        PNG,        // 支持透明度，无损压缩
        JPG,        // 有损压缩，文件小
        BMP,        // 无压缩，兼容性好
        TIFF,       // 高质量，支持透明度
        SVG,        // 矢量格式
        PDF         // 文档格式
    };
    Q_ENUM(ImageFormat)

    // 导出质量设置
    enum Quality {
        Low = 25,       // 快速导出，文件小
        Medium = 60,    // 平衡质量和大小
        High = 85,      // 高质量
        Maximum = 100   // 最高质量，文件大
    };
    Q_ENUM(Quality)

    // 背景模式
    enum BackgroundMode {
        Transparent,    // 透明背景
        White,          // 白色背景
        Black,          // 黑色背景
        Custom          // 自定义颜色
    };
    Q_ENUM(BackgroundMode)

    // 导出选项结构
    struct ExportOptions {
        QSize imageSize = QSize(1920, 1080);    // 导出尺寸
        int dpi = 96;                          // DPI设置
        Quality quality = Medium;                 // 图片质量
        BackgroundMode backgroundMode = Transparent;  // 背景模式
        QColor customBackgroundColor = Qt::transparent;// 自定义背景色
        bool antialiasing = true;               // 抗锯齿
        bool highQualityScaling = true;         // 高质量缩放
        QRectF exportRegion;                    // 导出区域（空表示全部）
        qreal scaleFactor = 1.0;                // 缩放因子
        bool preserveAspectRatio = true;        // 保持宽高比
        
        // 高级选项
        bool enableMultisampling = true;        // 多重采样
        int multisampleCount = 4;               // 采样数量
        bool enableMipmap = false;              // Mipmap生成
        bool optimizeForPrint = false;          // 打印优化
    };

public:
    explicit ImageExporter(QObject* parent = nullptr);
    ~ImageExporter();

    // 设置源
    void setScene(WhiteBoardScene* scene);
    void setView(WhiteBoardWidget* view);
    WhiteBoardScene* getScene() const;
    WhiteBoardWidget* getView() const;

    // 基础导出功能
    bool exportToFile(const QString& filePath, ImageFormat format = PNG, 
                     const ExportOptions& options = ExportOptions());
    
    QPixmap exportToPixmap(const ExportOptions& options = ExportOptions());
    QImage exportToImage(const ExportOptions& options = ExportOptions());
    
    // 格式特定导出
    bool exportToPng(const QString& filePath, const ExportOptions& options = ExportOptions());
    bool exportToJpg(const QString& filePath, const ExportOptions& options = ExportOptions());
    bool exportToSvg(const QString& filePath, const ExportOptions& options = ExportOptions());
    bool exportToPdf(const QString& filePath, const ExportOptions& options = ExportOptions());
    
    // 批量导出
    bool exportMultipleFormats(const QString& baseFilePath, 
                              const QList<ImageFormat>& formats,
                              const ExportOptions& options = ExportOptions());
    
    // 区域导出
    bool exportRegion(const QString& filePath, const QRectF& region,
                     ImageFormat format = PNG, const ExportOptions& options = ExportOptions());
    
    // 分页导出（大尺寸场景）
    bool exportTiled(const QString& baseFilePath, const QSize& tileSize,
                    ImageFormat format = PNG, const ExportOptions& options = ExportOptions());
    
    // 预览功能
    QPixmap generatePreview(const QSize& previewSize = QSize(400, 300));
    QRectF getContentBounds() const;
    QSize getOptimalSize(int maxWidth = 4096, int maxHeight = 4096) const;
    
    // 格式检测和验证
    static ImageFormat detectFormat(const QString& filePath);
    static QString formatToExtension(ImageFormat format);
    static QString formatToMimeType(ImageFormat format);
    static bool isFormatSupported(ImageFormat format);
    
    // 选项预设
    static ExportOptions getPresetOptions(const QString& presetName);
    static QStringList getAvailablePresets();
    
    // 错误处理
    QString getLastError() const;
    void clearLastError();
    
    // 进度回调
    typedef std::function<void(int progress, const QString& status)> ProgressCallback;
    void setProgressCallback(ProgressCallback callback);

signals:
    void exportStarted(const QString& filePath);
    void exportProgress(int percentage, const QString& status);
    void exportCompleted(const QString& filePath, qint64 fileSize);
    void exportFailed(const QString& error);

private:
    // 内部渲染方法
    QImage renderToImage(const ExportOptions& options);
    QPixmap renderToPixmap(const ExportOptions& options);
    
    // 渲染优化
    void setupHighQualityRendering(QPainter* painter, const ExportOptions& options);
    QImage applyAntialiasing(const QImage& source, int samples = 4);
    QImage scaleWithHighQuality(const QImage& source, const QSize& targetSize);
    
    // 背景处理
    void renderBackground(QPainter* painter, const QRectF& rect, const ExportOptions& options);
    
    // 格式特定处理
    bool saveAsPng(const QImage& image, const QString& filePath, const ExportOptions& options);
    bool saveAsJpg(const QImage& image, const QString& filePath, const ExportOptions& options);
    bool saveAsSvg(const QString& filePath, const ExportOptions& options);
    bool saveAsPdf(const QString& filePath, const ExportOptions& options);
    bool saveAsGeneric(const QString& filePath, ImageFormat format, const ExportOptions& options);
    
    // 工具方法
    QRectF calculateExportRegion(const ExportOptions& options) const;
    QSize calculateOptimalSize(const QRectF& contentRect, const ExportOptions& options) const;
    void updateProgress(int percentage, const QString& status);
    void setLastError(const QString& error);

private:
    WhiteBoardScene* m_scene;
    WhiteBoardWidget* m_view;
    QString m_lastError;
    ProgressCallback m_progressCallback;
    
    // 缓存
    mutable QRectF m_cachedContentBounds;
    mutable bool m_contentBoundsValid;
};

#endif // IMAGEEXPORTER_H
