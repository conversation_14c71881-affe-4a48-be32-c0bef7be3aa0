#include "ImageItem.h"
#include <QGraphicsScene>
#include <QGraphicsView>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonValue>
#include <QApplication>
#include <QDateTime>
#include <QFileInfo>
#include <QImageReader>
#include <QScreen>

// 静态常量定义
const qreal ImageItem::DEFAULT_WIDTH = 400.0;      // 默认显示宽度（4K基准）
const qreal ImageItem::DEFAULT_MAX_HEIGHT = 800.0; // 默认最大高度（4K基准）

// 简单的屏幕适配函数
namespace {
    qreal getScreenScaleFactor() {
        static qreal scaleFactor = 1.0;
        static bool calculated = false;

        if (!calculated) {
            QScreen* screen = QApplication::primaryScreen();
            if (screen) {
                // 基于4K基准计算缩放因子
                const qreal baseWidth = 4096.0;
                const qreal baseHeight = 2160.0;

                QSize screenSize = screen->size();
                qreal widthRatio = screenSize.width() / baseWidth;
                qreal heightRatio = screenSize.height() / baseHeight;

                // 使用较小的比例确保不超出屏幕
                scaleFactor = qMin(widthRatio, heightRatio);

                // 限制缩放范围
                scaleFactor = qBound(0.3, scaleFactor, 3.0);
            }
            calculated = true;
        }

        return scaleFactor;
    }

    qreal adaptSize(qreal originalSize) {
        return originalSize * getScreenScaleFactor();
    }

    QSizeF adaptSize(const QSizeF& originalSize) {
        qreal factor = getScreenScaleFactor();
        return QSizeF(originalSize.width() * factor, originalSize.height() * factor);
    }
}

ImageItem::ImageItem(const QString& imagePath, const QPointF& position, 
                     qreal displayWidth, qreal maxHeight, QGraphicsItem* parent)
    : QGraphicsItem(parent)
    , m_imagePath(imagePath)
    , m_displaySize(0, 0)
    , m_maxHeight(maxHeight > 0 ? maxHeight : DEFAULT_MAX_HEIGHT)
    , m_timestamp(QDateTime::currentMSecsSinceEpoch())
    , m_boundingRectValid(false)
{
    // 设置位置
    setPos(position);
    
    // 加载图片
    if (!loadImage(imagePath)) {
        qWarning() << "ImageItem: 无法加载图片" << imagePath;
        return;
    }
    
    // 计算显示尺寸
    qreal targetWidth = displayWidth > 0 ? displayWidth : DEFAULT_WIDTH;
    m_displaySize = calculateOptimalSize(targetWidth);

    // 应用屏幕适配
    m_displaySize = adaptSize(m_displaySize);
    m_maxHeight = adaptSize(m_maxHeight);
    
    // 设置图形项属性
    setFlag(QGraphicsItem::ItemIsMovable, true);
    setFlag(QGraphicsItem::ItemIsSelectable, true);
    setFlag(QGraphicsItem::ItemSendsGeometryChanges, true);
}

void ImageItem::paint(QPainter* painter, const QStyleOptionGraphicsItem* option, QWidget* widget)
{
    Q_UNUSED(option)
    Q_UNUSED(widget)

    if (m_pixmap.isNull()) {
        // 绘制占位符
        painter->setPen(QPen(Qt::gray, 2, Qt::DashLine));
        painter->setBrush(QBrush(Qt::lightGray, Qt::DiagCrossPattern));
        painter->drawRect(boundingRect());
        
        painter->setPen(Qt::black);
        painter->drawText(boundingRect(), Qt::AlignCenter, "图片加载失败");
        return;
    }

    // 设置高质量渲染
    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->setRenderHint(QPainter::SmoothPixmapTransform, true);

    // 绘制图片
    QRectF targetRect(0, 0, m_displaySize.width(), m_displaySize.height());
    painter->drawPixmap(targetRect, m_pixmap, m_pixmap.rect());
}

QRectF ImageItem::boundingRect() const
{
    if (!m_boundingRectValid) {
        m_cachedBoundingRect = calculateBoundingRect();
        m_boundingRectValid = true;
    }
    return m_cachedBoundingRect;
}

QPainterPath ImageItem::shape() const
{
    QPainterPath path;
    path.addRect(boundingRect());
    return path;
}

bool ImageItem::setImagePath(const QString& imagePath)
{
    if (m_imagePath == imagePath) {
        return true;
    }
    
    if (loadImage(imagePath)) {
        m_imagePath = imagePath;
        updateDisplaySize();
        invalidateBoundingRect();
        update();
        return true;
    }
    
    return false;
}

void ImageItem::setDisplayWidth(qreal width)
{
    if (width <= 0) {
        width = DEFAULT_WIDTH;
    }
    
    QSizeF newSize = calculateOptimalSize(width);
    if (newSize != m_displaySize) {
        m_displaySize = newSize;
        invalidateBoundingRect();
        update();
    }
}

void ImageItem::setMaxHeight(qreal maxHeight)
{
    if (maxHeight <= 0) {
        maxHeight = DEFAULT_MAX_HEIGHT;
    }
    
    if (m_maxHeight != maxHeight) {
        m_maxHeight = maxHeight;
        updateDisplaySize();
        invalidateBoundingRect();
        update();
    }
}

void ImageItem::setPosition(const QPointF& position)
{
    setPos(position);
}

QSizeF ImageItem::calculateOptimalSize(qreal targetWidth) const
{
    if (m_pixmap.isNull()) {
        return QSizeF(targetWidth > 0 ? targetWidth : DEFAULT_WIDTH, 100);
    }
    
    return calculateSizeWithConstraints(m_pixmap.size(), targetWidth);
}

void ImageItem::updateDisplaySize()
{
    if (!m_pixmap.isNull()) {
        m_displaySize = calculateOptimalSize(m_displaySize.width());
        // 应用屏幕适配
        m_displaySize = adaptSize(m_displaySize);
    }
}

QJsonObject ImageItem::toJson() const
{
    QJsonObject json;
    json["type"] = "ImageItem";
    json["imagePath"] = m_imagePath;
    json["position"] = QJsonObject{
        {"x", pos().x()},
        {"y", pos().y()}
    };
    json["displaySize"] = QJsonObject{
        {"width", m_displaySize.width()},
        {"height", m_displaySize.height()}
    };
    json["maxHeight"] = m_maxHeight;
    json["timestamp"] = m_timestamp;
    
    return json;
}

void ImageItem::fromJson(const QJsonObject& json)
{
    if (json["type"].toString() != "ImageItem") {
        return;
    }
    
    // 加载图片路径
    QString imagePath = json["imagePath"].toString();
    if (!imagePath.isEmpty()) {
        setImagePath(imagePath);
    }
    
    // 设置位置
    QJsonObject posObj = json["position"].toObject();
    setPos(posObj["x"].toDouble(), posObj["y"].toDouble());
    
    // 设置显示尺寸
    QJsonObject sizeObj = json["displaySize"].toObject();
    m_displaySize = QSizeF(sizeObj["width"].toDouble(), sizeObj["height"].toDouble());
    
    // 设置最大高度
    m_maxHeight = json["maxHeight"].toDouble(DEFAULT_MAX_HEIGHT);
    
    // 设置时间戳
    m_timestamp = json["timestamp"].toVariant().toLongLong();
    
    invalidateBoundingRect();
    update();
}

qint64 ImageItem::memoryUsage() const
{
    qint64 usage = sizeof(ImageItem);
    usage += m_imagePath.size() * sizeof(QChar);
    
    if (!m_pixmap.isNull()) {
        // 估算像素数据大小
        usage += m_pixmap.width() * m_pixmap.height() * 4; // ARGB32
    }
    
    return usage;
}

QString ImageItem::debugInfo() const
{
    return QString("ImageItem(path=%1, size=%2x%3, pos=%4,%5, valid=%6)")
           .arg(m_imagePath)
           .arg(m_displaySize.width())
           .arg(m_displaySize.height())
           .arg(pos().x())
           .arg(pos().y())
           .arg(isValid());
}

// 私有方法实现
bool ImageItem::loadImage(const QString& imagePath)
{
    if (imagePath.isEmpty()) {
        return false;
    }
    
    // 检查文件是否存在
    QFileInfo fileInfo(imagePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        qWarning() << "ImageItem: 图片文件不存在" << imagePath;
        return false;
    }
    
    // 使用QImageReader进行更好的错误处理
    QImageReader reader(imagePath);
    if (!reader.canRead()) {
        qWarning() << "ImageItem: 不支持的图片格式" << imagePath;
        return false;
    }
    
    // 加载图片
    QImage image = reader.read();
    if (image.isNull()) {
        qWarning() << "ImageItem: 图片加载失败" << imagePath << reader.errorString();
        return false;
    }
    
    m_pixmap = QPixmap::fromImage(image);
    return true;
}

void ImageItem::invalidateBoundingRect()
{
    m_boundingRectValid = false;
}

QRectF ImageItem::calculateBoundingRect() const
{
    return QRectF(0, 0, m_displaySize.width(), m_displaySize.height());
}

QSizeF ImageItem::calculateSizeWithConstraints(const QSizeF& originalSize, qreal targetWidth) const
{
    if (originalSize.isEmpty()) {
        return QSizeF(targetWidth, 100);
    }
    
    // 计算宽高比
    qreal aspectRatio = originalSize.width() / originalSize.height();
    
    // 根据目标宽度计算高度
    qreal width = targetWidth;
    qreal height = width / aspectRatio;
    
    // 应用最大高度限制
    if (height > m_maxHeight) {
        height = m_maxHeight;
        width = height * aspectRatio;
    }
    
    return QSizeF(width, height);
}
