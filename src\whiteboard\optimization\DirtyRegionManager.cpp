#include "DirtyRegionManager.h"
#include <QtMath>
#include <algorithm>

DirtyRegionManager::DirtyRegionManager()
{
}

void DirtyRegionManager::addDirtyRegion(const QRectF& rect)
{
    // 严格验证区域有效性
    if (!rect.isValid() || rect.isEmpty() ||
        rect.width() < 0.1 || rect.height() < 0.1 ||
        qIsNaN(rect.x()) || qIsNaN(rect.y()) ||
        qIsNaN(rect.width()) || qIsNaN(rect.height())) {
        return; // 跳过无效区域
    }

    // 确保区域至少有最小尺寸
    QRectF adjustedRect = ensureMinimumSize(rect);

    // 再次验证调整后的区域
    if (adjustedRect.isValid() && !adjustedRect.isEmpty()) {
        m_dirtyRegions.append(adjustedRect);

        // 自动优化：当区域数量过多时进行合并
        if (m_dirtyRegions.size() > m_maxRegions) {
            optimizeDirtyRegions();
        }
    }
}

void DirtyRegionManager::addDirtyPoint(const QPointF& point, qreal radius)
{
    // 确保半径至少为最小尺寸的一半
    qreal adjustedRadius = qMax(radius, MIN_REGION_SIZE / 2.0);
    QRectF rect(point.x() - adjustedRadius, point.y() - adjustedRadius,
                adjustedRadius * 2, adjustedRadius * 2);
    addDirtyRegion(rect);
}

void DirtyRegionManager::addDirtyPath(const QPointF& from, const QPointF& to, qreal width)
{
    // 创建包含路径的矩形区域
    qreal halfWidth = width / 2.0;
    QRectF rect(qMin(from.x(), to.x()) - halfWidth,
                qMin(from.y(), to.y()) - halfWidth,
                qAbs(to.x() - from.x()) + width,
                qAbs(to.y() - from.y()) + width);
    addDirtyRegion(rect);
}

void DirtyRegionManager::addConnectedRegions(const QRectF& rect1, const QRectF& rect2)
{
    if (rect1.isEmpty() && rect2.isEmpty()) {
        return;
    }

    if (rect1.isEmpty()) {
        addDirtyRegion(rect2);
        return;
    }

    if (rect2.isEmpty()) {
        addDirtyRegion(rect1);
        return;
    }

    // 添加两个区域
    addDirtyRegion(rect1);
    addDirtyRegion(rect2);

    // 如果两个区域不相交且距离较远，添加连接区域
    if (!rect1.intersects(rect2)) {
        qreal distance = calculateDistance(rect1, rect2);
        if (distance > m_mergeThreshold) {
            // 创建连接两个区域的矩形
            QRectF connectingRect = rect1.united(rect2);
            addDirtyRegion(connectingRect);
        }
    }
}

QRectF DirtyRegionManager::getMergedDirtyRegion() const
{
    if (m_dirtyRegions.isEmpty()) {
        return QRectF();
    }

    // 过滤掉无效的区域
    QList<QRectF> validRegions;
    for (const QRectF& region : m_dirtyRegions) {
        if (region.isValid() && !region.isEmpty() &&
            region.width() > 0.1 && region.height() > 0.1) {
            validRegions.append(region);
        }
    }

    if (validRegions.isEmpty()) {
        return QRectF();
    }

    if (validRegions.size() == 1) {
        return validRegions.first();
    }

    // 合并所有有效的脏区域
    QRectF merged = validRegions.first();
    for (int i = 1; i < validRegions.size(); ++i) {
        merged = merged.united(validRegions[i]);
    }

    // 确保合并后的区域仍然有效
    if (!merged.isValid() || merged.isEmpty()) {
        return QRectF();
    }

    return merged;
}

QList<QRectF> DirtyRegionManager::getOptimizedDirtyRegions() const
{
    if (m_dirtyRegions.size() <= 1) {
        return m_dirtyRegions;
    }
    
    QList<QRectF> optimized = m_dirtyRegions;
    
    bool merged = true;
    while (merged && optimized.size() > 1) {
        merged = false;
        for (int i = 0; i < optimized.size() - 1; ++i) {
            for (int j = i + 1; j < optimized.size(); ++j) {
                if (shouldMergeRegions(optimized[i], optimized[j])) {
                    optimized[i] = optimized[i].united(optimized[j]);
                    optimized.removeAt(j);
                    merged = true;
                    break;
                }
            }
            if (merged) break;
        }
    }
    
    return optimized;
}

void DirtyRegionManager::clearDirtyRegions()
{
    m_dirtyRegions.clear();
}

void DirtyRegionManager::optimizeDirtyRegions()
{
    if (m_dirtyRegions.size() <= 1) {
        return;
    }
    
    // 使用优化后的区域列表替换原列表
    m_dirtyRegions = getOptimizedDirtyRegions();
}

bool DirtyRegionManager::hasDirtyRegions() const
{
    return !m_dirtyRegions.isEmpty();
}

bool DirtyRegionManager::shouldUseFullRedraw(const QRectF& viewRect) const
{
    if (m_dirtyRegions.isEmpty() || viewRect.isEmpty()) {
        return false;
    }
    
    // 计算脏区域总面积
    qreal totalDirtyArea = 0.0;
    for (const QRectF& rect : m_dirtyRegions) {
        totalDirtyArea += rect.width() * rect.height();
    }
    
    qreal viewArea = viewRect.width() * viewRect.height();
    qreal dirtyRatio = totalDirtyArea / viewArea;
    
    return dirtyRatio > m_fullRedrawThreshold;
}

bool DirtyRegionManager::shouldMergeRegions(const QRectF& r1, const QRectF& r2) const
{
    // 检查是否重叠
    if (r1.intersects(r2)) {
        return true;
    }
    
    // 检查距离是否小于合并阈值
    qreal distance = calculateDistance(r1, r2);
    return distance <= m_mergeThreshold;
}

qreal DirtyRegionManager::calculateDistance(const QRectF& r1, const QRectF& r2) const
{
    // 计算两个矩形中心点之间的距离
    QPointF center1 = r1.center();
    QPointF center2 = r2.center();
    
    qreal dx = center1.x() - center2.x();
    qreal dy = center1.y() - center2.y();
    
    return qSqrt(dx * dx + dy * dy);
}

void DirtyRegionManager::mergeDirtyRegions()
{
    optimizeDirtyRegions();
}

QRectF DirtyRegionManager::ensureMinimumSize(const QRectF& rect) const
{
    if (rect.isEmpty()) {
        return rect;
    }

    QRectF result = rect;

    // 确保宽度至少为最小尺寸
    if (result.width() < MIN_REGION_SIZE) {
        qreal expansion = (MIN_REGION_SIZE - result.width()) / 2.0;
        result.setLeft(result.left() - expansion);
        result.setRight(result.right() + expansion);
    }

    // 确保高度至少为最小尺寸
    if (result.height() < MIN_REGION_SIZE) {
        qreal expansion = (MIN_REGION_SIZE - result.height()) / 2.0;
        result.setTop(result.top() - expansion);
        result.setBottom(result.bottom() + expansion);
    }

    return result;
}
