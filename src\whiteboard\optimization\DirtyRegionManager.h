#ifndef DIRTYREGIONMANAGER_H
#define DIRTYREGIONMANAGER_H

#include <QRectF>
#include <QPointF>
#include <QList>

/**
 * @brief 脏区域管理器 - 优化重绘性能
 *
 * 核心功能：
 * 1. 收集和管理需要重绘的区域
 * 2. 智能合并相邻的脏区域
 * 3. 提供优化的重绘区域
 */
class DirtyRegionManager
{
public:
    DirtyRegionManager();
    ~DirtyRegionManager() = default;

    // 添加脏区域
    void addDirtyRegion(const QRectF& rect);
    void addDirtyPoint(const QPointF& point, qreal radius = 5.0);
    void addDirtyPath(const QPointF& from, const QPointF& to, qreal width = 2.0);
    void addConnectedRegions(const QRectF& rect1, const QRectF& rect2); // 添加连接的区域

    // 获取脏区域
    QRectF getMergedDirtyRegion() const;
    QList<QRectF> getOptimizedDirtyRegions() const;

    // 管理操作
    void clearDirtyRegions();
    void optimizeDirtyRegions();

    // 查询方法
    bool hasDirtyRegions() const;
    bool shouldUseFullRedraw(const QRectF& viewRect) const;

    // 配置参数
    void setMergeThreshold(qreal threshold) { m_mergeThreshold = threshold; }
    void setMaxRegions(int maxRegions) { m_maxRegions = maxRegions; }

private:
    QList<QRectF> m_dirtyRegions;
    qreal m_mergeThreshold = 20.0;      // 优化：降低合并阈值，减少重绘区域
    int m_maxRegions = 5;               // 优化：减少最大区域数量，强制更积极的合并
    qreal m_fullRedrawThreshold = 0.15; // 优化：降低全屏重绘阈值（15%屏幕面积）

    static constexpr qreal MIN_REGION_SIZE = 20.0;  // 优化：减小最小脏区域尺寸

    // 内部方法
    void mergeDirtyRegions();
    bool shouldMergeRegions(const QRectF& r1, const QRectF& r2) const;
    qreal calculateDistance(const QRectF& r1, const QRectF& r2) const;
    QRectF ensureMinimumSize(const QRectF& rect) const;
};

#endif // DIRTYREGIONMANAGER_H
