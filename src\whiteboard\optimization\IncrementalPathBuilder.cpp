#include "IncrementalPathBuilder.h"
#include "../tools/ShapeToolManager.h"
#include "../core/WhiteBoardTypes.h"
#include <QtMath>



// PathSegment 实现
void IncrementalPathBuilder::PathSegment::addPoint(const QPointF& point)
{
    points.append(point);

    // 优化的增量边界计算：直接扩展现有边界而不是使用united()
    if (boundingRect.isNull()) {
        // 首次添加点：创建初始边界矩形
        boundingRect = QRectF(point.x(), point.y(), 1.0, 1.0);
    } else {
        // 增量扩展边界：只检查和更新需要扩展的边
        qreal left = boundingRect.left();
        qreal top = boundingRect.top();
        qreal right = boundingRect.right();
        qreal bottom = boundingRect.bottom();

        // 检查是否需要扩展边界
        if (point.x() < left) {
            left = point.x();
        } else if (point.x() > right) {
            right = point.x();
        }

        if (point.y() < top) {
            top = point.y();
        } else if (point.y() > bottom) {
            bottom = point.y();
        }

        // 只有当边界确实需要扩展时才更新
        if (left != boundingRect.left() || top != boundingRect.top() ||
            right != boundingRect.right() || bottom != boundingRect.bottom()) {
            boundingRect.setCoords(left, top, right, bottom);
        }
    }
    needsRebuild = true;
}

void IncrementalPathBuilder::PathSegment::clear()
{
    points.clear();
    boundingRect = QRectF();
    needsRebuild = true;
}

// IncrementalPathBuilder 实现
IncrementalPathBuilder::IncrementalPathBuilder()
{
}

void IncrementalPathBuilder::startPath(const QPointF& startPoint)
{
    m_currentPath = QPainterPath();
    m_currentPath.moveTo(startPoint);

    m_pendingSegment.clear();
    m_pendingSegment.addPoint(startPoint);

    m_isBuilding = true;
    m_needsRebuild = false;
    m_lastPoint = startPoint;
    m_startPoint = startPoint;  // 保存起始点



    // 初始化延迟边界计算状态
    m_incrementalBounds = QRectF(startPoint.x(), startPoint.y(), 1.0, 1.0);
    m_boundsNeedUpdate = false;
    invalidateBoundsCache();
}

void IncrementalPathBuilder::addPoint(const QPointF& point)
{
    if (!m_isBuilding) {
        return;
    }



    // 根据工具类型处理点
    if (isFreeDrawTool(m_currentToolType) || m_currentToolType == ToolType::Lasso) {
        handleFreeDrawPoint(point);
    } else {
        handleShapePoint(point);
    }

    m_lastPoint = point;
    updateIncrementalBounds(point);
}

void IncrementalPathBuilder::finishPath()
{
    if (!m_isBuilding) {
        return;
    }

    // 刷新待处理的段
    flushPendingSegment();

    if (m_dashConversionEnabled && !m_currentPath.isEmpty() && m_dashConverter.needsConversion(m_currentPen)) {
        QPainterPath solidPath = m_dashConverter.convertDashToSolid(m_currentPath, m_currentPen);
        if (!solidPath.isEmpty()) {
            m_currentPath = solidPath;
        }
    }


    m_isBuilding = false;
    m_needsRebuild = false;
}

void IncrementalPathBuilder::cancelPath()
{
    m_currentPath = QPainterPath();
    m_pendingSegment.clear();
    m_isBuilding = false;
    m_needsRebuild = false;
    m_incrementalBounds = QRectF();



    // 重置延迟边界计算状态
    m_boundsNeedUpdate = false;
    invalidateBoundsCache();
}

QPainterPath IncrementalPathBuilder::getCurrentPath() const
{
    if (m_needsRebuild) {
        // 需要重建路径时，创建临时路径
        QPainterPath tempPath = m_currentPath;
        
        // 添加待处理段的点
        if (!m_pendingSegment.isEmpty()) {
            for (const QPointF& point : m_pendingSegment.points) {
                tempPath.lineTo(point);
            }
        }
        
        return tempPath;
    }
    
    return m_currentPath;
}

QRectF IncrementalPathBuilder::getCurrentBounds() const
{
    // 延迟计算：只在需要时计算当前路径边界
    if (!m_currentBoundsValid) {
        m_cachedCurrentBounds = getCurrentPath().boundingRect();
        m_currentBoundsValid = true;
    }
    return m_cachedCurrentBounds;
}

QRectF IncrementalPathBuilder::getIncrementalBounds() const
{
    // 延迟更新增量边界
    updateBoundsIfNeeded();
    return m_incrementalBounds;
}

void IncrementalPathBuilder::rebuildPath()
{
    if (!m_needsRebuild) {
        return;
    }
    
    // 刷新待处理的段到主路径
    flushPendingSegment();
    m_needsRebuild = false;
}

void IncrementalPathBuilder::clearCache()
{
    m_pendingSegment.clear();
    m_incrementalBounds = QRectF();
    m_needsRebuild = false;

    // 重置延迟边界计算状态
    m_boundsNeedUpdate = false;
    invalidateBoundsCache();
}

void IncrementalPathBuilder::addPointToPendingSegment(const QPointF& point)
{
    m_pendingSegment.addPoint(point);
    m_needsRebuild = true;
    
    // 当待处理段达到批量大小时，刷新到主路径
    if (m_pendingSegment.pointCount() >= m_batchSize) {
        flushPendingSegment();
    }
}

void IncrementalPathBuilder::flushPendingSegment()
{
    if (m_pendingSegment.isEmpty()) {
        return;
    }

    // 将待处理段的点添加到主路径
    for (const QPointF& point : m_pendingSegment.points) {
        m_currentPath.lineTo(point);
    }

    m_pendingSegment.clear();
    m_needsRebuild = false;

    // 路径发生变化，使边界缓存失效
    invalidateBoundsCache();
}

void IncrementalPathBuilder::updateIncrementalBounds(const QPointF& point)
{
    Q_UNUSED(point)  // 参数未使用，但保留接口兼容性

    // 标记边界需要更新，但不立即计算
    m_boundsNeedUpdate = true;

    // 使边界缓存失效
    invalidateBoundsCache();
}

// 延迟边界计算的实现方法
void IncrementalPathBuilder::invalidateBoundsCache() const
{
    m_currentBoundsValid = false;
}

void IncrementalPathBuilder::updateBoundsIfNeeded() const
{
    if (!m_boundsNeedUpdate) {
        return;
    }

    // 只有在真正需要边界时才进行计算
    // 这里可以根据实际需求决定是否立即计算
    // 对于高频调用的场景，可以进一步延迟到真正使用时
    m_boundsNeedUpdate = false;
}

void IncrementalPathBuilder::handleFreeDrawPoint(const QPointF& point)
{
    // 自由绘制：直接添加点到待处理段
    addPointToPendingSegment(point);
}

void IncrementalPathBuilder::handleShapePoint(const QPointF& point)
{
    ShapeToolManager* manager = ShapeToolManager::instance();

    if (manager && manager->hasToolType(m_currentToolType)) {
        m_currentPath = manager->createPath(m_currentToolType, m_startPoint, point);
        m_pendingSegment.clear();
        m_needsRebuild = false;
    } else {
        m_currentPath = QPainterPath();
        m_currentPath.moveTo(m_startPoint);
        m_currentPath.lineTo(point);
        m_pendingSegment.clear();
        m_needsRebuild = false;
    }
}




