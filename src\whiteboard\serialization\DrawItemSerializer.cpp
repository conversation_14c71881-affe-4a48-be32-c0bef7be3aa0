#include "DrawItemSerializer.h"
#include "../graphics/DrawItem.h"

#include <QFile>
#include <QJsonParseError>
#include <QJsonDocument>
#include <QDateTime>

// 单个图形项序列化
QJsonObject DrawItemSerializer::serializeItem(const DrawItem* item)
{
    if (!item) {
        return QJsonObject();
    }

    // 直接使用DrawItem的toJson方法
    QJsonObject json = item->toJson();
    
    // 添加版本信息
    json["version"] = SERIALIZATION_VERSION;
    
    // 添加序列化器标识
    json["serializer"] = "DrawItemSerializer";
    
    return json;
}

DrawItem* DrawItemSerializer::deserializeItem(const QJsonObject& json)
{
    if (!isValidJson(json)) {
        return nullptr;
    }

    int version = getSerializationVersion(json);
    if (!isVersionCompatible(version)) {
        return nullptr;
    }
    
    // 从JSON获取基本信息
    ToolType toolType = static_cast<ToolType>(json["type"].toInt());
    
    // 创建基本的DrawItem（需要路径、画笔、画刷信息）
    QPainterPath path; // 临时路径，后续会被fromJson覆盖
    QPen pen;
    QBrush brush;
    
    // 从JSON恢复画笔信息
    if (json.contains("pen")) {
        QJsonObject penObj = json["pen"].toObject();
        pen.setColor(QColor(penObj["color"].toString()));
        pen.setWidthF(penObj["width"].toDouble());
        pen.setStyle(static_cast<Qt::PenStyle>(penObj["style"].toInt()));

        // 恢复线帽和连接样式，如果没有保存则使用抗锯齿默认值
        if (penObj.contains("capStyle")) {
            pen.setCapStyle(static_cast<Qt::PenCapStyle>(penObj["capStyle"].toInt()));
        } else {
            pen.setCapStyle(Qt::RoundCap);
        }

        if (penObj.contains("joinStyle")) {
            pen.setJoinStyle(static_cast<Qt::PenJoinStyle>(penObj["joinStyle"].toInt()));
        } else {
            pen.setJoinStyle(Qt::RoundJoin);
        }

        // 恢复自定义虚线模式
        if (pen.style() == Qt::CustomDashLine && penObj.contains("dashPattern")) {
            QJsonArray dashArray = penObj["dashPattern"].toArray();
            QVector<qreal> dashPattern;
            for (const QJsonValue& value : dashArray) {
                dashPattern.append(value.toDouble());
            }
            pen.setDashPattern(dashPattern);
        }
    }
    
    // 从JSON恢复画刷信息
    if (json.contains("brush")) {
        QJsonObject brushObj = json["brush"].toObject();
        brush.setColor(QColor(brushObj["color"].toString()));
        brush.setStyle(static_cast<Qt::BrushStyle>(brushObj["style"].toInt()));
    }
    
    // 创建DrawItem实例
    DrawItem* item = new DrawItem(path, pen, brush, toolType);
    
    // 使用fromJson恢复完整状态
    item->fromJson(json);
    
    return item;
}

// 批量序列化
QJsonArray DrawItemSerializer::serializeItems(const QList<DrawItem*>& items)
{
    QJsonArray jsonArray;
    
    for (const DrawItem* item : items) {
        if (item) {
            QJsonObject itemJson = serializeItem(item);
            if (!itemJson.isEmpty()) {
                jsonArray.append(itemJson);
            }
        }
    }
    
    return jsonArray;
}

QList<DrawItem*> DrawItemSerializer::deserializeItems(const QJsonArray& jsonArray)
{
    QList<DrawItem*> items;
    
    for (const QJsonValue& value : jsonArray) {
        if (value.isObject()) {
            DrawItem* item = deserializeItem(value.toObject());
            if (item) {
                items.append(item);
            }
        }
    }
    
    return items;
}

// 完整场景序列化
QJsonObject DrawItemSerializer::serializeScene(const QList<DrawItem*>& items, const QVariantMap& metadata)
{
    QJsonObject sceneJson;
    
    // 添加场景元数据
    sceneJson["version"] = SERIALIZATION_VERSION;
    sceneJson["type"] = "WhiteBoardScene";
    sceneJson["timestamp"] = QDateTime::currentMSecsSinceEpoch();
    
    // 添加用户元数据
    if (!metadata.isEmpty()) {
        QJsonObject metaObj;
        for (auto it = metadata.begin(); it != metadata.end(); ++it) {
            metaObj[it.key()] = QJsonValue::fromVariant(it.value());
        }
        sceneJson["metadata"] = metaObj;
    }
    
    // 序列化图形项
    QJsonArray itemsArray = serializeItems(items);
    sceneJson["items"] = itemsArray;
    sceneJson["itemCount"] = items.size();
    
    return sceneJson;
}

QList<DrawItem*> DrawItemSerializer::deserializeScene(const QJsonObject& sceneJson, QVariantMap* metadata)
{
    QList<DrawItem*> items;
    
    if (sceneJson["type"].toString() != "WhiteBoardScene") {
        return items;
    }

    int version = sceneJson["version"].toInt();
    if (!isVersionCompatible(version)) {
        return items;
    }
    
    // 提取元数据
    if (metadata && sceneJson.contains("metadata")) {
        QJsonObject metaObj = sceneJson["metadata"].toObject();
        for (auto it = metaObj.begin(); it != metaObj.end(); ++it) {
            (*metadata)[it.key()] = it.value().toVariant();
        }
    }
    
    // 反序列化图形项
    if (sceneJson.contains("items")) {
        QJsonArray itemsArray = sceneJson["items"].toArray();
        items = deserializeItems(itemsArray);
    }
    
    return items;
}

// 文件操作
bool DrawItemSerializer::saveToFile(const QString& filePath, const QList<DrawItem*>& items, const QVariantMap& metadata)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }
    
    QJsonObject sceneJson = serializeScene(items, metadata);
    QJsonDocument doc(sceneJson);
    
    qint64 bytesWritten = file.write(doc.toJson());
    file.close();
    
    if (bytesWritten == -1) {
        return false;
    }

    return true;
}

QList<DrawItem*> DrawItemSerializer::loadFromFile(const QString& filePath, QVariantMap* metadata)
{
    QList<DrawItem*> items;
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return items;
    }
    
    QByteArray data = file.readAll();
    file.close();
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    
    if (error.error != QJsonParseError::NoError) {
        return items;
    }

    items = deserializeScene(doc.object(), metadata);
    return items;
}


// 验证和兼容性
bool DrawItemSerializer::isValidJson(const QJsonObject& json)
{
    return json.contains("type") && json.contains("pen") && json.contains("brush");
}

int DrawItemSerializer::getSerializationVersion(const QJsonObject& json)
{
    return json["version"].toInt(1); // 默认版本1
}

bool DrawItemSerializer::isVersionCompatible(int version)
{
    return version >= 1 && version <= SERIALIZATION_VERSION;
}




