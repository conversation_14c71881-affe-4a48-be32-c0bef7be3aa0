#ifndef DRAWITEMSERIALIZER_H
#define DRAWITEMSERIALIZER_H

#include <QJsonObject>
#include <QJsonArray>
#include <QJsonDocument>
#include <QVariant>
#include <QPointF>
#include <QRectF>
#include <QLineF>
#include <QPolygonF>
#include <QPen>
#include <QColor>
#include <QPainterPath>
#include <QSizeF>

class DrawItem;

/**
 * @brief 图形项目序列化器 - 统一的序列化/反序列化接口
 * 
 * 核心功能：
 * 1. 支持所有DrawItem子类的序列化
 * 2. JSON格式存储，便于跨平台和版本兼容
 * 3. 类型安全的序列化/反序列化
 * 4. 支持批量操作和增量更新
 * 5. 版本控制和向后兼容性
 */
class DrawItemSerializer
{
public:
    // 序列化版本控制
    static const int SERIALIZATION_VERSION = 1;

public:
    // 单个图形项序列化
    static QJsonObject serializeItem(const DrawItem* item);
    static DrawItem* deserializeItem(const QJsonObject& json);
    
    // 批量序列化
    static QJsonArray serializeItems(const QList<DrawItem*>& items);
    static QList<DrawItem*> deserializeItems(const QJsonArray& jsonArray);
    
    // 完整场景序列化
    static QJsonObject serializeScene(const QList<DrawItem*>& items, const QVariantMap& metadata = QVariantMap());
    static QList<DrawItem*> deserializeScene(const QJsonObject& sceneJson, QVariantMap* metadata = nullptr);
    
    // 文件操作
    static bool saveToFile(const QString& filePath, const QList<DrawItem*>& items, const QVariantMap& metadata = QVariantMap());
    static QList<DrawItem*> loadFromFile(const QString& filePath, QVariantMap* metadata = nullptr);  
    
    // 验证和兼容性
    static bool isValidJson(const QJsonObject& json);
    static int getSerializationVersion(const QJsonObject& json);
    static bool isVersionCompatible(int version);


};

#endif // DRAWITEMSERIALIZER_H
