#include "SceneSerializer.h"
#include "DrawItemSerializer.h"
#include "../core/WhiteBoardScene.h"
#include "../graphics/DrawItem.h"
#include "../graphics/ImageItem.h"

#include <QTimer>
#include <QDir>
#include <QFile>
#include <QFileInfo>
#include <QJsonDocument>
#include <QJsonParseError>
#include <QDateTime>
#include <QStandardPaths>
#include <QCoreApplication>

SceneSerializer::SceneSerializer(QObject* parent)
    : QObject(parent)
    , m_scene(nullptr)
    , m_autoSaveTimer(nullptr)
    , m_autoSaveEnabled(false)
    , m_autoSaveInterval(300) // 5分钟
    , m_maxBackupCount(10)
    , m_sceneModified(false)
{
    // 设置默认目录
    m_autoSaveDirectory = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/WhiteBoard/AutoSave";
    m_backupDirectory = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/WhiteBoard/Backups";
    
    // 确保目录存在
    ensureDirectoryExists(m_autoSaveDirectory);
    ensureDirectoryExists(m_backupDirectory);
}

SceneSerializer::~SceneSerializer()
{
    cleanupAutoSave();
}

// 场景设置
void SceneSerializer::setScene(WhiteBoardScene* scene)
{
    if (m_scene) {
        // 断开旧场景的连接
        disconnect(m_scene, &WhiteBoardScene::sceneChanged, this, &SceneSerializer::onSceneChanged);
    }
    
    m_scene = scene;
    
    if (m_scene) {
        connect(m_scene, &WhiteBoardScene::sceneChanged, this, &SceneSerializer::onSceneChanged);
    }
}

WhiteBoardScene* SceneSerializer::getScene() const
{
    return m_scene;
}

// 完整场景序列化
bool SceneSerializer::saveScene(const QString& filePath, const QVariantMap& metadata)
{
    if (!m_scene) {
        setLastError("No scene set");
        return false;
    }
    
    try {
        // 合并元数据
        QVariantMap combinedMetadata = m_sceneMetadata;
        for (auto it = metadata.begin(); it != metadata.end(); ++it) {
            combinedMetadata[it.key()] = it.value();
        }
        
        // 获取所有图形项
        QList<DrawItem*> allDrawItems;
        QList<ImageItem*> allImageItems;
        QList<QGraphicsItem*> sceneItems = m_scene->items();

        for (QGraphicsItem* item : sceneItems) {
            int itemType = item->type();

            // 使用精确的类型匹配
            if (itemType == ImageItem::ImageItemType) {
                ImageItem* imageItem = static_cast<ImageItem*>(item);
                allImageItems.append(imageItem);
            } else if (itemType == DrawItem::DrawItemType) {
                DrawItem* drawItem = static_cast<DrawItem*>(item);
                allDrawItems.append(drawItem);
            }
        }
        
        // 使用扩展的序列化方法保存
        bool success = saveToFileWithImages(filePath, allDrawItems, allImageItems, combinedMetadata);
        
        if (success) {
            m_currentFilePath = filePath;
            m_lastSaveTime = QDateTime::currentDateTime();
            m_sceneModified = false;
            emit sceneSaved(filePath);
        } else {
            setLastError("Failed to save scene file");
        }
        
        return success;
        
    } catch (const std::exception& e) {
        setLastError(QString("Exception during save: %1").arg(e.what()));
        return false;
    }
}

bool SceneSerializer::loadScene(const QString& filePath, QVariantMap* metadata)
{
    if (!m_scene) {
        setLastError("No scene set");
        return false;
    }
    
    if (!isValidSceneFile(filePath)) {
        setLastError("Invalid scene file");
        return false;
    }
    
    try {
        // 清空当前场景
        m_scene->clear();
        
        // 加载图形项
        QVariantMap loadedMetadata;
        QList<DrawItem*> drawItems;
        QList<ImageItem*> imageItems;

        bool success = loadFromFileWithImages(filePath, drawItems, imageItems, &loadedMetadata);

        if (!success && QFileInfo(filePath).size() > 0) {
            setLastError("Failed to load items from file");
            return false;
        }

        // 添加到场景
        for (DrawItem* item : drawItems) {
            if (item) {
                m_scene->addGraphicsItem(item);
            }
        }

        for (ImageItem* item : imageItems) {
            if (item) {
                m_scene->addGraphicsItem(item);
            }
        }

        // 强制处理UI更新事件，确保导入的图形立即显示
        QCoreApplication::processEvents();
        
        // 更新元数据
        if (metadata) {
            *metadata = loadedMetadata;
        }
        m_sceneMetadata = loadedMetadata;
        
        m_currentFilePath = filePath;
        m_sceneModified = false;
        emit sceneLoaded(filePath);
        
        return true;
        
    } catch (const std::exception& e) {
        setLastError(QString("Exception during load: %1").arg(e.what()));
        return false;
    }
}

// JSON格式序列化
QJsonObject SceneSerializer::serializeScene(const QVariantMap& metadata)
{
    if (!m_scene) {
        return QJsonObject();
    }
    
    // 获取所有图形项
    QList<DrawItem*> allDrawItems;
    QList<ImageItem*> allImageItems;
    QList<QGraphicsItem*> sceneItems = m_scene->items();

    for (QGraphicsItem* item : sceneItems) {
        if (DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item)) {
            allDrawItems.append(drawItem);
        } else if (ImageItem* imageItem = qgraphicsitem_cast<ImageItem*>(item)) {
            allImageItems.append(imageItem);
        }
    }

    // 合并元数据
    QVariantMap combinedMetadata = m_sceneMetadata;
    for (auto it = metadata.begin(); it != metadata.end(); ++it) {
        combinedMetadata[it.key()] = it.value();
    }

    return serializeSceneWithImages(allDrawItems, allImageItems, combinedMetadata);
}

bool SceneSerializer::deserializeScene(const QJsonObject& sceneJson, QVariantMap* metadata)
{
    if (!m_scene) {
        setLastError("No scene set");
        return false;
    }
    
    // 清空当前场景
    m_scene->clear();
    
    // 反序列化图形项
    QVariantMap loadedMetadata;
    QList<DrawItem*> drawItems;
    QList<ImageItem*> imageItems;

    bool success = deserializeSceneWithImages(sceneJson, drawItems, imageItems, &loadedMetadata);

    if (!success) {
        setLastError("Failed to deserialize scene");
        return false;
    }

    // 添加到场景
    for (DrawItem* item : drawItems) {
        if (item) {
            m_scene->addGraphicsItem(item);
        }
    }

    for (ImageItem* item : imageItems) {
        if (item) {
            m_scene->addGraphicsItem(item);
        }
    }

    if (m_scene) {
        m_scene->update();
    }
    
    // 更新元数据
    if (metadata) {
        *metadata = loadedMetadata;
    }
    m_sceneMetadata = loadedMetadata;
    
    m_sceneModified = false;
    return true;
}

// 自动保存功能
void SceneSerializer::enableAutoSave(bool enabled, int intervalSeconds)
{
    m_autoSaveEnabled = enabled;
    m_autoSaveInterval = intervalSeconds;
    
    if (enabled) {
        setupAutoSave();
    } else {
        cleanupAutoSave();
    }
}

void SceneSerializer::setAutoSaveDirectory(const QString& directory)
{
    m_autoSaveDirectory = directory;
    ensureDirectoryExists(m_autoSaveDirectory);
}

QString SceneSerializer::getAutoSaveDirectory() const
{
    return m_autoSaveDirectory;
}

// 元数据管理
void SceneSerializer::setSceneMetadata(const QVariantMap& metadata)
{
    m_sceneMetadata = metadata;
    m_sceneModified = true;
}

QVariantMap SceneSerializer::getSceneMetadata() const
{
    return m_sceneMetadata;
}

void SceneSerializer::updateMetadata(const QString& key, const QVariant& value)
{
    m_sceneMetadata[key] = value;
    m_sceneModified = true;
}



// 错误处理
QString SceneSerializer::getLastError() const
{
    return m_lastError;
}

void SceneSerializer::clearLastError()
{
    m_lastError.clear();
}

// 私有槽函数
void SceneSerializer::performAutoSave()
{
    if (!m_autoSaveEnabled || !m_scene || !m_sceneModified) {
        return;
    }
    
    QString autoSaveFile = generateAutoSaveFileName();
    if (saveScene(autoSaveFile)) {
        emit autoSaveCompleted(autoSaveFile);
    } else {
        qWarning() << "Auto-save failed:" << getLastError();
    }
}

void SceneSerializer::onSceneChanged()
{
    m_sceneModified = true;
}

// 私有方法实现
void SceneSerializer::setupAutoSave()
{
    if (!m_autoSaveTimer) {
        m_autoSaveTimer = new QTimer(this);
        connect(m_autoSaveTimer, &QTimer::timeout, this, &SceneSerializer::performAutoSave);
    }

    m_autoSaveTimer->start(m_autoSaveInterval * 1000);
}

void SceneSerializer::cleanupAutoSave()
{
    if (m_autoSaveTimer) {
        m_autoSaveTimer->stop();
        m_autoSaveTimer->deleteLater();
        m_autoSaveTimer = nullptr;
    }
}

QString SceneSerializer::generateAutoSaveFileName() const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    return QDir(m_autoSaveDirectory).filePath(QString("autosave_%1.wbd").arg(timestamp));
}

QString SceneSerializer::generateBackupFileName() const
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    return QDir(m_backupDirectory).filePath(QString("backup_%1.wbd").arg(timestamp));
}





bool SceneSerializer::ensureDirectoryExists(const QString& dirPath)
{
    QDir dir;
    if (!dir.exists(dirPath)) {
        return dir.mkpath(dirPath);
    }
    return true;
}

bool SceneSerializer::isValidSceneFile(const QString& filePath)
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isReadable()) {
        return false;
    }

    // 简单的文件格式验证
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QByteArray data = file.readAll(); // 读取完整文件进行验证
    file.close();

    // 检查是否是有效的JSON
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        return false;
    }

    // 检查是否包含必要的场景字段
    QJsonObject rootObj = doc.object();

    // 检查基本字段
    bool hasBasicStructure = rootObj.contains("type") && rootObj.contains("version");

    // 检查是否有新格式的字段（drawItems + imageItems）或旧格式的字段（items）
    bool hasNewFormat = rootObj.contains("drawItems") || rootObj.contains("imageItems");
    bool hasOldFormat = rootObj.contains("items");

    bool hasValidStructure = hasBasicStructure && (hasNewFormat || hasOldFormat);

    return hasValidStructure;
}

qint64 SceneSerializer::getFileSize(const QString& filePath)
{
    QFileInfo fileInfo(filePath);
    return fileInfo.exists() ? fileInfo.size() : 0;
}

void SceneSerializer::setLastError(const QString& error)
{
    m_lastError = error;
    qWarning() << "SceneSerializer Error:" << error;
    emit errorOccurred(error);
}

// 备份管理
bool SceneSerializer::createBackup(const QString& backupPath)
{
    if (!m_scene) {
        setLastError("No scene set");
        return false;
    }

    QString actualBackupPath = backupPath.isEmpty() ? generateBackupFileName() : backupPath;

    QVariantMap backupMetadata = m_sceneMetadata;
    backupMetadata["backup_created"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    backupMetadata["original_file"] = m_currentFilePath;

    bool success = saveScene(actualBackupPath, backupMetadata);



    return success;
}

bool SceneSerializer::restoreFromBackup(const QString& backupPath)
{
    if (!QFileInfo::exists(backupPath)) {
        setLastError("Backup file does not exist");
        return false;
    }

    QVariantMap metadata;
    bool success = loadScene(backupPath, &metadata);

    if (success) {
        updateMetadata("restored_from_backup", backupPath);
        updateMetadata("restore_time", QDateTime::currentDateTime().toString(Qt::ISODate));
    }

    return success;
}

// 扩展的序列化方法，支持ImageItem
bool SceneSerializer::saveToFileWithImages(const QString& filePath,
                                          const QList<DrawItem*>& drawItems,
                                          const QList<ImageItem*>& imageItems,
                                          const QVariantMap& metadata)
{
    QJsonObject sceneJson = serializeSceneWithImages(drawItems, imageItems, metadata);

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        setLastError("Cannot open file for writing: " + filePath);
        return false;
    }

    QJsonDocument doc(sceneJson);
    qint64 bytesWritten = file.write(doc.toJson());
    file.close();

    if (bytesWritten == -1) {
        setLastError("Failed to write to file: " + filePath);
        return false;
    }

    return true;
}

bool SceneSerializer::loadFromFileWithImages(const QString& filePath,
                                           QList<DrawItem*>& drawItems,
                                           QList<ImageItem*>& imageItems,
                                           QVariantMap* metadata)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        setLastError("Cannot open file for reading: " + filePath);
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);

    if (error.error != QJsonParseError::NoError) {
        setLastError("JSON parse error: " + error.errorString());
        return false;
    }

    return deserializeSceneWithImages(doc.object(), drawItems, imageItems, metadata);
}

QJsonObject SceneSerializer::serializeSceneWithImages(const QList<DrawItem*>& drawItems,
                                                     const QList<ImageItem*>& imageItems,
                                                     const QVariantMap& metadata)
{
    QJsonObject sceneJson;

    // 添加场景元数据
    sceneJson["version"] = 1;
    sceneJson["type"] = "WhiteBoardScene";
    sceneJson["timestamp"] = QDateTime::currentMSecsSinceEpoch();

    // 添加用户元数据
    if (!metadata.isEmpty()) {
        QJsonObject metaObj;
        for (auto it = metadata.begin(); it != metadata.end(); ++it) {
            metaObj[it.key()] = QJsonValue::fromVariant(it.value());
        }
        sceneJson["metadata"] = metaObj;
    }

    // 序列化DrawItem
    QJsonArray drawItemsArray;
    for (const DrawItem* item : drawItems) {
        if (item && item->type() == DrawItem::DrawItemType) {
            QJsonObject itemJson = item->toJson();
            itemJson["itemClass"] = "DrawItem";
            drawItemsArray.append(itemJson);
        }
    }
    sceneJson["drawItems"] = drawItemsArray;
    sceneJson["drawItemCount"] = drawItems.size();

    // 序列化ImageItem
    QJsonArray imageItemsArray;
    for (const ImageItem* item : imageItems) {
        if (item && item->isValid()) {
            // 检查图片路径是否有效
            QString imagePath = item->imagePath();
            if (imagePath.isEmpty()) {
                qWarning() << "SceneSerializer: ImageItem路径为空，跳过序列化";
                continue;
            }

            QJsonObject itemJson = item->toJson();
            itemJson["itemClass"] = "ImageItem";
            imageItemsArray.append(itemJson);
        }
    }
    sceneJson["imageItems"] = imageItemsArray;
    sceneJson["imageItemCount"] = imageItems.size();

    // 总计数
    sceneJson["totalItemCount"] = drawItems.size() + imageItems.size();

    return sceneJson;
}

bool SceneSerializer::deserializeSceneWithImages(const QJsonObject& sceneJson,
                                                QList<DrawItem*>& drawItems,
                                                QList<ImageItem*>& imageItems,
                                                QVariantMap* metadata)
{
    if (sceneJson["type"].toString() != "WhiteBoardScene") {
        setLastError("Invalid scene type");
        return false;
    }

    int version = sceneJson["version"].toInt();
    if (version < 1) {
        setLastError("Unsupported scene version");
        return false;
    }

    // 提取元数据
    if (metadata && sceneJson.contains("metadata")) {
        QJsonObject metaObj = sceneJson["metadata"].toObject();
        for (auto it = metaObj.begin(); it != metaObj.end(); ++it) {
            (*metadata)[it.key()] = it.value().toVariant();
        }
    }

    // 反序列化DrawItem
    if (sceneJson.contains("drawItems")) {
        QJsonArray drawItemsArray = sceneJson["drawItems"].toArray();
        for (const QJsonValue& value : drawItemsArray) {
            QJsonObject itemJson = value.toObject();
            if (itemJson["itemClass"].toString() == "DrawItem") {
                DrawItem* item = DrawItemSerializer::deserializeItem(itemJson);
                if (item) {
                    drawItems.append(item);
                }
            }
        }
    }

    // 反序列化ImageItem
    if (sceneJson.contains("imageItems")) {
        QJsonArray imageItemsArray = sceneJson["imageItems"].toArray();
        for (const QJsonValue& value : imageItemsArray) {
            QJsonObject itemJson = value.toObject();
            if (itemJson["itemClass"].toString() == "ImageItem") {
                ImageItem* item = new ImageItem("", QPointF(0, 0));
                item->fromJson(itemJson);
                if (item->isValid()) {
                    imageItems.append(item);
                } else {
                    qWarning() << "SceneSerializer: ImageItem反序列化失败，路径:" << itemJson["imagePath"].toString();
                    delete item;
                }
            }
        }
    }

    return true;
}
