#ifndef SCENESERIALIZER_H
#define SCENESERIALIZER_H

#include <QObject>
#include <QJsonObject>
#include <QJsonArray>
#include <QVariantMap>
#include <QList>
#include <QString>
#include <QSizeF>
#include <QTimer>

class WhiteBoardScene;
class DrawItem;
class ImageItem;

/**
 * @brief 场景序列化管理器 - 管理整个白板场景的序列化
 * 
 * 核心功能：
 * 1. 完整场景的保存和加载
 * 2. 增量保存和加载
 * 3. 场景元数据管理
 * 4. 版本控制和兼容性处理
 * 5. 自动备份和恢复
 */
class SceneSerializer : public QObject
{
    Q_OBJECT

public:
    explicit SceneSerializer(QObject* parent = nullptr);
    ~SceneSerializer();

    // 场景设置
    void setScene(WhiteBoardScene* scene);
    WhiteBoardScene* getScene() const;

    // 完整场景序列化
    bool saveScene(const QString& filePath, const QVariantMap& metadata = QVariantMap());
    bool loadScene(const QString& filePath, QVariantMap* metadata = nullptr);
    
    // JSON格式序列化
    QJsonObject serializeScene(const QVariantMap& metadata = QVariantMap());
    bool deserializeScene(const QJsonObject& sceneJson, QVariantMap* metadata = nullptr);
    
    // 增量序列化（只保存变更的项目）
    bool saveIncremental(const QString& filePath, const QList<DrawItem*>& newItems, 
                        const QList<DrawItem*>& modifiedItems, 
                        const QList<qint64>& deletedItemIds);
    bool loadIncremental(const QString& filePath);
    
    // 自动保存功能
    void enableAutoSave(bool enabled, int intervalSeconds = 300); // 默认5分钟
    void setAutoSaveDirectory(const QString& directory);
    QString getAutoSaveDirectory() const;
    
    // 备份管理
    bool createBackup(const QString& backupPath);
    bool restoreFromBackup(const QString& backupPath);
    QStringList getAvailableBackups() const;
    
    // 元数据管理
    void setSceneMetadata(const QVariantMap& metadata);
    QVariantMap getSceneMetadata() const;
    void updateMetadata(const QString& key, const QVariant& value);
    

    
    // 验证和修复
    bool validateSceneFile(const QString& filePath);
    bool repairSceneFile(const QString& filePath, const QString& repairedFilePath);
    
    // 导出功能
    bool exportToJson(const QString& filePath, bool prettyFormat = true);
    bool exportToXml(const QString& filePath);

    // 增强的导出功能
    bool exportToSvg(const QString& filePath, const QSizeF& size = QSizeF());
    bool exportToPdf(const QString& filePath, const QSizeF& size = QSizeF());
    QString exportToJsonString(bool prettyFormat = true);
    QByteArray exportToCompressedData();
    
    // 错误处理
    QString getLastError() const;
    void clearLastError();

signals:
    void sceneLoaded(const QString& filePath);
    void sceneSaved(const QString& filePath);
    void autoSaveCompleted(const QString& filePath);
    void errorOccurred(const QString& error);

private slots:
    void performAutoSave();
    void onSceneChanged();

private:
    // 内部方法
    void setupAutoSave();
    void cleanupAutoSave();
    QString generateAutoSaveFileName() const;
    QString generateBackupFileName() const;

    // 扩展的序列化方法，支持ImageItem
    bool saveToFileWithImages(const QString& filePath,
                             const QList<DrawItem*>& drawItems,
                             const QList<ImageItem*>& imageItems,
                             const QVariantMap& metadata);
    bool loadFromFileWithImages(const QString& filePath,
                               QList<DrawItem*>& drawItems,
                               QList<ImageItem*>& imageItems,
                               QVariantMap* metadata);
    QJsonObject serializeSceneWithImages(const QList<DrawItem*>& drawItems,
                                        const QList<ImageItem*>& imageItems,
                                        const QVariantMap& metadata);
    bool deserializeSceneWithImages(const QJsonObject& sceneJson,
                                   QList<DrawItem*>& drawItems,
                                   QList<ImageItem*>& imageItems,
                                   QVariantMap* metadata);

    // 文件操作辅助
    bool ensureDirectoryExists(const QString& dirPath);
    bool isValidSceneFile(const QString& filePath);
    qint64 getFileSize(const QString& filePath);

    // 错误处理
    void setLastError(const QString& error);

private:
    WhiteBoardScene* m_scene;
    QVariantMap m_sceneMetadata;
    QString m_lastError;
    
    // 自动保存
    QTimer* m_autoSaveTimer;
    bool m_autoSaveEnabled;
    QString m_autoSaveDirectory;
    int m_autoSaveInterval;
    
    // 备份管理
    QString m_backupDirectory;
    int m_maxBackupCount;
    
    // 状态跟踪
    bool m_sceneModified;
    QString m_currentFilePath;
    QDateTime m_lastSaveTime;
};

#endif // SCENESERIALIZER_H
