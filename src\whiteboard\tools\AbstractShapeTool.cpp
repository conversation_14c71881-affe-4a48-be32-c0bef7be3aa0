#include "AbstractShapeTool.h"
#include <QtMath>
#include <QHash>

// 静态成员初始化
QHash<ToolType, AbstractShapeTool*> AbstractShapeTool::s_toolRegistry;

AbstractShapeTool::AbstractShapeTool(ToolType toolType)
    : m_toolType(toolType)
{
}

void AbstractShapeTool::registerTool(ToolType toolType, AbstractShapeTool* tool)
{
    if (tool) {
        s_toolRegistry[toolType] = tool;
    }
}

AbstractShapeTool* AbstractShapeTool::getTool(ToolType toolType)
{
    return s_toolRegistry.value(toolType, nullptr);
}

QList<ToolType> AbstractShapeTool::getAvailableTools()
{
    return s_toolRegistry.keys();
}

QRectF AbstractShapeTool::applyConstraints(const QRectF& rect) const
{
    QRectF result = rect;

    switch (m_config.constraint) {
    case ConstraintType::KeepRatio:
    case ConstraintType::FixedRatio:
        {
            qreal width = result.width();
            qreal height = result.height();
            qreal size = qMin(qAbs(width), qAbs(height));

            if (m_config.constraint == ConstraintType::FixedRatio) {
                size = qMin(size, size / m_config.fixedRatio);
            }

            // 保持拖拽方向的符号
            qreal newWidth = (width >= 0) ? size : -size;
            qreal newHeight = (height >= 0) ? size : -size;

            result = QRectF(result.topLeft(), QSizeF(newWidth, newHeight));
        }
        break;
    case ConstraintType::Symmetric:
        {
            QPointF center = result.center();
            qreal maxRadius = qMax(qAbs(result.width()), qAbs(result.height())) / 2.0;
            result = QRectF(center.x() - maxRadius, center.y() - maxRadius,
                           maxRadius * 2, maxRadius * 2);
        }
        break;
    default:
        break;
    }
    
    // 应用尺寸限制
//    if (result.width() < m_config.minSize) {
//        result.setWidth(m_config.minSize);
//    }
//    if (result.height() < m_config.minSize) {
//        result.setHeight(m_config.minSize);
//    }
    if (result.width() > m_config.maxSize) {
        result.setWidth(m_config.maxSize);
    }
    if (result.height() > m_config.maxSize) {
        result.setHeight(m_config.maxSize);
    }
    
    return result;
}

QPointF AbstractShapeTool::snapToGrid(const QPointF& point) const
{
    if (!m_config.snapToGrid) {
        return point;
    }
    
    // 简单的网格对齐，可以根据需要调整网格大小
    const qreal gridSize = 10.0;
    return QPointF(
        qRound(point.x() / gridSize) * gridSize,
        qRound(point.y() / gridSize) * gridSize
    );
}

qreal AbstractShapeTool::calculateDistance(const QPointF& p1, const QPointF& p2) const
{
    qreal dx = p1.x() - p2.x();
    qreal dy = p1.y() - p2.y();
    return qSqrt(dx * dx + dy * dy);
}
