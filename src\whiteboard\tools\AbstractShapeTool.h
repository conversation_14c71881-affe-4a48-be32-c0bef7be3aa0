#ifndef ABSTRACTSHAPETOOL_H
#define ABSTRACTSHAPETOOL_H

#include <QPainterPath>
#include <QPointF>
#include <QRectF>
#include <QHash>
#include <QList>
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 抽象图形工具基类
 * 
 * 提供统一的图形创建接口，支持：
 * 1. 实时路径更新
 * 2. 约束条件处理（如正方形、圆形）
 * 3. 参数配置
 * 4. 一键接入新图形
 */
class AbstractShapeTool
{
public:
    /**
     * @brief 图形约束类型
     */
    enum class ConstraintType {
        None,           // 无约束
        KeepRatio,      // 保持宽高比
        FixedRatio,     // 固定宽高比
        Symmetric       // 对称
    };

    /**
     * @brief 图形参数配置
     */
    struct ShapeConfig {
        ConstraintType constraint = ConstraintType::None;
        qreal fixedRatio = 1.0;         // 固定比例值
        bool snapToGrid = false;        // 是否对齐网格
        qreal minSize = 1.0;           // 最小尺寸
        qreal maxSize = 10000.0;       // 最大尺寸
    };

public:
    explicit AbstractShapeTool(ToolType toolType);
    virtual ~AbstractShapeTool() = default;

    // 核心接口
    virtual QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) = 0;
    virtual QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) = 0;
    virtual QString getToolName() const = 0;

    // 配置接口
    void setConfig(const ShapeConfig& config) { m_config = config; }
    ShapeConfig getConfig() const { return m_config; }
    ToolType getToolType() const { return m_toolType; }

    // 工具注册接口
    static void registerTool(ToolType toolType, AbstractShapeTool* tool);
    static AbstractShapeTool* getTool(ToolType toolType);
    static QList<ToolType> getAvailableTools();

protected:
    // 辅助方法
    QRectF applyConstraints(const QRectF& rect) const;
    QPointF snapToGrid(const QPointF& point) const;
    qreal calculateDistance(const QPointF& p1, const QPointF& p2) const;

private:
    ToolType m_toolType;
    ShapeConfig m_config;
    
    // 工具注册表
    static QHash<ToolType, AbstractShapeTool*> s_toolRegistry;
};

#endif // ABSTRACTSHAPETOOL_H
