# 图形工具系统

## 📁 文件结构

```
tools/
├── AbstractShapeTool.h/cpp      # 抽象基类
├── ShapeToolManager.h/cpp       # 工具管理器
├── AllTools.h                   # 便利包含头文件
├── README.md                    # 本文档
├── basic/                       # 基础工具
│   ├── FreeDrawTool.h/cpp      # 自由绘制
│   ├── LineTool.h/cpp          # 直线
│   └── DashedLineTool.h/cpp    # 虚线
├── shapes/                      # 几何形状工具
│   ├── RectangleTool.h/cpp     # 矩形
│   ├── SquareTool.h/cpp        # 正方形
│   ├── EllipseTool.h/cpp       # 椭圆
│   ├── CircleTool.h/cpp        # 圆形
│   ├── TriangleTool.h/cpp      # 三角形
│   └── RightTriangleTool.h/cpp # 直角三角形
└── special/                     # 特殊工具
    └── ArrowTool.h/cpp         # 箭头
```

## 🚀 一键接入新图形工具

### 方法1：标准流程

1. **选择合适的目录**：
   - `basic/` - 基础绘制工具
   - `shapes/` - 几何形状工具  
   - `special/` - 特殊功能工具

2. **创建工具类文件**：
```cpp
// 示例：创建五角星工具
// src/whiteboard/tools/shapes/StarTool.h
#ifndef STARTOOL_H
#define STARTOOL_H

#include "../AbstractShapeTool.h"

class StarTool : public AbstractShapeTool
{
public:
    StarTool();
    ~StarTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // STARTOOL_H
```

```cpp
// src/whiteboard/tools/shapes/StarTool.cpp
#include "StarTool.h"
#include <QtMath>

StarTool::StarTool() : AbstractShapeTool(ToolType::Star)
{
}

QPainterPath StarTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QPainterPath path;
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect).normalized();
    
    // 五角星绘制逻辑
    QPointF center = rect.center();
    qreal radius = qMin(rect.width(), rect.height()) / 2.0;
    
    // 创建五角星路径...
    
    return path;
}

QRectF StarTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    return applyConstraints(rect).normalized();
}

QString StarTool::getToolName() const
{
    return "Star";
}
```

3. **注册工具**：
   - 在 `AllTools.h` 中添加包含：`#include "shapes/StarTool.h"`
   - 在 `registerAllBasicTools()` 函数中添加：`manager->registerTool(new StarTool());`

4. **更新枚举**（如果需要新的ToolType）：
   - 在 `WhiteBoardTypes.h` 中添加新的枚举值

### 方法2：运行时注册

```cpp
// 在运行时动态注册新工具
ShapeToolManager::instance()->registerTool(new StarTool());
```

### 方法3：使用注册宏

```cpp
// 在工具类定义后使用宏
AUTO_REGISTER_SHAPE_TOOL(StarTool, ToolType::Star);
```

## 🔧 高级功能

### 约束条件配置

```cpp
// 设置工具约束（如正方形、圆形）
StarTool::StarTool() : AbstractShapeTool(ToolType::Star)
{
    ShapeConfig config;
    config.constraint = ConstraintType::KeepRatio;
    config.fixedRatio = 1.0;  // 保持1:1比例
    config.snapToGrid = true; // 启用网格对齐
    setConfig(config);
}
```

### 工具配置管理

```cpp
// 获取和设置工具配置
ShapeToolManager* manager = ShapeToolManager::instance();
auto config = manager->getToolConfig(ToolType::Star);
config.minSize = 10.0;
config.maxSize = 500.0;
manager->setToolConfig(ToolType::Star, config);
```

## 📋 最佳实践

1. **文件命名**：使用 `ToolNameTool.h/cpp` 格式
2. **类命名**：使用 `ToolNameTool` 格式
3. **目录选择**：根据工具功能选择合适的子目录
4. **约束处理**：使用 `applyConstraints()` 处理形状约束
5. **边界计算**：确保 `getBoundingRect()` 包含完整的绘制区域
6. **工具名称**：使用简洁明确的英文名称

## 🎯 扩展示例

### 添加心形工具

1. 创建 `src/whiteboard/tools/special/HeartTool.h/cpp`
2. 实现心形路径算法
3. 在 `AllTools.h` 中添加包含和注册
4. 完成！

新的架构让添加新图形变得非常简单，只需要专注于图形的绘制逻辑即可。
