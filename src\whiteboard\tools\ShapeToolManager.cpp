#include "ShapeToolManager.h"
#include "AllTools.h"
#include <QDebug>

// 静态成员初始化
ShapeToolManager* ShapeToolManager::s_instance = nullptr;

ShapeToolManager::ShapeToolManager(QObject* parent)
    : QObject(parent)
{
    // 不在构造函数中初始化工具，避免循环依赖
}

ShapeToolManager::~ShapeToolManager()
{
    clearAllTools();
}

ShapeToolManager* ShapeToolManager::instance()
{
    if (!s_instance) {
        s_instance = new ShapeToolManager();
    }
    return s_instance;
}

void ShapeToolManager::registerTool(AbstractShapeTool* tool)
{
    if (!tool) {
        return;
    }
    
    ToolType toolType = tool->getToolType();
    
    // 如果已存在，先删除旧的
    if (m_tools.contains(toolType)) {
        delete m_tools[toolType];
    }
    
    m_tools[toolType] = tool;
    emit toolRegistered(toolType, tool->getToolName());
}

void ShapeToolManager::unregisterTool(ToolType toolType)
{
    if (m_tools.contains(toolType)) {
        delete m_tools[toolType];
        m_tools.remove(toolType);
        emit toolUnregistered(toolType);
    }
}

AbstractShapeTool* ShapeToolManager::getTool(ToolType toolType) const
{
    return m_tools.value(toolType, nullptr);
}

bool ShapeToolManager::hasToolType(ToolType toolType) const
{
    const_cast<ShapeToolManager*>(this)->ensureInitialized();
    return m_tools.contains(toolType);
}

QList<ToolType> ShapeToolManager::getAvailableToolTypes() const
{
    return m_tools.keys();
}

QStringList ShapeToolManager::getAvailableToolNames() const
{
    QStringList names;
    for (auto it = m_tools.begin(); it != m_tools.end(); ++it) {
        names.append(it.value()->getToolName());
    }
    return names;
}

QPainterPath ShapeToolManager::createPath(ToolType toolType, const QPointF& startPoint, const QPointF& currentPoint)
{
    ensureInitialized();
    AbstractShapeTool* tool = getTool(toolType);
    if (tool) {
        return tool->createPath(startPoint, currentPoint);
    }

    QPainterPath path;
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    return path;
}

QRectF ShapeToolManager::getBoundingRect(ToolType toolType, const QPointF& startPoint, const QPointF& currentPoint)
{
    AbstractShapeTool* tool = getTool(toolType);
    if (tool) {
        return tool->getBoundingRect(startPoint, currentPoint);
    }
    
    return QRectF(startPoint, currentPoint).normalized();
}

void ShapeToolManager::setToolConfig(ToolType toolType, const AbstractShapeTool::ShapeConfig& config)
{
    AbstractShapeTool* tool = getTool(toolType);
    if (tool) {
        tool->setConfig(config);
        emit toolConfigChanged(toolType);
    }
}

AbstractShapeTool::ShapeConfig ShapeToolManager::getToolConfig(ToolType toolType) const
{
    AbstractShapeTool* tool = getTool(toolType);
    if (tool) {
        return tool->getConfig();
    }
    
    return AbstractShapeTool::ShapeConfig();
}

void ShapeToolManager::registerBasicTools()
{
    initializeBasicTools();
}

void ShapeToolManager::ensureInitialized()
{
    if (!m_initialized) {
        initializeBasicTools();
        m_initialized = true;
    }
}

void ShapeToolManager::clearAllTools()
{
    for (auto tool : m_tools) {
        delete tool;
    }
    m_tools.clear();
}

QString ShapeToolManager::getToolName(ToolType toolType) const
{
    AbstractShapeTool* tool = getTool(toolType);
    if (tool) {
        return tool->getToolName();
    }
    
    return QString("Unknown");
}

bool ShapeToolManager::isShapeTool(ToolType toolType) const
{
    return toolType != ToolType::FreeDraw && hasToolType(toolType);
}

void ShapeToolManager::initializeBasicTools()
{
    registerAllBasicTools(this);
}
