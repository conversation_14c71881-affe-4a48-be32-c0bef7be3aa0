#ifndef SHAPETOOLMANAGER_H
#define SHAPETOOLMANAGER_H

#include <QObject>
#include <QHash>
#include <QList>
#include "AbstractShapeTool.h"
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 图形工具管理器
 * 
 * 核心功能：
 * 1. 统一管理所有图形工具
 * 2. 提供工具注册和查找接口
 * 3. 支持动态加载新工具
 * 4. 工具配置管理
 */
class ShapeToolManager : public QObject
{
    Q_OBJECT

public:
    static ShapeToolManager* instance();
    ~ShapeToolManager();

    // 工具管理
    void registerTool(AbstractShapeTool* tool);
    void unregisterTool(ToolType toolType);
    AbstractShapeTool* getTool(ToolType toolType) const;
    
    // 工具查询
    bool hasToolType(ToolType toolType) const;
    QList<ToolType> getAvailableToolTypes() const;
    QStringList getAvailableToolNames() const;
    
    // 路径创建接口
    QPainterPath createPath(ToolType toolType, const QPointF& startPoint, const QPointF& currentPoint);
    QRectF getBoundingRect(ToolType toolType, const QPointF& startPoint, const QPointF& currentPoint);
    
    // 工具配置
    void setToolConfig(ToolType toolType, const AbstractShapeTool::ShapeConfig& config);
    AbstractShapeTool::ShapeConfig getToolConfig(ToolType toolType) const;
    
    // 批量操作
    void registerBasicTools();      // 注册所有基础工具
    void clearAllTools();           // 清除所有工具
    void ensureInitialized();       // 确保已初始化
    
    // 工具信息
    QString getToolName(ToolType toolType) const;
    bool isShapeTool(ToolType toolType) const;  // 判断是否为形状工具（非自由绘制）

signals:
    void toolRegistered(ToolType toolType, const QString& toolName);
    void toolUnregistered(ToolType toolType);
    void toolConfigChanged(ToolType toolType);

private:
    explicit ShapeToolManager(QObject* parent = nullptr);

    QHash<ToolType, AbstractShapeTool*> m_tools;
    static ShapeToolManager* s_instance;
    bool m_initialized = false;

    // 内部方法
    void initializeBasicTools();
};

/**
 * @brief 工具注册宏 - 简化新工具接入
 */
#define REGISTER_SHAPE_TOOL(ToolClass, toolType) \
    do { \
        ShapeToolManager::instance()->registerTool(new ToolClass()); \
    } while(0)

/**
 * @brief 自动注册器 - 支持静态注册
 */
template<typename ToolClass>
class ShapeToolAutoRegister
{
public:
    ShapeToolAutoRegister(ToolType toolType) {
        ShapeToolManager::instance()->registerTool(new ToolClass());
    }
};

#define AUTO_REGISTER_SHAPE_TOOL(ToolClass, toolType) \
    static ShapeToolAutoRegister<ToolClass> g_autoRegister_##ToolClass(toolType);

#endif // SHAPETOOLMANAGER_H
