#include "DashedLineTool.h"

DashedLineTool::DashedLineTool() 
    : AbstractShapeTool(ToolType::DashedLine)
{
}

QPainterPath DashedLineTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    // 虚线的路径创建与直线相同，样式由画笔控制
    QPainterPath path;
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    return path;
}

QRectF DashedLineTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    return QRectF(startPoint, currentPoint).normalized();
}

QString DashedLineTool::getToolName() const
{
    return "DashedLine";
}
