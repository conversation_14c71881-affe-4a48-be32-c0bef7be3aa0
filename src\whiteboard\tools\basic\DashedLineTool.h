#ifndef DASHEDLINETOOL_H
#define DASHEDLINETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 虚线工具
 * 
 * 特点：
 * - 绘制虚线样式的直线
 * - 路径创建与直线相同，样式由画笔控制
 * - 支持自定义虚线模式
 */
class DashedLineTool : public AbstractShapeTool
{
public:
    DashedLineTool();
    ~DashedLineTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // DASHEDLINETOOL_H
