#include "FreeDrawDashedTool.h"

FreeDrawDashedTool::FreeDrawDashedTool() 
    : AbstractShapeTool(ToolType::FreeDrawDashed)
{
}

QPainterPath FreeDrawDashedTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QPainterPath path;
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    return path;
}

QRectF FreeDrawDashedTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    return QRectF(startPoint, currentPoint).normalized();
}

QString FreeDrawDashedTool::getToolName() const
{
    return "FreeDrawDashed";
}
