#ifndef FREEDRAWDASHEDTOOL_H
#define FREEDRAWDASHEDTOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 自由绘制虚线工具
 * 
 * 特点：
 * - 支持连续路径绘制
 * - 使用虚线样式
 * - 适用于手写、涂鸦等场景
 */
class FreeDrawDashedTool : public AbstractShapeTool
{
public:
    FreeDrawDashedTool();
    ~FreeDrawDashedTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // FREEDRAWDASHEDTOOL_H
