#include "FreeDrawHighlighterTool.h"

FreeDrawHighlighterTool::FreeDrawHighlighterTool() 
    : AbstractShapeTool(ToolType::FreeDrawHighlighter)
{
}

QPainterPath FreeDrawHighlighterTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QPainterPath path;
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    return path;
}

QRectF FreeDrawHighlighterTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    return QRectF(startPoint, currentPoint).normalized();
}

QString FreeDrawHighlighterTool::getToolName() const
{
    return "FreeDrawHighlighter";
}
