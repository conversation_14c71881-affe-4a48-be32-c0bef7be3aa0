#ifndef FREEDRAWHIGHLIGHTERTOOL_H
#define FREEDRAWHIGHLIGHTERTOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 自由绘制荧光笔工具
 * 
 * 特点：
 * - 支持连续路径绘制
 * - 使用荧光笔样式（半透明、较粗线条）
 * - 适用于标记、高亮等场景
 */
class FreeDrawHighlighterTool : public AbstractShapeTool
{
public:
    FreeDrawHighlighterTool();
    ~FreeDrawHighlighterTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // FREEDRAWHIGHLIGHTERTOOL_H
