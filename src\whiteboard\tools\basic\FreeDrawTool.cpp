#include "FreeDrawTool.h"

FreeDrawTool::FreeDrawTool() 
    : AbstractShapeTool(ToolType::FreeDraw)
{
}

QPainterPath FreeDrawTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QPainterPath path;
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    return path;
}

QRectF FreeDrawTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    return QRectF(startPoint, currentPoint).normalized();
}

QString FreeDrawTool::getToolName() const
{
    return "FreeDraw";
}
