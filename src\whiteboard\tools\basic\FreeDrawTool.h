#ifndef FREEDRAWTOOL_H
#define FREEDRAWTOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 自由绘制工具
 * 
 * 特点：
 * - 支持连续路径绘制
 * - 适用于手写、涂鸦等场景
 */
class FreeDrawTool : public AbstractShapeTool
{
public:
    FreeDrawTool();
    ~FreeDrawTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // FREEDRAWTOOL_H
