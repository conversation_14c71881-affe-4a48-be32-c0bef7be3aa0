#include "LineTool.h"

LineTool::LineTool() 
    : AbstractShapeTool(ToolType::Line)
{
}

QPainterPath LineTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QPainterPath path;
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    return path;
}

QRectF LineTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    return QRectF(startPoint, currentPoint).normalized();
}

QString LineTool::getToolName() const
{
    return "Line";
}
