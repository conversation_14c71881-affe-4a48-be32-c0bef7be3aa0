#ifndef LINETOOL_H
#define LINETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 直线工具
 * 
 * 特点：
 * - 绘制从起点到终点的直线
 * - 支持实时预览
 * - 可配置线条样式
 */
class LineTool : public AbstractShapeTool
{
public:
    LineTool();
    ~LineTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // LINETOOL_H
