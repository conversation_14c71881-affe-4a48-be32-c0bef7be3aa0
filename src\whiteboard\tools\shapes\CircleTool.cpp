#include "CircleTool.h"

CircleTool::CircleTool() 
    : AbstractShapeTool(ToolType::Circle)
{
    // 设置圆形约束
    ShapeConfig config;
    config.constraint = ConstraintType::KeepRatio;
    config.fixedRatio = 1.0;
    setConfig(config);
}

QPainterPath CircleTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect);

    QPainterPath path;
    // 绘制时需要normalized，但边界计算时不需要
    path.addEllipse(rect.normalized());
    return path;
}

QRectF CircleTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect);
    // 返回约束后的矩形，不进行normalized，保持拖拽方向
    return rect;
}

QString CircleTool::getToolName() const
{
    return "Circle";
}
