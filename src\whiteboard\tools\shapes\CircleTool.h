#ifndef CIRCLETOOL_H
#define CIRCLETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 圆形工具
 * 
 * 特点：
 * - 绘制正圆（宽高比1:1）
 * - 自动保持宽高比约束
 * - 基于较短边确定半径
 */
class CircleTool : public AbstractShapeTool
{
public:
    CircleTool();
    ~CircleTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // CIRCLETOOL_H
