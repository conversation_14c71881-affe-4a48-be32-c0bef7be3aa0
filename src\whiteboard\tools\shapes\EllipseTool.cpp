#include "EllipseTool.h"

EllipseTool::EllipseTool() 
    : AbstractShapeTool(ToolType::Ellipse)
{
}

QPainterPath EllipseTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect);
    
    QPainterPath path;
    path.addEllipse(rect.normalized());
    return path;
}

QRectF EllipseTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    return applyConstraints(rect).normalized();
}

QString EllipseTool::getToolName() const
{
    return "Ellipse";
}
