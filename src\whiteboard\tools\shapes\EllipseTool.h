#ifndef ELLIPSETOOL_H
#define ELLIPSETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 椭圆工具
 * 
 * 特点：
 * - 绘制任意比例的椭圆
 * - 基于矩形边界创建椭圆
 * - 支持实时预览
 */
class EllipseTool : public AbstractShapeTool
{
public:
    EllipseTool();
    ~EllipseTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // ELLIPSETOOL_H
