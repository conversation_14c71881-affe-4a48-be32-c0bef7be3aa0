#include "RectangleTool.h"

RectangleTool::RectangleTool() 
    : AbstractShapeTool(ToolType::Rectangle)
{
}

QPainterPath RectangleTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect);
    
    QPainterPath path;
    path.addRect(rect.normalized());
    return path;
}

QRectF RectangleTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    return applyConstraints(rect).normalized();
}

QString RectangleTool::getToolName() const
{
    return "Rectangle";
}
