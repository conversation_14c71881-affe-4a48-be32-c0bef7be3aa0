#ifndef RECTANGLETOOL_H
#define RECTANGLETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 矩形工具
 * 
 * 特点：
 * - 绘制任意比例的矩形
 * - 支持实时预览
 * - 可配置约束条件
 */
class RectangleTool : public AbstractShapeTool
{
public:
    RectangleTool();
    ~RectangleTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // RECTANGLETOOL_H
