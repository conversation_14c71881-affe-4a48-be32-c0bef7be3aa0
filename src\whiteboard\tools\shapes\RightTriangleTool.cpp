#include "RightTriangleTool.h"

RightTriangleTool::RightTriangleTool() 
    : AbstractShapeTool(ToolType::RightTriangle)
{
}

QPainterPath RightTriangleTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect).normalized();
    
    QPainterPath path;
    
    // 直角三角形：直角在左下角
    QPointF topLeft(rect.left(), rect.top());
    QPointF bottomLeft(rect.left(), rect.bottom());
    QPointF bottomRight(rect.right(), rect.bottom());
    
    path.moveTo(topLeft);
    path.lineTo(bottomLeft);
    path.lineTo(bottomRight);
    path.closeSubpath();
    
    return path;
}

QRectF RightTriangleTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    return applyConstraints(rect).normalized();
}

QString RightTriangleTool::getToolName() const
{
    return "RightTriangle";
}
