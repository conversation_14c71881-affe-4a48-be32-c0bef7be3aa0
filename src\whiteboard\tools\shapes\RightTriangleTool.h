#ifndef RIGHTTRIANGLETOOL_H
#define RIGHTTRIANGLETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 直角三角形工具
 * 
 * 特点：
 * - 绘制直角三角形
 * - 直角在左下角
 * - 基于矩形边界创建
 */
class RightTriangleTool : public AbstractShapeTool
{
public:
    RightTriangleTool();
    ~RightTriangleTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // RIGHTTRIANGLETOOL_H
