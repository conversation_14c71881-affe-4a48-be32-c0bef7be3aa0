#include "SquareTool.h"

SquareTool::SquareTool() 
    : AbstractShapeTool(ToolType::Square)
{
    // 设置正方形约束
    ShapeConfig config;
    config.constraint = ConstraintType::KeepRatio;
    config.fixedRatio = 1.0;
    setConfig(config);
}

QPainterPath SquareTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect);

    QPainterPath path;
    // 绘制时需要normalized，但边界计算时不需要
    path.addRect(rect.normalized());
    return path;
}

QRectF SquareTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect);
    // 返回约束后的矩形，不进行normalized，保持拖拽方向
    return rect;
}

QString SquareTool::getToolName() const
{
    return "Square";
}
