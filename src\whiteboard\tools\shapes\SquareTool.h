#ifndef SQUARETOOL_H
#define SQUARETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 正方形工具
 * 
 * 特点：
 * - 绘制正方形（宽高比1:1）
 * - 自动保持宽高比约束
 * - 基于较短边确定尺寸
 */
class SquareTool : public AbstractShapeTool
{
public:
    SquareTool();
    ~SquareTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // SQUARETOOL_H
