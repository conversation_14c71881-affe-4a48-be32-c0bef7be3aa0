#include "TriangleTool.h"

TriangleTool::TriangleTool() 
    : AbstractShapeTool(ToolType::Triangle)
{
}

QPainterPath TriangleTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    rect = applyConstraints(rect).normalized();
    
    QPainterPath path;
    
    // 等腰三角形：顶点在矩形上边中点，底边是矩形下边
    QPointF top(rect.center().x(), rect.top());
    QPointF bottomLeft(rect.left(), rect.bottom());
    QPointF bottomRight(rect.right(), rect.bottom());
    
    path.moveTo(top);
    path.lineTo(bottomLeft);
    path.lineTo(bottomRight);
    path.closeSubpath();
    
    return path;
}

QRectF TriangleTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    QRectF rect(startPoint, currentPoint);
    return applyConstraints(rect).normalized();
}

QString TriangleTool::getToolName() const
{
    return "Triangle";
}
