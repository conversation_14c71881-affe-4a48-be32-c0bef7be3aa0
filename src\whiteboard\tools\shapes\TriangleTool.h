#ifndef TRIANGLETOOL_H
#define TRIANGLETOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 三角形工具
 * 
 * 特点：
 * - 绘制等腰三角形
 * - 顶点在矩形上边中点
 * - 底边为矩形下边
 */
class TriangleTool : public AbstractShapeTool
{
public:
    TriangleTool();
    ~TriangleTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;
};

#endif // TRIANGLETOOL_H
