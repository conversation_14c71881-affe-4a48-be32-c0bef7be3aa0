#include "ArrowTool.h"
#include <QtMath>

ArrowTool::ArrowTool() 
    : AbstractShapeTool(ToolType::Arrow)
{
}

QPainterPath ArrowTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    QPainterPath path;
    
    QPointF direction = currentPoint - startPoint;
    qreal length = calculateDistance(startPoint, currentPoint);
    
    if (length < 1.0) {
        return path; // 太短，不绘制
    }
    
    // 标准化方向向量
    direction /= length;
    
    // 箭头参数
    qreal arrowHeadLength = qMin(length * ARROW_HEAD_RATIO, MAX_ARROW_HEAD_SIZE);
    qreal arrowHeadWidth = arrowHeadLength * ARROW_HEAD_WIDTH;
    
    // 主线
    path.moveTo(startPoint);
    path.lineTo(currentPoint);
    
    // 箭头头部
    QPointF perpendicular(-direction.y(), direction.x());
    QPointF arrowHead1 = currentPoint - direction * arrowHeadLength + perpendicular * arrowHeadWidth;
    QPointF arrowHead2 = currentPoint - direction * arrowHeadLength - perpendicular * arrowHeadWidth;
    
    path.moveTo(currentPoint);
    path.lineTo(arrowHead1);
    path.moveTo(currentPoint);
    path.lineTo(arrowHead2);
    
    return path;
}

QRectF ArrowTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    // 箭头的边界需要考虑箭头头部
    QRectF rect(startPoint, currentPoint);
    qreal length = calculateDistance(startPoint, currentPoint);
    qreal arrowHeadLength = qMin(length * ARROW_HEAD_RATIO, MAX_ARROW_HEAD_SIZE);
    qreal arrowHeadWidth = arrowHeadLength * ARROW_HEAD_WIDTH;
    
    // 扩展边界以包含箭头头部
    rect = rect.adjusted(-arrowHeadWidth, -arrowHeadWidth, arrowHeadWidth, arrowHeadWidth);
    return rect.normalized();
}

QString ArrowTool::getToolName() const
{
    return "Arrow";
}
