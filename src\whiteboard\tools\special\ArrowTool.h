#ifndef ARROWTOOL_H
#define ARROWTOOL_H

#include "../AbstractShapeTool.h"

/**
 * @brief 箭头工具
 * 
 * 特点：
 * - 绘制带箭头的直线
 * - 自动计算箭头头部大小
 * - 支持自定义箭头参数
 */
class ArrowTool : public AbstractShapeTool
{
public:
    ArrowTool();
    ~ArrowTool() = default;
    
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;

private:
    // 箭头参数
    static constexpr qreal ARROW_HEAD_RATIO = 0.2;    // 箭头长度比例
    static constexpr qreal ARROW_HEAD_WIDTH = 0.6;    // 箭头宽度比例
    static constexpr qreal MAX_ARROW_HEAD_SIZE = 20.0; // 最大箭头尺寸
};

#endif // ARROWTOOL_H
