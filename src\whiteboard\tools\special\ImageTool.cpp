#include "ImageTool.h"
#include <QFileInfo>
#include <QImageReader>
#include <QDebug>
#include <QApplication>
#include <QScreen>

// 简单的屏幕适配函数
namespace {
    qreal getScreenScaleFactor() {
        static qreal scaleFactor = 1.0;
        static bool calculated = false;

        if (!calculated) {
            QScreen* screen = QApplication::primaryScreen();
            if (screen) {
                // 基于4K基准计算缩放因子
                const qreal baseWidth = 4096.0;
                const qreal baseHeight = 2160.0;

                QSize screenSize = screen->size();
                qreal widthRatio = screenSize.width() / baseWidth;
                qreal heightRatio = screenSize.height() / baseHeight;

                // 使用较小的比例确保不超出屏幕
                scaleFactor = qMin(widthRatio, heightRatio);

                // 限制缩放范围
                scaleFactor = qBound(0.3, scaleFactor, 3.0);
            }
            calculated = true;
        }

        return scaleFactor;
    }

    qreal adaptSize(qreal originalSize) {
        return originalSize * getScreenScaleFactor();
    }
}

ImageTool::ImageTool()
    : AbstractShapeTool(ToolType::Image)
{
    // 设置默认配置
    m_imageConfig.displayWidth = adaptSize(400.0);
    m_imageConfig.maxHeight = adaptSize(800.0);
    m_imageConfig.maintainAspectRatio = true;
}

QPainterPath ImageTool::createPath(const QPointF& startPoint, const QPointF& currentPoint)
{
    // 图片工具创建一个矩形路径作为预览
    QPainterPath path;
    
    if (m_imageConfig.imagePath.isEmpty()) {
        // 如果没有设置图片，创建一个默认大小的矩形
        QRectF rect(startPoint, QSizeF(m_imageConfig.displayWidth, 100));
        path.addRect(rect);
        return path;
    }
    
    // 计算图片显示尺寸
    QSizeF imageSize = calculateImageSize();
    QRectF rect(startPoint, imageSize);
    path.addRect(rect);
    
    return path;
}

QRectF ImageTool::getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint)
{
    Q_UNUSED(currentPoint)
    
    if (m_imageConfig.imagePath.isEmpty()) {
        return QRectF(startPoint, QSizeF(m_imageConfig.displayWidth, 100));
    }
    
    QSizeF imageSize = calculateImageSize();
    return QRectF(startPoint, imageSize);
}

QString ImageTool::getToolName() const
{
    return "Image";
}

void ImageTool::setImageConfig(const ImageConfig& config)
{
    m_imageConfig = config;

    // 应用屏幕适配
    if (m_imageConfig.displayWidth > 0) {
        m_imageConfig.displayWidth = adaptSize(m_imageConfig.displayWidth);
    }
    m_imageConfig.maxHeight = adaptSize(m_imageConfig.maxHeight);
}

ImageTool::ImageConfig ImageTool::getImageConfig() const
{
    return m_imageConfig;
}

void ImageTool::setImagePath(const QString& imagePath)
{
    if (validateImagePath(imagePath)) {
        m_imageConfig.imagePath = imagePath;
    } else {
        qWarning() << "ImageTool: 无效的图片路径" << imagePath;
    }
}

void ImageTool::setDisplayWidth(qreal width)
{
    if (width > 0) {
        m_imageConfig.displayWidth = adaptSize(width);
    }
}

void ImageTool::setMaxHeight(qreal maxHeight)
{
    if (maxHeight > 0) {
        m_imageConfig.maxHeight = adaptSize(maxHeight);
    }
}

QString ImageTool::getImagePath() const
{
    return m_imageConfig.imagePath;
}

qreal ImageTool::getDisplayWidth() const
{
    return m_imageConfig.displayWidth;
}

qreal ImageTool::getMaxHeight() const
{
    return m_imageConfig.maxHeight;
}

bool ImageTool::isImageValid() const
{
    return isValidImageFile(m_imageConfig.imagePath);
}

QSizeF ImageTool::calculateImageSize() const
{
    return calculateImageSize(m_imageConfig.imagePath, 
                             m_imageConfig.displayWidth, 
                             m_imageConfig.maxHeight);
}

QSizeF ImageTool::calculateImageSize(const QString& imagePath, qreal displayWidth, qreal maxHeight) const
{
    if (imagePath.isEmpty()) {
        qreal width = displayWidth > 0 ? displayWidth : adaptSize(400.0);
        return QSizeF(width, 100);
    }

    // 获取图片原始尺寸
    QSizeF originalSize = getImageFileSize(imagePath);
    if (originalSize.isEmpty()) {
        qreal width = displayWidth > 0 ? displayWidth : adaptSize(400.0);
        return QSizeF(width, 100);
    }

    // 应用约束计算最终尺寸
    qreal targetWidth = displayWidth > 0 ? displayWidth : adaptSize(400.0);
    qreal targetMaxHeight = maxHeight > 0 ? maxHeight : adaptSize(800.0);

    return calculateSizeWithConstraints(originalSize, targetWidth, targetMaxHeight);
}

bool ImageTool::isValidImageFile(const QString& filePath)
{
    if (filePath.isEmpty()) {
        return false;
    }
    
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        return false;
    }
    
    // 使用QImageReader检查是否为有效图片
    QImageReader reader(filePath);
    return reader.canRead();
}

QStringList ImageTool::getSupportedImageFormats()
{
    QStringList formats;
    QList<QByteArray> supportedFormats = QImageReader::supportedImageFormats();
    
    for (const QByteArray& format : supportedFormats) {
        formats << QString::fromLatin1(format).toLower();
    }
    
    return formats;
}

QSizeF ImageTool::getImageFileSize(const QString& filePath)
{
    if (!isValidImageFile(filePath)) {
        return QSizeF();
    }
    
    QImageReader reader(filePath);
    QSize size = reader.size();
    
    if (size.isValid()) {
        return QSizeF(size);
    }
    
    return QSizeF();
}

// 私有方法实现
QSizeF ImageTool::calculateSizeWithConstraints(const QSizeF& originalSize, qreal targetWidth, qreal maxHeight) const
{
    if (originalSize.isEmpty()) {
        return QSizeF(targetWidth, 100);
    }
    
    // 计算宽高比
    qreal aspectRatio = originalSize.width() / originalSize.height();
    
    // 根据目标宽度计算高度
    qreal width = targetWidth;
    qreal height = width / aspectRatio;
    
    // 应用最大高度限制
    if (height > maxHeight) {
        height = maxHeight;
        if (m_imageConfig.maintainAspectRatio) {
            width = height * aspectRatio;
        }
    }
    
    return QSizeF(width, height);
}

bool ImageTool::validateImagePath(const QString& imagePath) const
{
    return isValidImageFile(imagePath);
}
