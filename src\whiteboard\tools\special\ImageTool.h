#ifndef IMAGETOOL_H
#define IMAGETOOL_H

#include "../AbstractShapeTool.h"
#include <QString>
#include <QSizeF>

/**
 * @brief 图片工具
 * 
 * 特点：
 * - 支持本地图片文件加载
 * - 自动尺寸适配和比例保持
 * - 支持自定义显示宽度和最大高度
 * - 集成屏幕适配功能
 */
class ImageTool : public AbstractShapeTool
{
public:
    /**
     * @brief 图片配置参数
     */
    struct ImageConfig {
        QString imagePath;           // 图片文件路径
        qreal displayWidth = 0;      // 显示宽度（0表示使用默认值）
        qreal maxHeight = 800;       // 最大高度限制
        bool maintainAspectRatio = true; // 是否保持宽高比
    };

    ImageTool();
    ~ImageTool() = default;
    
    // AbstractShapeTool接口实现
    QPainterPath createPath(const QPointF& startPoint, const QPointF& currentPoint) override;
    QRectF getBoundingRect(const QPointF& startPoint, const QPointF& currentPoint) override;
    QString getToolName() const override;

    // 图片工具特有方法
    void setImageConfig(const ImageConfig& config);
    ImageConfig getImageConfig() const;
    
    // 便捷设置方法
    void setImagePath(const QString& imagePath);
    void setDisplayWidth(qreal width);
    void setMaxHeight(qreal maxHeight);
    
    // 图片信息获取
    QString getImagePath() const;
    qreal getDisplayWidth() const;
    qreal getMaxHeight() const;
    bool isImageValid() const;
    
    // 尺寸计算
    QSizeF calculateImageSize() const;
    QSizeF calculateImageSize(const QString& imagePath, qreal displayWidth = 0, qreal maxHeight = 800) const;
    
    // 静态工具方法
    static bool isValidImageFile(const QString& filePath);
    static QStringList getSupportedImageFormats();
    static QSizeF getImageFileSize(const QString& filePath);

private:
    ImageConfig m_imageConfig;
    
    // 辅助方法
    QSizeF calculateSizeWithConstraints(const QSizeF& originalSize, qreal targetWidth, qreal maxHeight) const;
    bool validateImagePath(const QString& imagePath) const;
};

#endif // IMAGETOOL_H
