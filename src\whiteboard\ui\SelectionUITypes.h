#ifndef SELECTIONUITYPES_H
#define SELECTIONUITYPES_H

#include <QPointF>
#include <QRectF>
#include <QColor>
#include <QApplication>
#include <QScreen>

/**
 * @brief 选择UI系统的类型定义
 * 
 * 包含锚点类型、选择锚点类型、工具栏操作等枚举定义
 */

/**
 * @brief 锚点类型枚举
 */
enum class AnchorType {
    None = -1,          // 无锚点

    // 8个基础锚点
    TopLeft = 0,        // 左上角
    TopCenter,          // 上中点
    TopRight,           // 右上角
    MiddleLeft,         // 左中点
    MiddleRight,        // 右中点
    BottomLeft,         // 左下角
    BottomCenter,       // 下中点
    BottomRight,        // 右下角

    // 特殊锚点
    Rotation,           // 旋转锚点

    // 直线/箭头专用锚点
    StartPoint,         // 起点
    EndPoint,           // 终点

    // 特殊锚点
    Center              // 中心点（独立值，用于移动操作）
};

/**
 * @brief 选择锚点类型 - 决定显示哪些锚点
 */
enum class SelectAnchorType {
    Line,               // 直线类型：只显示起点和终点
    FixedRatio,         // 固定比例类型：显示4个角点（正圆、正方形）
    Full,               // 完整类型：显示8个锚点
    Multiple            // 多选类型：显示8个锚点
};

/**
 * @brief 工具栏操作枚举
 */
enum class ToolbarAction {
    None = -1,          // 无操作
    Delete = 100,       // 删除操作
    Copy,               // 复制操作
    Cut,                // 剪切操作
    BringToFront,       // 置于顶层
    SendToBack,         // 置于底层
    Group,              // 组合
    Ungroup             // 取消组合
};

/**
 * @brief 命中测试结果枚举
 */
enum class HitTestResult {
    None,               // 未命中
    Anchor,             // 命中锚点
    Toolbar,            // 命中工具栏
    SelectionBox        // 命中选择框
};

/**
 * @brief 交互状态枚举
 */
enum class InteractionState {
    None,               // 无交互
    Moving,             // 移动中
    Resizing,           // 缩放中
    Rotating,           // 旋转中
    ToolbarAction       // 工具栏操作
};

/**
 * @brief 屏幕适配辅助函数
 */
namespace ScreenAdaptation {
    inline qreal getScreenScaleFactor() {
        static qreal scaleFactor = 1.0;
        static bool calculated = false;

        if (!calculated) {
            QScreen* screen = QApplication::primaryScreen();
            if (screen) {
                // 基于4K基准计算缩放因子
                const qreal baseWidth = 4096.0;
                const qreal baseHeight = 2160.0;

                QSize screenSize = screen->size();
                qreal widthRatio = screenSize.width() / baseWidth;
                qreal heightRatio = screenSize.height() / baseHeight;

                // 使用较小的比例确保不超出屏幕
                scaleFactor = qMin(widthRatio, heightRatio);

                // 限制缩放范围
                scaleFactor = qBound(0.3, scaleFactor, 3.0);
            }
            calculated = true;
        }

        return scaleFactor;
    }

    inline qreal adaptSize(qreal originalSize) {
        return originalSize * getScreenScaleFactor();
    }
}

/**
 * @brief 渲染常量（4K基准尺寸）
 */
namespace RenderConstants {
    // 基础常量（4K基准）
    namespace Base {
        const qreal ANCHOR_SIZE = 26.0;                  // 锚点大小
        const qreal ANCHOR_BORDER_WIDTH = 6.0;          // 锚点边框宽度
        const qreal SELECTION_BORDER_WIDTH = 6.0;       // 选择框边框宽度
        const qreal ROTATION_DISTANCE = 40.0;           // 旋转锚点距离顶部的距离
        const qreal ROTATION_ICON_DISTANCE = 24.0;       // 旋转图标距离旋转锚点的距离
        const qreal ROTATION_ICON_WIDTH = 34.0;         // 旋转图标宽度
        const qreal ROTATION_ICON_HEIGHT = 11.0;        // 旋转图标高度
        const qreal TOOLBAR_DISTANCE = 20.0;            // 工具栏距离选择框底部的距离
        const qreal TOOLBAR_ICON_SIZE = 48.0;           // 工具栏图标大小
        const qreal TOOLBAR_ICON_SPACING = 8.0;         // 工具栏图标间距
        const qreal HIT_TEST_TOLERANCE = 6.0;           // 命中测试容差
        const qreal ANCHOR_HIT_AREA = 32.0;             // 锚点命中区域大小（更大的点击区域）
    }

    // 颜色常量（不需要适配）
    const QColor ANCHOR_COLOR = QColor(255, 255, 255);     // 锚点填充颜色
    const QColor SELECTION_COLOR = QColor(95, 82, 227);    // 选择框颜色

    // 屏幕适配后的常量
    inline qreal ANCHOR_SIZE() { return ScreenAdaptation::adaptSize(Base::ANCHOR_SIZE); }
    inline qreal ANCHOR_BORDER_WIDTH() { return ScreenAdaptation::adaptSize(Base::ANCHOR_BORDER_WIDTH); }
    inline qreal SELECTION_BORDER_WIDTH() { return ScreenAdaptation::adaptSize(Base::SELECTION_BORDER_WIDTH); }
    inline qreal ROTATION_DISTANCE() { return ScreenAdaptation::adaptSize(Base::ROTATION_DISTANCE); }
    inline qreal ROTATION_ICON_DISTANCE() { return ScreenAdaptation::adaptSize(Base::ROTATION_ICON_DISTANCE); }
    inline qreal ROTATION_ICON_WIDTH() { return ScreenAdaptation::adaptSize(Base::ROTATION_ICON_WIDTH); }
    inline qreal ROTATION_ICON_HEIGHT() { return ScreenAdaptation::adaptSize(Base::ROTATION_ICON_HEIGHT); }
    inline qreal TOOLBAR_DISTANCE() { return ScreenAdaptation::adaptSize(Base::TOOLBAR_DISTANCE); }
    inline qreal TOOLBAR_ICON_SIZE() { return ScreenAdaptation::adaptSize(Base::TOOLBAR_ICON_SIZE); }
    inline qreal TOOLBAR_ICON_SPACING() { return ScreenAdaptation::adaptSize(Base::TOOLBAR_ICON_SPACING); }
    inline qreal HIT_TEST_TOLERANCE() { return ScreenAdaptation::adaptSize(Base::HIT_TEST_TOLERANCE); }
    inline qreal ANCHOR_HIT_AREA() { return ScreenAdaptation::adaptSize(Base::ANCHOR_HIT_AREA); }
}

/**
 * @brief 选择框属性结构
 */
struct SelectionBoxProperties {
    QRectF boundingRect;                    // 边界矩形
    SelectAnchorType anchorType;            // 锚点类型
    QList<AnchorType> visibleAnchors;       // 可见的锚点列表
    QColor selectionColor = RenderConstants::SELECTION_COLOR;
    QColor anchorColor = RenderConstants::ANCHOR_COLOR;
    qreal selectionBorderWidth = 0;         // 将在构造函数中初始化
    qreal anchorSize = 0;                   // 将在构造函数中初始化
    qreal anchorBorderWidth = 0;            // 将在构造函数中初始化
    bool showRotation = true;               // 是否显示旋转锚点
    bool showToolbar = true;                // 是否显示工具栏
    bool showHitAreas = false;              // 是否显示命中区域（调试用）
    QStringList toolbarIcons;               // 工具栏图标路径列表

    // 构造函数，初始化屏幕适配后的值
    SelectionBoxProperties() {
        selectionBorderWidth = RenderConstants::SELECTION_BORDER_WIDTH();
        anchorSize = RenderConstants::ANCHOR_SIZE();
        anchorBorderWidth = RenderConstants::ANCHOR_BORDER_WIDTH();
    }
};

/**
 * @brief 锚点信息结构
 */
struct AnchorInfo {
    AnchorType type;
    QPointF position;
    QRectF hitRect;                         // 命中测试矩形
    bool visible = true;
};

#endif // SELECTIONUITYPES_H
