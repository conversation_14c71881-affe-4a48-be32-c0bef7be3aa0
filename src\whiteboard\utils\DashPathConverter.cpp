#include "DashPathConverter.h"
#include "../constants/DrawingConstants.h"
#include <QtMath>
#include <QDebug>

DashPathConverter::DashPathConverter()
{
}

QPainterPath DashPathConverter::convertDashToSolid(const QPainterPath& originalPath, const QPen& pen)
{
    if (!needsConversion(pen) || originalPath.isEmpty()) {
        return originalPath;
    }
    
    QVector<qreal> pattern = pen.dashPattern();
    if (pattern.isEmpty()) {
        return originalPath;
    }

    // 转换为虚线段
    QVector<DashSegment> segments = pathToDashSegments(originalPath, pattern, pen.widthF());

    // 转换为实体路径
    QPainterPath solidPath = dashSegmentsToPath(segments);

    qDebug() << "[DASH_CONVERT] Converted dash path with" << segments.size() << "segments";
    return solidPath;
}

bool DashPathConverter::needsConversion(const QPen& pen) const
{
    return pen.style() == Qt::DashLine || 
           pen.style() == Qt::DotLine || 
           pen.style() == Qt::DashDotLine || 
           pen.style() == Qt::DashDotDotLine ||
           pen.style() == Qt::CustomDashLine;
}

QVector<DashPathConverter::DashSegment> DashPathConverter::pathToDashSegments(
    const QPainterPath& path, const QVector<qreal>& pattern, qreal penWidth)
{
    QVector<DashSegment> segments;
    
    if (pattern.isEmpty() || path.isEmpty()) {
        return segments;
    }

    // 使用路径的实际长度和百分比来精确匹配Qt的虚线行为
    qreal pathLength = path.length();
    if (pathLength <= 0.0) {
        return segments;
    }

    qreal patternLength = 0.0;
    for (qreal length : pattern) {
        patternLength += length * penWidth;
    }

    if (patternLength <= 0.0) {
        return segments;
    }

    // 沿路径按虚线模式分段
    qreal currentPathDistance = 0.0;
    int patternIndex = 0;
    bool isDash = true; // 第一个总是实线段
    qreal segmentStartDistance = 0.0;

    while (currentPathDistance < pathLength) {
        // 当前模式段的长度
        qreal currentPatternLength = pattern[patternIndex] * penWidth;
        qreal segmentEndDistance = qMin(pathLength, currentPathDistance + currentPatternLength);
        
        if (isDash && segmentEndDistance > segmentStartDistance) {
            // 创建实线段
            QPointF startPoint = path.pointAtPercent(segmentStartDistance / pathLength);
            QPointF endPoint = path.pointAtPercent(segmentEndDistance / pathLength);
            segments.append(DashSegment(startPoint, endPoint, true));
        }
        
        // 移动到下一个模式段
        currentPathDistance = segmentEndDistance;
        segmentStartDistance = currentPathDistance;
        patternIndex = (patternIndex + 1) % pattern.size();
        isDash = !isDash;
    }

    return segments;
}

QPainterPath DashPathConverter::dashSegmentsToPath(const QVector<DashSegment>& segments)
{
    QPainterPath path;
    
    for (const DashSegment& segment : segments) {
        if (segment.isDash) {
            // 确保每个虚线段都是独立的子路径
            path.moveTo(segment.start);
            path.lineTo(segment.end);
        }
    }
    
    return path;
}

qreal DashPathConverter::distance(const QPointF& p1, const QPointF& p2) const
{
    qreal dx = p2.x() - p1.x();
    qreal dy = p2.y() - p1.y();
    return qSqrt(dx * dx + dy * dy);
}
