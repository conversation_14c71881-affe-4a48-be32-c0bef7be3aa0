#ifndef DASHPATHCONVERTER_H
#define DASHPATHCONVERTER_H

#include <QPainterPath>
#include <QPen>
#include <QVector>
#include <QPointF>

/**
 * @brief 虚线路径转换器
 * 
 * 将虚线样式的路径转换为实体几何路径，解决橡皮擦切割后虚线相位问题
 * 
 * 核心思想：
 * 1. 将虚线模式转换为实际的线段几何
 * 2. 橡皮擦切割实体几何时不会有相位问题
 * 3. 性能优于复杂的相位计算
 */
class DashPathConverter
{
public:
    /**
     * @brief 虚线段信息
     */
    struct DashSegment {
        QPointF start;
        QPointF end;
        bool isDash;  // true=实线段, false=空隙段
        
        DashSegment(const QPointF& s, const QPointF& e, bool dash)
            : start(s), end(e), isDash(dash) {}
    };

public:
    DashPathConverter();
    ~DashPathConverter() = default;

    /**
     * @brief 将虚线路径转换为实体路径
     * @param originalPath 原始路径
     * @param pen 画笔（包含虚线样式）
     * @return 转换后的实体路径，如果不是虚线则返回原路径
     */
    QPainterPath convertDashToSolid(const QPainterPath& originalPath, const QPen& pen);

    /**
     * @brief 检查是否需要转换
     */
    bool needsConversion(const QPen& pen) const;

private:
    /**
     * @brief 将路径转换为虚线段
     */
    QVector<DashSegment> pathToDashSegments(const QPainterPath& path, const QVector<qreal>& pattern, qreal penWidth);

    /**
     * @brief 将虚线段转换为实体路径
     */
    QPainterPath dashSegmentsToPath(const QVector<DashSegment>& segments);

    /**
     * @brief 计算两点间距离
     */
    qreal distance(const QPointF& p1, const QPointF& p2) const;
};

#endif // DASHPATHCONVERTER_H
