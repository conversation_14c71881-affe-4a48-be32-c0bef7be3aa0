#pragma once

#include <QElapsedTimer>
#include <QDebug>
#include <QString>
#include <QHash>
#include <QMutex>

/**
 * @brief 绘制性能分析器 - 用于检测绘制操作的耗时
 * 
 * 功能特点：
 * 1. 支持代码中动态开启/关闭性能检测
 * 2. 提供简单的计时接口，自动输出耗时日志
 * 3. 支持嵌套计时和分类统计
 * 4. 线程安全的计时操作
 */
class DrawingPerformanceProfiler
{
public:
    /**
     * @brief 获取全局单例实例
     */
    static DrawingPerformanceProfiler& instance();

    /**
     * @brief 启用/禁用性能检测
     * @param enabled true=启用，false=禁用
     */
    void setEnabled(bool enabled);

    /**
     * @brief 检查是否启用了性能检测
     */
    bool isEnabled() const;

    /**
     * @brief 开始计时
     * @param name 计时名称，用于标识不同的绘制操作
     * @return 计时器ID，用于结束计时
     */
    int startTiming(const QString& name);

    /**
     * @brief 结束计时并输出日志
     * @param timerId 计时器ID
     */
    void endTiming(int timerId);

    /**
     * @brief 重置所有统计数据
     */
    void reset();

    /**
     * @brief 输出统计摘要
     */
    void printSummary();

private:
    DrawingPerformanceProfiler() = default;
    ~DrawingPerformanceProfiler() = default;
    DrawingPerformanceProfiler(const DrawingPerformanceProfiler&) = delete;
    DrawingPerformanceProfiler& operator=(const DrawingPerformanceProfiler&) = delete;

    struct TimingInfo {
        QString name;
        QElapsedTimer timer;
    };

    struct StatInfo {
        int count = 0;
        qint64 totalTime = 0;
        qint64 minTime = LLONG_MAX;
        qint64 maxTime = 0;
        qint64 avgTime = 0;
    };

    mutable QMutex m_mutex;
    bool m_enabled = false;
    int m_nextTimerId = 1;
    QHash<int, TimingInfo> m_activeTimers;
    QHash<QString, StatInfo> m_statistics;

    void updateStatistics(const QString& name, qint64 elapsedTime);
};

/**
 * @brief RAII风格的计时器类 - 自动管理计时生命周期
 */
class DrawingTimerScope
{
public:
    explicit DrawingTimerScope(const QString& name);
    ~DrawingTimerScope();

private:
    int m_timerId;
    bool m_valid;
};

// 便捷宏定义
#define DRAWING_TIMER(name) DrawingTimerScope _timer(name)
#define DRAWING_TIMER_START(name) DrawingPerformanceProfiler::instance().startTiming(name)
#define DRAWING_TIMER_END(id) DrawingPerformanceProfiler::instance().endTiming(id)
#define DRAWING_PROFILER_ENABLE(enabled) DrawingPerformanceProfiler::instance().setEnabled(enabled)
#define DRAWING_PROFILER_RESET() DrawingPerformanceProfiler::instance().reset()
#define DRAWING_PROFILER_SUMMARY() DrawingPerformanceProfiler::instance().printSummary()
