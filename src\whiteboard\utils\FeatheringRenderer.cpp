#include "FeatheringRenderer.h"
#include <QDebug>
#include <QtMath>

FeatheringRenderer::FeatheringRenderer()
{
}

void FeatheringRenderer::drawPathWithFeathering(QPainter* painter, const QPainterPath& path,
                                              const QPen& pen, const QBrush& brush, ToolType toolType)
{
    if (path.isEmpty() || !painter) {
        return;
    }

    // 保存原始状态
    painter->save();
    
    // 是否开启羽化
    if (toolType != ToolType::FreeDraw || !FEATHERING_ENABLED) {
        painter->setPen(pen);
        painter->setBrush(brush);
        painter->drawPath(path);
        painter->restore();
        return;
    }

    // 获取羽化配置
    FeatheringConfig config = getFeatheringConfig(pen.widthF());

    // 从外到内绘制羽化层
    for (int i = config.layers; i >= 0; i--) {
        QPen featherPen = createFeatherPen(pen, i, config);

        // 绘制当前层
        painter->setPen(featherPen);
        painter->setBrush(brush);
        painter->drawPath(path);
    }
    
    painter->restore();
}

FeatheringRenderer::FeatheringConfig FeatheringRenderer::getFeatheringConfig(qreal lineWidth)
{
    // 根据FloatMenuConstants中的线条宽度返回预设配置
    // PEN_WIDTH_SMALL: 4.3 (适配后)
    // PEN_WIDTH_MEDIUM: 8.5 (适配后)
    // PEN_WIDTH_LARGE: 12.8 (适配后)

    if (lineWidth <= 6.5) {
        return SMALL_PEN_CONFIG;   // 小画笔配置 (≤6.5)
    } else if (lineWidth <= 10.5) {
        return MEDIUM_PEN_CONFIG;  // 中画笔配置 (6.5-10.5)
    } else {
        return LARGE_PEN_CONFIG;   // 大画笔配置 (>10.5)
    }
}

QPen FeatheringRenderer::createFeatherPen(const QPen& originalPen, int layerIndex,
                                         const FeatheringConfig& config)
{
    QPen featherPen = originalPen;
    QColor originalColor = originalPen.color();
    qreal originalWidth = originalPen.widthF();

    featherPen.setCapStyle(Qt::RoundCap);
    featherPen.setJoinStyle(Qt::RoundJoin);

    if (layerIndex == 0) {
        // 最内层：使用原始颜色和宽度
        featherPen.setColor(originalColor);
        featherPen.setWidthF(originalWidth);
    } else {
        // 羽化层：调整透明度和宽度
        qreal layerRatio = static_cast<qreal>(layerIndex) / config.layers;

        qreal alphaMultiplier = 0.3 + (1.0 - layerRatio) * 0.7 * config.intensity;
        qreal widthMultiplier = 1.0 + layerRatio * config.widthFactor;

        // 设置透明度
        QColor featherColor = originalColor;
        int newAlpha = static_cast<int>(originalColor.alpha() * alphaMultiplier);
        featherColor.setAlpha(qMax(30, qMin(255, newAlpha))); // 确保最小30的透明度，最大255

        // 设置线条宽度
        qreal newWidth = originalWidth * widthMultiplier;
        featherPen.setColor(featherColor);
        featherPen.setWidthF(newWidth);
    }

    return featherPen;
}
