#ifndef FEATHERINGRENDERER_H
#define FEATHERINGRENDERER_H

#include <QPainter>
#include <QPen>
#include <QBrush>
#include <QPainterPath>
#include "../core/WhiteBoardTypes.h"

/**
 * @brief 羽化渲染器 - 提供统一的羽化抗锯齿绘制功能
 * 
 * 核心功能：
 * 1. 统一的羽化算法实现
 * 2. 自适应羽化强度计算
 * 3. 支持实时绘制和历史层绘制
 * 4. 性能优化的多层渲染
 */
class FeatheringRenderer
{
public:
    FeatheringRenderer();
    ~FeatheringRenderer() = default;

    /**
     * @brief 使用羽化效果绘制路径
     * @param painter 绘制器
     * @param path 要绘制的路径
     * @param pen 画笔
     * @param brush 画刷
     * @param toolType 工具类型（用于判断是否开启羽化）
     */
    static void drawPathWithFeathering(QPainter* painter, const QPainterPath& path,
                                     const QPen& pen, const QBrush& brush, ToolType toolType);


private:
    // 羽化配置结构
    struct FeatheringConfig {
        int layers;           // 羽化层数
        qreal intensity;      // 羽化强度
        qreal widthFactor;    // 宽度扩展因子
    };

    /**
     * @brief 根据线条宽度获取羽化配置
     * @param lineWidth 线条宽度
     * @return 羽化配置
     */
    static FeatheringConfig getFeatheringConfig(qreal lineWidth);

    /**
     * @brief 创建羽化画笔
     * @param originalPen 原始画笔
     * @param layerIndex 层索引 (0为最内层)
     * @param config 羽化配置
     * @return 羽化画笔
     */
    static QPen createFeatherPen(const QPen& originalPen, int layerIndex,
                               const FeatheringConfig& config);

    // 预设羽化配置
    static constexpr FeatheringConfig SMALL_PEN_CONFIG = {6, 1.0, 1.2};   // 小画笔：强羽化，更多层，更大宽度扩展
    static constexpr FeatheringConfig MEDIUM_PEN_CONFIG = {5, 0.8, 0.8};  // 中画笔：中等羽化
    static constexpr FeatheringConfig LARGE_PEN_CONFIG = {4, 0.6, 0.4};   // 大画笔：轻羽化

    static constexpr bool FEATHERING_ENABLED = false;               // 是否开启羽化效果（默认开启）
};

#endif // FEATHERINGRENDERER_H
