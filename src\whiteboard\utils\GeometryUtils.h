#ifndef GEOMETRYUTILS_H
#define GEOMETRYUTILS_H

#include <QPointF>
#include <QRectF>
#include <QLineF>
#include <QPainterPath>
#include <QSharedPointer>
#include <QList>

/**
 * @brief 图形几何关系工具类
 * 
 * 提供用于处理图形对象之间几何关系的静态工具方法
 */
class GeometryUtils
{
public:

    
    /**
     * @brief 计算两个路径是否相交
     * @param path1 第一个路径
     * @param path2 第二个路径
     * @return 是否相交
     */
    static bool pathsIntersect(const QPainterPath& path1, const QPainterPath& path2);
    
    /**
     * @brief 计算两个路径是否相交，支持为路径2指定宽高
     * @param path1 第一个路径
     * @param path2 第二个路径
     * @param path2Width 路径2的宽度（如橡皮擦宽度）
     * @param path2Height 路径2的高度（如橡皮擦高度）
     * @return 是否相交
     */
    static bool pathsIntersect(const QPainterPath& path1, const QPainterPath& path2, qreal path2Width, qreal path2Height = 0);
    
    /**
     * @brief 判断一个路径是否包含另一个路径
     * @param containerPath 容器路径
     * @param containedPath 被包含路径
     * @return 是否完全包含
     */
    static bool pathContains(const QPainterPath& containerPath, const QPainterPath& containedPath);
    
    /**
     * @brief 将路径转换为字符串
     * @param path 路径
     * @return 路径字符串
     */
    static QString pathToString(const QPainterPath& path);
    
    /**
     * @brief 计算两个路径的最小距离
     * @param path1 第一个路径
     * @param path2 第二个路径
     * @return 两路径间的最小距离
     */
    static qreal minimumPathDistance(const QPainterPath& path1, const QPainterPath& path2);
    
    /**
     * @brief 计算点到路径的最小距离
     * @param point 点
     * @param path 路径
     * @return 最小距离
     */
    static qreal pointToPathDistance(const QPointF& point, const QPainterPath& path);
    
    /**
     * @brief 计算两个线段的交点
     * @param line1 第一条线段
     * @param line2 第二条线段
     * @param intersectionPoint 如果相交，保存交点
     * @return 是否相交
     */
    static bool lineIntersection(const QLineF& line1, const QLineF& line2, QPointF& intersectionPoint);
    
    /**
     * @brief 判断多个路径是否可以组合
     * @param paths 路径列表
     * @return 是否可以组合
     */
    static bool canGroupPaths(const QList<QPainterPath>& paths);

    /**
     * @brief 计算多个路径的共同边界矩形
     * @param paths 路径列表
     * @return 共同边界矩形
     */
    static QRectF combinedBoundingRect(const QList<QPainterPath>& paths);

    /**
     * @brief 计算多个矩形的共同边界矩形
     * @param rects 矩形列表
     * @return 共同边界矩形
     */
    static QRectF combinedBoundingRect(const QList<QRectF>& rects);
    
    /**
     * @brief 确定两个矩形的对齐关系
     * @param rect1 第一个矩形
     * @param rect2 第二个矩形
     * @param tolerance 判断阈值
     * @return 对齐关系类型的组合值
     */
    static int alignmentRelation(const QRectF& rect1, const QRectF& rect2, qreal tolerance = 5.0);

    /**
     * @brief 检查矩形是否在区域内
     * @param innerRect 内部矩形
     * @param outerRect 外部区域
     * @param fullyContained 是否要求完全包含（默认为false，表示相交即可）
     * @return 是否在区域内
     */
    static bool rectInRect(const QRectF& innerRect, const QRectF& outerRect, bool fullyContained = false);
    
    /**
     * @brief 计算两个矩形的重叠区域面积百分比
     * @param rect1 第一个矩形
     * @param rect2 第二个矩形
     * @return 重叠区域占较小矩形的百分比(0-1)
     */
    static qreal rectangleOverlapPercentage(const QRectF& rect1, const QRectF& rect2);
    
    /**
     * @brief 将点对齐到网格
     * @param point 原始点
     * @param gridSize 网格大小
     * @return 对齐后的点
     */
    static QPointF snapToGrid(const QPointF& point, qreal gridSize);
    
    // 对齐关系常量
    static const int ALIGN_NONE = 0;         // 无对齐
    static const int ALIGN_LEFT = 1;         // 左对齐
    static const int ALIGN_RIGHT = 2;        // 右对齐
    static const int ALIGN_TOP = 4;          // 顶部对齐
    static const int ALIGN_BOTTOM = 8;       // 底部对齐
    static const int ALIGN_CENTER_X = 16;    // 水平居中对齐
    static const int ALIGN_CENTER_Y = 32;    // 垂直居中对齐
};

#endif // GEOMETRYUTILS_H 