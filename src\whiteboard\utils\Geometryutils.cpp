#include "geometryutils.h"
#include <cmath>
#include <QPolygonF>
#include <QVector2D>
#include <QLineF>
#include <QPen>
#include <QPainterPathStroker>
#include <algorithm>



bool GeometryUtils::pathsIntersect(const QPainterPath& path1, const QPainterPath& path2)
{
    if (path1.isEmpty() || path2.isEmpty()) {
        return false;
    }

    // 首先检查边界矩形是否相交
    if (!path1.boundingRect().intersects(path2.boundingRect())) {
        return false;
    }

    // 路径相交检测：使用Qt的intersected方法检测填充区域相交
    QPainterPath intersectionPath = path1.intersected(path2);
    return !intersectionPath.isEmpty();
}

bool GeometryUtils::pathsIntersect(const QPainterPath& path1, const QPainterPath& path2, qreal path2Width, qreal path2Height)
{
    if (path1.isEmpty() || path2.isEmpty()) {
        return false;
    }

    // 如果宽度为0，使用常规相交检测
    if (path2Width <= 0) {
        return pathsIntersect(path1, path2);
    }

    // 为路径2创建一个带宽度的路径
    QPainterPath widePath = path2;
    
    // 如果height未指定或为0，则使用width作为高度
    if (path2Height <= 0) {
        path2Height = path2Width;
    }
    
    // 使用笔触创建扩展路径
    QPainterPathStroker stroker;
    stroker.setWidth(path2Width);
    stroker.setCapStyle(Qt::RoundCap);
    stroker.setJoinStyle(Qt::RoundJoin);
    QPainterPath strokePath = stroker.createStroke(path2);
    
    // 将路径2的每个点扩展为矩形区域
    QPainterPath expandedPath;
    for (int i = 0; i < path2.elementCount(); ++i) {
        QPainterPath::Element element = path2.elementAt(i);
        QRectF rect(
            element.x - path2Width / 2.0,
            element.y - path2Height / 2.0,
            path2Width,
            path2Height
        );
        
        // 如果是第一个点，移动到该位置；否则添加矩形
        if (i == 0) {
            expandedPath.addRect(rect);
        } else {
            expandedPath = expandedPath.united(QPainterPath());
            expandedPath.addRect(rect);
        }
    }
    
    // 将扩展路径与笔触路径合并
    widePath = strokePath.united(expandedPath);
    
    // 检查边界矩形是否相交
    if (!path1.boundingRect().intersects(widePath.boundingRect())) {
        return false;
    }

    // 计算相交区域，如果非空则相交
    QPainterPath intersectionPath = path1.intersected(widePath);
    return !intersectionPath.isEmpty();
}

bool GeometryUtils::pathContains(const QPainterPath& containerPath, const QPainterPath& containedPath)
{
    if (containerPath.isEmpty() || containedPath.isEmpty()) {
        return false;
    }

    // 首先检查边界矩形是否包含
    if (!containerPath.boundingRect().contains(containedPath.boundingRect())) {
        return false;
    }

    // 使用路径包含检测
    return containerPath.contains(containedPath);
}


QString GeometryUtils::pathToString(const QPainterPath& path) {
    if (path.isEmpty()) return "空路径";
    
    QRectF bounds = path.boundingRect();
    QString result = QString("边界=(%1,%2,%3,%4), 元素数=%5")
            .arg(bounds.left()).arg(bounds.top())
            .arg(bounds.right()).arg(bounds.bottom())
            .arg(path.elementCount());
            
    return result;
}


qreal GeometryUtils::minimumPathDistance(const QPainterPath& path1, const QPainterPath& path2)
{
    if (path1.isEmpty() || path2.isEmpty()) {
        return std::numeric_limits<qreal>::max();
    }

    // 如果两个路径相交，返回0
    if (pathsIntersect(path1, path2)) {
        return 0.0;
    }

    // 获取边界矩形
    QRectF rect1 = path1.boundingRect();
    QRectF rect2 = path2.boundingRect();

    // 计算矩形之间的最小距离
    qreal dx = 0.0;
    qreal dy = 0.0;

    if (rect1.right() < rect2.left()) {
        dx = rect2.left() - rect1.right();
    } else if (rect2.right() < rect1.left()) {
        dx = rect1.left() - rect2.right();
    }

    if (rect1.bottom() < rect2.top()) {
        dy = rect2.top() - rect1.bottom();
    } else if (rect2.bottom() < rect1.top()) {
        dy = rect1.top() - rect2.bottom();
    }

    return std::sqrt(dx * dx + dy * dy);
}

qreal GeometryUtils::pointToPathDistance(const QPointF& point, const QPainterPath& path)
{
    if (path.isEmpty()) {
        return std::numeric_limits<qreal>::max();
    }

    qreal minDistance = std::numeric_limits<qreal>::max();

    // 方法1：采样路径上的点，计算到目标点的距离
    qreal pathLength = path.length();
    if (pathLength > 0) {
        // 根据路径长度调整采样密度，确保足够精确
        int sampleCount = qMax(100, qMin(1000, int(pathLength / 2)));

        for (int i = 0; i <= sampleCount; ++i) {
            qreal percent = qreal(i) / sampleCount;
            QPointF pathPoint = path.pointAtPercent(percent);

            qreal distance = QLineF(point, pathPoint).length();
            minDistance = qMin(minDistance, distance);
        }
    }

    // 方法2：检查路径的所有元素点
    for (int i = 0; i < path.elementCount(); ++i) {
        QPainterPath::Element element = path.elementAt(i);
        QPointF elementPoint(element.x, element.y);

        qreal distance = QLineF(point, elementPoint).length();
        minDistance = qMin(minDistance, distance);
    }

    // 方法3：如果路径很短，直接计算到起点和终点的距离
    if (pathLength < 10) {
        QPointF startPoint = path.pointAtPercent(0);
        QPointF endPoint = path.pointAtPercent(1);

        qreal startDistance = QLineF(point, startPoint).length();
        qreal endDistance = QLineF(point, endPoint).length();

        minDistance = qMin(minDistance, qMin(startDistance, endDistance));
    }

    return minDistance;
}

bool GeometryUtils::lineIntersection(const QLineF& line1, const QLineF& line2, QPointF& intersectionPoint)
{
    QLineF::IntersectionType type = line1.intersects(line2, &intersectionPoint);
    return type == QLineF::BoundedIntersection;
}

bool GeometryUtils::canGroupPaths(const QList<QPainterPath>& paths)
{
    if (paths.size() < 2) {
        return false;
    }

    // 在实际应用中，可能需要更复杂的逻辑来判断是否可以分组
    // 这里简单地认为只要有两个以上的路径就可以分组
    return true;
}

QRectF GeometryUtils::combinedBoundingRect(const QList<QPainterPath>& paths)
{
    if (paths.isEmpty()) {
        return QRectF();
    }

    QRectF result = paths.first().boundingRect();

    for (int i = 1; i < paths.size(); ++i) {
        result = result.united(paths.at(i).boundingRect());
    }

    return result;
}

QRectF GeometryUtils::combinedBoundingRect(const QList<QRectF>& rects)
{
    if (rects.isEmpty()) {
        return QRectF();
    }

    QRectF result = rects.first();

    for (int i = 1; i < rects.size(); ++i) {
        result = result.united(rects.at(i));
    }

    return result;
}

int GeometryUtils::alignmentRelation(const QRectF& rect1, const QRectF& rect2, qreal tolerance)
{
    int alignment = ALIGN_NONE;

    // 检查左对齐
    if (std::abs(rect1.left() - rect2.left()) <= tolerance) {
        alignment |= ALIGN_LEFT;
    }

    // 检查右对齐
    if (std::abs(rect1.right() - rect2.right()) <= tolerance) {
        alignment |= ALIGN_RIGHT;
    }

    // 检查顶部对齐
    if (std::abs(rect1.top() - rect2.top()) <= tolerance) {
        alignment |= ALIGN_TOP;
    }

    // 检查底部对齐
    if (std::abs(rect1.bottom() - rect2.bottom()) <= tolerance) {
        alignment |= ALIGN_BOTTOM;
    }

    // 检查水平居中
    if (std::abs(rect1.center().x() - rect2.center().x()) <= tolerance) {
        alignment |= ALIGN_CENTER_X;
    }

    // 检查垂直居中
    if (std::abs(rect1.center().y() - rect2.center().y()) <= tolerance) {
        alignment |= ALIGN_CENTER_Y;
    }

    return alignment;
}

bool GeometryUtils::rectInRect(const QRectF& innerRect, const QRectF& outerRect, bool fullyContained)
{
    if (fullyContained) {
        return outerRect.contains(innerRect);
    } else {
        return outerRect.intersects(innerRect);
    }
}

qreal GeometryUtils::rectangleOverlapPercentage(const QRectF& rect1, const QRectF& rect2)
{
    if (!rect1.intersects(rect2)) {
        return 0.0;
    }
    
    QRectF intersection = rect1.intersected(rect2);
    qreal intersectionArea = intersection.width() * intersection.height();
    
    qreal area1 = rect1.width() * rect1.height();
    qreal area2 = rect2.width() * rect2.height();
    
    // 计算重叠区域占较小矩形的百分比
    qreal smallerArea = std::min(area1, area2);
    
    if (smallerArea <= 0) {
        return 0.0;
    }
    
    return intersectionArea / smallerArea;
}

QPointF GeometryUtils::snapToGrid(const QPointF& point, qreal gridSize)
{
    if (gridSize <= 0) {
        return point;
    }
    
    qreal x = std::round(point.x() / gridSize) * gridSize;
    qreal y = std::round(point.y() / gridSize) * gridSize;
    
    return QPointF(x, y);
} 