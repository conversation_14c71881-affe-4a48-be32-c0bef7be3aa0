//
// Created by HLJY on 2025/7/24.
//

#include "LoadDllUtils.h"
#include <QCoreApplication>
#include <QDebug>

// 定义最大目录数和最大文件数
const int MAX_DIRECTORIES = 1000;
const int MAX_FILES = 10000;


bool LoadDllUtils::loadAppDlls(int argc, char *argv[]) {
    // 检查任意参数是否包含 --onlyPreload
    bool shouldOnlyLoadLibrary = false;
    for (int i = 1; i < argc; ++i) {
        if (QString(argv[i]) == "--onlyPreload") {
            shouldOnlyLoadLibrary = true;
            break;
        }
    }

    if (shouldOnlyLoadLibrary) {
        qInfo() << "[MAIN] 检测到 --onlyLoadLibrary 参数，仅加载库文件不启动应用";
        // 扫描当前exe文件所在目录下的所有dll文件，要递归扫描子目录，并加载dll
        // 需在创建QApplication之后调用
        QDir dir(QCoreApplication::instance()->applicationDirPath());
        loadLibrary(dir);

        qInfo() << "[MAIN] 加载库文件完成, 退出应用";
        return true;
    }
    return false;

}

// 使用引用传递计数器
void  LoadDllUtils::loadLibraryRecursive(const QDir &dir, int &dirCount, int &fileCount, int &loadDllCount) {
    if (dirCount >= MAX_DIRECTORIES) {
        qWarning() << "[MAIN] 已达到最大目录数，停止扫描: " << dirCount;
        return;
    }
    qDebug() << "[MAIN] 扫描目录: " << dir.path();
    dirCount++;

    for (const QString& file : dir.entryList(QDir::Files)) {
        if (fileCount >= MAX_FILES) {
            qWarning() << "[MAIN] 已达到最大文件数，停止扫描: " << fileCount;
            break;
        }
        fileCount++;
        if (file.endsWith(".dll")) {
            QLibrary lib(dir.absoluteFilePath(file));
            if (lib.isLoaded()) {
                qDebug() << "[MAIN] 库文件已加载，跳过:" << lib.fileName();
                continue;
            }
            bool success = lib.load();
            if (success) {
                qDebug() << "[MAIN] 加载库文件成功:" << lib.fileName();
                loadDllCount++;
            } else {
                qDebug() << "[MAIN] 加载库文件失败:" << lib.fileName() << lib.errorString();
            }
        }
    }

    // 递归扫描子目录
    for (const QString& subDir : dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot)) {
        if (dirCount >= MAX_DIRECTORIES) {
            qWarning() << "[MAIN] 已达到最大目录数，停止扫描: " << dirCount;
            break;
        }
        QDir subDirPath(dir.path() + "/" + subDir);
        qDebug() << "[MAIN] 递归扫描子目录: " << subDirPath.path();
        loadLibraryRecursive(subDirPath, dirCount, fileCount, loadDllCount);
    }
}

void  LoadDllUtils::loadLibrary(const QDir &dir) {
    int dirCount = 0;
    int fileCount = 0;
    int loadDllCount = 0;
    loadLibraryRecursive(dir, dirCount, fileCount, loadDllCount);
    qInfo() << "[MAIN] 扫描完成，目录数:" << dirCount << "文件数:" << fileCount << "加载DLL数:" << loadDllCount;
}