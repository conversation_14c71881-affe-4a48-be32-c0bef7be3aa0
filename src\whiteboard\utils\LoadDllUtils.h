//
// Created by HLJY on 2025/7/24.
//

#ifndef HL_WHITEBOARD_QT_LOADDLLUTILS_H
#define HL_WHITEBOARD_QT_LOADDLLUTILS_H
#include <QDir>
#include <QLibrary>

class LoadDllUtils {
public:
    static bool loadAppDlls(int argc, char *argv[]);
private:
    static void loadLibraryRecursive(const QDir &dir, int &dirCount, int &fileCount, int &loadDllCount);
    static void loadLibrary(const QDir &dir);
};


#endif //HL_WHITEBOARD_QT_LOADDLLUTILS_H
