﻿//
// Created by HLJY on 2025/6/19.
//

#include "QtCefZmqHandler.h"

#include "src/components/jsbridge/JSBridge.h"
#include "src/components/zmq/common/ZmqCommon.h"
#include "src/components/zmq/server/ZmqServer.h"

void QtCefZmqHandler::registerHandler() {
    ZmqServer::instance()->registerHandler("callQtCef", &callQtCef);
}

void QtCefZmqHandler::callQtCef(const ZmqMsg &request, ZmqResponseCallback callback) {
    auto cefName = request.getData()["cefName"];
    if (cefName.is_null()) {
        // call qt
        qInfo() << "callQtCef cefName is null!";
    }
    else {
        // call cef
        auto cefMethod = request.getData()["cefMethod"].get<std::string>();
        auto cefData = request.getData()["cefData"];

        auto jsRequest = JSBridgeMsg::newRequest(request.getId(), cefMethod, cefData);
        JSBridge::callJs( cefName.get<std::string>().c_str(), jsRequest, [request, callback](const JSBridgeContext& result) {
            std::cout << "callJs " << result.getData().serialize() << std::endl;
            const ZmqMsg res = ZmqMsg::newResponse(request, request.getData(), "200");

            callback(res);
        });
    }
}