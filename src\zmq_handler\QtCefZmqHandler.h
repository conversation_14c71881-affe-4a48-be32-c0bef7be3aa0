﻿//
// Created by HLJY on 2025/6/19.
//

#ifndef QTCEFZMQHANDLER_H
#define QTCEFZMQHANDLER_H
#include "src/components/zmq/common/ZmqCommon.h"
#include "src/components/zmq/handler/ZmqAutoRegister.h"
#include "src/components/zmq/handler/ZmqHandler.h"


class QtCefZmqHandler: public ZmqHandler {
public:
    void registerHandler();

    static void callQtCef(const ZmqMsg &request, ZmqResponseCallback callback);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static ZmqAutoRegister<QtCefZmqHandler> registrar;
};



#endif //QTCEFZMQHANDLER_H
