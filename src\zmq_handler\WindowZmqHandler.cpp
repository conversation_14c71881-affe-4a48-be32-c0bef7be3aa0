﻿//
// Created by HLJY on 2025/6/20.
//

#include <src/components/zmq/server/ZmqServer.h>
#include "WindowZmqHandler.h"
#include <QGuiApplication>
#include <QWindow>
#include <nlohmann/json.hpp>
#include <QCoreApplication>
#include <src/utils/WindowUtils.h>


void WindowZmqHandler::registerHandler() {
    ZmqServer::instance()->register<PERSON>and<PERSON>("win-restore", &restoreWindow);
    ZmqServer::instance()->registerHandler("win-minimize", &minimizeWindow);
    ZmqServer::instance()->registerHandler("app-exit", &exitApplication);
}

void WindowZmqHandler::restoreWindow(const ZmqMsg &request, ZmqResponseCallback callback) {
    WindowUtils::restoreMinimizeWindow();

    nlohmann::json j = {};
    callback(ZmqMsg::newResponse(request, j));
}

void WindowZmqHandler::minimizeWindow(const ZmqMsg &request, ZmqResponseCallback callback)
{
    WindowUtils::minimizeAllWindow();

    nlohmann::json j = {};
    callback(ZmqMsg::newResponse(request, j));
}

void WindowZmqHandler::exitApplication(const ZmqMsg &request, ZmqResponseCallback callback)
{
    nlohmann::json j = {};
    auto app = QCoreApplication::instance();
    if (app) {
        QMetaObject::invokeMethod(app, "quit", Qt::QueuedConnection);
    }
    callback(ZmqMsg::newResponse(request, j));
}
