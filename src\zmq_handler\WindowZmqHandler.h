﻿//
// Created by <PERSON>LJ<PERSON> on 2025/6/20.
//

#ifndef HL_WHITEBOARD_QT_WINDOWZMQHANDLER_H
#define HL_WHITEBOARD_QT_WINDOWZMQHANDLER_H


#include <src/components/zmq/handler/ZmqHandler.h>
#include <src/components/zmq/common/ZmqCommon.h>
#include <src/components/zmq/handler/ZmqAutoRegister.h>

class WindowZmqHandler: public ZmqHandler {
public:
    void registerHandler();

    /** 恢复窗口 */
    static void restoreWindow(const ZmqMsg &request, ZmqResponseCallback callback);
    /** 最小化窗口 */
    static void minimizeWindow(const ZmqMsg &request, ZmqResponseCallback callback);
    /** 退出应用 */
    static void exitApplication(const ZmqMsg &request, ZmqResponseCallback callback);

private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static ZmqAutoRegister<WindowZmqHandler> registrar;
};



#endif //HL_WHITEBOARD_QT_WINDOWZMQHANDLER_H
