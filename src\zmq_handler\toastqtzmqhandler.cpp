#include "toastqtzmqhandler.h"
#include "src/components/zmq/common/ZmqCommon.h"
#include "src/components/toast/Toast.h"
#include "src/components/zmq/server/ZmqServer.h"

ToastQtZmqHandler::ToastQtZmqHandler() {}
void ToastQtZmqHandler::registerHandler() {
    ZmqServer::instance()->registerHandler("toast", &toast);
}

void ToastQtZmqHandler::toast(const ZmqMsg &request, ZmqResponseCallback callback) {
    json j = request.getData();
    Toast::show(j);
    callback(ZmqMsg::newResponse(request, request.getData(), "200"));
}
