#ifndef TOASTQTZMQHANDLER_H
#define TOASTQTZMQHANDLER_H
#include "src/components/zmq/common/ZmqCommon.h"
#include "src/components/zmq/handler/ZmqAutoRegister.h"
#include "src/components/zmq/handler/ZmqHandler.h"
class ToastQtZmqHandler: public ZmqHandler
{
public:
    void registerHandler();
    ToastQtZmqHandler();
    static void toast(const ZmqMsg &request, ZmqResponseCallback callback);
private:
    // 自动注册（每个Handler类都需要这个静态成员）
    inline static ZmqAutoRegister<ToastQtZmqHandler> registrar;
};

#endif // TOASTQTZMQHANDLER_H
