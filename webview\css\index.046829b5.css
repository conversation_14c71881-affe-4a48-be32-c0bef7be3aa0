[data-theme='ocean'] {
  --bg-image: url('../webp/<EMAIL>');
  --el-color-primary: var(--color-primary);
}
:root {
  --min-width: 106.66667rem;
  --min-height: 60rem;
  --view-width: 145.91667rem;
  /** Color definitions */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-primary: #5F52E3;
  --color-success: #29DA80;
  --color-warning: #FFAA08;
  --color-error: #DA2934;
  --color-info: #B4B4B4;
  --color-warning-light: #FFD151;
  --color-error-light: #FF969D;
  --color-info-light: #D1D1D1;
  --color-white-light-85: rgba(255, 255, 255, 0.85);
  --color-white-light-65: rgba(255, 255, 255, 0.65);
  --color-white-light-45: #EFEFEF;
  --color-white-light-25: rgba(255, 255, 255, 0.25);
  --color-black-light-85: rgba(0, 0, 0, 0.85);
  --color-black-light-65: rgba(0, 0, 0, 0.65);
  --color-black-light-45: rgba(0, 0, 0, 0.45);
  --color-black-light-25: rgba(0, 0, 0, 0.25);
  --color-primary-light-9: rgba(95, 82, 227, 0.9);
  --color-primary-light-8: rgba(95, 82, 227, 0.8);
  --color-primary-light-7: rgba(95, 82, 227, 0.7);
  --color-primary-light-6: rgba(95, 82, 227, 0.6);
  --color-primary-light-5: rgba(95, 82, 227, 0.5);
  --color-primary-light-45: rgba(95, 82, 227, 0.45);
  --color-primary-light-4: rgba(95, 82, 227, 0.4);
  --color-primary-light-3: rgba(95, 82, 227, 0.3);
  --color-primary-light-2: rgba(95, 82, 227, 0.2);
  --color-primary-light-1: rgba(95, 82, 227, 0.1);
  --color-primary-dark-2: rgba(95, 82, 227, 0.2);
  --color-success-light-5: rgba(0, 153, 51, 0.5);
  --color-success-light-1: rgba(0, 153, 51, 0.1);
  /** Text color definitions */
  --text-color-primary: var(--color-primary);
  --text-color: var(--color-black);
  --text-color-regular: var(--color-black-light-85);
  --text-color-secondary: var(--color-black-light-65);
  --text-color-placeholder: var(--color-black-light-45);
  --text-color-disabled: var(--color-black-light-25);
  /** Border color definitions */
  --border-color: #dcdfe6;
  /** Fill color definitions */
  /** Background color definitions */
  /** Border properties */
  /** Border radius */
  /** Box shadow definitions */
  /** Typography */
  --font-family: "'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif";
  --font-size-large: 5.41667rem;
  --font-size-medium: 3.75rem;
  --font-size-base: 2.16667rem;
  --font-size-small: 1.91667rem;
  --font-size-extra-small: 1.75rem;
  --font-size-mini: 1.5rem;
  --font-family-number: 'Roboto-Black';
  --font-family-title: 'DouyinSansBold';
  /** Z-index */
  --z-index-normal: 1;
  --z-index-top: 1000;
  --z-index-popper: 2000;
  --z-index-dialog: 2500;
  --z-index-toolbar: 3000;
  --z-index-confirm: 4000;
  --z-index-system: 5000;
  /** Disabled styles */
  /** Common component size */
  /** Overlay color */
  /** Mask color */
  /** 板块间距 */
  --block-margin-default: 2.33333rem;
  --block-margin-small: 1.58333rem;
  /** 表单组件 */
  --select-background-color: #EFEEFC;
}
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
}
ul {
  list-style: none;
}
:focus {
  outline: none;
}
.flex {
  display: flex;
}
.flex-jc {
  justify-content: center;
}
.flex-ac {
  align-items: center;
}
.flex-v {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.active-scale {
  transform: scale(1);
  transition: transform 0.2s ease;
}
.active-scale:active {
  transform: scale(0.8);
  transition: transform 0.2s ease;
}
.pressed-scale {
  transform: scale(1);
  transition: transform 0.2s ease;
}
.pressed-scale.pressed {
  transform: scale(0.8);
  transition: transform 0.2s ease;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /* 限制文本为2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}
/* 渐入渐出的过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity ;
}
.fade-from,
.fade-leave-to {
  opacity: 0;
}
/* 显示时从小到大 */
.scale-fade-enter-active,
.scale-fade-leave-active {
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.scale-fade-enter-from,
.scale-fade-leave-to {
  transform: scale(0);
  /* 初始状态：缩小 */
  opacity: 0;
}
/* toast.css */
.toast {
  display: flex;
  align-items: center;
  height: 5.5rem;
  min-width: 28.16667rem;
  max-width: 83.33333rem;
  border-radius: 3.91667rem;
  box-shadow: 0 0.16667rem 1rem 0 rgba(0, 4, 61, 0.2);
  background-color: var(--color-info-light);
  transition: all 0.3s ease-in-out;
  opacity: 1;
  position: fixed;
  z-index: 9999999;
}
.toast .toast-icon-container {
  width: 4.5rem;
  height: 4.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 0.5rem;
  background-color: var(--color-info);
}
.toast .toast-icon {
  color: var(--color-white);
  font-size: 2.91667rem;
  width: 2.16667rem;
  height: 2.16667rem;
}
.toast .toast-message {
  color: var(--color-white);
  font-size: var(--font-size-base);
  line-height: var(--font-size-base);
  font-family: var(--font-family-title);
  padding: 0.33333rem 3.33333rem 0rem;
  text-align: center;
  flex: 1;
  letter-spacing: 0.16667rem;
}
.toast .toast-message.toast-message-few {
  letter-spacing: 0.75rem;
}
.toast-content {
  white-space: nowrap;
  display: flex;
  align-items: center;
  width: 100%;
}
.toast-success {
  background-color: var(--color-success);
}
.toast-success .toast-icon {
  color: var(--color-success);
}
.toast-success .toast-icon-container {
  background-color: rgba(255, 255, 255, 0.85);
}
.toast-warning {
  background-color: var(--color-warning-light);
}
.toast-warning .toast-icon-container {
  background-color: var(--color-warning);
}
.toast-error {
  background-color: var(--color-error-light);
}
.toast-error .toast-icon-container {
  background-color: var(--color-error);
}
.toast-error .toast-icon {
  font-size: 2.16667rem;
}
.common-select.el-select__popper {
  background: var(--select-background-color) !important;
  box-shadow: 0rem 0rem 0.83333rem 0rem rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  border-radius: 0 0 1.58333rem 1.58333rem !important;
  z-index: 99999999999;
  transition: none !important;
  /* 确保优先级足够高 */
}
.common-select.el-select__popper:before {
  top: 0;
  content: '';
  position: absolute;
  border-top: 0.08333rem dashed var(--color-primary);
  opacity: 0.5;
  left: 1.58333rem;
  right: 1.58333rem;
}
.common-select .el-select-dropdown {
  padding-bottom: 0.83333rem;
}
.common-select .el-select-dropdown__item {
  color: var(--text-color-regular);
  font-size: var(--font-size-base);
  line-height: 4.16667rem;
  height: 4.16667rem;
}
.common-select .el-select-dropdown__item.is-hovering {
  background: transparent !important;
}
.common-select .el-select-dropdown__item.is-selected {
  color: var(--color-primary) !important;
}
.common-select .el-select-dropdown__empty {
  color: var(--text-color-disabled);
  font-size: var(--font-size-extra-small);
  line-height: 4.16667rem;
}
.common-select .el-select-group__title {
  color: var(--text-color-disabled);
  font-size: var(--font-size-extra-small);
  line-height: 3.41667rem;
  height: 3.41667rem;
}
.common-button.el-button {
  --el-button-bg-color: var(--color-white-light-45);
  --el-button-text-color: var(--text-color-primary);
  --el-button-disabled-text-color: var(--text-color-disabled);
  --el-button-disabled-bg-color: var(--el-fill-color-blank);
  --el-button-hover-text-color: var(--text-color-primary);
  --el-button-hover-bg-color: var(--color-white-light-45);
  --el-button-active-text-color: var(--text-color-primary);
  --el-button-active-bg-color: var(--color-white-light-45);
  --el-button-hover-link-text-color: var(--text-color-primary);
  --el-button-active-color: var(--text-color-primary);
  font-size: 2.16667rem;
  line-height: 2.83333rem;
  font-weight: 400;
  letter-spacing: 0.33333rem;
  border: none;
  height: 6rem;
  padding: 1.58333rem;
  border-radius: 1.58333rem;
  outline: none;
}
.common-button.el-button + .el-button {
  margin-left: 2.33333rem;
}
.common-button.el-button--primary {
  --el-button-text-color: var(--color-white);
  --el-button-bg-color: var(--color-success);
  --el-button-active-color: var(--color-white);
  --el-button-hover-text-color: var(--color-white);
  --el-button-hover-link-text-color: var(--color-white);
  --el-button-hover-bg-color: var(--color-success);
  --el-button-active-bg-color: var(--color-success);
  --el-button-disabled-text-color: var(--color-white);
  --el-button-disabled-bg-color: var(--color-success-light-5);
  --el-button-active-text-color: var(--color-white);
}
.common-button.el-button--small {
  padding: 0.83333rem !important;
  font-size: 1.5rem;
  line-height: 1.5rem;
  height: auto;
  border-radius: 0.75rem;
  letter-spacing: 0.08333rem;
  min-width: 7.83333rem;
}
.common-button.el-button--small.el-button + .el-button {
  margin-left: 1.58333rem;
}
.common-button.el-button--info {
  --el-button-text-color: var(--text-color-primary);
  --el-button-hover-text-color: var(--text-color-primary);
  --el-button-hover-link-text-color: var(--text-color-primary);
  --el-button-disabled-text-color: var(--text-color-primary);
  --el-button-active-text-color: var(--text-color-primary);
  --el-button-bg-color: #A7A0F0;
  --el-button-hover-bg-color: #A7A0F0;
  --el-button-active-bg-color: #A7A0F0;
  --el-button-disabled-bg-color: #A7A0F0;
}
.common-pop-confirm.el-popper.is-light {
  padding: 1.58333rem;
  border-radius: 2.5rem;
  background: linear-gradient(180deg, #956EF7 0%, #6053E3 100%);
  color: #ffffff;
  width: auto !important;
  max-width: 41.66667rem;
  border: none;
  transition: none;
}
.common-pop-confirm.el-popper.is-light .el-popconfirm__icon {
  display: none;
}
.common-pop-confirm.el-popper.is-light .el-popconfirm__main {
  justify-content: center;
  font-family: var(--font-family-title);
  font-size: 1.91667rem;
  height: 1.91667rem;
}
.common-pop-confirm.el-popper.is-light .el-popconfirm__action {
  margin-top: 1.58333rem;
  font-size: 1.75rem;
}
.common-pop-confirm.el-popper.is-light .el-popper__arrow:before {
  background: #5F52E3;
  border-color: #5F52E3;
}
.custom-dialog-close-btn {
  width: 3rem;
  height: 3rem;
  background: #FFFFFF;
  border-radius: 0.66667rem;
  border: 0.16667rem solid rgba(0, 0, 0, 0.25);
  opacity: 0.75;
}
.custom-dialog-close-btn::before {
  content: '';
  display: block;
  width: 2.66667rem;
  height: 2.66667rem;
  margin-top: 0.16667rem;
  margin-left: 0.16667rem;
  background: url('data:image/webp;base64,UklGRhgDAABXRUJQVlA4WAoAAAAwAAAAQwAAQwAASUNDUMgBAAAAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADZWUDhMKQEAAC9DwBAQfxARCto2cm7v43AAHt/jD0B7yNiHf1E6ACuSbddKP3LygASkgAOQgINkBClPwrNAVJDzqXvq3F17//IV0X9Gbts48oWzvXmwfwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4Pk4vInfvVRa2wzu4bZdJHMa3MNfMi+dwLafykkncJnc8bUTmiayQGCBuSVmlhit8dTawZQara2m2hQZrFExaEKvVi5hmXq1yv50iq7OOiX7WYZq1Rz7WXPWz5ojNWdUnFMxQTy9pz6GrThKzeRe6bBaN6l8LeNqHaZyvh3exPXif+1vBgA=') no-repeat center center;
  background-size: 100% 100%;
}
.vdr.window {
  border: none;
  outline: 0.5rem solid var(--color-primary);
}
.vdr.window .handle {
  width: 1.66667rem;
  height: 1.66667rem;
  background: #ffffff;
  border: 0.5rem solid var(--color-primary);
  border-radius: 50%;
  z-index: 1;
}
.vdr.window .handle-tl {
  top: -1.08333rem;
  left: -1.08333rem;
}
.vdr.window .handle-tm {
  top: -1.08333rem;
}
.vdr.window .handle-tr {
  top: -1.08333rem;
  right: -1.08333rem;
}
.vdr.window .handle-mr {
  bottom: -1.08333rem;
  right: -1.08333rem;
}
.vdr.window .handle-br {
  right: -1.08333rem;
}
.vdr.window .handle-bm {
  bottom: -1.08333rem;
}
.vdr.window .handle-bl {
  bottom: -1.08333rem;
  left: -1.08333rem;
}
.vdr.window .handle-ml {
  left: -1.08333rem;
}
.vdr.permanent .handle {
  display: block !important;
}
body {
  overflow: auto;
  width: 100vw;
  height: 100vh;
  user-select: none;
}
@media (max-width: 720px) {
  .text {
    font-size: 1.66667rem;
  }
}
@media (max-width: 620px) {
  .versions {
    display: none;
  }
}
@media (max-width: 350px) {
  .tip,
  .actions {
    display: none;
  }
}
/* 滚动条容器 */
::-webkit-scrollbar {
  width: 0.66667rem;
  /* 设置滚动条宽度 */
  height: 0.66667rem;
  /* 设置水平滚动条的高度 */
}
/* 滚动条滑块 */
/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: transparent;
  /* 轨道颜色透明 */
  border-radius: 0.33333rem;
  /* 圆角 */
}
/* 鼠标悬停时的滚动条滑块 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
  /* 滚动条滑块悬停时的颜色 */
}
iframe {
  border: none !important;
}
