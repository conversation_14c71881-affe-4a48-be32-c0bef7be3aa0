:root{--el-popup-modal-bg-color:var(--el-color-black);--el-popup-modal-opacity:0.5}.v-modal-enter{animation:v-modal-in var(--el-transition-duration-fast) ease}.v-modal-leave{animation:v-modal-out var(--el-transition-duration-fast) ease forwards}@keyframes v-modal-in{0%{opacity:0}}@keyframes v-modal-out{to{opacity:0}}.v-modal{background:var(--el-popup-modal-bg-color);height:100%;left:0;opacity:var(--el-popup-modal-opacity);position:fixed;top:0;width:100%}.el-popup-parent--hidden{overflow:hidden}.el-dialog{--el-dialog-width:50%;--el-dialog-margin-top:15vh;--el-dialog-bg-color:var(--el-bg-color);--el-dialog-box-shadow:var(--el-box-shadow);--el-dialog-title-font-size:var(--el-font-size-large);--el-dialog-content-font-size:1.16667rem;--el-dialog-font-line-height:var(--el-font-line-height-primary);--el-dialog-padding-primary:1.33333rem;--el-dialog-border-radius:var(--el-border-radius-base);background:var(--el-dialog-bg-color);border-radius:var(--el-dialog-border-radius);box-shadow:var(--el-dialog-box-shadow);box-sizing:border-box;margin:var(--el-dialog-margin-top,15vh) auto 4.16667rem;overflow-wrap:break-word;padding:var(--el-dialog-padding-primary);position:relative;width:var(--el-dialog-width,50%)}.el-dialog:focus{outline:none!important}.el-dialog.is-align-center{margin:auto}.el-dialog.is-fullscreen{--el-dialog-width:100%;--el-dialog-margin-top:0;border-radius:0;height:100%;margin-bottom:0;overflow:auto}.el-dialog__wrapper{bottom:0;left:0;margin:0;overflow:auto;position:fixed;right:0;top:0}.el-dialog.is-draggable .el-dialog__header{cursor:move;-webkit-user-select:none;-moz-user-select:none;user-select:none}.el-dialog__header{padding-bottom:var(--el-dialog-padding-primary)}.el-dialog__header.show-close{padding-right:calc(var(--el-dialog-padding-primary) + var(--el-message-close-size, 1.33333rem))}.el-dialog__headerbtn{background:transparent;border:none;cursor:pointer;font-size:var(--el-message-close-size,1.33333rem);height:4rem;outline:none;padding:0;position:absolute;right:0;top:0;width:4rem}.el-dialog__headerbtn .el-dialog__close{color:var(--el-color-info);font-size:inherit}.el-dialog__headerbtn:focus .el-dialog__close,.el-dialog__headerbtn:hover .el-dialog__close{color:var(--el-color-primary)}.el-dialog__title{color:var(--el-text-color-primary);font-size:var(--el-dialog-title-font-size);line-height:var(--el-dialog-font-line-height)}.el-dialog__body{color:var(--el-text-color-regular);font-size:var(--el-dialog-content-font-size)}.el-dialog__footer{box-sizing:border-box;padding-top:var(--el-dialog-padding-primary);text-align:right}.el-dialog--center{text-align:center}.el-dialog--center .el-dialog__body{text-align:initial}.el-dialog--center .el-dialog__footer{text-align:inherit}.el-overlay-dialog{bottom:0;left:0;overflow:auto;position:fixed;right:0;top:0}.dialog-fade-enter-active{animation:modal-fade-in var(--el-transition-duration)}.dialog-fade-enter-active .el-overlay-dialog{animation:dialog-fade-in var(--el-transition-duration)}.dialog-fade-leave-active{animation:modal-fade-out var(--el-transition-duration)}.dialog-fade-leave-active .el-overlay-dialog{animation:dialog-fade-out var(--el-transition-duration)}@keyframes dialog-fade-in{0%{opacity:0;transform:translate3d(0,-1.66667rem,0)}to{opacity:1;transform:translateZ(0)}}@keyframes dialog-fade-out{0%{opacity:1;transform:translateZ(0)}to{opacity:0;transform:translate3d(0,-1.66667rem,0)}}@keyframes modal-fade-in{0%{opacity:0}to{opacity:1}}@keyframes modal-fade-out{0%{opacity:1}to{opacity:0}}.el-overlay{background-color:var(--el-overlay-color-lighter);bottom:0;height:100%;left:0;overflow:auto;position:fixed;right:0;top:0;z-index:2000}.el-overlay .el-overlay-root{height:0}.not-finished-status[data-v-f291b3c9] {
  text-align: center;
}
.not-finished-status .status-img[data-v-f291b3c9] {
  width: 18.41667rem;
  height: 14.16667rem;
  margin-bottom: 3.08333rem;
}
.not-finished-status .status-text[data-v-f291b3c9] {
  font-size: 1.91667rem;
  color: rgba(0, 0, 0, 0.45);
  letter-spacing: 0.08333rem;
  margin-bottom: 10.83333rem;
}
.not-finished-status .status-btns .el-button[data-v-f291b3c9] {
  width: 15.66667rem;
  height: 6rem;
  border-radius: 1.66667rem;
  font-weight: 500;
  font-size: 2.16667rem;
  letter-spacing: 0.33333rem;
  background: linear-gradient(180deg, #988AF3 0%, #5F52E3 100%), #29DA80;
  color: #FFFFFF;
}
.not-finished-status[data-v-37d9d44b] {
  text-align: center;
}
.not-finished-status .status-img[data-v-37d9d44b] {
  width: 18.41667rem;
  margin-bottom: 2.5rem;
}
.not-finished-status .status-text[data-v-37d9d44b] {
  font-size: 1.91667rem;
  color: rgba(0, 0, 0, 0.45);
  letter-spacing: 0.08333rem;
  margin-bottom: 10.83333rem;
}
.not-finished-status .status-btns .el-button[data-v-37d9d44b] {
  width: 15.66667rem;
  height: 6rem;
  background: #efefef;
  border-radius: 1.66667rem;
  font-weight: 500;
  font-size: 2.16667rem;
  color: #afaeb6;
  letter-spacing: 0.33333rem;
}
.not-finished-status .status-btns .start-button[data-v-37d9d44b] {
  margin-left: 2.33333rem;
  background: linear-gradient(180deg, #988af3 0%, #5f52e3 100%), #29da80;
  color: #ffffff;
}
.ai-summarized[data-v-e7ae99d0] {
  width: 100%;
  height: 100%;
}
.whiteboard-ai-summarized[data-v-e7ae99d0] .whiteboard {
  z-index: 2;
}
.progress-gif[data-v-e4bb0b33] {
  width: 16.08333rem;
}
.el-progress-text[data-v-e4bb0b33] {
  text-align: center;
  margin-top: 0rem;
  font-size: 1.91667rem;
  margin-top: -1.66667rem;
  color: rgba(0, 0, 0, 0.25);
}
.not-finished-status[data-v-ca8c6c09] {
  text-align: center;
}
.not-finished-status .status-img[data-v-ca8c6c09] {
  width: 16.91667rem;
  margin-bottom: 2.5rem;
}
.not-finished-status .status-text[data-v-ca8c6c09] {
  font-size: 1.91667rem;
  color: rgba(0, 0, 0, 0.45);
  letter-spacing: 0.08333rem;
  margin-bottom: 10.83333rem;
}
.not-finished-status .status-btns .el-button[data-v-ca8c6c09] {
  width: 15.66667rem;
  height: 6rem;
  background: #efefef;
  border-radius: 1.66667rem;
  font-weight: 500;
  font-size: 2.16667rem;
  color: #afaeb6;
  letter-spacing: 0.33333rem;
}
.not-finished-status .status-btns .start-button[data-v-ca8c6c09] {
  margin-left: 2.33333rem;
  background: linear-gradient(180deg, #988af3 0%, #5f52e3 100%), #29da80;
  color: #ffffff;
}
.class-summary[data-v-33c5d4e2] {
  display: flex;
  justify-content: center;
  align-items: center;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  position: fixed;
  background: rgba(0, 0, 0, 0.45);
  backdrop-filter: blur(1rem);
}
.class-summary-content[data-v-33c5d4e2] {
  position: relative;
  display: flex;
  height: 76.58333rem;
}
.class-summary-content .close-icon[data-v-33c5d4e2] {
  position: absolute;
  bottom: 0rem;
  left: 50%;
  transform: translateX(-50%);
  width: 3.75rem;
  height: 3.75rem;
  cursor: pointer;
}
.class-summary-content .set-icon[data-v-33c5d4e2] {
  position: absolute;
  bottom: 0rem;
  left: 57%;
  transform: translateX(-50%);
  cursor: pointer;
  width: 9.91667rem;
  height: 3.75rem;
  text-align: center;
  line-height: 3.75rem;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 1.91667rem;
  font-weight: 500;
  font-size: 2.16667rem;
  color: #ffffff;
}
.class-summary-content .set-icon .setting[data-v-33c5d4e2] {
  font-size: 2.5rem;
}
.class-summary-content .class-summary-classify[data-v-33c5d4e2] {
  width: 15.5rem;
  height: 62.66667rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 3.16667rem 0rem 0rem 3.16667rem;
  border: 0.41667rem solid #ffffff;
  backdrop-filter: blur(1.83333rem);
  margin-top: 8.16667rem;
  box-sizing: border-box;
  margin-right: -3.08333rem;
  padding-left: 1.33333rem;
}
.class-summary-content .class-summary-classify .class-date[data-v-33c5d4e2] {
  padding-left: 0.5rem;
  font-weight: 500;
  font-size: 1.58333rem;
  color: rgba(0, 0, 0, 0.65);
  margin: 1.66667rem 0 2.5rem;
}
.class-summary-content .class-summary-classify .classify-item[data-v-33c5d4e2] {
  position: relative;
  width: 10.66667rem;
  height: 4.33333rem;
  line-height: 4.33333rem;
  font-size: 1.83333rem;
  color: rgba(0, 0, 0, 0.65);
  padding-left: 1.5rem;
  box-sizing: border-box;
  cursor: pointer;
}
.class-summary-content .class-summary-classify .classify-item .classify-item-active-img[data-v-33c5d4e2] {
  display: none;
}
.class-summary-content .class-summary-classify .classify-item-active[data-v-33c5d4e2] {
  color: #5f52e3;
  font-weight: 600;
}
.class-summary-content .class-summary-classify .classify-item-active .classify-item-active-img[data-v-33c5d4e2] {
  display: block;
  position: absolute;
  width: 11.33333rem;
  top: -1rem;
  right: -0.08333rem;
  z-index: -1;
}
.class-summary-content .class-summary-detail[data-v-33c5d4e2] {
  width: 137.58333rem;
  height: 70.83333rem;
  background: #ffffff;
  border-radius: 3.16667rem;
  backdrop-filter: blur(1.83333rem);
  box-sizing: border-box;
  padding: 2.75rem 4.25rem 3rem;
  display: flex;
  flex-direction: column;
}
.class-summary-content .class-summary-detail .class-summary-detail__title[data-v-33c5d4e2] {
  width: 15.66667rem;
  margin: 0 auto 1.66667rem;
}
.class-summary-content .class-summary-detail .class-summary-detail__info[data-v-33c5d4e2] {
  width: 100%;
  height: calc(100% - 4.5rem);
  display: flex;
  justify-content: center;
  align-items: center;
}
.summary-dialog {
  background: transparent !important;
  box-shadow: unset !important;
}
.summary-dialog .el-dialog__header {
  display: none;
}
