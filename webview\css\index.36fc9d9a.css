.thumbnail-list[data-v-c9701fff] {
  height: 59.5rem;
  background: var(--color-black-light-65);
  border-radius: 3.16667rem;
  backdrop-filter: blur(0.83333rem);
  overflow: hidden;
}
.thumbnail-list-title-wrap[data-v-c9701fff] {
  width: 19.08333rem;
  height: 3.91667rem;
  margin: 0 auto;
  padding: 1.25rem 0;
}
.thumbnail-list-title[data-v-c9701fff] {
  color: var(--color-white);
  font-size: var(--font-size-extra-small);
  height: 1.75rem;
  line-height: 1.75rem;
  letter-spacing: 0.08333rem;
}
.thumbnail-list-title--small[data-v-c9701fff] {
  font-size: var(--font-size-mini);
  line-height: 1.91667rem;
  height: 3.91667rem;
}
.thumbnail-item[data-v-c9701fff] {
  line-height: 10.08333rem;
  overflow: hidden;
  position: relative;
}
.thumbnail-item + .thumbnail-item[data-v-c9701fff] {
  margin-top: 1.58333rem;
}
.thumbnail-item__image[data-v-c9701fff] {
  width: 16.41667rem;
  height: 9.33333rem;
  border-radius: 1.58333rem;
  border: 0.16667rem solid var(--color-white);
}
.thumbnail-item__num[data-v-c9701fff] {
  width: 4.75rem;
  height: 100%;
  font-weight: 600;
  font-size: var(--font-size-extra-small);
  color: var(--color-white);
  letter-spacing: 0.08333rem;
  text-align: center;
}
.thumbnail-item__active[data-v-c9701fff] {
  position: absolute;
  color: var(--color-white);
  width: 3.16667rem;
  height: 3.16667rem;
  font-size: var(--font-size-small);
  line-height: 3.16667rem;
  border-radius: 50%;
  background: #5f52e3;
  text-align: center;
  right: 7.66667rem;
  top: 50%;
  margin-top: -1.58333rem;
}
.thumbnail-scrollbar[data-v-c9701fff] {
  width: 22.75rem;
  height: 51.58333rem;
  overflow: auto;
  /* 滚动条容器 */
  /* 滚动条轨道 */
  /* 定义滚动条滑块的样式 */
}
.thumbnail-scrollbar[data-v-c9701fff]::-webkit-scrollbar {
  width: 0.41667rem;
  /* 设置滚动条宽度 */
  height: 0.41667rem;
  /* 设置水平滚动条的高度 */
  border-radius: 0.20833rem;
  /* 圆角 */
}
.thumbnail-scrollbar[data-v-c9701fff]::-webkit-scrollbar-track {
  background-color: transparent;
  /* 轨道颜色透明 */
  border-radius: 0.20833rem;
  /* 圆角 */
}
.thumbnail-scrollbar[data-v-c9701fff]::-webkit-scrollbar-thumb {
  width: 0.41667rem;
  margin-right: 0.41667rem;
  background-color: rgba(255, 255, 255, 0.1);
  /* 设置滑块的颜色 */
  border-radius: 0.20833rem;
  /* 圆角 */
}
.thumbnail-list-container[data-v-2f919413] {
  width: 100%;
  height: 100%;
}
#app {
  width: 100%;
  height: 100%;
}
