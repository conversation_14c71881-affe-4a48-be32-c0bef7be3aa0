const CEF_NAME = {
  RESOURCE_DIALOG_CEF: "资源",
  THUMBNAIL_CEF: "缩略图",
  CLASS_SUMMARY_DIALOG_CEF: "课堂小结"
};
const QT_RENDERER_MESSAGE_TYPE = {
  /** qt-创建cef */
  QT_CREATE_CEF: "createCef",
  /** qt-白板插入图片 */
  QT_INSERT_IMAGE_WHITEBOARD: "qt.insertImageWhiteboard",
  /** qt-激活绘制选择 */
  QT_ACTIVE_DRAW_SELECTION: "qt.activeDrawSelection",
  /** qt-激活课件选择 */
  QT_ACTIVE_RESOURCE_SELECTION: "qt.activeResourceSelection",
  /** qt浮窗-资源状态 */
  QT_UPDATE_RESOURCE_STATUS: "qt.updateResourceStatus",
  /** 上课页面-当前资源轨迹变更 */
  CLASSROOM_RESOURCE_TRACE_CHANGE: "qt.resourceTraceChange",
  /** 上课页面-获取当前资源保存状态 */
  CLASSROOM_RESOURCE_SAVED_STATUS: "classroom.getCurrentSavedStatus",
  /** qt-打开资源选择窗口 */
  QT_OPEN_RESOURCE_DIALOG: "qt.openResourceDialog",
  /** 上课页面-打开放大镜事件 */
  CLASSROOM_OPEN_MAGNIFIER: "classroom.openMagnifier",
  /** qt-显示AI小结章鱼 */
  QT_SHOW_AI_SUMMARY_OCTOPUS: "qt.showAISummaryOctopus",
  /** qt-关闭AI小结章鱼 */
  QT_CLOSE_AI_SUMMARY_OCTOPUS: "qt.closeAISummaryOctopus",
  /** qt-开始旋转AI小结章鱼 */
  QT_START_ROTATE_AISUMMARY_OCTOPUS: "qt.startRotateAISummaryOctopus",
  /** qt-停止旋转AI小结章鱼 */
  QT_STOP_ROTATE_AISUMMARY_OCTOPUS: "qt.stopRotateAISummaryOctopus",
  /** qt-显示AI小结章鱼消息 */
  QT_SHOW_AI_SUMMARY_MSG: "qt.showAISummaryMsg"
};
const QT_CEF_MESSAGE_TYPE = {
  /** 显示 */
  SHOW: "show",
  /** 隐藏 */
  HIDE: "hide",
  /** 关闭 */
  CLOSE: "close",
  /** 获取轨迹图片列表 */
  QT_GET_TRACE_IMAGE_LIST: "qt.getTraceImageList",
  /** 获取当前资源轨迹列表 */
  QT_GET_CURRENT_RESOURCE_TRACE_LIST: "qt.getCurrentResourceTraceList",
  /** 打开上课页面设置对话框 */
  QT_OPEN_CLASSROOM_SETTING_DIALOG: "qt.openClassroomSettingDialog",
  /** 打开设置对话框 */
  QT_OPEN_SETTING_DIALOG: "qt.openSettingDialog"
};
const CEF_RENDERER_MESSAGE_TYPE = {
  /** 上课页面-选择资源 */
  CLASSROOM_SELECT_RESOURCE: "classroom.selectResource",
  /** 资源cef-刷新资源列表 */
  RESOURCEDIALOG_REFRESH_RESOURCE_LIST: "resourcedialog.refreshResourceList",
  /** 上课页面-添加白板 */
  CLASSROOM_ADD_BOARD: "classroom.addBoard",
  /** 上课页面-上一帧 */
  CLASSROOM_PREV_FRAME: "classroom.prevFrame",
  /** 上课页面-下一帧 */
  CLASSROOM_NEXT_FRAME: "classroom.nextFrame",
  /** 上课页面-上一个资源 */
  CLASSROOM_PREV_RESOURCE: "classroom.prevResource",
  /** 上课页面-下一个资源 */
  CLASSROOM_NEXT_RESOURCE: "classroom.nextResource",
  /** 上课页面-上一页 */
  // CLASSROOM_PREV_PAGE: 'classroom.prevPage',
  /** 上课页面-下一页 */
  // CLASSROOM_NEXT_PAGE: 'classroom.nextPage',
  /** 上课页面-跳转到指定页 */
  CLASSROOM_JUMP_TO_PAGE: "classroom.jumpToPage",
  /** 上课页面关闭课件 */
  CLASSROOM_CLOSE_RESOURCE: "classroom.closeResource",
  /** 上课页面-退出 */
  CLASSROOM_EXIT: "classroom.exit",
  /** 上课页面-保存板书参数 */
  CLASSROOM_CURRENT_BOARD_PARAMS: "classroom.currentBoardParams",
  /** 缩略图-资源翻页 */
  THUMBNAIL_RESOURCE_PAGE_CHANGE: "thumbnail.resourcePageChange",
  /** 上课页面-记录当前资源已保存 */
  CLASSROOM_RESOURCE_SAVED: "classroom.resourceSaved",
  /** 上课页面-获取ai小结id */
  CLASSROOM_GET_AI_CLASS_SUMMARY_ID: "classroom.getAiClassSummaryId",
  /** 上课页面-修改AI小结开关 */
  CLASSROOM_SET_AI_SUMMARY_SWITCH: "classroom.setAiSummarySwitch",
  /** 上课页面-获取AI小结开关 */
  CLASSROOM_GET_AI_SUMMARY_SWITCH: "classroom.getAiSummarySwitch",
  /** 课堂小结-AI总结结束 */
  CLASS_SUMMARY_AI_SUMMARY_ACCOMPLISH: "classSummary.aiSummaryAccomplish",
  /** 上课页面-开始总结 */
  CLASSROOM_START_SUMMARY: "classroom.startSummary",
  /** 上课页面-获取章鱼状态 */
  CLASSROOM_GET_SUMMARY_STATUS: "classroom.getSummaryStatus"
};
export {
  CEF_RENDERER_MESSAGE_TYPE as C,
  QT_CEF_MESSAGE_TYPE as Q,
  QT_RENDERER_MESSAGE_TYPE as a,
  CEF_NAME as b
};
