import { f as px2rem, _ as _export_sfc$1, l as logger, g as getDeviceIdWidget, r as reportTrackEvent, h as getWindowUrlParams, a as Bridge, B as BridgeZmqUtils, c as setBusinessInfoWidget, d as initLoggerWidget, p as pinia } from "./index.5f541e77.js";
import { c as calcHtmlFontSize } from "./rem.5d1b1196.js";
import { O as isString, a as inject, c as computed, w as watch, n as nextTick, g as getCurrentInstance, r as ref, L as isArray, E as isObject, J as toRaw, d as defineComponent, ae as useSlots, b as openBlock, e as createBlock, f as withCtx, j as createBaseVNode, k as normalizeClass, u as unref, h as withDirectives, m as createElementBlock, a7 as isRef, I as withModifiers, af as vModelCheckbox, B as renderSlot, F as Fragment, G as createTextVNode, H as toDisplayString, D as createCommentVNode, C as resolveDynamicComponent, l as normalizeStyle, q as provide, K as toRefs, _ as mergeProps, ag as toHandlers, T as Transition, p as createVNode, ah as hasOwn, x as reactive, i as isFunction, ai as h, P as resolveComponent, v as vShow, R as renderList, s as shallowRef, z as onMounted, A as onUpdated, $ as onUnmounted, Q as resolveDirective, ad as createApp } from "./bootstrap.ab073eb8.js";
import { D as Dialog } from "./index.019c1871.js";
import { u as useNamespace, _ as _export_sfc, d as buildProps, e as definePropType, w as withInstall, m as withNoopInstall, q as question_filled_default, j as useLocale, f as addUnit, E as ElIcon, r as removeClass, o as addClass, p as loading_default, s as caret_right_default } from "./base.676dddc3.js";
import { i as isEqual, u as useTooltipContentProps, c as ElTooltip, s as selectKey, C as ClickOutside, L as Loading, f as eventBus, e as EventBusEnum, d as useEventBus } from "./index.b1cdae8b.js";
import { E as ElPopover } from "./el-popover.f2a526af.js";
import { S as SvgIcon } from "./index.24eb8257.js";
import { a as isNumber, d as isBoolean, b as isUndefined, f as isPropAbsent } from "./index.1c1fd1ce.js";
import { f as useSizeProp, k as useFormDisabled, b as useFormItem, u as useFormSize, d as useFormItemInputId, i as iconPropType, l as formItemContextKey } from "./use-form-common-props.6b0d7cd2.js";
import { u as useAriaProps, p as pick } from "./index.4d07c967.js";
import { U as UPDATE_MODEL_EVENT, d as debugWarn } from "./event.183fce42.js";
import { u as useDeprecated, b as buttonTypes, E as ElButton } from "./el-button.a9e8e4ae.js";
import { u as useEventListener } from "./typescript.063380fa.js";
import { E as EVENT_CODE } from "./index.4e3d2b08.js";
import { D as DocumentTypeEnum } from "./IResource.516d6004.js";
import { Q as QrCode } from "./index.cba57277.js";
import { E as ElImage } from "./el-image-viewer.10cb6477.js";
import { g as getUploadStatus, a as getResourceProgressList, u as uploadFile, r as resourceUpload, b as getResourceTree, c as recordResourceTree, d as getTeachPlanList, e as getResourceTreeRecord } from "./resource.65d32462.js";
import { a as showError } from "./toastWidget.c29e5ea4.js";
import { _ as __vite_glob_0_5, a as __vite_glob_0_23, b as __vite_glob_0_21 } from "./<EMAIL>";
import { l as loadImage } from "./image.77fe4778.js";
import { E as EVENT_CODE$1 } from "./bury.7c4aa589.js";
import { C as CEF_RENDERER_MESSAGE_TYPE, Q as QT_CEF_MESSAGE_TYPE } from "./IComm.f4ebabd4.js";
import "./encryptlong.f30353e7.js";
import "./base.649d38c6.js";
import "./isUndefined.a6a5e481.js";
import "./hlwhiteboard.b54f17ff.js";
import "./position.2e4d825a.js";
import "./axios.4311e8d4.js";
import "./toast.59704259.js";
import "./crypto-js.7319a219.js";
const checkboxProps = {
  modelValue: {
    type: [Number, String, Boolean],
    default: void 0
  },
  label: {
    type: [String, Boolean, Number, Object],
    default: void 0
  },
  value: {
    type: [String, Boolean, Number, Object],
    default: void 0
  },
  indeterminate: Boolean,
  disabled: Boolean,
  checked: Boolean,
  name: {
    type: String,
    default: void 0
  },
  trueValue: {
    type: [String, Number],
    default: void 0
  },
  falseValue: {
    type: [String, Number],
    default: void 0
  },
  trueLabel: {
    type: [String, Number],
    default: void 0
  },
  falseLabel: {
    type: [String, Number],
    default: void 0
  },
  id: {
    type: String,
    default: void 0
  },
  border: Boolean,
  size: useSizeProp,
  tabindex: [String, Number],
  validateEvent: {
    type: Boolean,
    default: true
  },
  ...useAriaProps(["ariaControls"])
};
const checkboxEmits = {
  [UPDATE_MODEL_EVENT]: (val) => isString(val) || isNumber(val) || isBoolean(val),
  change: (val) => isString(val) || isNumber(val) || isBoolean(val)
};
const checkboxGroupContextKey = Symbol("checkboxGroupContextKey");
const useCheckboxDisabled = ({
  model,
  isChecked
}) => {
  const checkboxGroup = inject(checkboxGroupContextKey, void 0);
  const isLimitDisabled = computed(() => {
    var _a, _b;
    const max = (_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value;
    const min = (_b = checkboxGroup == null ? void 0 : checkboxGroup.min) == null ? void 0 : _b.value;
    return !isUndefined(max) && model.value.length >= max && !isChecked.value || !isUndefined(min) && model.value.length <= min && isChecked.value;
  });
  const isDisabled = useFormDisabled(computed(() => (checkboxGroup == null ? void 0 : checkboxGroup.disabled.value) || isLimitDisabled.value));
  return {
    isDisabled,
    isLimitDisabled
  };
};
const useCheckboxEvent = (props, {
  model,
  isLimitExceeded,
  hasOwnLabel,
  isDisabled,
  isLabeledByFormItem
}) => {
  const checkboxGroup = inject(checkboxGroupContextKey, void 0);
  const { formItem } = useFormItem();
  const { emit } = getCurrentInstance();
  function getLabeledValue(value) {
    var _a, _b, _c, _d;
    return [true, props.trueValue, props.trueLabel].includes(value) ? (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true : (_d = (_c = props.falseValue) != null ? _c : props.falseLabel) != null ? _d : false;
  }
  function emitChangeEvent(checked, e) {
    emit("change", getLabeledValue(checked), e);
  }
  function handleChange(e) {
    if (isLimitExceeded.value)
      return;
    const target = e.target;
    emit("change", getLabeledValue(target.checked), e);
  }
  async function onClickRoot(e) {
    if (isLimitExceeded.value)
      return;
    if (!hasOwnLabel.value && !isDisabled.value && isLabeledByFormItem.value) {
      const eventTargets = e.composedPath();
      const hasLabel = eventTargets.some((item) => item.tagName === "LABEL");
      if (!hasLabel) {
        model.value = getLabeledValue([false, props.falseValue, props.falseLabel].includes(model.value));
        await nextTick();
        emitChangeEvent(model.value, e);
      }
    }
  }
  const validateEvent = computed(() => (checkboxGroup == null ? void 0 : checkboxGroup.validateEvent) || props.validateEvent);
  watch(() => props.modelValue, () => {
    if (validateEvent.value) {
      formItem == null ? void 0 : formItem.validate("change").catch((err) => debugWarn());
    }
  });
  return {
    handleChange,
    onClickRoot
  };
};
const useCheckboxModel = (props) => {
  const selfModel = ref(false);
  const { emit } = getCurrentInstance();
  const checkboxGroup = inject(checkboxGroupContextKey, void 0);
  const isGroup = computed(() => isUndefined(checkboxGroup) === false);
  const isLimitExceeded = ref(false);
  const model = computed({
    get() {
      var _a, _b;
      return isGroup.value ? (_a = checkboxGroup == null ? void 0 : checkboxGroup.modelValue) == null ? void 0 : _a.value : (_b = props.modelValue) != null ? _b : selfModel.value;
    },
    set(val) {
      var _a, _b;
      if (isGroup.value && isArray(val)) {
        isLimitExceeded.value = ((_a = checkboxGroup == null ? void 0 : checkboxGroup.max) == null ? void 0 : _a.value) !== void 0 && val.length > (checkboxGroup == null ? void 0 : checkboxGroup.max.value) && val.length > model.value.length;
        isLimitExceeded.value === false && ((_b = checkboxGroup == null ? void 0 : checkboxGroup.changeEvent) == null ? void 0 : _b.call(checkboxGroup, val));
      } else {
        emit(UPDATE_MODEL_EVENT, val);
        selfModel.value = val;
      }
    }
  });
  return {
    model,
    isGroup,
    isLimitExceeded
  };
};
const useCheckboxStatus = (props, slots, { model }) => {
  const checkboxGroup = inject(checkboxGroupContextKey, void 0);
  const isFocused = ref(false);
  const actualValue = computed(() => {
    if (!isPropAbsent(props.value)) {
      return props.value;
    }
    return props.label;
  });
  const isChecked = computed(() => {
    const value = model.value;
    if (isBoolean(value)) {
      return value;
    } else if (isArray(value)) {
      if (isObject(actualValue.value)) {
        return value.map(toRaw).some((o) => isEqual(o, actualValue.value));
      } else {
        return value.map(toRaw).includes(actualValue.value);
      }
    } else if (value !== null && value !== void 0) {
      return value === props.trueValue || value === props.trueLabel;
    } else {
      return !!value;
    }
  });
  const checkboxButtonSize = useFormSize(computed(() => {
    var _a;
    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;
  }), {
    prop: true
  });
  const checkboxSize = useFormSize(computed(() => {
    var _a;
    return (_a = checkboxGroup == null ? void 0 : checkboxGroup.size) == null ? void 0 : _a.value;
  }));
  const hasOwnLabel = computed(() => {
    return !!slots.default || !isPropAbsent(actualValue.value);
  });
  return {
    checkboxButtonSize,
    isChecked,
    isFocused,
    checkboxSize,
    hasOwnLabel,
    actualValue
  };
};
const useCheckbox = (props, slots) => {
  const { formItem: elFormItem } = useFormItem();
  const { model, isGroup, isLimitExceeded } = useCheckboxModel(props);
  const {
    isFocused,
    isChecked,
    checkboxButtonSize,
    checkboxSize,
    hasOwnLabel,
    actualValue
  } = useCheckboxStatus(props, slots, { model });
  const { isDisabled } = useCheckboxDisabled({ model, isChecked });
  const { inputId, isLabeledByFormItem } = useFormItemInputId(props, {
    formItemContext: elFormItem,
    disableIdGeneration: hasOwnLabel,
    disableIdManagement: isGroup
  });
  const { handleChange, onClickRoot } = useCheckboxEvent(props, {
    model,
    isLimitExceeded,
    hasOwnLabel,
    isDisabled,
    isLabeledByFormItem
  });
  const setStoreValue = () => {
    function addToStore() {
      var _a, _b;
      if (isArray(model.value) && !model.value.includes(actualValue.value)) {
        model.value.push(actualValue.value);
      } else {
        model.value = (_b = (_a = props.trueValue) != null ? _a : props.trueLabel) != null ? _b : true;
      }
    }
    props.checked && addToStore();
  };
  setStoreValue();
  useDeprecated({
    from: "label act as value",
    replacement: "value",
    version: "3.0.0",
    scope: "el-checkbox",
    ref: "https://element-plus.org/en-US/component/checkbox.html"
  }, computed(() => isGroup.value && isPropAbsent(props.value)));
  useDeprecated({
    from: "true-label",
    replacement: "true-value",
    version: "3.0.0",
    scope: "el-checkbox",
    ref: "https://element-plus.org/en-US/component/checkbox.html"
  }, computed(() => !!props.trueLabel));
  useDeprecated({
    from: "false-label",
    replacement: "false-value",
    version: "3.0.0",
    scope: "el-checkbox",
    ref: "https://element-plus.org/en-US/component/checkbox.html"
  }, computed(() => !!props.falseLabel));
  return {
    inputId,
    isLabeledByFormItem,
    isChecked,
    isDisabled,
    isFocused,
    checkboxButtonSize,
    checkboxSize,
    hasOwnLabel,
    model,
    actualValue,
    handleChange,
    onClickRoot
  };
};
const __default__$4 = defineComponent({
  name: "ElCheckbox"
});
const _sfc_main$g = /* @__PURE__ */ defineComponent({
  ...__default__$4,
  props: checkboxProps,
  emits: checkboxEmits,
  setup(__props) {
    const props = __props;
    const slots = useSlots();
    const {
      inputId,
      isLabeledByFormItem,
      isChecked,
      isDisabled,
      isFocused,
      checkboxSize,
      hasOwnLabel,
      model,
      actualValue,
      handleChange,
      onClickRoot
    } = useCheckbox(props, slots);
    const ns = useNamespace("checkbox");
    const compKls = computed(() => {
      return [
        ns.b(),
        ns.m(checkboxSize.value),
        ns.is("disabled", isDisabled.value),
        ns.is("bordered", props.border),
        ns.is("checked", isChecked.value)
      ];
    });
    const spanKls = computed(() => {
      return [
        ns.e("input"),
        ns.is("disabled", isDisabled.value),
        ns.is("checked", isChecked.value),
        ns.is("indeterminate", props.indeterminate),
        ns.is("focus", isFocused.value)
      ];
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(resolveDynamicComponent(!unref(hasOwnLabel) && unref(isLabeledByFormItem) ? "span" : "label"), {
        class: normalizeClass(unref(compKls)),
        "aria-controls": _ctx.indeterminate ? _ctx.ariaControls : null,
        onClick: unref(onClickRoot)
      }, {
        default: withCtx(() => {
          var _a, _b, _c, _d;
          return [
            createBaseVNode("span", {
              class: normalizeClass(unref(spanKls))
            }, [
              _ctx.trueValue || _ctx.falseValue || _ctx.trueLabel || _ctx.falseLabel ? withDirectives((openBlock(), createElementBlock("input", {
                key: 0,
                id: unref(inputId),
                "onUpdate:modelValue": ($event) => isRef(model) ? model.value = $event : null,
                class: normalizeClass(unref(ns).e("original")),
                type: "checkbox",
                indeterminate: _ctx.indeterminate,
                name: _ctx.name,
                tabindex: _ctx.tabindex,
                disabled: unref(isDisabled),
                "true-value": (_b = (_a = _ctx.trueValue) != null ? _a : _ctx.trueLabel) != null ? _b : true,
                "false-value": (_d = (_c = _ctx.falseValue) != null ? _c : _ctx.falseLabel) != null ? _d : false,
                onChange: unref(handleChange),
                onFocus: ($event) => isFocused.value = true,
                onBlur: ($event) => isFocused.value = false,
                onClick: withModifiers(() => {
                }, ["stop"])
              }, null, 42, ["id", "onUpdate:modelValue", "indeterminate", "name", "tabindex", "disabled", "true-value", "false-value", "onChange", "onFocus", "onBlur", "onClick"])), [
                [vModelCheckbox, unref(model)]
              ]) : withDirectives((openBlock(), createElementBlock("input", {
                key: 1,
                id: unref(inputId),
                "onUpdate:modelValue": ($event) => isRef(model) ? model.value = $event : null,
                class: normalizeClass(unref(ns).e("original")),
                type: "checkbox",
                indeterminate: _ctx.indeterminate,
                disabled: unref(isDisabled),
                value: unref(actualValue),
                name: _ctx.name,
                tabindex: _ctx.tabindex,
                onChange: unref(handleChange),
                onFocus: ($event) => isFocused.value = true,
                onBlur: ($event) => isFocused.value = false,
                onClick: withModifiers(() => {
                }, ["stop"])
              }, null, 42, ["id", "onUpdate:modelValue", "indeterminate", "disabled", "value", "name", "tabindex", "onChange", "onFocus", "onBlur", "onClick"])), [
                [vModelCheckbox, unref(model)]
              ]),
              createBaseVNode("span", {
                class: normalizeClass(unref(ns).e("inner"))
              }, null, 2)
            ], 2),
            unref(hasOwnLabel) ? (openBlock(), createElementBlock("span", {
              key: 0,
              class: normalizeClass(unref(ns).e("label"))
            }, [
              renderSlot(_ctx.$slots, "default"),
              !_ctx.$slots.default ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
                createTextVNode(toDisplayString(_ctx.label), 1)
              ], 64)) : createCommentVNode("v-if", true)
            ], 2)) : createCommentVNode("v-if", true)
          ];
        }),
        _: 3
      }, 8, ["class", "aria-controls", "onClick"]);
    };
  }
});
var Checkbox = /* @__PURE__ */ _export_sfc(_sfc_main$g, [["__file", "checkbox.vue"]]);
const __default__$3 = defineComponent({
  name: "ElCheckboxButton"
});
const _sfc_main$f = /* @__PURE__ */ defineComponent({
  ...__default__$3,
  props: checkboxProps,
  emits: checkboxEmits,
  setup(__props) {
    const props = __props;
    const slots = useSlots();
    const {
      isFocused,
      isChecked,
      isDisabled,
      checkboxButtonSize,
      model,
      actualValue,
      handleChange
    } = useCheckbox(props, slots);
    const checkboxGroup = inject(checkboxGroupContextKey, void 0);
    const ns = useNamespace("checkbox");
    const activeStyle = computed(() => {
      var _a, _b, _c, _d;
      const fillValue = (_b = (_a = checkboxGroup == null ? void 0 : checkboxGroup.fill) == null ? void 0 : _a.value) != null ? _b : "";
      return {
        backgroundColor: fillValue,
        borderColor: fillValue,
        color: (_d = (_c = checkboxGroup == null ? void 0 : checkboxGroup.textColor) == null ? void 0 : _c.value) != null ? _d : "",
        boxShadow: fillValue ? `-1px 0 0 0 ${fillValue}` : void 0
      };
    });
    const labelKls = computed(() => {
      return [
        ns.b("button"),
        ns.bm("button", checkboxButtonSize.value),
        ns.is("disabled", isDisabled.value),
        ns.is("checked", isChecked.value),
        ns.is("focus", isFocused.value)
      ];
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return openBlock(), createElementBlock("label", {
        class: normalizeClass(unref(labelKls))
      }, [
        _ctx.trueValue || _ctx.falseValue || _ctx.trueLabel || _ctx.falseLabel ? withDirectives((openBlock(), createElementBlock("input", {
          key: 0,
          "onUpdate:modelValue": ($event) => isRef(model) ? model.value = $event : null,
          class: normalizeClass(unref(ns).be("button", "original")),
          type: "checkbox",
          name: _ctx.name,
          tabindex: _ctx.tabindex,
          disabled: unref(isDisabled),
          "true-value": (_b = (_a = _ctx.trueValue) != null ? _a : _ctx.trueLabel) != null ? _b : true,
          "false-value": (_d = (_c = _ctx.falseValue) != null ? _c : _ctx.falseLabel) != null ? _d : false,
          onChange: unref(handleChange),
          onFocus: ($event) => isFocused.value = true,
          onBlur: ($event) => isFocused.value = false,
          onClick: withModifiers(() => {
          }, ["stop"])
        }, null, 42, ["onUpdate:modelValue", "name", "tabindex", "disabled", "true-value", "false-value", "onChange", "onFocus", "onBlur", "onClick"])), [
          [vModelCheckbox, unref(model)]
        ]) : withDirectives((openBlock(), createElementBlock("input", {
          key: 1,
          "onUpdate:modelValue": ($event) => isRef(model) ? model.value = $event : null,
          class: normalizeClass(unref(ns).be("button", "original")),
          type: "checkbox",
          name: _ctx.name,
          tabindex: _ctx.tabindex,
          disabled: unref(isDisabled),
          value: unref(actualValue),
          onChange: unref(handleChange),
          onFocus: ($event) => isFocused.value = true,
          onBlur: ($event) => isFocused.value = false,
          onClick: withModifiers(() => {
          }, ["stop"])
        }, null, 42, ["onUpdate:modelValue", "name", "tabindex", "disabled", "value", "onChange", "onFocus", "onBlur", "onClick"])), [
          [vModelCheckbox, unref(model)]
        ]),
        _ctx.$slots.default || _ctx.label ? (openBlock(), createElementBlock("span", {
          key: 2,
          class: normalizeClass(unref(ns).be("button", "inner")),
          style: normalizeStyle(unref(isChecked) ? unref(activeStyle) : void 0)
        }, [
          renderSlot(_ctx.$slots, "default", {}, () => [
            createTextVNode(toDisplayString(_ctx.label), 1)
          ])
        ], 6)) : createCommentVNode("v-if", true)
      ], 2);
    };
  }
});
var CheckboxButton = /* @__PURE__ */ _export_sfc(_sfc_main$f, [["__file", "checkbox-button.vue"]]);
const checkboxGroupProps = buildProps({
  modelValue: {
    type: definePropType(Array),
    default: () => []
  },
  disabled: Boolean,
  min: Number,
  max: Number,
  size: useSizeProp,
  fill: String,
  textColor: String,
  tag: {
    type: String,
    default: "div"
  },
  validateEvent: {
    type: Boolean,
    default: true
  },
  ...useAriaProps(["ariaLabel"])
});
const checkboxGroupEmits = {
  [UPDATE_MODEL_EVENT]: (val) => isArray(val),
  change: (val) => isArray(val)
};
const __default__$2 = defineComponent({
  name: "ElCheckboxGroup"
});
const _sfc_main$e = /* @__PURE__ */ defineComponent({
  ...__default__$2,
  props: checkboxGroupProps,
  emits: checkboxGroupEmits,
  setup(__props, { emit }) {
    const props = __props;
    const ns = useNamespace("checkbox");
    const { formItem } = useFormItem();
    const { inputId: groupId, isLabeledByFormItem } = useFormItemInputId(props, {
      formItemContext: formItem
    });
    const changeEvent = async (value) => {
      emit(UPDATE_MODEL_EVENT, value);
      await nextTick();
      emit("change", value);
    };
    const modelValue = computed({
      get() {
        return props.modelValue;
      },
      set(val) {
        changeEvent(val);
      }
    });
    provide(checkboxGroupContextKey, {
      ...pick(toRefs(props), [
        "size",
        "min",
        "max",
        "disabled",
        "validateEvent",
        "fill",
        "textColor"
      ]),
      modelValue,
      changeEvent
    });
    watch(() => props.modelValue, () => {
      if (props.validateEvent) {
        formItem == null ? void 0 : formItem.validate("change").catch((err) => debugWarn());
      }
    });
    return (_ctx, _cache) => {
      var _a;
      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {
        id: unref(groupId),
        class: normalizeClass(unref(ns).b("group")),
        role: "group",
        "aria-label": !unref(isLabeledByFormItem) ? _ctx.ariaLabel || "checkbox-group" : void 0,
        "aria-labelledby": unref(isLabeledByFormItem) ? (_a = unref(formItem)) == null ? void 0 : _a.labelId : void 0
      }, {
        default: withCtx(() => [
          renderSlot(_ctx.$slots, "default")
        ]),
        _: 3
      }, 8, ["id", "class", "aria-label", "aria-labelledby"]);
    };
  }
});
var CheckboxGroup = /* @__PURE__ */ _export_sfc(_sfc_main$e, [["__file", "checkbox-group.vue"]]);
const ElCheckbox = withInstall(Checkbox, {
  CheckboxButton,
  CheckboxGroup
});
withNoopInstall(CheckboxButton);
withNoopInstall(CheckboxGroup);
const __default__$1 = defineComponent({
  name: "ElCollapseTransition"
});
const _sfc_main$d = /* @__PURE__ */ defineComponent({
  ...__default__$1,
  setup(__props) {
    const ns = useNamespace("collapse-transition");
    const reset = (el) => {
      el.style.maxHeight = "";
      el.style.overflow = el.dataset.oldOverflow;
      el.style.paddingTop = el.dataset.oldPaddingTop;
      el.style.paddingBottom = el.dataset.oldPaddingBottom;
    };
    const on = {
      beforeEnter(el) {
        if (!el.dataset)
          el.dataset = {};
        el.dataset.oldPaddingTop = el.style.paddingTop;
        el.dataset.oldPaddingBottom = el.style.paddingBottom;
        if (el.style.height)
          el.dataset.elExistsHeight = el.style.height;
        el.style.maxHeight = 0;
        el.style.paddingTop = 0;
        el.style.paddingBottom = 0;
      },
      enter(el) {
        requestAnimationFrame(() => {
          el.dataset.oldOverflow = el.style.overflow;
          if (el.dataset.elExistsHeight) {
            el.style.maxHeight = el.dataset.elExistsHeight;
          } else if (el.scrollHeight !== 0) {
            el.style.maxHeight = `${el.scrollHeight}px`;
          } else {
            el.style.maxHeight = 0;
          }
          el.style.paddingTop = el.dataset.oldPaddingTop;
          el.style.paddingBottom = el.dataset.oldPaddingBottom;
          el.style.overflow = "hidden";
        });
      },
      afterEnter(el) {
        el.style.maxHeight = "";
        el.style.overflow = el.dataset.oldOverflow;
      },
      enterCancelled(el) {
        reset(el);
      },
      beforeLeave(el) {
        if (!el.dataset)
          el.dataset = {};
        el.dataset.oldPaddingTop = el.style.paddingTop;
        el.dataset.oldPaddingBottom = el.style.paddingBottom;
        el.dataset.oldOverflow = el.style.overflow;
        el.style.maxHeight = `${el.scrollHeight}px`;
        el.style.overflow = "hidden";
      },
      leave(el) {
        if (el.scrollHeight !== 0) {
          el.style.maxHeight = 0;
          el.style.paddingTop = 0;
          el.style.paddingBottom = 0;
        }
      },
      afterLeave(el) {
        reset(el);
      },
      leaveCancelled(el) {
        reset(el);
      }
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(Transition, mergeProps({
        name: unref(ns).b()
      }, toHandlers(on)), {
        default: withCtx(() => [
          renderSlot(_ctx.$slots, "default")
        ]),
        _: 3
      }, 16, ["name"]);
    };
  }
});
var CollapseTransition = /* @__PURE__ */ _export_sfc(_sfc_main$d, [["__file", "collapse-transition.vue"]]);
const ElCollapseTransition = withInstall(CollapseTransition);
const popconfirmProps = buildProps({
  title: String,
  confirmButtonText: String,
  cancelButtonText: String,
  confirmButtonType: {
    type: String,
    values: buttonTypes,
    default: "primary"
  },
  cancelButtonType: {
    type: String,
    values: buttonTypes,
    default: "text"
  },
  icon: {
    type: iconPropType,
    default: () => question_filled_default
  },
  iconColor: {
    type: String,
    default: "#f90"
  },
  hideIcon: {
    type: Boolean,
    default: false
  },
  hideAfter: {
    type: Number,
    default: 200
  },
  teleported: useTooltipContentProps.teleported,
  persistent: useTooltipContentProps.persistent,
  width: {
    type: [String, Number],
    default: 150
  }
});
const popconfirmEmits = {
  confirm: (e) => e instanceof MouseEvent,
  cancel: (e) => e instanceof MouseEvent
};
const __default__ = defineComponent({
  name: "ElPopconfirm"
});
const _sfc_main$c = /* @__PURE__ */ defineComponent({
  ...__default__,
  props: popconfirmProps,
  emits: popconfirmEmits,
  setup(__props, { emit }) {
    const props = __props;
    const { t } = useLocale();
    const ns = useNamespace("popconfirm");
    const tooltipRef = ref();
    const hidePopper = () => {
      var _a, _b;
      (_b = (_a = tooltipRef.value) == null ? void 0 : _a.onClose) == null ? void 0 : _b.call(_a);
    };
    const style = computed(() => {
      return {
        width: addUnit(props.width)
      };
    });
    const confirm = (e) => {
      emit("confirm", e);
      hidePopper();
    };
    const cancel = (e) => {
      emit("cancel", e);
      hidePopper();
    };
    const finalConfirmButtonText = computed(() => props.confirmButtonText || t("el.popconfirm.confirmButtonText"));
    const finalCancelButtonText = computed(() => props.cancelButtonText || t("el.popconfirm.cancelButtonText"));
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElTooltip), mergeProps({
        ref_key: "tooltipRef",
        ref: tooltipRef,
        trigger: "click",
        effect: "light"
      }, _ctx.$attrs, {
        "popper-class": `${unref(ns).namespace.value}-popover`,
        "popper-style": unref(style),
        teleported: _ctx.teleported,
        "fallback-placements": ["bottom", "top", "right", "left"],
        "hide-after": _ctx.hideAfter,
        persistent: _ctx.persistent
      }), {
        content: withCtx(() => [
          createBaseVNode("div", {
            class: normalizeClass(unref(ns).b())
          }, [
            createBaseVNode("div", {
              class: normalizeClass(unref(ns).e("main"))
            }, [
              !_ctx.hideIcon && _ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {
                key: 0,
                class: normalizeClass(unref(ns).e("icon")),
                style: normalizeStyle({ color: _ctx.iconColor })
              }, {
                default: withCtx(() => [
                  (openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))
                ]),
                _: 1
              }, 8, ["class", "style"])) : createCommentVNode("v-if", true),
              createTextVNode(" " + toDisplayString(_ctx.title), 1)
            ], 2),
            createBaseVNode("div", {
              class: normalizeClass(unref(ns).e("action"))
            }, [
              renderSlot(_ctx.$slots, "actions", {
                confirm,
                cancel
              }, () => [
                createVNode(unref(ElButton), {
                  size: "small",
                  type: _ctx.cancelButtonType === "text" ? "" : _ctx.cancelButtonType,
                  text: _ctx.cancelButtonType === "text",
                  onClick: cancel
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(finalCancelButtonText)), 1)
                  ]),
                  _: 1
                }, 8, ["type", "text"]),
                createVNode(unref(ElButton), {
                  size: "small",
                  type: _ctx.confirmButtonType === "text" ? "" : _ctx.confirmButtonType,
                  text: _ctx.confirmButtonType === "text",
                  onClick: confirm
                }, {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(unref(finalConfirmButtonText)), 1)
                  ]),
                  _: 1
                }, 8, ["type", "text"])
              ])
            ], 2)
          ], 2)
        ]),
        default: withCtx(() => [
          _ctx.$slots.reference ? renderSlot(_ctx.$slots, "reference", { key: 0 }) : createCommentVNode("v-if", true)
        ]),
        _: 3
      }, 16, ["popper-class", "popper-style", "teleported", "hide-after", "persistent"]);
    };
  }
});
var Popconfirm = /* @__PURE__ */ _export_sfc(_sfc_main$c, [["__file", "popconfirm.vue"]]);
const ElPopconfirm = withInstall(Popconfirm);
const NODE_KEY = "$treeNodeId";
const markNodeData = function(node, data) {
  if (!data || data[NODE_KEY])
    return;
  Object.defineProperty(data, NODE_KEY, {
    value: node.id,
    enumerable: false,
    configurable: false,
    writable: false
  });
};
const getNodeKey = (key, data) => data == null ? void 0 : data[key || NODE_KEY];
const handleCurrentChange = (store, emit, setCurrent) => {
  const preCurrentNode = store.value.currentNode;
  setCurrent();
  const currentNode = store.value.currentNode;
  if (preCurrentNode === currentNode)
    return;
  emit("current-change", currentNode ? currentNode.data : null, currentNode);
};
const getChildState = (node) => {
  let all = true;
  let none = true;
  let allWithoutDisable = true;
  for (let i = 0, j = node.length; i < j; i++) {
    const n = node[i];
    if (n.checked !== true || n.indeterminate) {
      all = false;
      if (!n.disabled) {
        allWithoutDisable = false;
      }
    }
    if (n.checked !== false || n.indeterminate) {
      none = false;
    }
  }
  return { all, none, allWithoutDisable, half: !all && !none };
};
const reInitChecked = function(node) {
  if (node.childNodes.length === 0 || node.loading)
    return;
  const { all, none, half } = getChildState(node.childNodes);
  if (all) {
    node.checked = true;
    node.indeterminate = false;
  } else if (half) {
    node.checked = false;
    node.indeterminate = true;
  } else if (none) {
    node.checked = false;
    node.indeterminate = false;
  }
  const parent = node.parent;
  if (!parent || parent.level === 0)
    return;
  if (!node.store.checkStrictly) {
    reInitChecked(parent);
  }
};
const getPropertyFromData = function(node, prop) {
  const props = node.store.props;
  const data = node.data || {};
  const config = props[prop];
  if (isFunction(config)) {
    return config(data, node);
  } else if (isString(config)) {
    return data[config];
  } else if (isUndefined(config)) {
    const dataProp = data[prop];
    return dataProp === void 0 ? "" : dataProp;
  }
};
let nodeIdSeed = 0;
class Node {
  constructor(options) {
    this.id = nodeIdSeed++;
    this.text = null;
    this.checked = false;
    this.indeterminate = false;
    this.data = null;
    this.expanded = false;
    this.parent = null;
    this.visible = true;
    this.isCurrent = false;
    this.canFocus = false;
    for (const name in options) {
      if (hasOwn(options, name)) {
        this[name] = options[name];
      }
    }
    this.level = 0;
    this.loaded = false;
    this.childNodes = [];
    this.loading = false;
    if (this.parent) {
      this.level = this.parent.level + 1;
    }
  }
  initialize() {
    const store = this.store;
    if (!store) {
      throw new Error("[Node]store is required!");
    }
    store.registerNode(this);
    const props = store.props;
    if (props && typeof props.isLeaf !== "undefined") {
      const isLeaf = getPropertyFromData(this, "isLeaf");
      if (isBoolean(isLeaf)) {
        this.isLeafByUser = isLeaf;
      }
    }
    if (store.lazy !== true && this.data) {
      this.setData(this.data);
      if (store.defaultExpandAll) {
        this.expanded = true;
        this.canFocus = true;
      }
    } else if (this.level > 0 && store.lazy && store.defaultExpandAll && !this.isLeafByUser) {
      this.expand();
    }
    if (!isArray(this.data)) {
      markNodeData(this, this.data);
    }
    if (!this.data)
      return;
    const defaultExpandedKeys = store.defaultExpandedKeys;
    const key = store.key;
    if (key && defaultExpandedKeys && defaultExpandedKeys.includes(this.key)) {
      this.expand(null, store.autoExpandParent);
    }
    if (key && store.currentNodeKey !== void 0 && this.key === store.currentNodeKey) {
      store.currentNode = this;
      store.currentNode.isCurrent = true;
    }
    if (store.lazy) {
      store._initDefaultCheckedNode(this);
    }
    this.updateLeafState();
    if (this.parent && (this.level === 1 || this.parent.expanded === true))
      this.canFocus = true;
  }
  setData(data) {
    if (!isArray(data)) {
      markNodeData(this, data);
    }
    this.data = data;
    this.childNodes = [];
    let children;
    if (this.level === 0 && isArray(this.data)) {
      children = this.data;
    } else {
      children = getPropertyFromData(this, "children") || [];
    }
    for (let i = 0, j = children.length; i < j; i++) {
      this.insertChild({ data: children[i] });
    }
  }
  get label() {
    return getPropertyFromData(this, "label");
  }
  get key() {
    const nodeKey = this.store.key;
    if (this.data)
      return this.data[nodeKey];
    return null;
  }
  get disabled() {
    return getPropertyFromData(this, "disabled");
  }
  get nextSibling() {
    const parent = this.parent;
    if (parent) {
      const index = parent.childNodes.indexOf(this);
      if (index > -1) {
        return parent.childNodes[index + 1];
      }
    }
    return null;
  }
  get previousSibling() {
    const parent = this.parent;
    if (parent) {
      const index = parent.childNodes.indexOf(this);
      if (index > -1) {
        return index > 0 ? parent.childNodes[index - 1] : null;
      }
    }
    return null;
  }
  contains(target, deep = true) {
    return (this.childNodes || []).some((child) => child === target || deep && child.contains(target));
  }
  remove() {
    const parent = this.parent;
    if (parent) {
      parent.removeChild(this);
    }
  }
  insertChild(child, index, batch) {
    if (!child)
      throw new Error("InsertChild error: child is required.");
    if (!(child instanceof Node)) {
      if (!batch) {
        const children = this.getChildren(true);
        if (!children.includes(child.data)) {
          if (isUndefined(index) || index < 0) {
            children.push(child.data);
          } else {
            children.splice(index, 0, child.data);
          }
        }
      }
      Object.assign(child, {
        parent: this,
        store: this.store
      });
      child = reactive(new Node(child));
      if (child instanceof Node) {
        child.initialize();
      }
    }
    child.level = this.level + 1;
    if (isUndefined(index) || index < 0) {
      this.childNodes.push(child);
    } else {
      this.childNodes.splice(index, 0, child);
    }
    this.updateLeafState();
  }
  insertBefore(child, ref2) {
    let index;
    if (ref2) {
      index = this.childNodes.indexOf(ref2);
    }
    this.insertChild(child, index);
  }
  insertAfter(child, ref2) {
    let index;
    if (ref2) {
      index = this.childNodes.indexOf(ref2);
      if (index !== -1)
        index += 1;
    }
    this.insertChild(child, index);
  }
  removeChild(child) {
    const children = this.getChildren() || [];
    const dataIndex = children.indexOf(child.data);
    if (dataIndex > -1) {
      children.splice(dataIndex, 1);
    }
    const index = this.childNodes.indexOf(child);
    if (index > -1) {
      this.store && this.store.deregisterNode(child);
      child.parent = null;
      this.childNodes.splice(index, 1);
    }
    this.updateLeafState();
  }
  removeChildByData(data) {
    let targetNode = null;
    for (let i = 0; i < this.childNodes.length; i++) {
      if (this.childNodes[i].data === data) {
        targetNode = this.childNodes[i];
        break;
      }
    }
    if (targetNode) {
      this.removeChild(targetNode);
    }
  }
  expand(callback, expandParent) {
    const done = () => {
      if (expandParent) {
        let parent = this.parent;
        while (parent.level > 0) {
          parent.expanded = true;
          parent = parent.parent;
        }
      }
      this.expanded = true;
      if (callback)
        callback();
      this.childNodes.forEach((item) => {
        item.canFocus = true;
      });
    };
    if (this.shouldLoadData()) {
      this.loadData((data) => {
        if (isArray(data)) {
          if (this.checked) {
            this.setChecked(true, true);
          } else if (!this.store.checkStrictly) {
            reInitChecked(this);
          }
          done();
        }
      });
    } else {
      done();
    }
  }
  doCreateChildren(array, defaultProps = {}) {
    array.forEach((item) => {
      this.insertChild(Object.assign({ data: item }, defaultProps), void 0, true);
    });
  }
  collapse() {
    this.expanded = false;
    this.childNodes.forEach((item) => {
      item.canFocus = false;
    });
  }
  shouldLoadData() {
    return this.store.lazy === true && this.store.load && !this.loaded;
  }
  updateLeafState() {
    if (this.store.lazy === true && this.loaded !== true && typeof this.isLeafByUser !== "undefined") {
      this.isLeaf = this.isLeafByUser;
      return;
    }
    const childNodes = this.childNodes;
    if (!this.store.lazy || this.store.lazy === true && this.loaded === true) {
      this.isLeaf = !childNodes || childNodes.length === 0;
      return;
    }
    this.isLeaf = false;
  }
  setChecked(value, deep, recursion, passValue) {
    this.indeterminate = value === "half";
    this.checked = value === true;
    if (this.store.checkStrictly)
      return;
    if (!(this.shouldLoadData() && !this.store.checkDescendants)) {
      const { all, allWithoutDisable } = getChildState(this.childNodes);
      if (!this.isLeaf && !all && allWithoutDisable) {
        this.checked = false;
        value = false;
      }
      const handleDescendants = () => {
        if (deep) {
          const childNodes = this.childNodes;
          for (let i = 0, j = childNodes.length; i < j; i++) {
            const child = childNodes[i];
            passValue = passValue || value !== false;
            const isCheck = child.disabled ? child.checked : passValue;
            child.setChecked(isCheck, deep, true, passValue);
          }
          const { half, all: all2 } = getChildState(childNodes);
          if (!all2) {
            this.checked = all2;
            this.indeterminate = half;
          }
        }
      };
      if (this.shouldLoadData()) {
        this.loadData(() => {
          handleDescendants();
          reInitChecked(this);
        }, {
          checked: value !== false
        });
        return;
      } else {
        handleDescendants();
      }
    }
    const parent = this.parent;
    if (!parent || parent.level === 0)
      return;
    if (!recursion) {
      reInitChecked(parent);
    }
  }
  getChildren(forceInit = false) {
    if (this.level === 0)
      return this.data;
    const data = this.data;
    if (!data)
      return null;
    const props = this.store.props;
    let children = "children";
    if (props) {
      children = props.children || "children";
    }
    if (data[children] === void 0) {
      data[children] = null;
    }
    if (forceInit && !data[children]) {
      data[children] = [];
    }
    return data[children];
  }
  updateChildren() {
    const newData = this.getChildren() || [];
    const oldData = this.childNodes.map((node) => node.data);
    const newDataMap = {};
    const newNodes = [];
    newData.forEach((item, index) => {
      const key = item[NODE_KEY];
      const isNodeExists = !!key && oldData.findIndex((data) => data[NODE_KEY] === key) >= 0;
      if (isNodeExists) {
        newDataMap[key] = { index, data: item };
      } else {
        newNodes.push({ index, data: item });
      }
    });
    if (!this.store.lazy) {
      oldData.forEach((item) => {
        if (!newDataMap[item[NODE_KEY]])
          this.removeChildByData(item);
      });
    }
    newNodes.forEach(({ index, data }) => {
      this.insertChild({ data }, index);
    });
    this.updateLeafState();
  }
  loadData(callback, defaultProps = {}) {
    if (this.store.lazy === true && this.store.load && !this.loaded && (!this.loading || Object.keys(defaultProps).length)) {
      this.loading = true;
      const resolve = (children) => {
        this.childNodes = [];
        this.doCreateChildren(children, defaultProps);
        this.loaded = true;
        this.loading = false;
        this.updateLeafState();
        if (callback) {
          callback.call(this, children);
        }
      };
      const reject = () => {
        this.loading = false;
      };
      this.store.load(this, resolve, reject);
    } else {
      if (callback) {
        callback.call(this);
      }
    }
  }
  eachNode(callback) {
    const arr = [this];
    while (arr.length) {
      const node = arr.shift();
      arr.unshift(...node.childNodes);
      callback(node);
    }
  }
  reInitChecked() {
    if (this.store.checkStrictly)
      return;
    reInitChecked(this);
  }
}
class TreeStore {
  constructor(options) {
    this.currentNode = null;
    this.currentNodeKey = null;
    for (const option in options) {
      if (hasOwn(options, option)) {
        this[option] = options[option];
      }
    }
    this.nodesMap = {};
  }
  initialize() {
    this.root = new Node({
      data: this.data,
      store: this
    });
    this.root.initialize();
    if (this.lazy && this.load) {
      const loadFn = this.load;
      loadFn(this.root, (data) => {
        this.root.doCreateChildren(data);
        this._initDefaultCheckedNodes();
      });
    } else {
      this._initDefaultCheckedNodes();
    }
  }
  filter(value) {
    const filterNodeMethod = this.filterNodeMethod;
    const lazy = this.lazy;
    const traverse = async function(node) {
      const childNodes = node.root ? node.root.childNodes : node.childNodes;
      for (const [index, child] of childNodes.entries()) {
        child.visible = filterNodeMethod.call(child, value, child.data, child);
        if (index % 80 === 0 && index > 0) {
          await nextTick();
        }
        traverse(child);
      }
      if (!node.visible && childNodes.length) {
        let allHidden = true;
        allHidden = !childNodes.some((child) => child.visible);
        if (node.root) {
          node.root.visible = allHidden === false;
        } else {
          node.visible = allHidden === false;
        }
      }
      if (!value)
        return;
      if (node.visible && !node.isLeaf) {
        if (!lazy || node.loaded) {
          node.expand();
        }
      }
    };
    traverse(this);
  }
  setData(newVal) {
    const instanceChanged = newVal !== this.root.data;
    if (instanceChanged) {
      this.nodesMap = {};
      this.root.setData(newVal);
      this._initDefaultCheckedNodes();
      this.setCurrentNodeKey(this.currentNodeKey);
    } else {
      this.root.updateChildren();
    }
  }
  getNode(data) {
    if (data instanceof Node)
      return data;
    const key = isObject(data) ? getNodeKey(this.key, data) : data;
    return this.nodesMap[key] || null;
  }
  insertBefore(data, refData) {
    const refNode = this.getNode(refData);
    refNode.parent.insertBefore({ data }, refNode);
  }
  insertAfter(data, refData) {
    const refNode = this.getNode(refData);
    refNode.parent.insertAfter({ data }, refNode);
  }
  remove(data) {
    const node = this.getNode(data);
    if (node && node.parent) {
      if (node === this.currentNode) {
        this.currentNode = null;
      }
      node.parent.removeChild(node);
    }
  }
  append(data, parentData) {
    const parentNode = !isPropAbsent(parentData) ? this.getNode(parentData) : this.root;
    if (parentNode) {
      parentNode.insertChild({ data });
    }
  }
  _initDefaultCheckedNodes() {
    const defaultCheckedKeys = this.defaultCheckedKeys || [];
    const nodesMap = this.nodesMap;
    defaultCheckedKeys.forEach((checkedKey) => {
      const node = nodesMap[checkedKey];
      if (node) {
        node.setChecked(true, !this.checkStrictly);
      }
    });
  }
  _initDefaultCheckedNode(node) {
    const defaultCheckedKeys = this.defaultCheckedKeys || [];
    if (defaultCheckedKeys.includes(node.key)) {
      node.setChecked(true, !this.checkStrictly);
    }
  }
  setDefaultCheckedKey(newVal) {
    if (newVal !== this.defaultCheckedKeys) {
      this.defaultCheckedKeys = newVal;
      this._initDefaultCheckedNodes();
    }
  }
  registerNode(node) {
    const key = this.key;
    if (!node || !node.data)
      return;
    if (!key) {
      this.nodesMap[node.id] = node;
    } else {
      const nodeKey = node.key;
      if (nodeKey !== void 0)
        this.nodesMap[node.key] = node;
    }
  }
  deregisterNode(node) {
    const key = this.key;
    if (!key || !node || !node.data)
      return;
    node.childNodes.forEach((child) => {
      this.deregisterNode(child);
    });
    delete this.nodesMap[node.key];
  }
  getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
    const checkedNodes = [];
    const traverse = function(node) {
      const childNodes = node.root ? node.root.childNodes : node.childNodes;
      childNodes.forEach((child) => {
        if ((child.checked || includeHalfChecked && child.indeterminate) && (!leafOnly || leafOnly && child.isLeaf)) {
          checkedNodes.push(child.data);
        }
        traverse(child);
      });
    };
    traverse(this);
    return checkedNodes;
  }
  getCheckedKeys(leafOnly = false) {
    return this.getCheckedNodes(leafOnly).map((data) => (data || {})[this.key]);
  }
  getHalfCheckedNodes() {
    const nodes = [];
    const traverse = function(node) {
      const childNodes = node.root ? node.root.childNodes : node.childNodes;
      childNodes.forEach((child) => {
        if (child.indeterminate) {
          nodes.push(child.data);
        }
        traverse(child);
      });
    };
    traverse(this);
    return nodes;
  }
  getHalfCheckedKeys() {
    return this.getHalfCheckedNodes().map((data) => (data || {})[this.key]);
  }
  _getAllNodes() {
    const allNodes = [];
    const nodesMap = this.nodesMap;
    for (const nodeKey in nodesMap) {
      if (hasOwn(nodesMap, nodeKey)) {
        allNodes.push(nodesMap[nodeKey]);
      }
    }
    return allNodes;
  }
  updateChildren(key, data) {
    const node = this.nodesMap[key];
    if (!node)
      return;
    const childNodes = node.childNodes;
    for (let i = childNodes.length - 1; i >= 0; i--) {
      const child = childNodes[i];
      this.remove(child.data);
    }
    for (let i = 0, j = data.length; i < j; i++) {
      const child = data[i];
      this.append(child, node.data);
    }
  }
  _setCheckedKeys(key, leafOnly = false, checkedKeys) {
    const allNodes = this._getAllNodes().sort((a, b) => a.level - b.level);
    const cache = /* @__PURE__ */ Object.create(null);
    const keys = Object.keys(checkedKeys);
    allNodes.forEach((node) => node.setChecked(false, false));
    const cacheCheckedChild = (node) => {
      node.childNodes.forEach((child) => {
        var _a;
        cache[child.data[key]] = true;
        if ((_a = child.childNodes) == null ? void 0 : _a.length) {
          cacheCheckedChild(child);
        }
      });
    };
    for (let i = 0, j = allNodes.length; i < j; i++) {
      const node = allNodes[i];
      const nodeKey = node.data[key].toString();
      const checked = keys.includes(nodeKey);
      if (!checked) {
        if (node.checked && !cache[nodeKey]) {
          node.setChecked(false, false);
        }
        continue;
      }
      if (node.childNodes.length) {
        cacheCheckedChild(node);
      }
      if (node.isLeaf || this.checkStrictly) {
        node.setChecked(true, false);
        continue;
      }
      node.setChecked(true, true);
      if (leafOnly) {
        node.setChecked(false, false);
        const traverse = function(node2) {
          const childNodes = node2.childNodes;
          childNodes.forEach((child) => {
            if (!child.isLeaf) {
              child.setChecked(false, false);
            }
            traverse(child);
          });
        };
        traverse(node);
      }
    }
  }
  setCheckedNodes(array, leafOnly = false) {
    const key = this.key;
    const checkedKeys = {};
    array.forEach((item) => {
      checkedKeys[(item || {})[key]] = true;
    });
    this._setCheckedKeys(key, leafOnly, checkedKeys);
  }
  setCheckedKeys(keys, leafOnly = false) {
    this.defaultCheckedKeys = keys;
    const key = this.key;
    const checkedKeys = {};
    keys.forEach((key2) => {
      checkedKeys[key2] = true;
    });
    this._setCheckedKeys(key, leafOnly, checkedKeys);
  }
  setDefaultExpandedKeys(keys) {
    keys = keys || [];
    this.defaultExpandedKeys = keys;
    keys.forEach((key) => {
      const node = this.getNode(key);
      if (node)
        node.expand(null, this.autoExpandParent);
    });
  }
  setChecked(data, checked, deep) {
    const node = this.getNode(data);
    if (node) {
      node.setChecked(!!checked, deep);
    }
  }
  getCurrentNode() {
    return this.currentNode;
  }
  setCurrentNode(currentNode) {
    const prevCurrentNode = this.currentNode;
    if (prevCurrentNode) {
      prevCurrentNode.isCurrent = false;
    }
    this.currentNode = currentNode;
    this.currentNode.isCurrent = true;
  }
  setUserCurrentNode(node, shouldAutoExpandParent = true) {
    const key = node[this.key];
    const currNode = this.nodesMap[key];
    this.setCurrentNode(currNode);
    if (shouldAutoExpandParent && this.currentNode.level > 1) {
      this.currentNode.parent.expand(null, true);
    }
  }
  setCurrentNodeKey(key, shouldAutoExpandParent = true) {
    this.currentNodeKey = key;
    if (key === null || key === void 0) {
      this.currentNode && (this.currentNode.isCurrent = false);
      this.currentNode = null;
      return;
    }
    const node = this.getNode(key);
    if (node) {
      this.setCurrentNode(node);
      if (shouldAutoExpandParent && this.currentNode.level > 1) {
        this.currentNode.parent.expand(null, true);
      }
    }
  }
}
const _sfc_main$b = defineComponent({
  name: "ElTreeNodeContent",
  props: {
    node: {
      type: Object,
      required: true
    },
    renderContent: Function
  },
  setup(props) {
    const ns = useNamespace("tree");
    const nodeInstance = inject("NodeInstance");
    const tree = inject("RootTree");
    return () => {
      const node = props.node;
      const { data, store } = node;
      return props.renderContent ? props.renderContent(h, { _self: nodeInstance, node, data, store }) : renderSlot(tree.ctx.slots, "default", { node, data }, () => [
        h("span", { class: ns.be("node", "label") }, [node.label])
      ]);
    };
  }
});
var NodeContent = /* @__PURE__ */ _export_sfc(_sfc_main$b, [["__file", "tree-node-content.vue"]]);
function useNodeExpandEventBroadcast(props) {
  const parentNodeMap = inject("TreeNodeMap", null);
  const currentNodeMap = {
    treeNodeExpand: (node) => {
      if (props.node !== node) {
        props.node.collapse();
      }
    },
    children: []
  };
  if (parentNodeMap) {
    parentNodeMap.children.push(currentNodeMap);
  }
  provide("TreeNodeMap", currentNodeMap);
  return {
    broadcastExpanded: (node) => {
      if (!props.accordion)
        return;
      for (const childNode of currentNodeMap.children) {
        childNode.treeNodeExpand(node);
      }
    }
  };
}
const dragEventsKey = Symbol("dragEvents");
function useDragNodeHandler({ props, ctx, el$, dropIndicator$, store }) {
  const ns = useNamespace("tree");
  const dragState = ref({
    showDropIndicator: false,
    draggingNode: null,
    dropNode: null,
    allowDrop: true,
    dropType: null
  });
  const treeNodeDragStart = ({ event, treeNode }) => {
    if (isFunction(props.allowDrag) && !props.allowDrag(treeNode.node)) {
      event.preventDefault();
      return false;
    }
    event.dataTransfer.effectAllowed = "move";
    try {
      event.dataTransfer.setData("text/plain", "");
    } catch (e) {
    }
    dragState.value.draggingNode = treeNode;
    ctx.emit("node-drag-start", treeNode.node, event);
  };
  const treeNodeDragOver = ({ event, treeNode }) => {
    const dropNode = treeNode;
    const oldDropNode = dragState.value.dropNode;
    if (oldDropNode && oldDropNode.node.id !== dropNode.node.id) {
      removeClass(oldDropNode.$el, ns.is("drop-inner"));
    }
    const draggingNode = dragState.value.draggingNode;
    if (!draggingNode || !dropNode)
      return;
    let dropPrev = true;
    let dropInner = true;
    let dropNext = true;
    let userAllowDropInner = true;
    if (isFunction(props.allowDrop)) {
      dropPrev = props.allowDrop(draggingNode.node, dropNode.node, "prev");
      userAllowDropInner = dropInner = props.allowDrop(draggingNode.node, dropNode.node, "inner");
      dropNext = props.allowDrop(draggingNode.node, dropNode.node, "next");
    }
    event.dataTransfer.dropEffect = dropInner || dropPrev || dropNext ? "move" : "none";
    if ((dropPrev || dropInner || dropNext) && (oldDropNode == null ? void 0 : oldDropNode.node.id) !== dropNode.node.id) {
      if (oldDropNode) {
        ctx.emit("node-drag-leave", draggingNode.node, oldDropNode.node, event);
      }
      ctx.emit("node-drag-enter", draggingNode.node, dropNode.node, event);
    }
    if (dropPrev || dropInner || dropNext) {
      dragState.value.dropNode = dropNode;
    } else {
      dragState.value.dropNode = null;
    }
    if (dropNode.node.nextSibling === draggingNode.node) {
      dropNext = false;
    }
    if (dropNode.node.previousSibling === draggingNode.node) {
      dropPrev = false;
    }
    if (dropNode.node.contains(draggingNode.node, false)) {
      dropInner = false;
    }
    if (draggingNode.node === dropNode.node || draggingNode.node.contains(dropNode.node)) {
      dropPrev = false;
      dropInner = false;
      dropNext = false;
    }
    const targetPosition = dropNode.$el.querySelector(`.${ns.be("node", "content")}`).getBoundingClientRect();
    const treePosition = el$.value.getBoundingClientRect();
    let dropType;
    const prevPercent = dropPrev ? dropInner ? 0.25 : dropNext ? 0.45 : 1 : -1;
    const nextPercent = dropNext ? dropInner ? 0.75 : dropPrev ? 0.55 : 0 : 1;
    let indicatorTop = -9999;
    const distance = event.clientY - targetPosition.top;
    if (distance < targetPosition.height * prevPercent) {
      dropType = "before";
    } else if (distance > targetPosition.height * nextPercent) {
      dropType = "after";
    } else if (dropInner) {
      dropType = "inner";
    } else {
      dropType = "none";
    }
    const iconPosition = dropNode.$el.querySelector(`.${ns.be("node", "expand-icon")}`).getBoundingClientRect();
    const dropIndicator = dropIndicator$.value;
    if (dropType === "before") {
      indicatorTop = iconPosition.top - treePosition.top;
    } else if (dropType === "after") {
      indicatorTop = iconPosition.bottom - treePosition.top;
    }
    dropIndicator.style.top = `${indicatorTop}px`;
    dropIndicator.style.left = `${iconPosition.right - treePosition.left}px`;
    if (dropType === "inner") {
      addClass(dropNode.$el, ns.is("drop-inner"));
    } else {
      removeClass(dropNode.$el, ns.is("drop-inner"));
    }
    dragState.value.showDropIndicator = dropType === "before" || dropType === "after";
    dragState.value.allowDrop = dragState.value.showDropIndicator || userAllowDropInner;
    dragState.value.dropType = dropType;
    ctx.emit("node-drag-over", draggingNode.node, dropNode.node, event);
  };
  const treeNodeDragEnd = (event) => {
    const { draggingNode, dropType, dropNode } = dragState.value;
    event.preventDefault();
    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = "move";
    }
    if (draggingNode && dropNode) {
      const draggingNodeCopy = { data: draggingNode.node.data };
      if (dropType !== "none") {
        draggingNode.node.remove();
      }
      if (dropType === "before") {
        dropNode.node.parent.insertBefore(draggingNodeCopy, dropNode.node);
      } else if (dropType === "after") {
        dropNode.node.parent.insertAfter(draggingNodeCopy, dropNode.node);
      } else if (dropType === "inner") {
        dropNode.node.insertChild(draggingNodeCopy);
      }
      if (dropType !== "none") {
        store.value.registerNode(draggingNodeCopy);
        if (store.value.key) {
          draggingNode.node.eachNode((node) => {
            var _a;
            (_a = store.value.nodesMap[node.data[store.value.key]]) == null ? void 0 : _a.setChecked(node.checked, !store.value.checkStrictly);
          });
        }
      }
      removeClass(dropNode.$el, ns.is("drop-inner"));
      ctx.emit("node-drag-end", draggingNode.node, dropNode.node, dropType, event);
      if (dropType !== "none") {
        ctx.emit("node-drop", draggingNode.node, dropNode.node, dropType, event);
      }
    }
    if (draggingNode && !dropNode) {
      ctx.emit("node-drag-end", draggingNode.node, null, dropType, event);
    }
    dragState.value.showDropIndicator = false;
    dragState.value.draggingNode = null;
    dragState.value.dropNode = null;
    dragState.value.allowDrop = true;
  };
  provide(dragEventsKey, {
    treeNodeDragStart,
    treeNodeDragOver,
    treeNodeDragEnd
  });
  return {
    dragState
  };
}
const _sfc_main$a = defineComponent({
  name: "ElTreeNode",
  components: {
    ElCollapseTransition,
    ElCheckbox,
    NodeContent,
    ElIcon,
    Loading: loading_default
  },
  props: {
    node: {
      type: Node,
      default: () => ({})
    },
    props: {
      type: Object,
      default: () => ({})
    },
    accordion: Boolean,
    renderContent: Function,
    renderAfterExpand: Boolean,
    showCheckbox: {
      type: Boolean,
      default: false
    }
  },
  emits: ["node-expand"],
  setup(props, ctx) {
    const ns = useNamespace("tree");
    const { broadcastExpanded } = useNodeExpandEventBroadcast(props);
    const tree = inject("RootTree");
    const expanded = ref(false);
    const childNodeRendered = ref(false);
    const oldChecked = ref();
    const oldIndeterminate = ref();
    const node$ = ref();
    const dragEvents = inject(dragEventsKey);
    const instance = getCurrentInstance();
    provide("NodeInstance", instance);
    if (props.node.expanded) {
      expanded.value = true;
      childNodeRendered.value = true;
    }
    const childrenKey = tree.props.props["children"] || "children";
    watch(() => {
      var _a;
      const children = (_a = props.node.data) == null ? void 0 : _a[childrenKey];
      return children && [...children];
    }, () => {
      props.node.updateChildren();
    });
    watch(() => props.node.indeterminate, (val) => {
      handleSelectChange(props.node.checked, val);
    });
    watch(() => props.node.checked, (val) => {
      handleSelectChange(val, props.node.indeterminate);
    });
    watch(() => props.node.childNodes.length, () => props.node.reInitChecked());
    watch(() => props.node.expanded, (val) => {
      nextTick(() => expanded.value = val);
      if (val) {
        childNodeRendered.value = true;
      }
    });
    const getNodeKey$1 = (node) => {
      return getNodeKey(tree.props.nodeKey, node.data);
    };
    const getNodeClass = (node) => {
      const nodeClassFunc = props.props.class;
      if (!nodeClassFunc) {
        return {};
      }
      let className;
      if (isFunction(nodeClassFunc)) {
        const { data } = node;
        className = nodeClassFunc(data, node);
      } else {
        className = nodeClassFunc;
      }
      if (isString(className)) {
        return { [className]: true };
      } else {
        return className;
      }
    };
    const handleSelectChange = (checked, indeterminate) => {
      if (oldChecked.value !== checked || oldIndeterminate.value !== indeterminate) {
        tree.ctx.emit("check-change", props.node.data, checked, indeterminate);
      }
      oldChecked.value = checked;
      oldIndeterminate.value = indeterminate;
    };
    const handleClick = (e) => {
      handleCurrentChange(tree.store, tree.ctx.emit, () => {
        var _a;
        const nodeKeyProp = (_a = tree == null ? void 0 : tree.props) == null ? void 0 : _a.nodeKey;
        if (nodeKeyProp) {
          const curNodeKey = getNodeKey$1(props.node);
          tree.store.value.setCurrentNodeKey(curNodeKey);
        } else {
          tree.store.value.setCurrentNode(props.node);
        }
      });
      tree.currentNode.value = props.node;
      if (tree.props.expandOnClickNode) {
        handleExpandIconClick();
      }
      if ((tree.props.checkOnClickNode || props.node.isLeaf && tree.props.checkOnClickLeaf) && !props.node.disabled) {
        handleCheckChange(!props.node.checked);
      }
      tree.ctx.emit("node-click", props.node.data, props.node, instance, e);
    };
    const handleContextMenu = (event) => {
      var _a;
      if ((_a = tree.instance.vnode.props) == null ? void 0 : _a["onNodeContextmenu"]) {
        event.stopPropagation();
        event.preventDefault();
      }
      tree.ctx.emit("node-contextmenu", event, props.node.data, props.node, instance);
    };
    const handleExpandIconClick = () => {
      if (props.node.isLeaf)
        return;
      if (expanded.value) {
        tree.ctx.emit("node-collapse", props.node.data, props.node, instance);
        props.node.collapse();
      } else {
        props.node.expand(() => {
          ctx.emit("node-expand", props.node.data, props.node, instance);
        });
      }
    };
    const handleCheckChange = (value) => {
      props.node.setChecked(value, !(tree == null ? void 0 : tree.props.checkStrictly));
      nextTick(() => {
        const store = tree.store.value;
        tree.ctx.emit("check", props.node.data, {
          checkedNodes: store.getCheckedNodes(),
          checkedKeys: store.getCheckedKeys(),
          halfCheckedNodes: store.getHalfCheckedNodes(),
          halfCheckedKeys: store.getHalfCheckedKeys()
        });
      });
    };
    const handleChildNodeExpand = (nodeData, node, instance2) => {
      broadcastExpanded(node);
      tree.ctx.emit("node-expand", nodeData, node, instance2);
    };
    const handleDragStart = (event) => {
      if (!tree.props.draggable)
        return;
      dragEvents.treeNodeDragStart({ event, treeNode: props });
    };
    const handleDragOver = (event) => {
      event.preventDefault();
      if (!tree.props.draggable)
        return;
      dragEvents.treeNodeDragOver({
        event,
        treeNode: { $el: node$.value, node: props.node }
      });
    };
    const handleDrop = (event) => {
      event.preventDefault();
    };
    const handleDragEnd = (event) => {
      if (!tree.props.draggable)
        return;
      dragEvents.treeNodeDragEnd(event);
    };
    return {
      ns,
      node$,
      tree,
      expanded,
      childNodeRendered,
      oldChecked,
      oldIndeterminate,
      getNodeKey: getNodeKey$1,
      getNodeClass,
      handleSelectChange,
      handleClick,
      handleContextMenu,
      handleExpandIconClick,
      handleCheckChange,
      handleChildNodeExpand,
      handleDragStart,
      handleDragOver,
      handleDrop,
      handleDragEnd,
      CaretRight: caret_right_default
    };
  }
});
function _sfc_render$1(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_el_icon = resolveComponent("el-icon");
  const _component_el_checkbox = resolveComponent("el-checkbox");
  const _component_loading = resolveComponent("loading");
  const _component_node_content = resolveComponent("node-content");
  const _component_el_tree_node = resolveComponent("el-tree-node");
  const _component_el_collapse_transition = resolveComponent("el-collapse-transition");
  return withDirectives((openBlock(), createElementBlock("div", {
    ref: "node$",
    class: normalizeClass([
      _ctx.ns.b("node"),
      _ctx.ns.is("expanded", _ctx.expanded),
      _ctx.ns.is("current", _ctx.node.isCurrent),
      _ctx.ns.is("hidden", !_ctx.node.visible),
      _ctx.ns.is("focusable", !_ctx.node.disabled),
      _ctx.ns.is("checked", !_ctx.node.disabled && _ctx.node.checked),
      _ctx.getNodeClass(_ctx.node)
    ]),
    role: "treeitem",
    tabindex: "-1",
    "aria-expanded": _ctx.expanded,
    "aria-disabled": _ctx.node.disabled,
    "aria-checked": _ctx.node.checked,
    draggable: _ctx.tree.props.draggable,
    "data-key": _ctx.getNodeKey(_ctx.node),
    onClick: withModifiers(_ctx.handleClick, ["stop"]),
    onContextmenu: _ctx.handleContextMenu,
    onDragstart: withModifiers(_ctx.handleDragStart, ["stop"]),
    onDragover: withModifiers(_ctx.handleDragOver, ["stop"]),
    onDragend: withModifiers(_ctx.handleDragEnd, ["stop"]),
    onDrop: withModifiers(_ctx.handleDrop, ["stop"])
  }, [
    createBaseVNode("div", {
      class: normalizeClass(_ctx.ns.be("node", "content")),
      style: normalizeStyle({ paddingLeft: (_ctx.node.level - 1) * _ctx.tree.props.indent + "px" })
    }, [
      _ctx.tree.props.icon || _ctx.CaretRight ? (openBlock(), createBlock(_component_el_icon, {
        key: 0,
        class: normalizeClass([
          _ctx.ns.be("node", "expand-icon"),
          _ctx.ns.is("leaf", _ctx.node.isLeaf),
          {
            expanded: !_ctx.node.isLeaf && _ctx.expanded
          }
        ]),
        onClick: withModifiers(_ctx.handleExpandIconClick, ["stop"])
      }, {
        default: withCtx(() => [
          (openBlock(), createBlock(resolveDynamicComponent(_ctx.tree.props.icon || _ctx.CaretRight)))
        ]),
        _: 1
      }, 8, ["class", "onClick"])) : createCommentVNode("v-if", true),
      _ctx.showCheckbox ? (openBlock(), createBlock(_component_el_checkbox, {
        key: 1,
        "model-value": _ctx.node.checked,
        indeterminate: _ctx.node.indeterminate,
        disabled: !!_ctx.node.disabled,
        onClick: withModifiers(() => {
        }, ["stop"]),
        onChange: _ctx.handleCheckChange
      }, null, 8, ["model-value", "indeterminate", "disabled", "onClick", "onChange"])) : createCommentVNode("v-if", true),
      _ctx.node.loading ? (openBlock(), createBlock(_component_el_icon, {
        key: 2,
        class: normalizeClass([_ctx.ns.be("node", "loading-icon"), _ctx.ns.is("loading")])
      }, {
        default: withCtx(() => [
          createVNode(_component_loading)
        ]),
        _: 1
      }, 8, ["class"])) : createCommentVNode("v-if", true),
      createVNode(_component_node_content, {
        node: _ctx.node,
        "render-content": _ctx.renderContent
      }, null, 8, ["node", "render-content"])
    ], 6),
    createVNode(_component_el_collapse_transition, null, {
      default: withCtx(() => [
        !_ctx.renderAfterExpand || _ctx.childNodeRendered ? withDirectives((openBlock(), createElementBlock("div", {
          key: 0,
          class: normalizeClass(_ctx.ns.be("node", "children")),
          role: "group",
          "aria-expanded": _ctx.expanded
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.node.childNodes, (child) => {
            return openBlock(), createBlock(_component_el_tree_node, {
              key: _ctx.getNodeKey(child),
              "render-content": _ctx.renderContent,
              "render-after-expand": _ctx.renderAfterExpand,
              "show-checkbox": _ctx.showCheckbox,
              node: child,
              accordion: _ctx.accordion,
              props: _ctx.props,
              onNodeExpand: _ctx.handleChildNodeExpand
            }, null, 8, ["render-content", "render-after-expand", "show-checkbox", "node", "accordion", "props", "onNodeExpand"]);
          }), 128))
        ], 10, ["aria-expanded"])), [
          [vShow, _ctx.expanded]
        ]) : createCommentVNode("v-if", true)
      ]),
      _: 1
    })
  ], 42, ["aria-expanded", "aria-disabled", "aria-checked", "draggable", "data-key", "onClick", "onContextmenu", "onDragstart", "onDragover", "onDragend", "onDrop"])), [
    [vShow, _ctx.node.visible]
  ]);
}
var ElTreeNode = /* @__PURE__ */ _export_sfc(_sfc_main$a, [["render", _sfc_render$1], ["__file", "tree-node.vue"]]);
function useKeydown({ el$ }, store) {
  const ns = useNamespace("tree");
  const treeItems = shallowRef([]);
  const checkboxItems = shallowRef([]);
  onMounted(() => {
    initTabIndex();
  });
  onUpdated(() => {
    treeItems.value = Array.from(el$.value.querySelectorAll("[role=treeitem]"));
    checkboxItems.value = Array.from(el$.value.querySelectorAll("input[type=checkbox]"));
  });
  watch(checkboxItems, (val) => {
    val.forEach((checkbox) => {
      checkbox.setAttribute("tabindex", "-1");
    });
  });
  const handleKeydown = (ev) => {
    const currentItem = ev.target;
    if (!currentItem.className.includes(ns.b("node")))
      return;
    const code = ev.code;
    treeItems.value = Array.from(el$.value.querySelectorAll(`.${ns.is("focusable")}[role=treeitem]`));
    const currentIndex = treeItems.value.indexOf(currentItem);
    let nextIndex;
    if ([EVENT_CODE.up, EVENT_CODE.down].includes(code)) {
      ev.preventDefault();
      if (code === EVENT_CODE.up) {
        nextIndex = currentIndex === -1 ? 0 : currentIndex !== 0 ? currentIndex - 1 : treeItems.value.length - 1;
        const startIndex = nextIndex;
        while (true) {
          if (store.value.getNode(treeItems.value[nextIndex].dataset.key).canFocus)
            break;
          nextIndex--;
          if (nextIndex === startIndex) {
            nextIndex = -1;
            break;
          }
          if (nextIndex < 0) {
            nextIndex = treeItems.value.length - 1;
          }
        }
      } else {
        nextIndex = currentIndex === -1 ? 0 : currentIndex < treeItems.value.length - 1 ? currentIndex + 1 : 0;
        const startIndex = nextIndex;
        while (true) {
          if (store.value.getNode(treeItems.value[nextIndex].dataset.key).canFocus)
            break;
          nextIndex++;
          if (nextIndex === startIndex) {
            nextIndex = -1;
            break;
          }
          if (nextIndex >= treeItems.value.length) {
            nextIndex = 0;
          }
        }
      }
      nextIndex !== -1 && treeItems.value[nextIndex].focus();
    }
    if ([EVENT_CODE.left, EVENT_CODE.right].includes(code)) {
      ev.preventDefault();
      currentItem.click();
    }
    const hasInput = currentItem.querySelector('[type="checkbox"]');
    if ([EVENT_CODE.enter, EVENT_CODE.numpadEnter, EVENT_CODE.space].includes(code) && hasInput) {
      ev.preventDefault();
      hasInput.click();
    }
  };
  useEventListener(el$, "keydown", handleKeydown);
  const initTabIndex = () => {
    var _a;
    treeItems.value = Array.from(el$.value.querySelectorAll(`.${ns.is("focusable")}[role=treeitem]`));
    checkboxItems.value = Array.from(el$.value.querySelectorAll("input[type=checkbox]"));
    const checkedItem = el$.value.querySelectorAll(`.${ns.is("checked")}[role=treeitem]`);
    if (checkedItem.length) {
      checkedItem[0].setAttribute("tabindex", "0");
      return;
    }
    (_a = treeItems.value[0]) == null ? void 0 : _a.setAttribute("tabindex", "0");
  };
}
const _sfc_main$9 = defineComponent({
  name: "ElTree",
  components: { ElTreeNode },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    emptyText: {
      type: String
    },
    renderAfterExpand: {
      type: Boolean,
      default: true
    },
    nodeKey: String,
    checkStrictly: Boolean,
    defaultExpandAll: Boolean,
    expandOnClickNode: {
      type: Boolean,
      default: true
    },
    checkOnClickNode: Boolean,
    checkOnClickLeaf: {
      type: Boolean,
      default: true
    },
    checkDescendants: {
      type: Boolean,
      default: false
    },
    autoExpandParent: {
      type: Boolean,
      default: true
    },
    defaultCheckedKeys: Array,
    defaultExpandedKeys: Array,
    currentNodeKey: [String, Number],
    renderContent: Function,
    showCheckbox: {
      type: Boolean,
      default: false
    },
    draggable: {
      type: Boolean,
      default: false
    },
    allowDrag: Function,
    allowDrop: Function,
    props: {
      type: Object,
      default: () => ({
        children: "children",
        label: "label",
        disabled: "disabled"
      })
    },
    lazy: {
      type: Boolean,
      default: false
    },
    highlightCurrent: Boolean,
    load: Function,
    filterNodeMethod: Function,
    accordion: Boolean,
    indent: {
      type: Number,
      default: 18
    },
    icon: {
      type: iconPropType
    }
  },
  emits: [
    "check-change",
    "current-change",
    "node-click",
    "node-contextmenu",
    "node-collapse",
    "node-expand",
    "check",
    "node-drag-start",
    "node-drag-end",
    "node-drop",
    "node-drag-leave",
    "node-drag-enter",
    "node-drag-over"
  ],
  setup(props, ctx) {
    const { t } = useLocale();
    const ns = useNamespace("tree");
    const selectInfo = inject(selectKey, null);
    const store = ref(new TreeStore({
      key: props.nodeKey,
      data: props.data,
      lazy: props.lazy,
      props: props.props,
      load: props.load,
      currentNodeKey: props.currentNodeKey,
      checkStrictly: props.checkStrictly,
      checkDescendants: props.checkDescendants,
      defaultCheckedKeys: props.defaultCheckedKeys,
      defaultExpandedKeys: props.defaultExpandedKeys,
      autoExpandParent: props.autoExpandParent,
      defaultExpandAll: props.defaultExpandAll,
      filterNodeMethod: props.filterNodeMethod
    }));
    store.value.initialize();
    const root = ref(store.value.root);
    const currentNode = ref(null);
    const el$ = ref(null);
    const dropIndicator$ = ref(null);
    const { broadcastExpanded } = useNodeExpandEventBroadcast(props);
    const { dragState } = useDragNodeHandler({
      props,
      ctx,
      el$,
      dropIndicator$,
      store
    });
    useKeydown({ el$ }, store);
    const isEmpty = computed(() => {
      const { childNodes } = root.value;
      const hasFilteredOptions = selectInfo ? selectInfo.hasFilteredOptions !== 0 : false;
      return (!childNodes || childNodes.length === 0 || childNodes.every(({ visible }) => !visible)) && !hasFilteredOptions;
    });
    watch(() => props.currentNodeKey, (newVal) => {
      store.value.setCurrentNodeKey(newVal);
    });
    watch(() => props.defaultCheckedKeys, (newVal) => {
      store.value.setDefaultCheckedKey(newVal);
    });
    watch(() => props.defaultExpandedKeys, (newVal) => {
      store.value.setDefaultExpandedKeys(newVal);
    });
    watch(() => props.data, (newVal) => {
      store.value.setData(newVal);
    }, { deep: true });
    watch(() => props.checkStrictly, (newVal) => {
      store.value.checkStrictly = newVal;
    });
    const filter = (value) => {
      if (!props.filterNodeMethod)
        throw new Error("[Tree] filterNodeMethod is required when filter");
      store.value.filter(value);
    };
    const getNodeKey$1 = (node) => {
      return getNodeKey(props.nodeKey, node.data);
    };
    const getNodePath = (data) => {
      if (!props.nodeKey)
        throw new Error("[Tree] nodeKey is required in getNodePath");
      const node = store.value.getNode(data);
      if (!node)
        return [];
      const path = [node.data];
      let parent = node.parent;
      while (parent && parent !== root.value) {
        path.push(parent.data);
        parent = parent.parent;
      }
      return path.reverse();
    };
    const getCheckedNodes = (leafOnly, includeHalfChecked) => {
      return store.value.getCheckedNodes(leafOnly, includeHalfChecked);
    };
    const getCheckedKeys = (leafOnly) => {
      return store.value.getCheckedKeys(leafOnly);
    };
    const getCurrentNode = () => {
      const currentNode2 = store.value.getCurrentNode();
      return currentNode2 ? currentNode2.data : null;
    };
    const getCurrentKey = () => {
      if (!props.nodeKey)
        throw new Error("[Tree] nodeKey is required in getCurrentKey");
      const currentNode2 = getCurrentNode();
      return currentNode2 ? currentNode2[props.nodeKey] : null;
    };
    const setCheckedNodes = (nodes, leafOnly) => {
      if (!props.nodeKey)
        throw new Error("[Tree] nodeKey is required in setCheckedNodes");
      store.value.setCheckedNodes(nodes, leafOnly);
    };
    const setCheckedKeys = (keys, leafOnly) => {
      if (!props.nodeKey)
        throw new Error("[Tree] nodeKey is required in setCheckedKeys");
      store.value.setCheckedKeys(keys, leafOnly);
    };
    const setChecked = (data, checked, deep) => {
      store.value.setChecked(data, checked, deep);
    };
    const getHalfCheckedNodes = () => {
      return store.value.getHalfCheckedNodes();
    };
    const getHalfCheckedKeys = () => {
      return store.value.getHalfCheckedKeys();
    };
    const setCurrentNode = (node, shouldAutoExpandParent = true) => {
      if (!props.nodeKey)
        throw new Error("[Tree] nodeKey is required in setCurrentNode");
      handleCurrentChange(store, ctx.emit, () => {
        broadcastExpanded(node);
        store.value.setUserCurrentNode(node, shouldAutoExpandParent);
      });
    };
    const setCurrentKey = (key, shouldAutoExpandParent = true) => {
      if (!props.nodeKey)
        throw new Error("[Tree] nodeKey is required in setCurrentKey");
      handleCurrentChange(store, ctx.emit, () => {
        broadcastExpanded();
        store.value.setCurrentNodeKey(key, shouldAutoExpandParent);
      });
    };
    const getNode = (data) => {
      return store.value.getNode(data);
    };
    const remove = (data) => {
      store.value.remove(data);
    };
    const append = (data, parentNode) => {
      store.value.append(data, parentNode);
    };
    const insertBefore = (data, refNode) => {
      store.value.insertBefore(data, refNode);
    };
    const insertAfter = (data, refNode) => {
      store.value.insertAfter(data, refNode);
    };
    const handleNodeExpand = (nodeData, node, instance) => {
      broadcastExpanded(node);
      ctx.emit("node-expand", nodeData, node, instance);
    };
    const updateKeyChildren = (key, data) => {
      if (!props.nodeKey)
        throw new Error("[Tree] nodeKey is required in updateKeyChild");
      store.value.updateChildren(key, data);
    };
    provide("RootTree", {
      ctx,
      props,
      store,
      root,
      currentNode,
      instance: getCurrentInstance()
    });
    provide(formItemContextKey, void 0);
    return {
      ns,
      store,
      root,
      currentNode,
      dragState,
      el$,
      dropIndicator$,
      isEmpty,
      filter,
      getNodeKey: getNodeKey$1,
      getNodePath,
      getCheckedNodes,
      getCheckedKeys,
      getCurrentNode,
      getCurrentKey,
      setCheckedNodes,
      setCheckedKeys,
      setChecked,
      getHalfCheckedNodes,
      getHalfCheckedKeys,
      setCurrentNode,
      setCurrentKey,
      t,
      getNode,
      remove,
      append,
      insertBefore,
      insertAfter,
      handleNodeExpand,
      updateKeyChildren
    };
  }
});
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_el_tree_node = resolveComponent("el-tree-node");
  return openBlock(), createElementBlock("div", {
    ref: "el$",
    class: normalizeClass([
      _ctx.ns.b(),
      _ctx.ns.is("dragging", !!_ctx.dragState.draggingNode),
      _ctx.ns.is("drop-not-allow", !_ctx.dragState.allowDrop),
      _ctx.ns.is("drop-inner", _ctx.dragState.dropType === "inner"),
      { [_ctx.ns.m("highlight-current")]: _ctx.highlightCurrent }
    ]),
    role: "tree"
  }, [
    (openBlock(true), createElementBlock(Fragment, null, renderList(_ctx.root.childNodes, (child) => {
      return openBlock(), createBlock(_component_el_tree_node, {
        key: _ctx.getNodeKey(child),
        node: child,
        props: _ctx.props,
        accordion: _ctx.accordion,
        "render-after-expand": _ctx.renderAfterExpand,
        "show-checkbox": _ctx.showCheckbox,
        "render-content": _ctx.renderContent,
        onNodeExpand: _ctx.handleNodeExpand
      }, null, 8, ["node", "props", "accordion", "render-after-expand", "show-checkbox", "render-content", "onNodeExpand"]);
    }), 128)),
    _ctx.isEmpty ? (openBlock(), createElementBlock("div", {
      key: 0,
      class: normalizeClass(_ctx.ns.e("empty-block"))
    }, [
      renderSlot(_ctx.$slots, "empty", {}, () => {
        var _a;
        return [
          createBaseVNode("span", {
            class: normalizeClass(_ctx.ns.e("empty-text"))
          }, toDisplayString((_a = _ctx.emptyText) != null ? _a : _ctx.t("el.tree.emptyText")), 3)
        ];
      })
    ], 2)) : createCommentVNode("v-if", true),
    withDirectives(createBaseVNode("div", {
      ref: "dropIndicator$",
      class: normalizeClass(_ctx.ns.e("drop-indicator"))
    }, null, 2), [
      [vShow, _ctx.dragState.showDropIndicator]
    ])
  ], 2);
}
var Tree = /* @__PURE__ */ _export_sfc(_sfc_main$9, [["render", _sfc_render], ["__file", "tree.vue"]]);
const ElTree = withInstall(Tree);
const _hoisted_1$8 = { class: "select-container flex flex-ac" };
const _hoisted_2$7 = { key: 0 };
const _hoisted_3$6 = { class: "option-list" };
const _hoisted_4$4 = ["onClick"];
const _hoisted_5$3 = { class: "option-item-text" };
const _sfc_main$8 = /* @__PURE__ */ defineComponent({
  __name: "TeachPlanSelect",
  props: {
    teachPlanList: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    isSelectedBefore: {
      type: Boolean,
      default: false
    }
  },
  emits: ["select"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const buttonRef = ref();
    const popoverRef = ref();
    const showPopover = ref(false);
    const titleRef = ref();
    const onClickOutside = () => {
      popoverRef.value?.hide();
    };
    const props = __props;
    watch(
      () => props.teachPlanList,
      (list) => {
        if (list.length > 1 && !props.isSelectedBefore) {
          showPopover.value = true;
        }
      },
      {
        immediate: true
      }
    );
    const selectedPlan = computed(() => {
      return props.teachPlanList.find((item) => item.selected) || { courseName: "" };
    });
    const adjustFontSize = () => {
      const element = titleRef.value;
      if (!element) {
        return;
      }
      const computedStyle = window.getComputedStyle(element);
      const lineHeight = parseFloat(computedStyle.lineHeight);
      if (element.scrollHeight > lineHeight + 4) {
        element.style.fontSize = px2rem(26) + "rem";
        element.style.lineHeight = px2rem(34) + "rem";
      }
    };
    const handleSelect = (item) => {
      props.teachPlanList.forEach((plan) => {
        plan.selected = false;
      });
      item.selected = true;
      emit("select", item);
      onClickOutside();
    };
    watch(
      selectedPlan,
      () => {
        nextTick(() => {
          adjustFontSize();
        });
      },
      {
        immediate: true
      }
    );
    return (_ctx, _cache) => {
      const _component_el_popover = ElPopover;
      return openBlock(), createElementBlock(Fragment, null, [
        createBaseVNode("div", _hoisted_1$8, [
          (openBlock(), createElementBlock("div", {
            key: selectedPlan.value.courseName,
            ref_key: "titleRef",
            ref: titleRef,
            class: "select-text"
          }, [
            createTextVNode(toDisplayString(selectedPlan.value.courseName) + " ", 1),
            !__props.teachPlanList.length && !__props.loading ? (openBlock(), createElementBlock("span", _hoisted_2$7, "暂无计划")) : createCommentVNode("", true)
          ])),
          withDirectives((openBlock(), createElementBlock("div", {
            ref_key: "buttonRef",
            ref: buttonRef,
            class: "select-icon active-scale"
          }, [
            createVNode(SvgIcon, { "icon-class": "switch" })
          ])), [
            [unref(ClickOutside), onClickOutside]
          ])
        ]),
        createBaseVNode("div", null, [
          createVNode(_component_el_popover, {
            ref_key: "popoverRef",
            ref: popoverRef,
            visible: showPopover.value,
            "onUpdate:visible": _cache[0] || (_cache[0] = ($event) => showPopover.value = $event),
            "virtual-ref": buttonRef.value,
            "show-arrow": false,
            trigger: "click",
            "virtual-triggering": "",
            "popper-class": "plan-popover",
            disabled: props.teachPlanList.length === 0,
            placement: "bottom-end"
          }, {
            default: withCtx(() => [
              createBaseVNode("div", _hoisted_3$6, [
                (openBlock(true), createElementBlock(Fragment, null, renderList(props.teachPlanList, (item) => {
                  return openBlock(), createElementBlock("div", {
                    key: item.teachPlanId,
                    class: normalizeClass(["option-item flex flex-ac", { "is-selected": item.selected }]),
                    onClick: ($event) => handleSelect(item)
                  }, [
                    createBaseVNode("div", _hoisted_5$3, toDisplayString(item.courseName), 1),
                    item.selected ? (openBlock(), createBlock(SvgIcon, {
                      key: 0,
                      "icon-class": "select"
                    })) : createCommentVNode("", true)
                  ], 10, _hoisted_4$4);
                }), 128))
              ])
            ]),
            _: 1
          }, 8, ["visible", "virtual-ref", "disabled"])
        ])
      ], 64);
    };
  }
});
const TeachPlanSelect_vue_vue_type_style_index_0_scoped_8df7386d_lang = "";
const TeachPlanSelect_vue_vue_type_style_index_1_lang = "";
const TeachPlanSelect = /* @__PURE__ */ _export_sfc$1(_sfc_main$8, [["__scopeId", "data-v-8df7386d"]]);
const elTree = "";
const elCheckbox = "";
const _hoisted_1$7 = { class: "resource-tree" };
const _hoisted_2$6 = { class: "custom-tree-node" };
const _hoisted_3$5 = { class: "node-label" };
const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  __name: "ResourceTree",
  props: {
    resourceTree: {
      type: Array,
      default: () => []
    }
  },
  emits: ["select"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const treeRef = ref(null);
    const props = __props;
    const emit = __emit;
    const selectedNode = ref(null);
    const defaultExpandedKeys = ref([]);
    watch(
      () => props.resourceTree,
      (newTree) => {
        nextTick(() => {
          if (treeRef.value) {
            const node = treeRef.value.getNode(selectedNode.value?.data?.directoryId);
            if (node) {
              defaultExpandedKeys.value = getParentKeys(newTree, node.data.directoryId);
              selectedNode.value = node;
              emit("select", node.data);
            } else if (newTree.length > 0) {
              const firstLeafNode = findFirstLeafNode(newTree);
              if (firstLeafNode) {
                defaultExpandedKeys.value = getParentKeys(newTree, firstLeafNode.directoryId);
                const node2 = treeRef.value.getNode(firstLeafNode.directoryId);
                selectedNode.value = node2;
                emit("select", node2.data);
              }
            }
          }
        });
      },
      {
        immediate: true
      }
    );
    const findFirstLeafNode = (tree) => {
      for (const node of tree) {
        if (node.children && node.children.length > 0) {
          const leafNode = findFirstLeafNode(node.children);
          if (leafNode) {
            return leafNode;
          }
        } else {
          return node;
        }
      }
      return null;
    };
    const getParentKeys = (tree, targetId, path = []) => {
      for (const node of tree) {
        if (node.directoryId === targetId) {
          return [...path, node.directoryId];
        }
        if (node.children && node.children.length > 0) {
          const result = getParentKeys(node.children, targetId, [...path, node.directoryId]);
          if (result.length > 0) {
            return result;
          }
        }
      }
      return [];
    };
    const isSelectedOrAncestor = (node) => {
      let current = selectedNode.value;
      while (current) {
        if (current === node)
          return true;
        current = current.parent;
      }
      return false;
    };
    const getLevel1Root = (node) => {
      if (!node)
        return null;
      let current = node;
      let prev = null;
      while (current.parent) {
        prev = current;
        current = current.parent;
      }
      return prev;
    };
    const isInSameFirstLevel = (node) => {
      if (!node || !selectedNode.value)
        return false;
      return getLevel1Root(node) === getLevel1Root(selectedNode.value);
    };
    const isSelected = (node) => {
      console.log("isSelected", node, selectedNode.value);
      return selectedNode.value === node;
    };
    const defaultProps = {
      children: "children",
      label: "directoryName"
    };
    const handleNodeClick = (_data, node) => {
      if (node.isLeaf) {
        console.log(node);
        selectedNode.value = node;
        emit("select", node.data);
      }
    };
    const setSelectedNode = (id) => {
      defaultExpandedKeys.value = getParentKeys(props.resourceTree, id);
      const node = treeRef.value.getNode(id);
      selectedNode.value = node;
      emit("select", node.data);
    };
    __expose({
      selectedNode,
      setSelectedNode
    });
    return (_ctx, _cache) => {
      const _component_el_tree = ElTree;
      return openBlock(), createElementBlock("div", _hoisted_1$7, [
        createVNode(_component_el_tree, {
          ref_key: "treeRef",
          ref: treeRef,
          data: props.resourceTree,
          props: defaultProps,
          "render-after-expand": "",
          "default-expanded-keys": defaultExpandedKeys.value,
          indent: 0,
          accordion: "",
          "empty-text": "暂无数据",
          "node-key": "directoryId",
          onNodeClick: handleNodeClick
        }, {
          default: withCtx(({ node }) => [
            createBaseVNode("div", {
              class: normalizeClass([
                `level-${node.level}`,
                {
                  "is-expanded": node.expanded,
                  "is-selected": isSelectedOrAncestor(node),
                  "is-highlight": isInSameFirstLevel(node)
                }
              ])
            }, [
              createBaseVNode("div", _hoisted_2$6, [
                !node.isLeaf ? (openBlock(), createBlock(SvgIcon, {
                  key: 0,
                  "icon-class": "arrow-right",
                  class: normalizeClass(["custom-arrow", { "is-expanded": node.expanded }])
                }, null, 8, ["class"])) : isSelected(node) ? (openBlock(), createBlock(SvgIcon, {
                  key: 1,
                  "icon-class": "fish",
                  class: "leaf-icon"
                })) : createCommentVNode("", true),
                createBaseVNode("div", _hoisted_3$5, toDisplayString(node.label), 1)
              ])
            ], 2)
          ]),
          _: 1
        }, 8, ["data", "default-expanded-keys"])
      ]);
    };
  }
});
const ResourceTree_vue_vue_type_style_index_0_scoped_22a17eb3_lang = "";
const ResourceTree = /* @__PURE__ */ _export_sfc$1(_sfc_main$7, [["__scopeId", "data-v-22a17eb3"]]);
const elPopconfirm = "";
const _hoisted_1$6 = { class: "progress-bar-container flex flex-ac" };
const _hoisted_2$5 = { class: "progress-bar" };
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    progress: {
      type: Number,
      default: 0
    }
  },
  setup(__props) {
    const props = __props;
    const iconClass = computed(() => {
      if (!props.progress) {
        return "";
      }
      if (props.progress === 100) {
        return "select";
      }
      if (props.progress < 100) {
        return "fish";
      }
      return "";
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$6, [
        createBaseVNode("div", _hoisted_2$5, [
          createBaseVNode("div", {
            class: "progress-bar-fill",
            style: normalizeStyle({ width: props.progress + "%" })
          }, null, 4)
        ]),
        createBaseVNode("div", {
          class: normalizeClass(["progress-icon", { finish: iconClass.value === "select" }]),
          style: normalizeStyle({ left: props.progress + "%" })
        }, [
          createVNode(SvgIcon, { "icon-class": iconClass.value }, null, 8, ["icon-class"])
        ], 6)
      ]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_89bce9df_lang = "";
const ProgressBar = /* @__PURE__ */ _export_sfc$1(_sfc_main$6, [["__scopeId", "data-v-89bce9df"]]);
const _hoisted_1$5 = { class: "resource-item" };
const _hoisted_2$4 = { class: "resource-image" };
const _hoisted_3$4 = {
  key: 0,
  class: "transcoding flex flex-jc flex-ac flex-v"
};
const _hoisted_4$3 = {
  key: 1,
  class: "error flex flex-jc flex-ac flex-v"
};
const _hoisted_5$2 = { class: "resource-title-wrap" };
const _hoisted_6$2 = { class: "resource-title" };
const _hoisted_7$2 = { class: "resource-progress" };
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "ResourceItem",
  props: {
    resource: {
      type: Object,
      default: () => {
      }
    },
    documentType: {
      type: String,
      default: ""
    },
    classTeacher: {
      type: Object,
      default: () => {
      }
    },
    fileUseType: {
      type: String,
      default: ""
    }
  },
  emits: ["selectResource", "refresh", "error"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    let fileType = "";
    const fileStatus = ref("");
    const progress = computed(() => {
      return Math.round(props.resource.teachingProgress * 100);
    });
    const progressText = computed(() => {
      if (progress.value >= 100) {
        return "已完成授课";
      } else if (progress.value > 0) {
        return `已授课${progress.value}%`;
      } else {
        return "未开始授课";
      }
    });
    const handleClick = () => {
      if (!props.resource.documentHtmlUrl)
        return;
      emit("selectResource", props.resource, props.documentType);
    };
    let statusCheckInterval = null;
    const checkUploadStatus = async () => {
      try {
        if (!props.resource.fileTransId)
          return;
        const params = {
          saasSchoolId: props.classTeacher.saasSchoolId,
          saasUserId: props.classTeacher.saasUserId,
          fileUseType: props.fileUseType,
          fileType,
          fileTransId: props.resource.fileTransId
        };
        const {
          data: { status }
        } = await getUploadStatus(params);
        console.log("当前上传状态:", status);
        if (status !== "transcoding") {
          if (status !== "completed") {
            emit("error", props.resource);
            showError("文件转码失败");
          } else {
            emit("refresh");
            eventBus.emit(EventBusEnum.TRANS_COMPLETE);
          }
          stopStatusCheck();
          console.log("上传已完成或失败，停止查询");
        } else {
          startStatusCheck();
        }
      } catch (error) {
        logger.warn("【ResourceItem】", "查询上传状态失败:", error);
        stopStatusCheck();
      }
    };
    const startStatusCheck = () => {
      statusCheckInterval = setTimeout(checkUploadStatus, 5e3);
    };
    const stopStatusCheck = () => {
      if (statusCheckInterval) {
        clearTimeout(statusCheckInterval);
        statusCheckInterval = null;
      }
    };
    const getFileType = () => {
      const ext = props.resource.documentName.split(".").pop()?.toLowerCase();
      switch (ext) {
        case "ppt":
        case "pptx":
          fileType = "ppt";
          break;
        case "pdf":
          fileType = "pdf";
          break;
        case "mp3":
          fileType = "audio";
          break;
        case "mp4":
          fileType = "video";
          break;
        case "jpg":
        case "png":
        case "gif":
          fileType = "img";
          break;
        default:
          fileType = "";
      }
    };
    watch(
      () => props.resource,
      () => {
        if (props.resource.fileTransId) {
          fileStatus.value = "transcoding";
          getFileType();
          checkUploadStatus();
        } else {
          fileStatus.value = "";
          stopStatusCheck();
        }
      },
      {
        deep: true,
        immediate: true
      }
    );
    onUnmounted(() => {
      stopStatusCheck();
    });
    return (_ctx, _cache) => {
      const _component_el_image = ElImage;
      const _directive_click_db = resolveDirective("click-db");
      return withDirectives((openBlock(), createElementBlock("div", _hoisted_1$5, [
        createBaseVNode("div", _hoisted_2$4, [
          fileStatus.value === "transcoding" ? (openBlock(), createElementBlock("div", _hoisted_3$4, [
            createVNode(Loading),
            _cache[0] || (_cache[0] = createBaseVNode("span", { class: "text" }, "转码中", -1))
          ])) : fileStatus.value === "error" ? (openBlock(), createElementBlock("div", _hoisted_4$3, [
            createVNode(SvgIcon, { "icon-class": "refresh" }),
            _cache[1] || (_cache[1] = createBaseVNode("span", { class: "text" }, "转码失败，刷新重试", -1))
          ])) : (openBlock(), createBlock(_component_el_image, {
            key: 2,
            class: "image",
            fit: "contain",
            src: props.resource.thumbnailUrl + "1.jpg",
            alt: ""
          }, {
            error: withCtx(() => _cache[2] || (_cache[2] = [
              createBaseVNode("div", { class: "error flex flex-jc flex-ac flex-v" }, [
                createBaseVNode("span", { class: "text" }, "图片加载失败")
              ], -1)
            ])),
            _: 1
          }, 8, ["src"]))
        ]),
        createBaseVNode("div", {
          class: normalizeClass(["resource-info", [props.documentType]])
        }, [
          createBaseVNode("div", _hoisted_5$2, [
            createBaseVNode("div", _hoisted_6$2, toDisplayString(props.resource.documentName), 1)
          ]),
          __props.documentType === unref(DocumentTypeEnum).COURSEWARE ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
            createVNode(ProgressBar, { progress: progress.value }, null, 8, ["progress"]),
            createBaseVNode("div", _hoisted_7$2, toDisplayString(progressText.value), 1)
          ], 64)) : createCommentVNode("", true)
        ], 2)
      ])), [
        [_directive_click_db, handleClick]
      ]);
    };
  }
});
const ResourceItem_vue_vue_type_style_index_0_scoped_6cf229fc_lang = "";
const ResourceItem = /* @__PURE__ */ _export_sfc$1(_sfc_main$5, [["__scopeId", "data-v-6cf229fc"]]);
const _hoisted_1$4 = ["width", "height"];
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    src: {
      type: String,
      default: ""
    },
    width: {
      type: Number,
      default: 400
    },
    height: {
      type: Number,
      default: 300
    }
  },
  setup(__props) {
    const props = __props;
    const canvasRef = ref();
    const { scale } = calcHtmlFontSize();
    const widthRatio = computed(() => {
      return props.width * scale;
    });
    const heightRatio = computed(() => {
      return props.height * scale;
    });
    const loadGif = async (src) => {
      try {
        if (!src) {
          logger.info("【GIFStaticView】", "src为空，不加载gif");
          return;
        }
        await nextTick();
        const image = await loadImage(src);
        if (!canvasRef.value) {
          logger.warn("【GIFStaticView】", "canvasRef为空，不加载gif");
          return;
        }
        const canvas = canvasRef.value;
        const ctx = canvas.getContext("2d");
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const imgWidth = image.width;
        const imgHeight = image.height;
        const imgAspectRatio = imgWidth / imgHeight;
        const canvasAspectRatio = canvasWidth / canvasHeight;
        let width = canvasWidth;
        let height = canvasHeight;
        let offsetX = 0;
        let offsetY = 0;
        if (imgAspectRatio < canvasAspectRatio) {
          width = canvasWidth;
          height = width / imgAspectRatio;
        } else {
          height = canvasHeight;
          width = height * imgAspectRatio;
        }
        offsetX = (canvasWidth - width) / 2;
        offsetY = (canvasHeight - height) / 2;
        ctx && ctx.drawImage(image, offsetX, offsetY, width, height);
      } catch (e) {
        logger.error("【GIFStaticView】", "gif加载失败", e, src);
      }
    };
    watch(
      () => props.src,
      loadGif,
      {
        immediate: true
      }
    );
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("canvas", {
        class: "gif-static-view",
        ref_key: "canvasRef",
        ref: canvasRef,
        width: widthRatio.value,
        height: heightRatio.value
      }, null, 8, _hoisted_1$4);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_f4c38cdd_lang = "";
const GIFStaticView = /* @__PURE__ */ _export_sfc$1(_sfc_main$4, [["__scopeId", "data-v-f4c38cdd"]]);
const _hoisted_1$3 = {
  ref: "target",
  class: "material-item"
};
const _hoisted_2$3 = { class: "resource-image" };
const _hoisted_3$3 = ["src"];
const _hoisted_4$2 = { class: "resource-title" };
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "MaterialItem",
  props: {
    resource: {
      type: Object,
      default: () => {
      }
    },
    documentType: {
      type: String,
      default: ""
    },
    classTeacher: {
      type: Object,
      default: () => {
      }
    },
    fileUseType: {
      type: String,
      default: ""
    }
  },
  emits: ["selectResource"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const mediaInfo = computed(() => {
      return getMediaDisplay(props.resource.documentUrl);
    });
    function getMediaDisplay(documentUrl) {
      if (!documentUrl)
        return { type: "image", url: __vite_glob_0_5 };
      const ext = documentUrl.split(".").pop()?.toLowerCase();
      if (ext) {
        if (["jpg", "png", "gif", "jpeg"].includes(ext)) {
          return {
            type: "image",
            url: documentUrl + "?x-oss-process=image/resize,w_200",
            ext
          };
        }
        if (ext === "mp4" || ext === "mov") {
          return { type: "video", url: documentUrl, ext };
        }
        if (ext === "mp3") {
          return { type: "audio", url: __vite_glob_0_5, ext };
        }
      }
      return { type: "image", url: __vite_glob_0_5, ext };
    }
    const handleClick = () => {
      if (!props.resource.documentUrl)
        return;
      emit("selectResource", props.resource, props.documentType);
    };
    return (_ctx, _cache) => {
      const _component_el_image = ElImage;
      const _directive_click_db = resolveDirective("click-db");
      return withDirectives((openBlock(), createElementBlock("div", _hoisted_1$3, [
        createBaseVNode("div", _hoisted_2$3, [
          mediaInfo.value.ext === "gif" ? (openBlock(), createBlock(GIFStaticView, {
            key: 0,
            src: mediaInfo.value.url,
            width: 133,
            height: 84
          }, null, 8, ["src"])) : mediaInfo.value.type === "image" ? (openBlock(), createBlock(_component_el_image, {
            key: 1,
            fit: "cover",
            class: "image",
            src: mediaInfo.value.url,
            alt: "Image"
          }, null, 8, ["src"])) : mediaInfo.value.type === "video" ? (openBlock(), createElementBlock("video", {
            key: 2,
            class: "image",
            src: mediaInfo.value.url,
            preload: "metadata"
          }, null, 8, _hoisted_3$3)) : mediaInfo.value.type === "audio" ? (openBlock(), createBlock(_component_el_image, {
            key: 3,
            class: "image",
            src: mediaInfo.value.url,
            alt: "Audio Cover"
          }, null, 8, ["src"])) : createCommentVNode("", true)
        ]),
        createBaseVNode("div", {
          class: normalizeClass(["resource-info", [props.documentType]])
        }, [
          createBaseVNode("span", _hoisted_4$2, toDisplayString(props.resource.documentName), 1)
        ], 2)
      ])), [
        [_directive_click_db, handleClick]
      ]);
    };
  }
});
const MaterialItem_vue_vue_type_style_index_0_scoped_8a3d8cfb_lang = "";
const MaterialItem = /* @__PURE__ */ _export_sfc$1(_sfc_main$3, [["__scopeId", "data-v-8a3d8cfb"]]);
const _hoisted_1$2 = { class: "uploading-wrapper flex flex-ac flex-v flex-jc" };
const _hoisted_2$2 = { class: "uploading-container flex flex-ac flex-v" };
const _hoisted_3$2 = { class: "uploading-image-box flex flex-jc flex-ac" };
const _hoisted_4$1 = { class: "uploading-content" };
const _hoisted_5$1 = { class: "flex flex-v flex-ac" };
const _hoisted_6$1 = { class: "title" };
const _hoisted_7$1 = { class: "uploading-progress-bar flex flex-ac" };
const _hoisted_8$1 = { class: "progress-bar" };
const _hoisted_9$1 = {
  key: 0,
  class: "transcoding-tips"
};
const _hoisted_10$1 = {
  key: 1,
  class: "tips-container"
};
const _sfc_main$2 = /* @__PURE__ */ defineComponent({
  __name: "Uploading",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    progress: {
      type: Number,
      default: 0
    },
    showTips: {
      type: Boolean,
      default: false
    },
    loadingType: {
      type: String
    },
    showMinButton: {
      type: Boolean,
      default: false
    }
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const props = __props;
    const loadingText = computed(() => {
      if (props.loadingType === DocumentTypeEnum.COURSEWARE && props.showMinButton) {
        return "转码中";
      }
      return "上传中";
    });
    const handleClose = () => {
      emit("close");
    };
    return (_ctx, _cache) => {
      return withDirectives((openBlock(), createElementBlock("div", _hoisted_1$2, [
        createBaseVNode("div", _hoisted_2$2, [
          createBaseVNode("div", _hoisted_3$2, [
            _cache[0] || (_cache[0] = createBaseVNode("img", {
              class: "image",
              src: __vite_glob_0_23,
              alt: "background"
            }, null, -1)),
            createBaseVNode("span", null, toDisplayString(props.progress) + "%", 1)
          ]),
          _cache[2] || (_cache[2] = createBaseVNode("div", { class: "bg-top" }, [
            createBaseVNode("img", {
              src: __vite_glob_0_21,
              alt: "bg"
            })
          ], -1)),
          createBaseVNode("div", _hoisted_4$1, [
            createBaseVNode("div", _hoisted_5$1, [
              createBaseVNode("div", _hoisted_6$1, toDisplayString(loadingText.value), 1),
              createBaseVNode("div", _hoisted_7$1, [
                createBaseVNode("div", _hoisted_8$1, [
                  createBaseVNode("div", {
                    class: "progress-bar-fill",
                    style: normalizeStyle({ width: props.progress + "%" })
                  }, null, 4)
                ]),
                createBaseVNode("div", {
                  class: "progress-icon",
                  style: normalizeStyle({ left: props.progress + "%" })
                }, [
                  createVNode(SvgIcon, { "icon-class": "fish" })
                ], 4)
              ])
            ]),
            __props.loadingType === unref(DocumentTypeEnum).COURSEWARE && __props.showMinButton ? (openBlock(), createElementBlock("div", _hoisted_9$1, " 转码等待时间较长，建议先最小化窗口，完成后会自动提醒您 ")) : createCommentVNode("", true),
            props.showTips ? (openBlock(), createElementBlock("div", _hoisted_10$1, _cache[1] || (_cache[1] = [
              createBaseVNode("div", { class: "title" }, "不想在课堂上转码等待？", -1),
              createBaseVNode("div", { class: "text" }, [
                createTextVNode(" 老师可前往 "),
                createBaseVNode("div", { class: "highlight" }, "工作台-星讲台"),
                createTextVNode(" 在课前上传资源至云端 ")
              ], -1),
              createBaseVNode("div", { class: "text" }, "系统自动转码不需等待 ，还可永久保存随时调用", -1)
            ]))) : createCommentVNode("", true)
          ])
        ]),
        __props.loadingType === unref(DocumentTypeEnum).COURSEWARE && __props.showMinButton ? (openBlock(), createElementBlock("div", {
          key: 0,
          class: "minimize",
          onClick: handleClose
        }, [
          createVNode(SvgIcon, {
            class: "minimize-icon",
            "icon-class": "min-size2"
          }),
          _cache[3] || (_cache[3] = createTextVNode(" 最小化 "))
        ])) : createCommentVNode("", true)
      ], 512)), [
        [vShow, props.visible]
      ]);
    };
  }
});
const Uploading_vue_vue_type_style_index_0_scoped_63c5c183_lang = "";
const Uploading = /* @__PURE__ */ _export_sfc$1(_sfc_main$2, [["__scopeId", "data-v-63c5c183"]]);
const _hoisted_1$1 = { class: "resource-container flex flex-v" };
const _hoisted_2$1 = { class: "scroll-container" };
const _hoisted_3$1 = { class: "title-text" };
const _hoisted_4 = { key: 0 };
const _hoisted_5 = { class: "resource-list" };
const _hoisted_6 = {
  key: 0,
  class: "no-data"
};
const _hoisted_7 = { class: "resource-list material" };
const _hoisted_8 = {
  key: 0,
  class: "no-data"
};
const _hoisted_9 = { class: "bottom-button flex flex-ac flex-jc" };
const _hoisted_10 = { class: "confirm-button-group" };
const _hoisted_11 = { class: "flex flex-v flex-ac mobile-qrcode-container" };
const _hoisted_12 = ["accept"];
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "ResourceList",
  props: {
    documentTypeList: {},
    resourceTree: {},
    selectedNode: {},
    selectedPlan: {},
    classTeacher: {}
  },
  emits: ["refresh", "select", "allComplete"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const popConfirmRef = ref();
    const emit = __emit;
    const props = __props;
    const qrcodeText = ref("");
    const uploading = ref(false);
    const uploadProgress = ref(0);
    const showTips = computed(() => {
      return !props.selectedPlan && !props.selectedNode;
    });
    let transId = "";
    let fileType = "";
    const resourceList = computed(() => {
      if (!props.documentTypeList)
        return [];
      const type = props.documentTypeList.find(
        (item) => item.documentType === DocumentTypeEnum.COURSEWARE
      );
      if (!type)
        return [];
      return type.documentList.filter((document2) => {
        return document2.transStatus !== "error";
      });
    });
    const materialList = computed(() => {
      if (!props.documentTypeList)
        return [];
      const type = props.documentTypeList.find(
        (item) => item.documentType === DocumentTypeEnum.MATERIAL
      );
      if (!type)
        return [];
      return type.documentList;
    });
    watch(
      resourceList,
      () => {
        console.log("resourceList changes");
        getProgress();
      },
      {
        deep: false
      }
    );
    let isFirst = true;
    const getProgress = async () => {
      if (resourceList.value.length) {
        const fileIds = resourceList.value.map((item) => item.documentId);
        try {
          const { data } = await getResourceProgressList({
            fileIds,
            saasClassId: props.classTeacher?.saasClassId
          });
          const progressList = data.teachingProgressList;
          resourceList.value.forEach((item) => {
            const resource = progressList.find((item2) => item2.fileId === item.documentId);
            if (resource) {
              item.teachingProgress = resource.teachingProgress;
              item.anchorPoint = resource.anchorPoint;
              item.total = resource.total;
            }
          });
          const isAllComplete = resourceList.value.every((item) => item.teachingProgress === 1);
          if (isAllComplete && isFirst) {
            emit("allComplete");
          }
          isFirst = false;
        } catch (e) {
          logger.warn("【ResourceList】", "进度获取失败", e);
        }
      }
    };
    const fileInput = ref(null);
    const currentUploadType = ref(DocumentTypeEnum.COURSEWARE);
    const acceptType = computed(() => {
      if (currentUploadType.value === DocumentTypeEnum.COURSEWARE) {
        return ".ppt,.pptx,.pdf";
      }
      return ".mp3,.mp4,.jpg,.jpeg,.png,.gif";
    });
    const upLoadingMinBtn = ref(false);
    const fileUseType = computed(() => {
      if (props.selectedPlan && props.selectedNode) {
        return "normal";
      }
      return "temp";
    });
    const selectResource = (resource, type) => {
      const teachPlanId = props.selectedPlan?.teachPlanId;
      const directoryId = props.selectedNode?.directoryId;
      if (teachPlanId) {
        resource.teachPlanId = teachPlanId;
      }
      if (directoryId) {
        resource.directoryId = directoryId;
      }
      emit("select", { type, resource });
    };
    let serialNumber = "";
    const showQrCode = async () => {
      try {
        serialNumber = await getDeviceIdWidget();
      } catch (e) {
        console.log("【ResourceList】获取设备信息失败：", e);
      }
      const query = {
        saasClassId: props.classTeacher.saasClassId,
        saasSchoolId: props.classTeacher.saasSchoolId,
        saasCampusId: props.classTeacher.saasCampusId,
        staffId: serialNumber,
        saasUserId: props.classTeacher.saasUserId,
        teachPlanId: props.selectedPlan?.teachPlanId || "",
        directoryId: props.selectedNode?.directoryId || ""
      };
      const queryString = new URLSearchParams(query).toString();
      qrcodeText.value = `${"https://class.hailiangedu.com/black-board-mobile"}/upload-images?${queryString}`;
    };
    const handleImport = (type, skipFileLimit) => {
      upLoadingMinBtn.value = false;
      if (props.resourceTree?.length && !props.selectedNode) {
        showError("请选择目录");
        return;
      }
      if (type === DocumentTypeEnum.COURSEWARE && resourceList.value.length >= 3 && !skipFileLimit && props.selectedNode) {
        return;
      }
      currentUploadType.value = type;
      nextTick(() => {
        fileInput.value?.click();
      });
    };
    const handleImportCancel = () => {
      popConfirmRef.value.$refs.tooltipRef.hide();
    };
    const validateFile = (file) => {
      return new Promise((resolve) => {
        const ext = file.name.split(".").pop()?.toLowerCase();
        let maxSize = 0;
        if (["jpg", "jpeg", "png", "gif"].includes(ext || "")) {
          maxSize = 20 * 1024 * 1024;
        } else if (["ppt", "pptx", "pdf", "mp3"].includes(ext || "")) {
          maxSize = 200 * 1024 * 1024;
        } else if (["mp4"].includes(ext || "")) {
          maxSize = 1024 * 1024 * 1024;
        } else {
          showError("不支持的文件类型");
          resolve(false);
          return;
        }
        if (file.size > maxSize) {
          showError(`文件大小不能超过 ${maxSize / (1024 * 1024)}MB`);
          resolve(false);
          return;
        }
        if (["jpg", "jpeg", "png", "gif"].includes(ext || "")) {
          const img = new Image();
          img.src = URL.createObjectURL(file);
          img.onload = () => {
            if (img.width > 5e3 || img.height > 5e3) {
              showError("图片分辨率不能超过 5000x5000");
              URL.revokeObjectURL(img.src);
              resolve(false);
              return;
            }
            URL.revokeObjectURL(img.src);
            resolve(true);
          };
          img.onerror = () => {
            showError("图片加载失败");
            URL.revokeObjectURL(img.src);
            resolve(false);
          };
        } else {
          resolve(true);
        }
      });
    };
    const handleFileChange = async (event) => {
      const input = event.target;
      const file = input.files?.[0];
      input.value = "";
      if (file) {
        try {
          const isValid = await validateFile(file);
          if (!isValid)
            return;
          uploading.value = true;
          updateProgress(0);
          const { fileUrl: url } = await uploadFile(file, { group: DocumentTypeEnum.COURSEWARE });
          const ext = file.name.split(".").pop()?.toLowerCase();
          switch (ext) {
            case "ppt":
            case "pptx":
              fileType = "ppt";
              break;
            case "pdf":
              fileType = "pdf";
              break;
            case "mp3":
              fileType = "audio";
              break;
            case "mp4":
              fileType = "video";
              break;
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
              fileType = "img";
              break;
            default:
              fileType = "";
          }
          const params = {
            saasSchoolId: props.classTeacher.saasSchoolId,
            saasUserId: props.classTeacher.saasUserId,
            fileUseType: fileUseType.value,
            fileName: file.name,
            fileUrl: encodeURI(url),
            fileType,
            documentType: currentUploadType.value
          };
          if (props.selectedPlan && props.selectedNode) {
            params.teachPlanId = props.selectedPlan?.teachPlanId;
            params.directoryId = props.selectedNode?.directoryId;
          }
          const {
            data: { fileTransId }
          } = await resourceUpload(params);
          if (currentUploadType.value === DocumentTypeEnum.COURSEWARE && !fileTransId) {
            throw new Error("文件上传失败");
          }
          transId = fileTransId;
          console.log("文件上传成功", fileTransId, url);
          if (currentUploadType.value === DocumentTypeEnum.COURSEWARE) {
            upLoadingMinBtn.value = true;
            startStatusCheck();
          } else {
            clearTime();
            console.log("素材上传成功", transId, url);
            reportTrackEvent(EVENT_CODE$1.IMPORT_RESOURCES.code, {
              pathId: transId,
              sourceUrl: encodeURI(url),
              sourceType: "material"
            });
            emit("refresh");
          }
        } catch (error) {
          logger.error("【ResourceList】", "文件上传失败:", error);
          showError("文件上传失败");
          clearTime();
        }
      }
    };
    let statusCheckInterval = null;
    const checkUploadStatus = async () => {
      try {
        const params = {
          saasSchoolId: props.classTeacher.saasSchoolId,
          saasUserId: props.classTeacher.saasUserId,
          fileUseType: fileUseType.value,
          fileType,
          fileTransId: transId
        };
        const {
          data: { status, documentId, resultUrl }
        } = await getUploadStatus(params);
        console.log("当前上传状态:", status);
        if (status !== "transcoding") {
          if (status !== "completed") {
            showError("文件转码失败");
          }
          stopStatusCheck();
          clearTime();
          reportTrackEvent(EVENT_CODE$1.IMPORT_RESOURCES.code, {
            pathId: documentId,
            sourceUrl: resultUrl || "",
            sourceType: "courseware"
          });
          emit("refresh");
          console.log("上传已完成或失败，停止查询", documentId, resultUrl);
        } else {
          startStatusCheck();
        }
      } catch (error) {
        logger.warn("【ResourceList】", "查询上传状态失败:", error);
        stopStatusCheck();
        clearTime();
      }
    };
    const handleRefresh = () => {
      emit("refresh");
    };
    const handleUploadError = (item) => {
      resourceList.value.find((resource) => {
        if (resource.documentId === item.documentId) {
          resource.fileTransId = "";
          resource.transStatus = "error";
        }
      });
    };
    const startStatusCheck = () => {
      statusCheckInterval = setTimeout(checkUploadStatus, 5e3);
    };
    const stopStatusCheck = () => {
      if (statusCheckInterval) {
        clearTimeout(statusCheckInterval);
        statusCheckInterval = null;
      }
    };
    let timeout = null;
    const updateProgress = (progress) => {
      if (progress <= 100) {
        uploadProgress.value = progress;
        const delay = Math.max(100, 1e3 - progress * 10);
        timeout = setTimeout(() => updateProgress(progress + 1), delay);
      }
    };
    const clearTime = () => {
      uploading.value = false;
      uploadProgress.value = 0;
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
    };
    const handleStatusClose = () => {
      clearTime();
      stopStatusCheck();
      emit("refresh");
    };
    const adjustFontSize = () => {
      const titles = document.querySelectorAll(".resource-title");
      titles.forEach((title) => {
        const element = title;
        const computedStyle = window.getComputedStyle(element);
        const lineHeight = parseFloat(computedStyle.lineHeight);
        if (element.scrollHeight > lineHeight + 4) {
          element.style.fontSize = px2rem(18) + "rem";
          element.style.lineHeight = px2rem(20) + "rem";
        }
      });
    };
    watch(
      resourceList,
      () => {
        nextTick(() => {
          adjustFontSize();
        });
      },
      {
        immediate: true
      }
    );
    onMounted(async () => {
      adjustFontSize();
      window.addEventListener("resize", adjustFontSize);
    });
    onUnmounted(() => {
      clearTime();
      stopStatusCheck();
      window.removeEventListener("resize", adjustFontSize);
    });
    __expose({
      getProgress
    });
    return (_ctx, _cache) => {
      const _component_el_button = ElButton;
      const _component_el_popconfirm = ElPopconfirm;
      const _component_el_popover = ElPopover;
      return openBlock(), createElementBlock(Fragment, null, [
        createBaseVNode("div", _hoisted_1$1, [
          createBaseVNode("div", _hoisted_2$1, [
            createBaseVNode("div", _hoisted_3$1, [
              _cache[3] || (_cache[3] = createTextVNode(" 课件 ")),
              _ctx.selectedNode ? (openBlock(), createElementBlock("span", _hoisted_4, toDisplayString(resourceList.value.length ? `（${resourceList.value.length}/3）` : ""), 1)) : createCommentVNode("", true)
            ]),
            createBaseVNode("div", _hoisted_5, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(resourceList.value, (item) => {
                return openBlock(), createBlock(ResourceItem, {
                  key: item.documentId,
                  resource: item,
                  "file-use-type": fileUseType.value,
                  "class-teacher": _ctx.classTeacher,
                  "document-type": unref(DocumentTypeEnum).COURSEWARE,
                  onSelectResource: selectResource,
                  onRefresh: handleRefresh,
                  onError: handleUploadError
                }, null, 8, ["resource", "file-use-type", "class-teacher", "document-type"]);
              }), 128)),
              resourceList.value.length === 0 ? (openBlock(), createElementBlock("div", _hoisted_6, "暂无课件")) : createCommentVNode("", true)
            ]),
            _cache[4] || (_cache[4] = createBaseVNode("div", { class: "title-text" }, "素材", -1)),
            createBaseVNode("div", _hoisted_7, [
              (openBlock(true), createElementBlock(Fragment, null, renderList(materialList.value, (item) => {
                return openBlock(), createBlock(MaterialItem, {
                  key: item.documentId,
                  resource: item,
                  "document-type": unref(DocumentTypeEnum).MATERIAL,
                  onSelectResource: selectResource
                }, null, 8, ["resource", "document-type"]);
              }), 128)),
              materialList.value.length === 0 ? (openBlock(), createElementBlock("div", _hoisted_8, "暂无素材")) : createCommentVNode("", true)
            ])
          ]),
          createBaseVNode("div", _hoisted_9, [
            createVNode(_component_el_popconfirm, {
              ref_key: "popConfirmRef",
              ref: popConfirmRef,
              title: "提示",
              placement: "top",
              disabled: !_ctx.selectedNode || resourceList.value.length < 3,
              "popper-class": "common-pop-confirm"
            }, {
              actions: withCtx(() => [
                _cache[7] || (_cache[7] = createBaseVNode("span", null, "最多3个课件，继续上传会替换掉最近的一个", -1)),
                createBaseVNode("div", _hoisted_10, [
                  createVNode(_component_el_button, {
                    size: "small",
                    class: "common-button active-scale",
                    type: "info",
                    onClick: handleImportCancel
                  }, {
                    default: withCtx(() => _cache[5] || (_cache[5] = [
                      createTextVNode("取消")
                    ])),
                    _: 1
                  }),
                  createVNode(_component_el_button, {
                    size: "small",
                    class: "common-button active-scale",
                    type: "primary",
                    onClick: _cache[0] || (_cache[0] = ($event) => handleImport(unref(DocumentTypeEnum).COURSEWARE, true))
                  }, {
                    default: withCtx(() => _cache[6] || (_cache[6] = [
                      createTextVNode("继续上传")
                    ])),
                    _: 1
                  })
                ])
              ]),
              reference: withCtx(() => [
                createBaseVNode("div", {
                  class: "import-button flex flex-ac active-scale hl-sls-track",
                  "data-text": "导入课件",
                  "data-module": "星讲台-资源",
                  onClick: _cache[1] || (_cache[1] = ($event) => handleImport(unref(DocumentTypeEnum).COURSEWARE))
                }, [
                  createVNode(SvgIcon, { "icon-class": "kejian" }),
                  _cache[8] || (_cache[8] = createTextVNode("导入课件 "))
                ])
              ]),
              _: 1
            }, 8, ["disabled"]),
            createBaseVNode("div", {
              class: "import-button flex flex-ac active-scale hl-sls-track",
              "data-text": "导入素材",
              "data-module": "星讲台-资源",
              onClick: _cache[2] || (_cache[2] = ($event) => handleImport(unref(DocumentTypeEnum).MATERIAL))
            }, [
              createVNode(SvgIcon, { "icon-class": "sucai" }),
              _cache[9] || (_cache[9] = createTextVNode("导入素材 "))
            ]),
            createVNode(_component_el_popover, {
              offset: 20,
              trigger: "click",
              "popper-class": "qrcode-popover",
              class: "box-item",
              placement: "right"
            }, {
              reference: withCtx(() => [
                createBaseVNode("div", {
                  class: "import-button flex flex-ac hl-sls-track",
                  "data-text": "手机上传图片",
                  "data-module": "星讲台-资源",
                  onClick: showQrCode
                }, [
                  createVNode(SvgIcon, { "icon-class": "sucai" }),
                  _cache[11] || (_cache[11] = createTextVNode("手机上传图片 "))
                ])
              ]),
              default: withCtx(() => [
                createBaseVNode("div", _hoisted_11, [
                  createVNode(QrCode, {
                    class: "qrcode",
                    text: qrcodeText.value
                  }, null, 8, ["text"]),
                  _cache[10] || (_cache[10] = createTextVNode(" 手机扫一扫，上传图片 "))
                ])
              ]),
              _: 1
            }),
            createBaseVNode("input", {
              ref_key: "fileInput",
              ref: fileInput,
              type: "file",
              style: { "display": "none" },
              accept: acceptType.value,
              onChange: handleFileChange
            }, null, 40, _hoisted_12)
          ])
        ]),
        createVNode(Uploading, {
          visible: uploading.value,
          "loading-type": currentUploadType.value,
          "show-tips": showTips.value,
          "show-min-button": upLoadingMinBtn.value,
          progress: uploadProgress.value,
          onClose: handleStatusClose
        }, null, 8, ["visible", "loading-type", "show-tips", "show-min-button", "progress"])
      ], 64);
    };
  }
});
const ResourceList_vue_vue_type_style_index_0_scoped_21a51907_lang = "";
const ResourceList_vue_vue_type_style_index_1_lang = "";
const ResourceList = /* @__PURE__ */ _export_sfc$1(_sfc_main$1, [["__scopeId", "data-v-21a51907"]]);
const _hoisted_1 = { class: "resource-dialog-container flex" };
const _hoisted_2 = { class: "resource-tree-container" };
const _hoisted_3 = { class: "resource-list-container" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  emits: ["update:visible", "select"],
  setup(__props, { emit: __emit }) {
    const urlParams = getWindowUrlParams();
    logger.info("【资源弹窗】", "显示", urlParams);
    const classTeacher = ref({
      saasUserId: "",
      saasSchoolId: "",
      saasCampusId: "",
      saasClassId: "",
      saasClassName: "",
      userName: "",
      avatar: "",
      saasSubjectId: "",
      saasSubjectName: "",
      saasSubjectCode: "",
      disciplineCode: "",
      disciplineId: "",
      disciplineName: "",
      lessonId: ""
    });
    const emit = __emit;
    const visible = ref(true);
    const resourceTreeRef = ref(null);
    const resourceListRef = ref(null);
    const teachPlanList = ref([]);
    const selectedPlan = ref();
    const resourceTree = ref([]);
    const documentTypeList = ref();
    const selectedNode = ref();
    let selectedResource = {
      teachPlanId: "",
      directoryId: ""
    };
    const isSelectedBefore = ref(false);
    const getSelectedResource = async (params) => {
      const { data } = await getResourceTreeRecord(params);
      if (data.teachPlanId && data.directoryId) {
        isSelectedBefore.value = true;
      }
      selectedResource = data;
    };
    const handlePlanChange = (item) => {
      selectedPlan.value = item;
      selectedNode.value = void 0;
      documentTypeList.value = [];
      getResourceTreeData();
    };
    const getResourceTreeData = async () => {
      const data = {
        saasUserId: classTeacher.value?.saasUserId,
        saasSchoolId: classTeacher.value?.saasSchoolId,
        rootDirectoryId: selectedPlan.value?.rootDirectoryId
      };
      try {
        const res = await getResourceTree(data);
        const list = res.data.teachingDirectoryTreeDTOList;
        if (list && list.length) {
          if (selectedResource.directoryId) {
            const node = findNodeInTree(list, selectedResource.directoryId);
            if (node) {
              console.log("选中的资源", node);
              resourceTreeRef.value.selectedNode = { data: node };
            }
            selectedResource.directoryId = "";
          }
          nextTick(() => {
            resourceTree.value = list;
          });
        } else if (res.data.teachingTempDocumentDTOList) {
          resourceTree.value = [];
          documentTypeList.value = res.data.teachingTempDocumentDTOList;
        }
        console.log("获取资源树成功", res);
      } catch (e) {
        logger.warn("【ResourceDialog】", "获取资源树失败", e);
      }
    };
    const findNodeInTree = (tree, directoryId) => {
      for (const node of tree) {
        if (node.directoryId === directoryId) {
          return node;
        }
        if (node.children && node.children.length > 0) {
          const foundNode = findNodeInTree(node.children, directoryId);
          if (foundNode) {
            return foundNode;
          }
        }
      }
      return void 0;
    };
    const handleTreeChange = (item) => {
      console.log("切换资源目录", item);
      const params = {
        saasClassId: classTeacher.value?.saasClassId,
        saasSchoolId: classTeacher.value?.saasSchoolId,
        saasUserId: classTeacher.value?.saasUserId,
        teachPlanId: selectedPlan.value?.teachPlanId,
        rootDirectoryId: selectedPlan.value?.rootDirectoryId,
        directoryId: item.directoryId
      };
      recordResourceTree(params);
      selectedNode.value = item;
      documentTypeList.value = item.documentTypeList;
    };
    const init = async () => {
      try {
        const classroomData = await BridgeZmqUtils.callEelectronRenderer(
          CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS
        );
        const newTeacher = classroomData.teacher || {};
        const oldTeacher = classTeacher.value;
        if (newTeacher.saasUserId === oldTeacher.saasUserId && newTeacher.saasClassId === oldTeacher.saasClassId && newTeacher.saasSchoolId === oldTeacher.saasSchoolId && newTeacher.saasSubjectCode === oldTeacher.saasSubjectCode) {
          logger.debug("【资源弹窗】", "上课信息未改变，不更新资源弹窗");
          return;
        }
        classTeacher.value = classroomData.teacher || {};
        teachPlanList.value = [];
        resourceTree.value = [];
        documentTypeList.value = [];
        const data = {
          saasSubjectCode: classTeacher.value?.saasSubjectCode,
          saasSchoolId: classTeacher.value?.saasSchoolId,
          saasUserId: classTeacher.value?.saasUserId,
          saasClassId: classTeacher.value?.saasClassId
        };
        await getSelectedResource(data);
        const res = await getTeachPlanList(data);
        const list = res.data.teachingPlanDTOList;
        if (list && list.length) {
          if (selectedResource.teachPlanId) {
            const plan = list.find((item) => item.teachPlanId === selectedResource.teachPlanId);
            if (plan) {
              plan.selected = true;
              selectedPlan.value = plan;
            } else {
              list[0].selected = true;
              selectedPlan.value = list[0];
            }
            selectedResource.teachPlanId = "";
          } else {
            list[0].selected = true;
            selectedPlan.value = list[0];
          }
          nextTick(() => {
            getResourceTreeData();
          });
        } else {
          nextTick(() => {
            getResourceTreeData();
          });
        }
        teachPlanList.value = list;
      } catch (error) {
        logger.warn("【ResourceDialog】", "获取教学计划失败", error);
      }
    };
    const getNextLeafNode = (tree, currentNodeId) => {
      let foundCurrentNode = false;
      const findNextLeaf = (nodes) => {
        for (const node of nodes) {
          if (foundCurrentNode && !node.children?.length) {
            return node;
          }
          if (node.directoryId === currentNodeId) {
            foundCurrentNode = true;
          }
          if (node.children?.length) {
            const nextLeaf = findNextLeaf(node.children);
            if (nextLeaf) {
              return nextLeaf;
            }
          }
        }
        return void 0;
      };
      return findNextLeaf(tree);
    };
    const handleAllComplete = () => {
      if (!selectedNode.value) {
        console.warn("当前没有选中节点");
        return;
      }
      const currentDirectoryId = selectedNode.value.directoryId;
      const nextLeafNode = getNextLeafNode(resourceTree.value, currentDirectoryId);
      if (nextLeafNode) {
        resourceTreeRef.value.setSelectedNode(nextLeafNode.directoryId);
      } else {
        console.warn("没有更多的叶子节点");
      }
    };
    const handleRefresh = () => {
      getResourceTreeData();
    };
    const handleResourceSelect = ({
      resource,
      type
    }) => {
      console.log("选中资源", resource, type);
      emit("update:visible", false);
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_SELECT_RESOURCE, {
        resource,
        type
      }).then(() => {
        logger.info("【ResourceDialog】", "资源打开成功", resource, type);
        Bridge.getInstance().callVoid("hide");
      }).catch((e) => {
        logger.error("【ResourceDialog】", "资源打开失败", e);
      });
    };
    const onClose = () => {
      const bridge = Bridge.getInstance();
      bridge.callVoid("hide");
    };
    useEventBus(EventBusEnum.TRANS_COMPLETE, () => {
      Bridge.getInstance().callVoid(EventBusEnum.TRANS_COMPLETE);
    });
    document.addEventListener("contextmenu", function(event) {
      event.preventDefault();
    });
    onMounted(() => {
      const bridge = Bridge.getInstance();
      bridge.on(CEF_RENDERER_MESSAGE_TYPE.RESOURCEDIALOG_REFRESH_RESOURCE_LIST, async (res) => {
        logger.info("【ResourceDialog】", "收到刷新资源列表消息", res);
        const data = res.data;
        if (!data.extraBiz?.extraBizId || data.extraBiz.extraBizId === selectedPlan.value?.teachPlanId) {
          await getResourceTreeData();
        }
        return "刷新成功";
      });
      bridge.on(QT_CEF_MESSAGE_TYPE.SHOW, () => {
        if (resourceListRef.value) {
          resourceListRef.value.getProgress();
        }
        init();
      });
      init();
      BridgeZmqUtils.callEelectronRenderer(CEF_RENDERER_MESSAGE_TYPE.CLASSROOM_CURRENT_BOARD_PARAMS).then((res) => {
        const classTeacher2 = res.teacher || {};
        setBusinessInfoWidget({
          schoolId: classTeacher2.saasSchoolId,
          campusId: classTeacher2.saasCampusId,
          classId: classTeacher2.saasClassId,
          className: classTeacher2.saasClassName,
          subjectCode: classTeacher2.saasSubjectCode,
          subjectName: classTeacher2.saasSubjectName,
          userId: classTeacher2.saasUserId
        });
      }).catch((e) => {
        logger.error("【资源弹窗】", "获取当前上课信息失败", e);
      });
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(Dialog, {
        visible: visible.value,
        "show-close": "",
        onClose
      }, {
        default: withCtx(() => [
          createBaseVNode("div", _hoisted_1, [
            createBaseVNode("div", _hoisted_2, [
              createVNode(TeachPlanSelect, {
                "is-selected-before": isSelectedBefore.value,
                "teach-plan-list": teachPlanList.value,
                onSelect: handlePlanChange
              }, null, 8, ["is-selected-before", "teach-plan-list"]),
              createVNode(ResourceTree, {
                ref_key: "resourceTreeRef",
                ref: resourceTreeRef,
                "resource-tree": resourceTree.value,
                onSelect: handleTreeChange
              }, null, 8, ["resource-tree"])
            ]),
            createBaseVNode("div", _hoisted_3, [
              createVNode(ResourceList, {
                ref_key: "resourceListRef",
                ref: resourceListRef,
                "document-type-list": documentTypeList.value,
                "resource-tree": resourceTree.value,
                "selected-node": selectedNode.value,
                "selected-plan": selectedPlan.value,
                "class-teacher": classTeacher.value,
                onSelect: handleResourceSelect,
                onRefresh: handleRefresh,
                onAllComplete: handleAllComplete
              }, null, 8, ["document-type-list", "resource-tree", "selected-node", "selected-plan", "class-teacher"])
            ]),
            _cache[0] || (_cache[0] = createBaseVNode("div", { class: "background" }, null, -1))
          ])
        ]),
        _: 1
      }, 8, ["visible"]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_c7c22399_lang = "";
const App = /* @__PURE__ */ _export_sfc$1(_sfc_main, [["__scopeId", "data-v-c7c22399"]]);
const clickDb = {
  beforeMount(el, binding) {
    let lastTouchTime = 0;
    const handleTouchStart = (event) => {
      const currentTime = (/* @__PURE__ */ new Date()).getTime();
      const timeDifference = currentTime - lastTouchTime;
      if (timeDifference < 300 && timeDifference > 0) {
        binding.value(event);
      }
      lastTouchTime = currentTime;
    };
    el.addEventListener("pointerdown", handleTouchStart);
    el._touchStartHandler = handleTouchStart;
  },
  unmounted(el) {
    el.removeEventListener("pointerdown", el._touchStartHandler);
  }
};
initLoggerWidget();
calcHtmlFontSize(true);
const app = createApp(App);
app.use(pinia).mount("#app");
app.directive("click-db", clickDb);
