import { l as logger } from "./index.5f541e77.js";
async function getStartMenuApps() {
  if (typeof window.api.invokeAddon !== "function") {
    return [];
  }
  try {
    console.time();
    const res = await window.api.invokeAddon("getStartMenuItemList");
    console.timeEnd();
    if (res.success) {
      return res.result;
    } else {
      throw res.error;
    }
  } catch (e) {
    logger.warn("【addon】获取开始菜单应用失败: ", e);
    return [];
  }
}
async function openSystemApp(appBase) {
  try {
    if (!appBase?.app) {
      throw new Error("应用信息为空");
    }
    const { app } = appBase;
    await window.api.invokeAddon("run", "appx", {
      data: {
        target: app.data.target,
        params: app.data.params
      }
    });
    return Promise.resolve(true);
  } catch (e) {
    logger.warn("【addon】应用打开失败：", JSON.stringify(appBase.app), e);
    return Promise.reject(e);
  }
}
async function openApp(url, params) {
  try {
    await window.api.invokeAddon("run", "appx", {
      data: {
        target: url,
        params
      }
    });
    return Promise.resolve(true);
  } catch (e) {
    logger.warn("【addon】应用打开失败：", url, params, e);
    return Promise.reject(e);
  }
}
export {
  openApp as a,
  getStartMenuApps as g,
  openSystemApp as o
};
