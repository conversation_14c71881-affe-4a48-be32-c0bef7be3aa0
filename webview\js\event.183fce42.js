class ElementPlusError extends Error {
  constructor(m) {
    super(m);
    this.name = "ElementPlusError";
  }
}
function throwError(scope, m) {
  throw new ElementPlusError(`[${scope}] ${m}`);
}
function debugWarn(scope, message) {
}
const UPDATE_MODEL_EVENT = "update:modelValue";
const CHANGE_EVENT = "change";
const INPUT_EVENT = "input";
export {
  CHANGE_EVENT as C,
  INPUT_EVENT as I,
  UPDATE_MODEL_EVENT as U,
  debugWarn as d,
  throwError as t
};
