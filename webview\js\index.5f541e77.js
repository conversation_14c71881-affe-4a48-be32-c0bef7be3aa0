import { a as getDefaultExportFromCjs } from "./encryptlong.f30353e7.js";
import { ay as create<PERSON>inia } from "./bootstrap.ab073eb8.js";
import { s as src_default } from "./base.649d38c6.js";
function serializeError(error) {
  return {
    name: error.name,
    message: error.message,
    stack: error.stack
  };
}
function px2rem(px) {
  return px / 12;
}
function blobToBase64(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(blob);
  });
}
function arrayBufferToBase64(arrayBuffer) {
  const uint8Array = new Uint8Array(arrayBuffer);
  let binaryString = "";
  for (let i = 0; i < uint8Array.length; i++) {
    binaryString += String.fromCharCode(uint8Array[i]);
  }
  return btoa(binaryString);
}
function searchToParams(search) {
  try {
    const params = {};
    const urlParams = new URLSearchParams(search);
    urlParams.forEach((value, key) => {
      params[key] = value;
    });
    return params;
  } catch (e) {
    return {};
  }
}
function getWindowUrlParams() {
  return searchToParams(window.location.search);
}
function minitz(y, m, d, h, i, s, tz, throwOnInvalid) {
  return minitz.fromTZ(minitz.tp(y, m, d, h, i, s, tz), throwOnInvalid);
}
minitz.fromTZISO = (localTimeStr, tz, throwOnInvalid) => {
  return minitz.fromTZ(parseISOLocal(localTimeStr, tz), throwOnInvalid);
};
minitz.fromTZ = function(tp, throwOnInvalid) {
  const inDate = new Date(Date.UTC(tp.y, tp.m - 1, tp.d, tp.h, tp.i, tp.s)), offset = getTimezoneOffset(tp.tz, inDate), dateGuess = new Date(inDate.getTime() - offset), dateOffsGuess = getTimezoneOffset(tp.tz, dateGuess);
  if (dateOffsGuess - offset === 0) {
    return dateGuess;
  } else {
    const dateGuess2 = new Date(inDate.getTime() - dateOffsGuess), dateOffsGuess2 = getTimezoneOffset(tp.tz, dateGuess2);
    if (dateOffsGuess2 - dateOffsGuess === 0) {
      return dateGuess2;
    } else if (!throwOnInvalid && dateOffsGuess2 - dateOffsGuess > 0) {
      return dateGuess2;
    } else if (!throwOnInvalid) {
      return dateGuess;
    } else {
      throw new Error("Invalid date passed to fromTZ()");
    }
  }
};
minitz.toTZ = function(d, tzStr) {
  const localDateString = d.toLocaleString("en-US", { timeZone: tzStr }).replace(/[\u202f]/, " ");
  const td = new Date(localDateString);
  return { y: td.getFullYear(), m: td.getMonth() + 1, d: td.getDate(), h: td.getHours(), i: td.getMinutes(), s: td.getSeconds(), tz: tzStr };
};
minitz.tp = (y, m, d, h, i, s, tz) => {
  return { y, m, d, h, i, s, tz };
};
function getTimezoneOffset(timeZone, date = /* @__PURE__ */ new Date()) {
  let tz = date.toLocaleString("en-US", { timeZone, timeZoneName: "short" }).split(" ").slice(-1)[0];
  tz = tz.replace(/\bAKST\b/g, "-0900").replace(/\bAKDT\b/g, "-0800").replace(/\bAST\b/g, "-0400");
  const dateString = date.toLocaleString("en-US").replace(/[\u202f]/, " ");
  return Date.parse(`${dateString} GMT`) - Date.parse(`${dateString} ${tz}`);
}
function parseISOLocal(dtStr, tz) {
  const pd = new Date(Date.parse(dtStr));
  if (isNaN(pd)) {
    throw new Error("minitz: Invalid ISO8601 passed to parser.");
  }
  const stringEnd = dtStr.substring(9);
  if (dtStr.includes("Z") || stringEnd.includes("-") || stringEnd.includes("+")) {
    return minitz.tp(pd.getUTCFullYear(), pd.getUTCMonth() + 1, pd.getUTCDate(), pd.getUTCHours(), pd.getUTCMinutes(), pd.getUTCSeconds(), "Etc/UTC");
  } else {
    return minitz.tp(pd.getFullYear(), pd.getMonth() + 1, pd.getDate(), pd.getHours(), pd.getMinutes(), pd.getSeconds(), tz);
  }
}
minitz.minitz = minitz;
function CronOptions(options) {
  if (options === void 0) {
    options = {};
  }
  delete options.name;
  options.legacyMode = options.legacyMode === void 0 ? true : options.legacyMode;
  options.paused = options.paused === void 0 ? false : options.paused;
  options.maxRuns = options.maxRuns === void 0 ? Infinity : options.maxRuns;
  options.catch = options.catch === void 0 ? false : options.catch;
  options.interval = options.interval === void 0 ? 0 : parseInt(options.interval, 10);
  options.utcOffset = options.utcOffset === void 0 ? void 0 : parseInt(options.utcOffset, 10);
  options.unref = options.unref === void 0 ? false : options.unref;
  if (options.startAt) {
    options.startAt = new CronDate(options.startAt, options.timezone);
  }
  if (options.stopAt) {
    options.stopAt = new CronDate(options.stopAt, options.timezone);
  }
  if (options.interval !== null) {
    if (isNaN(options.interval)) {
      throw new Error("CronOptions: Supplied value for interval is not a number");
    } else if (options.interval < 0) {
      throw new Error("CronOptions: Supplied value for interval can not be negative");
    }
  }
  if (options.utcOffset !== void 0) {
    if (isNaN(options.utcOffset)) {
      throw new Error("CronOptions: Invalid value passed for utcOffset, should be number representing minutes offset from UTC.");
    } else if (options.utcOffset < -870 || options.utcOffset > 870) {
      throw new Error("CronOptions: utcOffset out of bounds.");
    }
    if (options.utcOffset !== void 0 && options.timezone) {
      throw new Error("CronOptions: Combining 'utcOffset' with 'timezone' is not allowed.");
    }
  }
  if (options.unref !== true && options.unref !== false) {
    throw new Error("CronOptions: Unref should be either true, false or undefined(false).");
  }
  return options;
}
const LAST_OCCURRENCE = 32;
const ANY_OCCURRENCE = 1 | 2 | 4 | 8 | 16 | LAST_OCCURRENCE;
const OCCURRENCE_BITMASKS = [1, 2, 4, 8, 16];
function CronPattern(pattern, timezone) {
  this.pattern = pattern;
  this.timezone = timezone;
  this.second = Array(60).fill(0);
  this.minute = Array(60).fill(0);
  this.hour = Array(24).fill(0);
  this.day = Array(31).fill(0);
  this.month = Array(12).fill(0);
  this.dayOfWeek = Array(7).fill(0);
  this.lastDayOfMonth = false;
  this.starDOM = false;
  this.starDOW = false;
  this.parse();
}
CronPattern.prototype.parse = function() {
  if (!(typeof this.pattern === "string" || this.pattern.constructor === String)) {
    throw new TypeError("CronPattern: Pattern has to be of type string.");
  }
  if (this.pattern.indexOf("@") >= 0)
    this.pattern = this.handleNicknames(this.pattern).trim();
  const parts = this.pattern.replace(/\s+/g, " ").split(" ");
  if (parts.length < 5 || parts.length > 6) {
    throw new TypeError("CronPattern: invalid configuration format ('" + this.pattern + "'), exacly five or six space separated parts required.");
  }
  if (parts.length === 5) {
    parts.unshift("0");
  }
  if (parts[3].indexOf("L") >= 0) {
    parts[3] = parts[3].replace("L", "");
    this.lastDayOfMonth = true;
  }
  if (parts[3] == "*") {
    this.starDOM = true;
  }
  if (parts[4].length >= 3)
    parts[4] = this.replaceAlphaMonths(parts[4]);
  if (parts[5].length >= 3)
    parts[5] = this.replaceAlphaDays(parts[5]);
  if (parts[5] == "*") {
    this.starDOW = true;
  }
  if (this.pattern.indexOf("?") >= 0) {
    const initDate = new CronDate(/* @__PURE__ */ new Date(), this.timezone).getDate(true);
    parts[0] = parts[0].replace("?", initDate.getSeconds());
    parts[1] = parts[1].replace("?", initDate.getMinutes());
    parts[2] = parts[2].replace("?", initDate.getHours());
    if (!this.starDOM)
      parts[3] = parts[3].replace("?", initDate.getDate());
    parts[4] = parts[4].replace("?", initDate.getMonth() + 1);
    if (!this.starDOW)
      parts[5] = parts[5].replace("?", initDate.getDay());
  }
  this.throwAtIllegalCharacters(parts);
  this.partToArray("second", parts[0], 0, 1);
  this.partToArray("minute", parts[1], 0, 1);
  this.partToArray("hour", parts[2], 0, 1);
  this.partToArray("day", parts[3], -1, 1);
  this.partToArray("month", parts[4], -1, 1);
  this.partToArray("dayOfWeek", parts[5], 0, ANY_OCCURRENCE);
  if (this.dayOfWeek[7]) {
    this.dayOfWeek[0] = this.dayOfWeek[7];
  }
};
CronPattern.prototype.partToArray = function(type, conf, valueIndexOffset, defaultValue) {
  const arr = this[type];
  const lastDayOfMonth = type === "day" && this.lastDayOfMonth;
  if (conf === "" && !lastDayOfMonth)
    throw new TypeError("CronPattern: configuration entry " + type + " (" + conf + ") is empty, check for trailing spaces.");
  if (conf === "*")
    return arr.fill(defaultValue);
  const split = conf.split(",");
  if (split.length > 1) {
    for (let i = 0; i < split.length; i++) {
      this.partToArray(type, split[i], valueIndexOffset, defaultValue);
    }
  } else if (conf.indexOf("-") !== -1 && conf.indexOf("/") !== -1) {
    this.handleRangeWithStepping(conf, type, valueIndexOffset, defaultValue);
  } else if (conf.indexOf("-") !== -1) {
    this.handleRange(conf, type, valueIndexOffset, defaultValue);
  } else if (conf.indexOf("/") !== -1) {
    this.handleStepping(conf, type, valueIndexOffset, defaultValue);
  } else if (conf !== "") {
    this.handleNumber(conf, type, valueIndexOffset, defaultValue);
  }
};
CronPattern.prototype.throwAtIllegalCharacters = function(parts) {
  for (let i = 0; i < parts.length; i++) {
    const reValidCron = i === 5 ? /[^/*0-9,\-#L]+/ : /[^/*0-9,-]+/;
    if (reValidCron.test(parts[i])) {
      throw new TypeError("CronPattern: configuration entry " + i + " (" + parts[i] + ") contains illegal characters.");
    }
  }
};
CronPattern.prototype.handleNumber = function(conf, type, valueIndexOffset, defaultValue) {
  const result = this.extractNth(conf, type);
  const i = parseInt(result[0], 10) + valueIndexOffset;
  if (isNaN(i)) {
    throw new TypeError("CronPattern: " + type + " is not a number: '" + conf + "'");
  }
  this.setPart(type, i, result[1] || defaultValue);
};
CronPattern.prototype.setPart = function(part, index, value) {
  if (!Object.prototype.hasOwnProperty.call(this, part)) {
    throw new TypeError("CronPattern: Invalid part specified: " + part);
  }
  if (part === "dayOfWeek") {
    if (index === 7)
      index = 0;
    if ((index < 0 || index > 6) && index !== "L") {
      throw new RangeError("CronPattern: Invalid value for dayOfWeek: " + index);
    }
    this.setNthWeekdayOfMonth(index, value);
    return;
  }
  if (part === "second" || part === "minute") {
    if (index < 0 || index >= 60) {
      throw new RangeError("CronPattern: Invalid value for " + part + ": " + index);
    }
  } else if (part === "hour") {
    if (index < 0 || index >= 24) {
      throw new RangeError("CronPattern: Invalid value for " + part + ": " + index);
    }
  } else if (part === "day") {
    if (index < 0 || index >= 31) {
      throw new RangeError("CronPattern: Invalid value for " + part + ": " + index);
    }
  } else if (part === "month") {
    if (index < 0 || index >= 12) {
      throw new RangeError("CronPattern: Invalid value for " + part + ": " + index);
    }
  }
  this[part][index] = value;
};
CronPattern.prototype.handleRangeWithStepping = function(conf, type, valueIndexOffset, defaultValue) {
  const result = this.extractNth(conf, type);
  const matches = result[0].match(/^(\d+)-(\d+)\/(\d+)$/);
  if (matches === null)
    throw new TypeError("CronPattern: Syntax error, illegal range with stepping: '" + conf + "'");
  let [, lower, upper, steps] = matches;
  lower = parseInt(lower, 10) + valueIndexOffset;
  upper = parseInt(upper, 10) + valueIndexOffset;
  steps = parseInt(steps, 10);
  if (isNaN(lower))
    throw new TypeError("CronPattern: Syntax error, illegal lower range (NaN)");
  if (isNaN(upper))
    throw new TypeError("CronPattern: Syntax error, illegal upper range (NaN)");
  if (isNaN(steps))
    throw new TypeError("CronPattern: Syntax error, illegal stepping: (NaN)");
  if (steps === 0)
    throw new TypeError("CronPattern: Syntax error, illegal stepping: 0");
  if (steps > this[type].length)
    throw new TypeError("CronPattern: Syntax error, steps cannot be greater than maximum value of part (" + this[type].length + ")");
  if (lower > upper)
    throw new TypeError("CronPattern: From value is larger than to value: '" + conf + "'");
  for (let i = lower; i <= upper; i += steps) {
    this.setPart(type, i, result[1] || defaultValue);
  }
};
CronPattern.prototype.extractNth = function(conf, type) {
  let rest = conf;
  let nth;
  if (rest.includes("#")) {
    if (type !== "dayOfWeek") {
      throw new Error("CronPattern: nth (#) only allowed in day-of-week field");
    }
    nth = rest.split("#")[1];
    rest = rest.split("#")[0];
  }
  return [rest, nth];
};
CronPattern.prototype.handleRange = function(conf, type, valueIndexOffset, defaultValue) {
  const result = this.extractNth(conf, type);
  const split = result[0].split("-");
  if (split.length !== 2) {
    throw new TypeError("CronPattern: Syntax error, illegal range: '" + conf + "'");
  }
  const lower = parseInt(split[0], 10) + valueIndexOffset, upper = parseInt(split[1], 10) + valueIndexOffset;
  if (isNaN(lower)) {
    throw new TypeError("CronPattern: Syntax error, illegal lower range (NaN)");
  } else if (isNaN(upper)) {
    throw new TypeError("CronPattern: Syntax error, illegal upper range (NaN)");
  }
  if (lower > upper) {
    throw new TypeError("CronPattern: From value is larger than to value: '" + conf + "'");
  }
  for (let i = lower; i <= upper; i++) {
    this.setPart(type, i, result[1] || defaultValue);
  }
};
CronPattern.prototype.handleStepping = function(conf, type, valueIndexOffset, defaultValue) {
  const result = this.extractNth(conf, type);
  const split = result[0].split("/");
  if (split.length !== 2) {
    throw new TypeError("CronPattern: Syntax error, illegal stepping: '" + conf + "'");
  }
  let start = 0;
  if (split[0] !== "*") {
    start = parseInt(split[0], 10) + valueIndexOffset;
  }
  const steps = parseInt(split[1], 10);
  if (isNaN(steps))
    throw new TypeError("CronPattern: Syntax error, illegal stepping: (NaN)");
  if (steps === 0)
    throw new TypeError("CronPattern: Syntax error, illegal stepping: 0");
  if (steps > this[type].length)
    throw new TypeError("CronPattern: Syntax error, max steps for part is (" + this[type].length + ")");
  for (let i = start; i < this[type].length; i += steps) {
    this.setPart(type, i, result[1] || defaultValue);
  }
};
CronPattern.prototype.replaceAlphaDays = function(conf) {
  return conf.replace(/-sun/gi, "-7").replace(/sun/gi, "0").replace(/mon/gi, "1").replace(/tue/gi, "2").replace(/wed/gi, "3").replace(/thu/gi, "4").replace(/fri/gi, "5").replace(/sat/gi, "6");
};
CronPattern.prototype.replaceAlphaMonths = function(conf) {
  return conf.replace(/jan/gi, "1").replace(/feb/gi, "2").replace(/mar/gi, "3").replace(/apr/gi, "4").replace(/may/gi, "5").replace(/jun/gi, "6").replace(/jul/gi, "7").replace(/aug/gi, "8").replace(/sep/gi, "9").replace(/oct/gi, "10").replace(/nov/gi, "11").replace(/dec/gi, "12");
};
CronPattern.prototype.handleNicknames = function(pattern) {
  const cleanPattern = pattern.trim().toLowerCase();
  if (cleanPattern === "@yearly" || cleanPattern === "@annually") {
    return "0 0 1 1 *";
  } else if (cleanPattern === "@monthly") {
    return "0 0 1 * *";
  } else if (cleanPattern === "@weekly") {
    return "0 0 * * 0";
  } else if (cleanPattern === "@daily") {
    return "0 0 * * *";
  } else if (cleanPattern === "@hourly") {
    return "0 * * * *";
  } else {
    return pattern;
  }
};
CronPattern.prototype.setNthWeekdayOfMonth = function(index, nthWeekday) {
  if (nthWeekday === "L") {
    this["dayOfWeek"][index] = this["dayOfWeek"][index] | LAST_OCCURRENCE;
  } else if (nthWeekday < 6 && nthWeekday > 0) {
    this["dayOfWeek"][index] = this["dayOfWeek"][index] | OCCURRENCE_BITMASKS[nthWeekday - 1];
  } else if (nthWeekday === ANY_OCCURRENCE) {
    this["dayOfWeek"][index] = ANY_OCCURRENCE;
  } else {
    throw new TypeError(`CronPattern: nth weekday of of range, should be 1-5 or L. Value: ${nthWeekday}`);
  }
};
const DaysOfMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
const RecursionSteps = [["month", "year", 0], ["day", "month", -1], ["hour", "day", 0], ["minute", "hour", 0], ["second", "minute", 0]];
function CronDate(d, tz) {
  this.tz = tz;
  if (d && d instanceof Date) {
    if (!isNaN(d)) {
      this.fromDate(d);
    } else {
      throw new TypeError("CronDate: Invalid date passed to CronDate constructor");
    }
  } else if (d === void 0) {
    this.fromDate(/* @__PURE__ */ new Date());
  } else if (d && typeof d === "string") {
    this.fromString(d);
  } else if (d instanceof CronDate) {
    this.fromCronDate(d);
  } else {
    throw new TypeError("CronDate: Invalid type (" + typeof d + ") passed to CronDate constructor");
  }
}
CronDate.prototype.isNthWeekdayOfMonth = function(year, month, day, nth) {
  const date = new Date(Date.UTC(year, month, day));
  const weekday = date.getUTCDay();
  let count = 0;
  for (let d = 1; d <= day; d++) {
    if (new Date(Date.UTC(year, month, d)).getUTCDay() === weekday) {
      count++;
    }
  }
  if (nth & ANY_OCCURRENCE && OCCURRENCE_BITMASKS[count - 1] & nth) {
    return true;
  }
  if (nth & LAST_OCCURRENCE) {
    const daysInMonth = new Date(Date.UTC(year, month + 1, 0)).getUTCDate();
    for (let d = day + 1; d <= daysInMonth; d++) {
      if (new Date(Date.UTC(year, month, d)).getUTCDay() === weekday) {
        return false;
      }
    }
    return true;
  }
  return false;
};
CronDate.prototype.fromDate = function(inDate) {
  if (this.tz !== void 0) {
    if (typeof this.tz === "number") {
      this.ms = inDate.getUTCMilliseconds();
      this.second = inDate.getUTCSeconds();
      this.minute = inDate.getUTCMinutes() + this.tz;
      this.hour = inDate.getUTCHours();
      this.day = inDate.getUTCDate();
      this.month = inDate.getUTCMonth();
      this.year = inDate.getUTCFullYear();
      this.apply();
    } else {
      const d = minitz.toTZ(inDate, this.tz);
      this.ms = inDate.getMilliseconds();
      this.second = d.s;
      this.minute = d.i;
      this.hour = d.h;
      this.day = d.d;
      this.month = d.m - 1;
      this.year = d.y;
    }
  } else {
    this.ms = inDate.getMilliseconds();
    this.second = inDate.getSeconds();
    this.minute = inDate.getMinutes();
    this.hour = inDate.getHours();
    this.day = inDate.getDate();
    this.month = inDate.getMonth();
    this.year = inDate.getFullYear();
  }
};
CronDate.prototype.fromCronDate = function(d) {
  this.tz = d.tz;
  this.year = d.year;
  this.month = d.month;
  this.day = d.day;
  this.hour = d.hour;
  this.minute = d.minute;
  this.second = d.second;
  this.ms = d.ms;
};
CronDate.prototype.apply = function() {
  if (this.month > 11 || this.day > DaysOfMonth[this.month] || this.hour > 59 || this.minute > 59 || this.second > 59 || this.hour < 0 || this.minute < 0 || this.second < 0) {
    const d = new Date(Date.UTC(this.year, this.month, this.day, this.hour, this.minute, this.second, this.ms));
    this.ms = d.getUTCMilliseconds();
    this.second = d.getUTCSeconds();
    this.minute = d.getUTCMinutes();
    this.hour = d.getUTCHours();
    this.day = d.getUTCDate();
    this.month = d.getUTCMonth();
    this.year = d.getUTCFullYear();
    return true;
  } else {
    return false;
  }
};
CronDate.prototype.fromString = function(str) {
  return this.fromDate(minitz.fromTZISO(str, this.tz));
};
CronDate.prototype.findNext = function(options, target, pattern, offset) {
  const originalTarget = this[target];
  let lastDayOfMonth;
  if (pattern.lastDayOfMonth) {
    if (this.month !== 1) {
      lastDayOfMonth = DaysOfMonth[this.month];
    } else {
      lastDayOfMonth = new Date(Date.UTC(this.year, this.month + 1, 0, 0, 0, 0, 0)).getUTCDate();
    }
  }
  const fDomWeekDay = !pattern.starDOW && target == "day" ? new Date(Date.UTC(this.year, this.month, 1, 0, 0, 0, 0)).getUTCDay() : void 0;
  for (let i = this[target] + offset; i < pattern[target].length; i++) {
    let match = pattern[target][i];
    if (target === "day" && pattern.lastDayOfMonth && i - offset == lastDayOfMonth) {
      match = true;
    }
    if (target === "day" && !pattern.starDOW) {
      let dowMatch = pattern.dayOfWeek[(fDomWeekDay + (i - offset - 1)) % 7];
      if (dowMatch && dowMatch & ANY_OCCURRENCE) {
        dowMatch = this.isNthWeekdayOfMonth(this.year, this.month, i - offset, dowMatch);
      } else if (dowMatch) {
        throw new Error(`CronDate: Invalid value for dayOfWeek encountered. ${dowMatch}`);
      }
      if (options.legacyMode && !pattern.starDOM) {
        match = match || dowMatch;
      } else {
        match = match && dowMatch;
      }
    }
    if (match) {
      this[target] = i - offset;
      return originalTarget !== this[target] ? 2 : 1;
    }
  }
  return 3;
};
CronDate.prototype.recurse = function(pattern, options, doing) {
  const res = this.findNext(options, RecursionSteps[doing][0], pattern, RecursionSteps[doing][2]);
  if (res > 1) {
    let resetLevel = doing + 1;
    while (resetLevel < RecursionSteps.length) {
      this[RecursionSteps[resetLevel][0]] = -RecursionSteps[resetLevel][2];
      resetLevel++;
    }
    if (res === 3) {
      this[RecursionSteps[doing][1]]++;
      this[RecursionSteps[doing][0]] = -RecursionSteps[doing][2];
      this.apply();
      return this.recurse(pattern, options, 0);
    } else if (this.apply()) {
      return this.recurse(pattern, options, doing - 1);
    }
  }
  doing += 1;
  if (doing >= RecursionSteps.length) {
    return this;
  } else if (this.year >= 3e3) {
    return null;
  } else {
    return this.recurse(pattern, options, doing);
  }
};
CronDate.prototype.increment = function(pattern, options, hasPreviousRun) {
  this.second += options.interval > 1 && hasPreviousRun ? options.interval : 1;
  this.ms = 0;
  this.apply();
  return this.recurse(pattern, options, 0);
};
CronDate.prototype.getDate = function(internal) {
  if (internal || this.tz === void 0) {
    return new Date(this.year, this.month, this.day, this.hour, this.minute, this.second, this.ms);
  } else {
    if (typeof this.tz === "number") {
      return new Date(Date.UTC(this.year, this.month, this.day, this.hour, this.minute - this.tz, this.second, this.ms));
    } else {
      return minitz(this.year, this.month + 1, this.day, this.hour, this.minute, this.second, this.tz);
    }
  }
};
CronDate.prototype.getTime = function() {
  return this.getDate().getTime();
};
function isFunction(v) {
  return Object.prototype.toString.call(v) === "[object Function]" || "function" === typeof v || v instanceof Function;
}
function unrefTimer(timer) {
  if (typeof Deno !== "undefined" && typeof Deno.unrefTimer !== "undefined") {
    Deno.unrefTimer(timer);
  } else if (timer && typeof timer.unref !== "undefined") {
    timer.unref();
  }
}
const maxDelay = 30 * 1e3;
const scheduledJobs = [];
function Cron(pattern, fnOrOptions1, fnOrOptions2) {
  if (!(this instanceof Cron)) {
    return new Cron(pattern, fnOrOptions1, fnOrOptions2);
  }
  let options, func;
  if (isFunction(fnOrOptions1)) {
    func = fnOrOptions1;
  } else if (typeof fnOrOptions1 === "object") {
    options = fnOrOptions1;
  } else if (fnOrOptions1 !== void 0) {
    throw new Error("Cron: Invalid argument passed for optionsIn. Should be one of function, or object (options).");
  }
  if (isFunction(fnOrOptions2)) {
    func = fnOrOptions2;
  } else if (typeof fnOrOptions2 === "object") {
    options = fnOrOptions2;
  } else if (fnOrOptions2 !== void 0) {
    throw new Error("Cron: Invalid argument passed for funcIn. Should be one of function, or object (options).");
  }
  this.name = options ? options.name : void 0;
  this.options = CronOptions(options);
  this._states = { kill: false, blocking: false, previousRun: void 0, currentRun: void 0, once: void 0, currentTimeout: void 0, maxRuns: options ? options.maxRuns : void 0, paused: options ? options.paused : false, pattern: void 0 };
  if (pattern && (pattern instanceof Date || typeof pattern === "string" && pattern.indexOf(":") > 0)) {
    this._states.once = new CronDate(pattern, this.options.timezone || this.options.utcOffset);
  } else {
    this._states.pattern = new CronPattern(pattern, this.options.timezone);
  }
  if (this.name) {
    const existing = scheduledJobs.find((j) => j.name === this.name);
    if (existing) {
      throw new Error("Cron: Tried to initialize new named job '" + this.name + "', but name already taken.");
    } else {
      scheduledJobs.push(this);
    }
  }
  if (func !== void 0) {
    this.fn = func;
    this.schedule();
  }
  return this;
}
Cron.prototype.nextRun = function(prev) {
  const next = this._next(prev);
  return next ? next.getDate() : null;
};
Cron.prototype.nextRuns = function(n, previous) {
  if (n > this._states.maxRuns) {
    n = this._states.maxRuns;
  }
  const enumeration = [];
  let prev = previous || this._states.currentRun;
  while (n-- && (prev = this.nextRun(prev))) {
    enumeration.push(prev);
  }
  return enumeration;
};
Cron.prototype.getPattern = function() {
  return this._states.pattern ? this._states.pattern.pattern : void 0;
};
Cron.prototype.isRunning = function() {
  const nextRunTime = this.nextRun(this._states.currentRun);
  const isRunning = !this._states.paused;
  const isScheduled = this.fn !== void 0;
  const notIsKilled = !this._states.kill;
  return isRunning && isScheduled && notIsKilled && nextRunTime !== null;
};
Cron.prototype.isStopped = function() {
  return this._states.kill;
};
Cron.prototype.isBusy = function() {
  return this._states.blocking;
};
Cron.prototype.currentRun = function() {
  return this._states.currentRun ? this._states.currentRun.getDate() : null;
};
Cron.prototype.previousRun = function() {
  return this._states.previousRun ? this._states.previousRun.getDate() : null;
};
Cron.prototype.msToNext = function(prev) {
  prev = prev || /* @__PURE__ */ new Date();
  const next = this._next(prev);
  if (next) {
    return next.getTime() - prev.getTime();
  } else {
    return null;
  }
};
Cron.prototype.stop = function() {
  this._states.kill = true;
  if (this._states.currentTimeout) {
    clearTimeout(this._states.currentTimeout);
  }
  const jobIndex = scheduledJobs.indexOf(this);
  if (jobIndex >= 0) {
    scheduledJobs.splice(jobIndex, 1);
  }
};
Cron.prototype.pause = function() {
  this._states.paused = true;
  return !this._states.kill;
};
Cron.prototype.resume = function() {
  this._states.paused = false;
  return !this._states.kill;
};
Cron.prototype.schedule = function(func) {
  if (func && this.fn) {
    throw new Error("Cron: It is not allowed to schedule two functions using the same Croner instance.");
  } else if (func) {
    this.fn = func;
  }
  let waitMs = this.msToNext();
  const target = this.nextRun(this._states.currentRun);
  if (waitMs === null || target === null)
    return this;
  if (waitMs > maxDelay) {
    waitMs = maxDelay;
  }
  this._states.currentTimeout = setTimeout(() => this._checkTrigger(target), waitMs);
  if (this._states.currentTimeout && this.options.unref) {
    unrefTimer(this._states.currentTimeout);
  }
  return this;
};
Cron.prototype._trigger = async function(initiationDate) {
  this._states.blocking = true;
  this._states.currentRun = new CronDate(void 0, this.options.timezone || this.options.utcOffset);
  if (this.options.catch) {
    try {
      await this.fn(this, this.options.context);
    } catch (_e) {
      if (isFunction(this.options.catch)) {
        this.options.catch(_e, this);
      }
    }
  } else {
    await this.fn(this, this.options.context);
  }
  this._states.previousRun = new CronDate(initiationDate, this.options.timezone || this.options.utcOffset);
  this._states.blocking = false;
};
Cron.prototype.trigger = async function() {
  await this._trigger();
};
Cron.prototype._checkTrigger = function(target) {
  const now = /* @__PURE__ */ new Date(), shouldRun = !this._states.paused && now.getTime() >= target, isBlocked = this._states.blocking && this.options.protect;
  if (shouldRun && !isBlocked) {
    this._states.maxRuns--;
    this._trigger();
  } else {
    if (shouldRun && isBlocked && isFunction(this.options.protect)) {
      setTimeout(() => this.options.protect(this), 0);
    }
  }
  this.schedule();
};
Cron.prototype._next = function(prev) {
  const hasPreviousRun = prev || this._states.currentRun ? true : false;
  prev = new CronDate(prev, this.options.timezone || this.options.utcOffset);
  if (this.options.startAt && prev && prev.getTime() < this.options.startAt.getTime()) {
    prev = this.options.startAt;
  }
  const nextRun = this._states.once || new CronDate(prev, this.options.timezone || this.options.utcOffset).increment(this._states.pattern, this.options, hasPreviousRun);
  if (this._states.once && this._states.once.getTime() <= prev.getTime()) {
    return null;
  } else if (nextRun === null || this._states.maxRuns <= 0 || this._states.kill || this.options.stopAt && nextRun.getTime() >= this.options.stopAt.getTime()) {
    return null;
  } else {
    return nextRun;
  }
};
Cron.Cron = Cron;
Cron.scheduledJobs = scheduledJobs;
const { parse: $parse, stringify: $stringify } = JSON;
const Primitive = String;
const primitive = "string";
const object = "object";
const noop = (_, value) => value;
const set = (known, input, value) => {
  const index = Primitive(input.push(value) - 1);
  known.set(value, index);
  return index;
};
const stringify = (value, replacer, space) => {
  const $ = replacer && typeof replacer === object ? (k, v) => k === "" || -1 < replacer.indexOf(k) ? v : void 0 : replacer || noop;
  const known = /* @__PURE__ */ new Map();
  const input = [];
  const output = [];
  let i = +set(known, input, $.call({ "": value }, "", value));
  let firstRun = !i;
  while (i < input.length) {
    firstRun = true;
    output[i] = $stringify(input[i++], replace, space);
  }
  return "[" + output.join(",") + "]";
  function replace(key, value2) {
    if (firstRun) {
      firstRun = !firstRun;
      return value2;
    }
    const after = $.call(this, key, value2);
    switch (typeof after) {
      case object:
        if (after === null)
          return after;
      case primitive:
        return known.get(after) || set(known, input, after);
    }
    return after;
  }
};
function isObject(value) {
  var type = typeof value;
  return value != null && (type == "object" || type == "function");
}
var isObject_1 = isObject;
const isObject$1 = /* @__PURE__ */ getDefaultExportFromCjs(isObject_1);
const name = "hl-black-board";
const version = "1.4.2";
const description = "An electron app from Hailiang";
const main$1 = "./out/main/index.js";
const author = "hailiangedu.com";
const homepage = "https://electron-vite.org";
const scripts = {
  format: "prettier --write .",
  lint: "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix",
  "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false",
  "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false",
  typecheck: "npm run typecheck:node && npm run typecheck:web",
  start: "electron-vite preview",
  dev: "electron-vite dev --watch --mode development",
  "dev:test": "cross-env NODE_ENV=test electron-vite dev --watch --mode test",
  "dev:win": "chcp 65001 && npm run dev:test",
  build: "npm run typecheck:web && electron-vite build --mode production",
  "build:dev": "npm run typecheck:web && cross-env NODE_ENV=development electron-vite build --mode development",
  "build:test": "npm run typecheck:web && cross-env NODE_ENV=test electron-vite build --mode test",
  "build:win": "npm run prebuild && npm run build && cross-env NODE_ENV=production electron-builder --win --config electron-builder.config.js",
  "build:win:dev": "cross-env BUILD_ENV=-dev npm run prebuild && npm run build:dev && cross-env NODE_ENV=development electron-builder --win --config electron-builder.config.js",
  "build:win:test": "cross-env BUILD_ENV=-test npm run prebuild && npm run build:test && cross-env NODE_ENV=test electron-builder --win --config electron-builder.config.js",
  "build:mac": "npm run prebuild && npm run build && cross-env NODE_ENV=production electron-builder --mac --config electron-builder.config.js",
  "build:mac:dev": "cross-env BUILD_ENV=-dev npm run prebuild && npm run build:dev && cross-env NODE_ENV=development electron-builder --mac --config electron-builder.config.js",
  "build:mac:test": "cross-env BUILD_ENV=-test npm run prebuild && npm run build:test && cross-env NODE_ENV=test electron-builder --mac --config electron-builder.config.js",
  "build:linux": "npm run build && electron-builder --linux",
  postinstall: "electron-builder install-app-deps",
  "build:unpack": "npm run build && electron-builder --dir",
  prebuild: "node scripts/set-app-name.js"
};
const dependencies = {
  "@electron-toolkit/preload": "^3.0.0",
  "@electron-toolkit/utils": "^4.0.0",
  "@element-plus/icons-vue": "^2.3.1",
  "@types/auto-launch": "^5.0.5",
  "@types/qrcode": "^1.5.5",
  "@vueuse/core": "^13.0.0",
  "ali-oss": "^6.23.0",
  "auto-launch": "^5.0.6",
  axios: "^1.7.9",
  croner: "^7.0.8",
  "cross-env": "^7.0.3",
  "crypto-js": "^4.2.0",
  "dom-to-image": "^2.6.0",
  dotenv: "^16.4.7",
  "electron-log": "^5.3.3",
  "electron-store": "^8.1.0",
  "electron-updater": "^6.6.2",
  "element-plus": "^2.9.6",
  encryptlong: "^3.1.4",
  "esdk-obs-browserjs": "^3.24.3",
  "esdk-obs-nodejs": "^3.24.3",
  eventemitter3: "^5.0.1",
  flatted: "^3.3.3",
  "grid-layout-plus": "^1.0.6",
  "hl-mqtt-client": "^1.0.1",
  "hl-syslog-client": "1.0.0-beta-16",
  icojs: "^0.17.1",
  jsmind: "^0.8.7",
  mime: "^3.0.0",
  nanoid: "^3.3.7",
  pinia: "^2.2.2",
  "pinia-plugin-persistedstate": "^3.2.3",
  "postcss-plugin-px2rem": "^0.8.1",
  qrcode: "^1.5.4",
  swiper: "^8.4.7",
  tyme4ts: "^1.2.2",
  uuid: "^7.0.3",
  "vue-draggable-resizable": "^3.0.0",
  "vue-router": "^4.4.5",
  winreg: "^1.2.5",
  xml2js: "^0.6.2",
  zeromq: "^6.4.2"
};
const devDependencies = {
  "@electron-toolkit/eslint-config": "^1.0.0",
  "@electron-toolkit/eslint-config-ts": "^2.0.0",
  "@electron-toolkit/tsconfig": "^1.0.1",
  "@hl/whiteboard": "1.2.1",
  "@rushstack/eslint-patch": "^1.10.3",
  "@types/better-sqlite3": "^7.6.12",
  "@types/node": "^20.14.8",
  "@vitejs/plugin-vue": "^4.6.2",
  "@vue/eslint-config-prettier": "^9.0.0",
  "@vue/eslint-config-typescript": "^12.0.0",
  electron: "^35.1.4",
  "electron-builder": "^24.13.3",
  "electron-devtools-installer": "^3.2.0",
  "electron-icon-builder": "^2.0.1",
  "electron-rebuild": "^3.2.9",
  "electron-vite": "^1.0.29",
  eslint: "^8.57.0",
  "eslint-plugin-vue": "^9.26.0",
  husky: "^8.0.3",
  less: "^4.2.0",
  prettier: "^3.3.2",
  "prettier-eslint": "^16.3.0",
  typescript: "^5.5.2",
  "unplugin-auto-import": "^0.18.0",
  "unplugin-vue-components": "^0.27.0",
  vite: "^4.5.5",
  "vite-plugin-html": "^3.2.2",
  "vite-plugin-vue-devtools": "^1.0.0-rc.8",
  vue: "^3.2.45",
  "vue-tsc": "^2.0.22"
};
const pgk = {
  name,
  version,
  description,
  main: main$1,
  author,
  homepage,
  scripts,
  dependencies,
  devDependencies
};
const MESSAGE_TYPE = {
  // ---------------- 通用 start ----------------
  // 打印console
  PRINT_LOG: "printLog",
  // 最小化
  MINIMIZE: "minimize",
  // 最大化
  MAXIMIZE: "maximize",
  // 关闭窗口
  EXIT_APP: "exit-app",
  // 获取设备MAC地址
  GET_DEVICE_MAC: "get-device-mac",
  // 获取主板序列号
  GET_MOTHERBOARD_SERIAL_NUMBER: "get-motherboard-serial-number",
  // 用默认浏览器打开网页
  OPEN_URL_IN_BROWSER: "open-url-in-browser",
  // 打开资源管理器
  OPEN_FILE_EXPLORE: "open-file-explore",
  // 获取设备UUID
  GET_DEVICE_UUID: "get-device-uuid",
  // 打开devtools
  OPEN_DEVTOOL: "open-devtool",
  // ---------------- 通用 end ----------------
  // ---------------- 系统 start ----------------
  // 获取已安装的软件信息
  GET_INSTALLED_SOFTWARE_INFO: "get-installed-software-info",
  // 运行软件
  RUN_APP: "run-app",
  // 启动AI课堂顾问
  START_AI_CLASSROOM_ADVISOR: "start-ai-classroom-advisor",
  // 调试查找AI课堂顾问
  DEBUG_FIND_AI_CLASSROOM_ADVISOR: "debug-find-ai-classroom-advisor",
  // 打开系统网络设置
  OPEN_SYSTEM_NETWORK: "open-system-network",
  // 调用插件
  INVOKE_ADDON: "invoke-addon",
  // 请求摄像头权限
  REQUEST_MEDIA_CAMERA: "request-media-camera",
  // 请求麦克风权限
  REQUEST_MEDIA_MICROPHONE: "request-media-microphone",
  // ---------------- 系统 end ----------------
  // ---------------- 更新 start ----------------
  // 是否为不受支持的版本
  IS_UNSUPPORTED_VERSION: "is-unsupported-version",
  // 检查更新
  CHECK_UPDATE_AND_NOTIFY: "check-update-and-notify",
  // 安装更新
  QUIT_AND_INSTALL: "quit-and-install",
  // ---------------- 更新 end ----------------
  // ---------------- 持久化存储 start ----------------
  STORE_SET: "store-set",
  STORE_GET: "store-get",
  STORE_DELETE: "store-delete",
  STORE_ONCE: "store-once",
  STORE_GET_ALL: "store-get-all",
  // ---------------- 持久化存储 end ----------------
  // ---------------- 日志 start ----------------
  LOG_INFO: "log-info",
  LOG_WARN: "log-warn",
  LOG_ERROR: "log-error",
  LOG_DEBUG: "log-debug",
  LOG_VERBOSE: "log-verbose",
  LOG_SILLY: "log-silly",
  // 获取日志文件路径
  LOG_GET_LOG_FILE_PATH: "log-get-log-file-path",
  // 上传日志
  UPLOAD_LOG: "upload-log",
  // 修改日志层级
  LOG_CHANGE_LEVEL: "log-change-level",
  CHANGE_BIND_SCHOOL: "change-bind-school",
  // ---------------- 日志 end ----------------
  // ---------------- 主窗口 start ----------------
  // 窗口最大化
  WIN_MAXIMIZE: "win-maximize",
  // 窗口已最大化
  WIN_MAXIMIZED: "win-maximized",
  // 窗口最小化
  WIN_MINIMIZE: "win-minimize",
  // 窗口已最小化
  WIN_MINIMIZED: "win-minimized",
  // 窗口已恢复大小
  WIN_UNMAXIMIZED: "win-unmaximized",
  // 关闭主窗口
  WIN_SHOW_OR_CLOSE: "win-show-or-close",
  // 切换窗口显示/隐藏
  WIN_TOGGLE_VISIBILITY: "win-toggle-visibility",
  // 窗口恢复显示
  WIN_STORE: "win-restore",
  // ---------------- 主窗口 end ----------------
  // ---------------- 折叠窗口 start ----------------
  // 折叠窗口非内容区域鼠标穿透
  FOLD_WIN_IGNORE_MOUSE: "fold-win-ignore-mouse",
  // 折叠窗口显示或隐藏
  FOLD_WIN_SHOW_OR_CLOSE: "fold-win-show-or-close",
  // 折叠窗口移动
  FOLD_WIN_MOVE: "fold-win-move",
  // 折叠窗口获取窗口位置
  FOLD_GET_WIN_POSITION: "fold-get-win-position",
  // 折叠窗口设置窗口位置
  FOLD_SET_WIN_POSITION: "fold-set-win-position",
  // 获取屏幕信息
  GET_SCREEN_INFO: "get-screen-info",
  FOLD_WIN_RESIZE: "fold-win-resize",
  // ---------------- 折叠窗口 end ----------------
  // ---------------- 关于自动更新通讯 start ----------------
  UPDATE_CHECK: "updateCheck",
  // 检查更新
  UPDATE_STATUS: "updateStatus",
  // 更新状态响应到主窗口
  UPDATE_CHECKING: "updateChecking",
  // 开始检测更新事件
  UPDATE_AVAILABLE: "updateAvailable",
  // 有可用更新
  UPDATE_LATEST: "updateLatest",
  // 当前为最新版本，无可用更新
  UPDATE_DOWNLOAD_START: "updateDownloadStart",
  // 开始下载更新
  UPDATE_DOWNLOAD_PROGRESS: "updateDownloadProgress",
  // 更新进度
  UPDATE_DOWNLOAD_END: "updateDownloadEnd",
  // 下载结束
  UPDATE_ERROR: "updateError",
  // 更新出错
  UPDATE_INSTALL_APP: "updateInstallApp",
  // 安装新版本
  // ---------------- 关于自动更新通讯 end ----------------
  GET_APP_PATH: "getAPPPath",
  // 获取APP安装地址
  GET_APP_VERSION: "getAPPVersion",
  // 获取APP版本号
  START_RECORDER: "startRecorderUpload",
  RESIZE_SIZE: "resizeSize",
  // 重置窗口大小至底部
  MOVE_WINDOW: "moveWindow",
  // 移动窗口'
  RECOVERY_SIZE: "recoverySize",
  // 恢复原生大小
  // ---------------- 主副窗口之间 start ----------------
  OPEN_FLOAT_WINDOW: "openFloatWindow",
  // 打开悬浮窗
  SET_IGNORE_MOUSE_EVENTS: "set-ignore-mouse-events",
  // 设置鼠标穿透
  CLOSE_FLOAT_WINDOW: "closeFloatWindow",
  // 关闭悬浮窗
  // ---------------- 主副窗口之间 end ------------------
  // ---------------- 文件类工具方法 start ----------------
  SAVE_FILE: "SAVE_FILE",
  // 保存文件
  SCREENSHOT_FULL_SCREEN: "screenshotFullScreen",
  // 截图全屏
  // ---------------- 文件类工具方法 end ------------------
  // ---------------- zmq start ----------------
  ZMQ_CONNECT: "zmqConnect",
  // 连接zmq
  ZMQ_CONNECTED: "zmqConnected",
  // 连接成功
  ZMQ_SEND_REQUEST: "callQt",
  // 发送消息
  ZMQ_REGISTER_HANDLER: "zmqRegisterHandler",
  // 订阅消息
  ZMQ_JS_CALLBACK: "zmqJsCallback",
  // 订阅消息回调
  // ---------------- zeromq end ----------------
  ZMQ_QUERY_CONNECT_STATUS: "zmqQueryConnectStatus",
  // 查询连接状态
  // ---------------- zeromq end ----------------
  // ---------------- AI课堂顾问 start ----------------
  AI_CLASSROOM_ADVISOR_UPDATE_SEND_REQUEST: "call-ai-classroom-advisor"
  // 更新基础信息
  // ---------------- AI课堂顾问 end ----------------
};
const ZMQ_MESSAGE_TYPE = {
  ...MESSAGE_TYPE,
  /** 调用ElectronRenderer 的js方法 */
  CALL_ELECTRON_RENDERER: "callElectronRenderer",
  /** 调用Electron的API */
  CALL_ELECTRON: "callElectron",
  /** 调用qt的cef方法 */
  CALL_QT_CEF: "callQtCef"
};
const ZMQ_ENDPOINT_ENUM = {
  /** qt白板应用 */
  HL_WHITE_BOARD: "hl-white-board",
  /** AI课堂顾问 */
  AI_CLASSROOM_ADVISOR: "hr-ai"
};
var getRandomValues = typeof crypto != "undefined" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto != "undefined" && typeof msCrypto.getRandomValues == "function" && msCrypto.getRandomValues.bind(msCrypto);
var rnds8 = new Uint8Array(16);
function rng() {
  if (!getRandomValues) {
    throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
  }
  return getRandomValues(rnds8);
}
var byteToHex = [];
for (var i = 0; i < 256; ++i) {
  byteToHex[i] = (i + 256).toString(16).substr(1);
}
function bytesToUuid(buf, offset) {
  var i = offset || 0;
  var bth = byteToHex;
  return [bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], "-", bth[buf[i++]], bth[buf[i++]], "-", bth[buf[i++]], bth[buf[i++]], "-", bth[buf[i++]], bth[buf[i++]], "-", bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]], bth[buf[i++]]].join("");
}
function v4(options, buf, offset) {
  var i = buf && offset || 0;
  if (typeof options == "string") {
    buf = options === "binary" ? new Array(16) : null;
    options = null;
  }
  options = options || {};
  var rnds = options.random || (options.rng || rng)();
  rnds[6] = rnds[6] & 15 | 64;
  rnds[8] = rnds[8] & 63 | 128;
  if (buf) {
    for (var ii = 0; ii < 16; ++ii) {
      buf[i + ii] = rnds[ii];
    }
  }
  return buf || bytesToUuid(rnds);
}
class BridgeError extends Error {
  constructor(message, code) {
    super(message);
    this.code = code;
    this.name = "BridgeError";
  }
}
const ErrorCodes = {
  BRIDGE_NOT_INITIALIZED: "BRIDGE_NOT_INITIALIZED",
  // 桥接未初始化
  INVALID_METHOD: "INVALID_METHOD",
  // 无效方法
  INVALID_DATA: "INVALID_DATA",
  // 无效数据
  TIMEOUT: "TIMEOUT",
  // 超时
  RESPONSE_ERROR: "RESPONSE_ERROR",
  // 响应错误
  EVENT_ERROR: "EVENT_ERROR"
  // 事件错误
};
const _Bridge = class _Bridge2 {
  /**
   * 私有构造函数，防止外部直接实例化
   */
  constructor() {
    this.bridge = null;
    this.activeQueries = /* @__PURE__ */ new Map();
    this.initializeBridge();
  }
  /**
   * 静态方法，用于创建响应
   * @param id 请求id
   * @param data 响应数据
   * @param status 响应状态
   * @returns 错误响应消息对象
   */
  static newResponse(data, status = "200", req) {
    return {
      id: req?.id || v4(),
      method: "__response__",
      data,
      status,
      success: status === "200",
      requestMethod: req?.method
    };
  }
  /**
   * 创建错误响应
   * @param id 请求id
   * @param error 错误信息
   * @returns 错误响应消息对象
   */
  static newErrorResponse(error, req) {
    return _Bridge2.newResponse(error, "ERROR", req);
  }
  /**
   * 安全序列化
   * @param input 字符串
   * @returns
   */
  static safeParse(input) {
    try {
      const parsed = JSON.parse(input);
      if (parsed.status) {
        parsed.success = parsed.status === "200";
      }
      return parsed;
    } catch (e) {
      return {
        id: "parse-error",
        method: "__parse_error__",
        status: "ERROR",
        data: e?.message || e || "未知异常",
        success: false
      };
    }
  }
  /**
   * 初始化桥接对象
   */
  initializeBridge() {
    try {
      this.bridge = window.CallBridge;
      if (!this.bridge) {
        throw new BridgeError(
          "未找到 QCefView 桥接对象。请确保桥接对象名称设置正确。",
          ErrorCodes.BRIDGE_NOT_INITIALIZED
        );
      }
    } catch (error) {
      logger.error("初始化桥接对象失败:", error);
    }
  }
  /**
   * 获取 Bridge 单例实例
   * @returns Bridge 单例实例
   */
  static getInstance() {
    if (!_Bridge2.instance) {
      _Bridge2.instance = new _Bridge2();
    }
    return _Bridge2.instance;
  }
  /**
   * 检查桥接对象是否可用
   * @throws BridgeError 如果桥接对象未初始化
   */
  checkBridge() {
    if (!this.bridge) {
      throw new BridgeError(
        "桥接对象未初始化，请确保 QCefView 已正确加载",
        ErrorCodes.BRIDGE_NOT_INITIALIZED
      );
    }
  }
  /**
   * 验证方法名
   * @param method - 要验证的方法名
   * @throws BridgeError 如果方法名无效
   */
  validateMethod(method) {
    if (!method || typeof method !== "string" || method.trim().length === 0) {
      throw new BridgeError("无效的方法名", ErrorCodes.INVALID_METHOD);
    }
  }
  /**
   * 调用有返回值的原生方法
   * @param method - 要调用的方法名
   * @param data - 传递给方法的数据
   * @param timeout - 超时时间（毫秒），默认为 DEFAULT_TIMEOUT
   * @returns Promise<T> - 包含原生方法返回值的 Promise
   * @template T - 返回值的类型
   * @throws BridgeError 如果桥接未初始化、方法名或数据无效、调用超时或响应错误
   */
  call(method, data, options) {
    try {
      this.checkBridge();
      this.validateMethod(method);
      if (!this.bridge)
        return Promise.reject(new Error("Bridge not initialized"));
      return new Promise((resolve, reject) => {
        const { timeout = _Bridge2.DEFAULT_TIMEOUT } = options || {};
        const request = {
          method,
          data,
          id: v4()
        };
        const timeoutId = setTimeout(() => {
          try {
            window.cefViewQueryCancel(cefViewQueryId);
            this.activeQueries.delete(request.id);
          } finally {
            reject(new BridgeError("桥接调用超时", ErrorCodes.TIMEOUT));
          }
        }, timeout);
        this.activeQueries.set(request.id, timeoutId);
        const cefViewQueryId = window.cefViewQuery({
          request: JSON.stringify(request),
          onSuccess: (response) => {
            clearTimeout(this.activeQueries.get(request.id));
            this.activeQueries.delete(request.id);
            const data2 = _Bridge2.safeParse(response);
            if (data2.success) {
              resolve(data2.data);
            } else {
              reject(new BridgeError(data2.data || "未知异常", ErrorCodes.RESPONSE_ERROR));
              logger.warn("【JSBridge】", "call失败", data2, request);
            }
          },
          onFailure: (error) => {
            clearTimeout(this.activeQueries.get(request.id));
            this.activeQueries.delete(request.id);
            reject(
              new BridgeError(error?.message || error || "未知异常", ErrorCodes.RESPONSE_ERROR)
            );
            logger.error("【JSBridge】", "call异常", error, request);
          }
        });
      });
    } catch (error) {
      logger.error("【JSBridge】", "call异常:", error, method, data);
      if (error instanceof BridgeError) {
        throw error;
      }
      throw new BridgeError("call异常", ErrorCodes.RESPONSE_ERROR);
    }
  }
  /**
   * 调用无返回值的原生方法
   * @param method - 要调用的方法名
   * @param [data] 调用参数
   * @param [id] 响应id
   * @returns Promise<void> - 表示方法调用完成的 Promise
   * @throws BridgeError 如果桥接未初始化、方法名或数据无效、或调用失败
   */
  callVoid(method, data, id) {
    try {
      this.checkBridge();
      this.validateMethod(method);
      if (!this.bridge)
        return Promise.reject(new Error("Bridge not initialized"));
      return new Promise((resolve, reject) => {
        const request = JSON.stringify({
          id: id || v4(),
          method,
          data: data || {}
        });
        try {
          this.bridge.invoke(
            method,
            request
          );
          resolve();
        } catch (error) {
          reject(new BridgeError("callVoid异常", ErrorCodes.RESPONSE_ERROR));
          logger.error("【JSBridge】", "callVoid异常:", error, request);
        }
      });
    } catch (error) {
      logger.error("【JSBridge】", "callVoid异常:", error, method, data);
      if (error instanceof BridgeError) {
        throw error;
      }
      throw new BridgeError("callVoid异常", ErrorCodes.RESPONSE_ERROR);
    }
  }
  /**
   * 添加事件监听器
   * @param eventName - 事件名称
   * @param callback - 事件回调函数
   */
  on(eventName, callback) {
    try {
      this.checkBridge();
      this.bridge?.addEventListener(eventName, async (...args) => {
        try {
          args[0] = JSON.parse(args[0]);
        } catch (e) {
          args[0] = {
            result: args[0]
          };
          logger.warn("【bridge】", "json序列化失败", e);
        }
        try {
          const result = await callback.apply(this, args);
          this.callVoid("__js_callback__", result, args[0]?.id);
        } catch (e) {
          logger.warn("【bridge】", "事件回调失败", e);
          this.callVoid(
            "__js_callback__",
            {
              success: false,
              message: e.message
            },
            args[0]?.id
          );
        }
      });
    } catch (error) {
      logger.error(`添加事件监听器失败: ${eventName}`, error);
    }
  }
  /**
   * 移除事件监听器
   * @param eventName - 事件名称
   * @param callback - 事件回调函数
   */
  off(eventName, callback) {
    try {
      this.checkBridge();
      this.bridge?.removeEventListener(eventName, callback);
    } catch (error) {
      logger.error(`移除事件监听器失败: ${eventName}`, error);
    }
  }
};
_Bridge.instance = null;
_Bridge.DEFAULT_TIMEOUT = 3e4;
let Bridge = _Bridge;
class BridgeZmqUtils {
  /**
   * 调用Electron的渲染进程的js方法
   * @param rendererName 渲染窗口名称
   * @param rendererMethod 请求方法
   * @param rendererData 请求数据
   * @param identity 应用标识
   */
  static callEelectronRenderer(rendererMethod, rendererData = {}, identity = "black-board-client", rendererName = "main") {
    const bridge = Bridge.getInstance();
    return bridge.call(ZMQ_MESSAGE_TYPE.CALL_ELECTRON_RENDERER, {
      identity,
      rendererName,
      rendererMethod,
      rendererData
    });
  }
  /**
   * 调用Electron的API
   * @param electronMethod 请求方法
   * @param electronData 请求数据
   * @param identity 应用标识
   * @returns
   */
  static callEelectron(electronMethod, electronData = {}, options) {
    const bridge = Bridge.getInstance();
    return bridge.call(
      ZMQ_MESSAGE_TYPE.CALL_ELECTRON,
      {
        identity: options?.identity || "black-board-client",
        electronMethod,
        electronData
      },
      options
    );
  }
}
function extractStackInfo(stackLine) {
  const regex = /at\s+(\S+)\s+\((.*):(\d+):(\d+)\)/;
  const match = stackLine.match(regex);
  if (match) {
    const methodName = match[1];
    const line = match[3];
    const column = match[4];
    const arr = match[2].split("/");
    const fileName = arr[arr.length - 1];
    return `${methodName} (${fileName}, line: ${line}, column: ${column} ${line}:${column})`;
  } else {
    return stackLine;
  }
}
function processStack(stack) {
  const stackArr = stack.split("\n");
  const formattedStack = extractStackInfo(stackArr[2]);
  return formattedStack;
}
function reportSls(type, args) {
  const _track = window._track;
  if (!_track) {
    console.warn("【log】_track未定义");
    return;
  }
  let eventInfo = {
    message: ""
  };
  const firstArg = args[0];
  if (args.length === 1) {
    if (isObject$1(firstArg)) {
      eventInfo = firstArg;
    } else {
      eventInfo = {
        message: firstArg
      };
    }
  } else {
    eventInfo = {
      message: args.map((item) => {
        if (isObject$1(item)) {
          return stringify(item).replace(/(^\[|\]$)/g, "");
        } else {
          return item;
        }
      }).join(" ")
    };
  }
  _track.sendTrackLog(eventInfo, type);
}
function electronPrint(type, args) {
  const electronLog = window.electronLog;
  if (electronLog) {
    const stack = new Error().stack;
    const stackInfo = processStack(stack);
    electronLog[type](...args, stackInfo);
  } else {
    console[type](...args);
  }
}
const logger = {
  debug(...args) {
    try {
      electronPrint("debug", args);
    } catch (error) {
      console.warn("【log】日志打印失败", error, ...args);
    }
  },
  info(...args) {
    try {
      reportSls("INFO", args);
      electronPrint("info", args);
    } catch (error) {
      console.warn("【log】日志打印失败", error, ...args);
    }
  },
  warn(...args) {
    try {
      reportSls("WARN", args);
      electronPrint("warn", args);
    } catch (error) {
      console.warn("【log】日志打印失败", error, ...args);
    }
  },
  error(...args) {
    try {
      reportSls("ERROR", args);
      const electronLog = window.electronLog;
      if (electronLog) {
        const processedArgs = args.map((item) => {
          if (item instanceof Error) {
            return serializeError(item);
          } else {
            return item;
          }
        });
        const prefix = "❗️❗️❗️";
        electronLog.error(prefix, ...processedArgs);
      } else {
        console.error("❗️❗️❗️", ...args);
      }
    } catch (error) {
      console.warn("【log】日志打印失败", error, ...args);
    }
  }
};
let cacheDeviceId;
async function getDeviceId() {
  try {
    if (cacheDeviceId) {
      return cacheDeviceId;
    }
    const res = await window.api.getDeviceId();
    if (!Array.isArray(res) || res.length <= 0) {
      console.warn("【log】初始化日志失败,【log】获取设备号失败，序列号为空");
      return null;
    }
    cacheDeviceId = res[0].deviceId;
    return cacheDeviceId;
  } catch (e) {
    console.warn("【log】初始化日志失败,设备号获取失败", e);
  }
}
async function initLogger() {
  try {
    const _track = window._track;
    if (!_track) {
      console.warn("【log】_track未定义");
      return;
    }
    const deviceId = await getDeviceId();
    if (!deviceId) {
      return;
    }
    _track.setBusinessInfo({
      deviceId
    });
    logger.debug(`【log】初始化日志成功`, deviceId);
  } catch (e) {
    console.warn("【log】初始化日志失败", e);
  }
}
async function getDeviceIdWidget() {
  try {
    if (cacheDeviceId) {
      return cacheDeviceId;
    }
    const res = await BridgeZmqUtils.callEelectron(
      ZMQ_MESSAGE_TYPE.GET_DEVICE_UUID,
      {},
      {
        timeout: 5e3
      }
    );
    const deviceId = res.deviceId;
    if (deviceId) {
      cacheDeviceId = deviceId;
    } else {
      throw "获取的设备号为空";
    }
    return cacheDeviceId;
  } catch (e) {
    console.warn("【log】初始化日志失败,设备号获取失败", e);
  }
}
async function initLoggerWidget() {
  try {
    const _track = window._track;
    if (!_track) {
      console.warn("【log】_track未定义");
      return;
    }
    const deviceId = await getDeviceIdWidget();
    if (!deviceId) {
      return;
    }
    _track.setBusinessInfo({
      deviceId
    });
    logger.debug(`【log】初始化日志成功`);
  } catch (e) {
    console.warn("【log】初始化日志失败", e);
  }
}
async function startHeartbeat() {
  try {
    logger.info(`【心跳】版本号：${pgk.version},时间：${(/* @__PURE__ */ new Date()).toLocaleString()} `);
    Cron("*/15 * * * *", () => {
      logger.info(`【心跳】版本号：${pgk.version},时间：${(/* @__PURE__ */ new Date()).toLocaleString()} `);
    });
  } catch (e) {
    console.warn("【log】开启心跳失败", e);
  }
}
async function setBusinessInfo(businessInfo) {
  try {
    const _track = window._track;
    if (!_track) {
      console.warn("【log】_track未定义");
      return;
    }
    const deviceId = await getDeviceId();
    if (!deviceId) {
      return;
    }
    _track.setBusinessInfo({
      ...businessInfo,
      deviceId
    });
    logger.debug("【log】初始化成功");
  } catch (e) {
    console.warn("【log】初始化日志失败", e);
  }
}
async function setBusinessInfoWidget(businessInfo) {
  try {
    const _track = window._track;
    if (!_track) {
      console.warn("【log】_track未定义");
      return;
    }
    const deviceId = await getDeviceIdWidget();
    if (!deviceId) {
      return;
    }
    _track.setBusinessInfo({
      ...businessInfo,
      deviceId
    });
    logger.debug("【log】初始化成功");
  } catch (e) {
    console.warn("【log】初始化日志失败", e);
  }
}
function reportTrackEvent(code, eventInfo, businessInfo) {
  try {
    const _track = window._track;
    if (!_track) {
      console.warn("【log】_track未定义");
      return;
    }
    _track.sendTrackEvent({ code, eventInfo, businessInfo });
    logger.debug("【log】业务埋点上报成功", code, eventInfo);
  } catch (e) {
    console.warn("【log】业务埋点上报失败", e, code, eventInfo);
  }
}
class WarnError extends Error {
  constructor(message, err) {
    super(message);
    if (err) {
      this.err = err;
    }
  }
}
const main = "";
const _export_sfc = (sfc, props) => {
  const target = sfc.__vccOpts || sfc;
  for (const [key, val] of props) {
    target[key] = val;
  }
  return target;
};
const pinia = createPinia();
pinia.use(src_default);
export {
  BridgeZmqUtils as B,
  Cron as C,
  MESSAGE_TYPE as M,
  WarnError as W,
  ZMQ_MESSAGE_TYPE as Z,
  _export_sfc as _,
  Bridge as a,
  blobToBase64 as b,
  setBusinessInfoWidget as c,
  initLoggerWidget as d,
  setBusinessInfo as e,
  px2rem as f,
  getDeviceIdWidget as g,
  getWindowUrlParams as h,
  initLogger as i,
  arrayBufferToBase64 as j,
  ZMQ_ENDPOINT_ENUM as k,
  logger as l,
  pinia as p,
  reportTrackEvent as r,
  startHeartbeat as s,
  v4 as v
};
