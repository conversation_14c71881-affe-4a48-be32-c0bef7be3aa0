import { d as buildProps, e as definePropType, u as useNamespace, f as addUnit, j as useLocale, E as ElIcon, _ as _export_sfc, a5 as hasClass, o as addClass, J as getStyle, r as removeClass, N as defaultNamespace, w as withInstall } from "./base.676dddc3.js";
import { u as useDeprecated, E as ElButton } from "./el-button.a9e8e4ae.js";
import { aj as NOOP, d as defineComponent, p as createVNode, B as renderSlot, ai as h, z as onMounted, M as watchEffect, o as onBeforeUnmount, i as isFunction, a as inject, c as computed, b as openBlock, m as createElementBlock, j as createBaseVNode, k as normalizeClass, u as unref, H as toDisplayString, f as withCtx, e as createBlock, C as resolveDynamicComponent, D as createCommentVNode, l as normalizeStyle, a7 as isRef, w as watch, ab as onScopeDispose, r as ref, n as nextTick, g as getCurrentInstance, ae as useSlots, q as provide, T as Transition, h as withDirectives, _ as mergeProps, az as createSlots, v as vShow, G as createTextVNode, a2 as storeToRefs, F as Fragment, R as renderList } from "./bootstrap.ab073eb8.js";
import { _ as _export_sfc$1 } from "./index.5f541e77.js";
import { _ as __unplugin_components_0, S as SummaryTypeEnum } from "./aiSummary.f2c3b92e.js";
import { a as useClassroomStore, u as useDeviceStore } from "./classroom.6507dbe9.js";
import { S as SvgIcon } from "./index.24eb8257.js";
import { F as FOCUS_TRAP_INJECTION_KEY, c as getScrollBarWidth, a as ElFocusTrap, b as ElTeleport } from "./index.4e3d2b08.js";
import { i as iconPropType, C as CloseComponents, a as useId, n as useGlobalConfig } from "./use-form-common-props.6b0d7cd2.js";
import { U as UPDATE_MODEL_EVENT, t as throwError } from "./event.183fce42.js";
import { d as isBoolean, i as isClient, h as useZIndex, u as useTimeoutFn } from "./index.1c1fd1ce.js";
import { i as isUndefined } from "./isUndefined.a6a5e481.js";
var PatchFlags = /* @__PURE__ */ ((PatchFlags2) => {
  PatchFlags2[PatchFlags2["TEXT"] = 1] = "TEXT";
  PatchFlags2[PatchFlags2["CLASS"] = 2] = "CLASS";
  PatchFlags2[PatchFlags2["STYLE"] = 4] = "STYLE";
  PatchFlags2[PatchFlags2["PROPS"] = 8] = "PROPS";
  PatchFlags2[PatchFlags2["FULL_PROPS"] = 16] = "FULL_PROPS";
  PatchFlags2[PatchFlags2["HYDRATE_EVENTS"] = 32] = "HYDRATE_EVENTS";
  PatchFlags2[PatchFlags2["STABLE_FRAGMENT"] = 64] = "STABLE_FRAGMENT";
  PatchFlags2[PatchFlags2["KEYED_FRAGMENT"] = 128] = "KEYED_FRAGMENT";
  PatchFlags2[PatchFlags2["UNKEYED_FRAGMENT"] = 256] = "UNKEYED_FRAGMENT";
  PatchFlags2[PatchFlags2["NEED_PATCH"] = 512] = "NEED_PATCH";
  PatchFlags2[PatchFlags2["DYNAMIC_SLOTS"] = 1024] = "DYNAMIC_SLOTS";
  PatchFlags2[PatchFlags2["HOISTED"] = -1] = "HOISTED";
  PatchFlags2[PatchFlags2["BAIL"] = -2] = "BAIL";
  return PatchFlags2;
})(PatchFlags || {});
const useSameTarget = (handleClick) => {
  if (!handleClick) {
    return { onClick: NOOP, onMousedown: NOOP, onMouseup: NOOP };
  }
  let mousedownTarget = false;
  let mouseupTarget = false;
  const onClick = (e) => {
    if (mousedownTarget && mouseupTarget) {
      handleClick(e);
    }
    mousedownTarget = mouseupTarget = false;
  };
  const onMousedown = (e) => {
    mousedownTarget = e.target === e.currentTarget;
  };
  const onMouseup = (e) => {
    mouseupTarget = e.target === e.currentTarget;
  };
  return { onClick, onMousedown, onMouseup };
};
const overlayProps = buildProps({
  mask: {
    type: Boolean,
    default: true
  },
  customMaskEvent: Boolean,
  overlayClass: {
    type: definePropType([
      String,
      Array,
      Object
    ])
  },
  zIndex: {
    type: definePropType([String, Number])
  }
});
const overlayEmits = {
  click: (evt) => evt instanceof MouseEvent
};
const BLOCK = "overlay";
var Overlay = defineComponent({
  name: "ElOverlay",
  props: overlayProps,
  emits: overlayEmits,
  setup(props, { slots, emit }) {
    const ns = useNamespace(BLOCK);
    const onMaskClick = (e) => {
      emit("click", e);
    };
    const { onClick, onMousedown, onMouseup } = useSameTarget(props.customMaskEvent ? void 0 : onMaskClick);
    return () => {
      return props.mask ? createVNode("div", {
        class: [ns.b(), props.overlayClass],
        style: {
          zIndex: props.zIndex
        },
        onClick,
        onMousedown,
        onMouseup
      }, [renderSlot(slots, "default")], PatchFlags.STYLE | PatchFlags.CLASS | PatchFlags.PROPS, ["onClick", "onMouseup", "onMousedown"]) : h("div", {
        class: props.overlayClass,
        style: {
          zIndex: props.zIndex,
          position: "fixed",
          top: "0px",
          right: "0px",
          bottom: "0px",
          left: "0px"
        }
      }, [renderSlot(slots, "default")]);
    };
  }
});
const ElOverlay = Overlay;
const dialogInjectionKey = Symbol("dialogInjectionKey");
const dialogContentProps = buildProps({
  center: Boolean,
  alignCenter: Boolean,
  closeIcon: {
    type: iconPropType
  },
  draggable: Boolean,
  overflow: Boolean,
  fullscreen: Boolean,
  headerClass: String,
  bodyClass: String,
  footerClass: String,
  showClose: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ""
  },
  ariaLevel: {
    type: String,
    default: "2"
  }
});
const dialogContentEmits = {
  close: () => true
};
const useDraggable = (targetRef, dragRef, draggable, overflow) => {
  let transform = {
    offsetX: 0,
    offsetY: 0
  };
  const onMousedown = (e) => {
    const downX = e.clientX;
    const downY = e.clientY;
    const { offsetX, offsetY } = transform;
    const targetRect = targetRef.value.getBoundingClientRect();
    const targetLeft = targetRect.left;
    const targetTop = targetRect.top;
    const targetWidth = targetRect.width;
    const targetHeight = targetRect.height;
    const clientWidth = document.documentElement.clientWidth;
    const clientHeight = document.documentElement.clientHeight;
    const minLeft = -targetLeft + offsetX;
    const minTop = -targetTop + offsetY;
    const maxLeft = clientWidth - targetLeft - targetWidth + offsetX;
    const maxTop = clientHeight - targetTop - targetHeight + offsetY;
    const onMousemove = (e2) => {
      let moveX = offsetX + e2.clientX - downX;
      let moveY = offsetY + e2.clientY - downY;
      if (!(overflow == null ? void 0 : overflow.value)) {
        moveX = Math.min(Math.max(moveX, minLeft), maxLeft);
        moveY = Math.min(Math.max(moveY, minTop), maxTop);
      }
      transform = {
        offsetX: moveX,
        offsetY: moveY
      };
      if (targetRef.value) {
        targetRef.value.style.transform = `translate(${addUnit(moveX)}, ${addUnit(moveY)})`;
      }
    };
    const onMouseup = () => {
      document.removeEventListener("mousemove", onMousemove);
      document.removeEventListener("mouseup", onMouseup);
    };
    document.addEventListener("mousemove", onMousemove);
    document.addEventListener("mouseup", onMouseup);
  };
  const onDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.addEventListener("mousedown", onMousedown);
    }
  };
  const offDraggable = () => {
    if (dragRef.value && targetRef.value) {
      dragRef.value.removeEventListener("mousedown", onMousedown);
    }
  };
  const resetPosition = () => {
    transform = {
      offsetX: 0,
      offsetY: 0
    };
    if (targetRef.value) {
      targetRef.value.style.transform = "none";
    }
  };
  onMounted(() => {
    watchEffect(() => {
      if (draggable.value) {
        onDraggable();
      } else {
        offDraggable();
      }
    });
  });
  onBeforeUnmount(() => {
    offDraggable();
  });
  return {
    resetPosition
  };
};
const composeRefs = (...refs) => {
  return (el) => {
    refs.forEach((ref2) => {
      if (isFunction(ref2)) {
        ref2(el);
      } else {
        ref2.value = el;
      }
    });
  };
};
const __default__$1 = defineComponent({ name: "ElDialogContent" });
const _sfc_main$7 = /* @__PURE__ */ defineComponent({
  ...__default__$1,
  props: dialogContentProps,
  emits: dialogContentEmits,
  setup(__props, { expose }) {
    const props = __props;
    const { t } = useLocale();
    const { Close } = CloseComponents;
    const { dialogRef, headerRef, bodyId, ns, style } = inject(dialogInjectionKey);
    const { focusTrapRef } = inject(FOCUS_TRAP_INJECTION_KEY);
    const dialogKls = computed(() => [
      ns.b(),
      ns.is("fullscreen", props.fullscreen),
      ns.is("draggable", props.draggable),
      ns.is("align-center", props.alignCenter),
      { [ns.m("center")]: props.center }
    ]);
    const composedDialogRef = composeRefs(focusTrapRef, dialogRef);
    const draggable = computed(() => props.draggable);
    const overflow = computed(() => props.overflow);
    const { resetPosition } = useDraggable(dialogRef, headerRef, draggable, overflow);
    expose({
      resetPosition
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", {
        ref: unref(composedDialogRef),
        class: normalizeClass(unref(dialogKls)),
        style: normalizeStyle(unref(style)),
        tabindex: "-1"
      }, [
        createBaseVNode("header", {
          ref_key: "headerRef",
          ref: headerRef,
          class: normalizeClass([unref(ns).e("header"), _ctx.headerClass, { "show-close": _ctx.showClose }])
        }, [
          renderSlot(_ctx.$slots, "header", {}, () => [
            createBaseVNode("span", {
              role: "heading",
              "aria-level": _ctx.ariaLevel,
              class: normalizeClass(unref(ns).e("title"))
            }, toDisplayString(_ctx.title), 11, ["aria-level"])
          ]),
          _ctx.showClose ? (openBlock(), createElementBlock("button", {
            key: 0,
            "aria-label": unref(t)("el.dialog.close"),
            class: normalizeClass(unref(ns).e("headerbtn")),
            type: "button",
            onClick: ($event) => _ctx.$emit("close")
          }, [
            createVNode(unref(ElIcon), {
              class: normalizeClass(unref(ns).e("close"))
            }, {
              default: withCtx(() => [
                (openBlock(), createBlock(resolveDynamicComponent(_ctx.closeIcon || unref(Close))))
              ]),
              _: 1
            }, 8, ["class"])
          ], 10, ["aria-label", "onClick"])) : createCommentVNode("v-if", true)
        ], 2),
        createBaseVNode("div", {
          id: unref(bodyId),
          class: normalizeClass([unref(ns).e("body"), _ctx.bodyClass])
        }, [
          renderSlot(_ctx.$slots, "default")
        ], 10, ["id"]),
        _ctx.$slots.footer ? (openBlock(), createElementBlock("footer", {
          key: 0,
          class: normalizeClass([unref(ns).e("footer"), _ctx.footerClass])
        }, [
          renderSlot(_ctx.$slots, "footer")
        ], 2)) : createCommentVNode("v-if", true)
      ], 6);
    };
  }
});
var ElDialogContent = /* @__PURE__ */ _export_sfc(_sfc_main$7, [["__file", "dialog-content.vue"]]);
const dialogProps = buildProps({
  ...dialogContentProps,
  appendToBody: Boolean,
  appendTo: {
    type: definePropType([String, Object]),
    default: "body"
  },
  beforeClose: {
    type: definePropType(Function)
  },
  destroyOnClose: Boolean,
  closeOnClickModal: {
    type: Boolean,
    default: true
  },
  closeOnPressEscape: {
    type: Boolean,
    default: true
  },
  lockScroll: {
    type: Boolean,
    default: true
  },
  modal: {
    type: Boolean,
    default: true
  },
  openDelay: {
    type: Number,
    default: 0
  },
  closeDelay: {
    type: Number,
    default: 0
  },
  top: {
    type: String
  },
  modelValue: Boolean,
  modalClass: String,
  headerClass: String,
  bodyClass: String,
  footerClass: String,
  width: {
    type: [String, Number]
  },
  zIndex: {
    type: Number
  },
  trapFocus: Boolean,
  headerAriaLevel: {
    type: String,
    default: "2"
  }
});
const dialogEmits = {
  open: () => true,
  opened: () => true,
  close: () => true,
  closed: () => true,
  [UPDATE_MODEL_EVENT]: (value) => isBoolean(value),
  openAutoFocus: () => true,
  closeAutoFocus: () => true
};
const useLockscreen = (trigger, options = {}) => {
  if (!isRef(trigger)) {
    throwError("[useLockscreen]", "You need to pass a ref param to this function");
  }
  const ns = options.ns || useNamespace("popup");
  const hiddenCls = computed(() => ns.bm("parent", "hidden"));
  if (!isClient || hasClass(document.body, hiddenCls.value)) {
    return;
  }
  let scrollBarWidth = 0;
  let withoutHiddenClass = false;
  let bodyWidth = "0";
  const cleanup = () => {
    setTimeout(() => {
      if (typeof document === "undefined")
        return;
      if (withoutHiddenClass && document) {
        document.body.style.width = bodyWidth;
        removeClass(document.body, hiddenCls.value);
      }
    }, 200);
  };
  watch(trigger, (val) => {
    if (!val) {
      cleanup();
      return;
    }
    withoutHiddenClass = !hasClass(document.body, hiddenCls.value);
    if (withoutHiddenClass) {
      bodyWidth = document.body.style.width;
      addClass(document.body, hiddenCls.value);
    }
    scrollBarWidth = getScrollBarWidth(ns.namespace.value);
    const bodyHasOverflow = document.documentElement.clientHeight < document.body.scrollHeight;
    const bodyOverflowY = getStyle(document.body, "overflowY");
    if (scrollBarWidth > 0 && (bodyHasOverflow || bodyOverflowY === "scroll") && withoutHiddenClass) {
      document.body.style.width = `calc(100% - ${scrollBarWidth}px)`;
    }
  });
  onScopeDispose(() => cleanup());
};
const useDialog = (props, targetRef) => {
  var _a;
  const instance = getCurrentInstance();
  const emit = instance.emit;
  const { nextZIndex } = useZIndex();
  let lastPosition = "";
  const titleId = useId();
  const bodyId = useId();
  const visible = ref(false);
  const closed = ref(false);
  const rendered = ref(false);
  const zIndex = ref((_a = props.zIndex) != null ? _a : nextZIndex());
  let openTimer = void 0;
  let closeTimer = void 0;
  const namespace = useGlobalConfig("namespace", defaultNamespace);
  const style = computed(() => {
    const style2 = {};
    const varPrefix = `--${namespace.value}-dialog`;
    if (!props.fullscreen) {
      if (props.top) {
        style2[`${varPrefix}-margin-top`] = props.top;
      }
      if (props.width) {
        style2[`${varPrefix}-width`] = addUnit(props.width);
      }
    }
    return style2;
  });
  const overlayDialogStyle = computed(() => {
    if (props.alignCenter) {
      return { display: "flex" };
    }
    return {};
  });
  function afterEnter() {
    emit("opened");
  }
  function afterLeave() {
    emit("closed");
    emit(UPDATE_MODEL_EVENT, false);
    if (props.destroyOnClose) {
      rendered.value = false;
    }
  }
  function beforeLeave() {
    emit("close");
  }
  function open() {
    closeTimer == null ? void 0 : closeTimer();
    openTimer == null ? void 0 : openTimer();
    if (props.openDelay && props.openDelay > 0) {
      ({ stop: openTimer } = useTimeoutFn(() => doOpen(), props.openDelay));
    } else {
      doOpen();
    }
  }
  function close() {
    openTimer == null ? void 0 : openTimer();
    closeTimer == null ? void 0 : closeTimer();
    if (props.closeDelay && props.closeDelay > 0) {
      ({ stop: closeTimer } = useTimeoutFn(() => doClose(), props.closeDelay));
    } else {
      doClose();
    }
  }
  function handleClose() {
    function hide(shouldCancel) {
      if (shouldCancel)
        return;
      closed.value = true;
      visible.value = false;
    }
    if (props.beforeClose) {
      props.beforeClose(hide);
    } else {
      close();
    }
  }
  function onModalClick() {
    if (props.closeOnClickModal) {
      handleClose();
    }
  }
  function doOpen() {
    if (!isClient)
      return;
    visible.value = true;
  }
  function doClose() {
    visible.value = false;
  }
  function onOpenAutoFocus() {
    emit("openAutoFocus");
  }
  function onCloseAutoFocus() {
    emit("closeAutoFocus");
  }
  function onFocusoutPrevented(event) {
    var _a2;
    if (((_a2 = event.detail) == null ? void 0 : _a2.focusReason) === "pointer") {
      event.preventDefault();
    }
  }
  if (props.lockScroll) {
    useLockscreen(visible);
  }
  function onCloseRequested() {
    if (props.closeOnPressEscape) {
      handleClose();
    }
  }
  watch(() => props.modelValue, (val) => {
    if (val) {
      closed.value = false;
      open();
      rendered.value = true;
      zIndex.value = isUndefined(props.zIndex) ? nextZIndex() : zIndex.value++;
      nextTick(() => {
        emit("open");
        if (targetRef.value) {
          targetRef.value.parentElement.scrollTop = 0;
          targetRef.value.parentElement.scrollLeft = 0;
          targetRef.value.scrollTop = 0;
        }
      });
    } else {
      if (visible.value) {
        close();
      }
    }
  });
  watch(() => props.fullscreen, (val) => {
    if (!targetRef.value)
      return;
    if (val) {
      lastPosition = targetRef.value.style.transform;
      targetRef.value.style.transform = "";
    } else {
      targetRef.value.style.transform = lastPosition;
    }
  });
  onMounted(() => {
    if (props.modelValue) {
      visible.value = true;
      rendered.value = true;
      open();
    }
  });
  return {
    afterEnter,
    afterLeave,
    beforeLeave,
    handleClose,
    onModalClick,
    close,
    doClose,
    onOpenAutoFocus,
    onCloseAutoFocus,
    onCloseRequested,
    onFocusoutPrevented,
    titleId,
    bodyId,
    closed,
    style,
    overlayDialogStyle,
    rendered,
    visible,
    zIndex
  };
};
const __default__ = defineComponent({
  name: "ElDialog",
  inheritAttrs: false
});
const _sfc_main$6 = /* @__PURE__ */ defineComponent({
  ...__default__,
  props: dialogProps,
  emits: dialogEmits,
  setup(__props, { expose }) {
    const props = __props;
    const slots = useSlots();
    useDeprecated({
      scope: "el-dialog",
      from: "the title slot",
      replacement: "the header slot",
      version: "3.0.0",
      ref: "https://element-plus.org/en-US/component/dialog.html#slots"
    }, computed(() => !!slots.title));
    const ns = useNamespace("dialog");
    const dialogRef = ref();
    const headerRef = ref();
    const dialogContentRef = ref();
    const {
      visible,
      titleId,
      bodyId,
      style,
      overlayDialogStyle,
      rendered,
      zIndex,
      afterEnter,
      afterLeave,
      beforeLeave,
      handleClose,
      onModalClick,
      onOpenAutoFocus,
      onCloseAutoFocus,
      onCloseRequested,
      onFocusoutPrevented
    } = useDialog(props, dialogRef);
    provide(dialogInjectionKey, {
      dialogRef,
      headerRef,
      bodyId,
      ns,
      rendered,
      style
    });
    const overlayEvent = useSameTarget(onModalClick);
    const draggable = computed(() => props.draggable && !props.fullscreen);
    const resetPosition = () => {
      var _a;
      (_a = dialogContentRef.value) == null ? void 0 : _a.resetPosition();
    };
    expose({
      visible,
      dialogContentRef,
      resetPosition
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElTeleport), {
        to: _ctx.appendTo,
        disabled: _ctx.appendTo !== "body" ? false : !_ctx.appendToBody
      }, {
        default: withCtx(() => [
          createVNode(Transition, {
            name: "dialog-fade",
            onAfterEnter: unref(afterEnter),
            onAfterLeave: unref(afterLeave),
            onBeforeLeave: unref(beforeLeave),
            persisted: ""
          }, {
            default: withCtx(() => [
              withDirectives(createVNode(unref(ElOverlay), {
                "custom-mask-event": "",
                mask: _ctx.modal,
                "overlay-class": _ctx.modalClass,
                "z-index": unref(zIndex)
              }, {
                default: withCtx(() => [
                  createBaseVNode("div", {
                    role: "dialog",
                    "aria-modal": "true",
                    "aria-label": _ctx.title || void 0,
                    "aria-labelledby": !_ctx.title ? unref(titleId) : void 0,
                    "aria-describedby": unref(bodyId),
                    class: normalizeClass(`${unref(ns).namespace.value}-overlay-dialog`),
                    style: normalizeStyle(unref(overlayDialogStyle)),
                    onClick: unref(overlayEvent).onClick,
                    onMousedown: unref(overlayEvent).onMousedown,
                    onMouseup: unref(overlayEvent).onMouseup
                  }, [
                    createVNode(unref(ElFocusTrap), {
                      loop: "",
                      trapped: unref(visible),
                      "focus-start-el": "container",
                      onFocusAfterTrapped: unref(onOpenAutoFocus),
                      onFocusAfterReleased: unref(onCloseAutoFocus),
                      onFocusoutPrevented: unref(onFocusoutPrevented),
                      onReleaseRequested: unref(onCloseRequested)
                    }, {
                      default: withCtx(() => [
                        unref(rendered) ? (openBlock(), createBlock(ElDialogContent, mergeProps({
                          key: 0,
                          ref_key: "dialogContentRef",
                          ref: dialogContentRef
                        }, _ctx.$attrs, {
                          center: _ctx.center,
                          "align-center": _ctx.alignCenter,
                          "close-icon": _ctx.closeIcon,
                          draggable: unref(draggable),
                          overflow: _ctx.overflow,
                          fullscreen: _ctx.fullscreen,
                          "header-class": _ctx.headerClass,
                          "body-class": _ctx.bodyClass,
                          "footer-class": _ctx.footerClass,
                          "show-close": _ctx.showClose,
                          title: _ctx.title,
                          "aria-level": _ctx.headerAriaLevel,
                          onClose: unref(handleClose)
                        }), createSlots({
                          header: withCtx(() => [
                            !_ctx.$slots.title ? renderSlot(_ctx.$slots, "header", {
                              key: 0,
                              close: unref(handleClose),
                              titleId: unref(titleId),
                              titleClass: unref(ns).e("title")
                            }) : renderSlot(_ctx.$slots, "title", { key: 1 })
                          ]),
                          default: withCtx(() => [
                            renderSlot(_ctx.$slots, "default")
                          ]),
                          _: 2
                        }, [
                          _ctx.$slots.footer ? {
                            name: "footer",
                            fn: withCtx(() => [
                              renderSlot(_ctx.$slots, "footer")
                            ])
                          } : void 0
                        ]), 1040, ["center", "align-center", "close-icon", "draggable", "overflow", "fullscreen", "header-class", "body-class", "footer-class", "show-close", "title", "aria-level", "onClose"])) : createCommentVNode("v-if", true)
                      ]),
                      _: 3
                    }, 8, ["trapped", "onFocusAfterTrapped", "onFocusAfterReleased", "onFocusoutPrevented", "onReleaseRequested"])
                  ], 46, ["aria-label", "aria-labelledby", "aria-describedby", "onClick", "onMousedown", "onMouseup"])
                ]),
                _: 3
              }, 8, ["mask", "overlay-class", "z-index"]), [
                [vShow, unref(visible)]
              ])
            ]),
            _: 3
          }, 8, ["onAfterEnter", "onAfterLeave", "onBeforeLeave"])
        ]),
        _: 3
      }, 8, ["to", "disabled"]);
    };
  }
});
var Dialog = /* @__PURE__ */ _export_sfc(_sfc_main$6, [["__file", "dialog.vue"]]);
const ElDialog = withInstall(Dialog);
const elDialog = "";
const elOverlay = "";
const _imports_0$4 = "" + new URL("../png/icon-summary-active.ca35d4b4.png", import.meta.url).href;
const _imports_1 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const _imports_2 = "" + new URL("../png/icon-close.1f30f44a.png", import.meta.url).href;
const _imports_0$3 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const _hoisted_1$4 = { class: "not-finished-status" };
const _hoisted_2$3 = { class: "status-btns" };
const _sfc_main$5 = /* @__PURE__ */ defineComponent({
  __name: "ShortTime",
  emits: ["closeSummarize"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    function onCloseSummarize() {
      emits("closeSummarize");
    }
    return (_ctx, _cache) => {
      const _component_el_button = ElButton;
      return openBlock(), createElementBlock("div", _hoisted_1$4, [
        _cache[1] || (_cache[1] = createBaseVNode("img", {
          class: "status-img",
          src: _imports_0$3,
          alt: "toggle"
        }, null, -1)),
        _cache[2] || (_cache[2] = createBaseVNode("div", { class: "status-text" }, "老师我学习时间太短了，还不能完成总结", -1)),
        createBaseVNode("div", _hoisted_2$3, [
          createVNode(_component_el_button, {
            class: "start-button",
            onClick: onCloseSummarize
          }, {
            default: withCtx(() => _cache[0] || (_cache[0] = [
              createTextVNode("好的")
            ])),
            _: 1
          })
        ])
      ]);
    };
  }
});
const ShortTime_vue_vue_type_style_index_0_scoped_f291b3c9_lang = "";
const ShortTime = /* @__PURE__ */ _export_sfc$1(_sfc_main$5, [["__scopeId", "data-v-f291b3c9"]]);
const _imports_0$2 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const _hoisted_1$3 = { class: "not-finished-status" };
const _hoisted_2$2 = { class: "status-btns" };
const _sfc_main$4 = /* @__PURE__ */ defineComponent({
  __name: "NotFinished",
  emits: ["startSummarize", "closeSummarize"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    function onStartSummarize() {
      emits("startSummarize");
    }
    function onCloseSummarize() {
      emits("closeSummarize");
    }
    return (_ctx, _cache) => {
      const _component_el_button = ElButton;
      return openBlock(), createElementBlock("div", _hoisted_1$3, [
        _cache[2] || (_cache[2] = createBaseVNode("img", {
          class: "status-img",
          src: _imports_0$2,
          alt: "toggle"
        }, null, -1)),
        _cache[3] || (_cache[3] = createBaseVNode("div", { class: "status-text" }, "本节课还未结束，您需要现在AI总结吗？", -1)),
        createBaseVNode("div", _hoisted_2$2, [
          createVNode(_component_el_button, {
            class: "hl-sls-track",
            "data-text": "暂不需要",
            "data-module": "星讲台-AI课堂小结",
            onClick: onCloseSummarize
          }, {
            default: withCtx(() => _cache[0] || (_cache[0] = [
              createTextVNode("暂不需要")
            ])),
            _: 1
          }),
          createVNode(_component_el_button, {
            class: "start-button hl-sls-track",
            "data-text": "开始总结",
            "data-module": "星讲台-AI课堂小结",
            onClick: onStartSummarize
          }, {
            default: withCtx(() => _cache[1] || (_cache[1] = [
              createTextVNode("开始总结")
            ])),
            _: 1
          })
        ])
      ]);
    };
  }
});
const NotFinished_vue_vue_type_style_index_0_scoped_37d9d44b_lang = "";
const NotFinished = /* @__PURE__ */ _export_sfc$1(_sfc_main$4, [["__scopeId", "data-v-37d9d44b"]]);
const _hoisted_1$2 = { class: "ai-summarized" };
const _sfc_main$3 = /* @__PURE__ */ defineComponent({
  __name: "AISummarized",
  props: {
    // 是否是AI小结列表
    isSummaryList: {
      type: Boolean,
      default: true
    },
    xmindJsonData: {
      type: Object,
      default: () => {
      }
    },
    snapshotUrl: {
      type: String,
      default: ""
    }
  },
  setup(__props) {
    const classroomStore = useClassroomStore();
    const { classTeacher } = storeToRefs(classroomStore);
    const deviceStore = useDeviceStore();
    const { serialNumber } = storeToRefs(deviceStore);
    const props = __props;
    const { xmindJsonData } = props;
    const xmindRef = ref(null);
    const aiClassSummaryId = ref("");
    const { currentResource } = storeToRefs(useClassroomStore());
    onMounted(() => {
      console.log("aisUMMARIZED10S", "总结渲染");
      aiClassSummaryId.value = localStorage.getItem("aiClassSummaryId");
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1$2, [
        createVNode(__unplugin_components_0, {
          ref_key: "xmindRef",
          ref: xmindRef,
          key: "jsmind_container_ximd",
          "xmind-json-data": unref(xmindJsonData),
          "snapshot-url": __props.snapshotUrl,
          "serial-number": unref(serialNumber),
          "class-teacher": unref(classTeacher),
          aiClassSummaryId: aiClassSummaryId.value,
          "current-resource": unref(currentResource),
          "is-summary-list": __props.isSummaryList,
          "container-id": "jsmind_container_ximd"
        }, null, 8, ["xmind-json-data", "snapshot-url", "serial-number", "class-teacher", "aiClassSummaryId", "current-resource", "is-summary-list"])
      ]);
    };
  }
});
const AISummarized_vue_vue_type_style_index_0_scoped_e7ae99d0_lang = "";
const AISummarized = /* @__PURE__ */ _export_sfc$1(_sfc_main$3, [["__scopeId", "data-v-e7ae99d0"]]);
const _imports_0$1 = "" + new URL("../gif/progress-bar.87288927.gif", import.meta.url).href;
const AIGenerating_vue_vue_type_style_index_0_scoped_e4bb0b33_lang = "";
const _sfc_main$2 = {};
function _sfc_render(_ctx, _cache) {
  return openBlock(), createElementBlock("div", null, _cache[0] || (_cache[0] = [
    createBaseVNode("img", {
      class: "progress-gif",
      src: _imports_0$1,
      alt: "toggle"
    }, null, -1),
    createBaseVNode("div", { class: "el-progress-text" }, "AI生成中，请等待", -1)
  ]));
}
const AIGenerating = /* @__PURE__ */ _export_sfc$1(_sfc_main$2, [["render", _sfc_render], ["__scopeId", "data-v-e4bb0b33"]]);
const _imports_0 = "" + new URL("../webp/<EMAIL>", import.meta.url).href;
const _hoisted_1$1 = { class: "not-finished-status" };
const _hoisted_2$1 = { class: "status-btns" };
const _sfc_main$1 = /* @__PURE__ */ defineComponent({
  __name: "AIDefeated",
  emits: ["closeSummarize"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    function onCloseSummarize() {
      emits("closeSummarize");
    }
    return (_ctx, _cache) => {
      const _component_el_button = ElButton;
      return openBlock(), createElementBlock("div", _hoisted_1$1, [
        _cache[1] || (_cache[1] = createBaseVNode("img", {
          class: "status-img",
          src: _imports_0,
          alt: "toggle"
        }, null, -1)),
        _cache[2] || (_cache[2] = createBaseVNode("div", { class: "status-text" }, "小章鱼迷路了，生成总结失败。", -1)),
        createBaseVNode("div", _hoisted_2$1, [
          createVNode(_component_el_button, { onClick: onCloseSummarize }, {
            default: withCtx(() => _cache[0] || (_cache[0] = [
              createTextVNode("退出")
            ])),
            _: 1
          })
        ])
      ]);
    };
  }
});
const AIDefeated_vue_vue_type_style_index_0_scoped_ca8c6c09_lang = "";
const AIDefeated = /* @__PURE__ */ _export_sfc$1(_sfc_main$1, [["__scopeId", "data-v-ca8c6c09"]]);
const _hoisted_1 = { class: "class-summary" };
const _hoisted_2 = { class: "class-summary-content" };
const _hoisted_3 = {
  key: 0,
  class: "class-summary-classify"
};
const _hoisted_4 = { class: "class-date" };
const _hoisted_5 = ["onClick"];
const _hoisted_6 = { class: "class-summary-detail" };
const _hoisted_7 = { class: "class-summary-detail__info" };
const _sfc_main = /* @__PURE__ */ defineComponent({
  __name: "index",
  props: {
    summaryDialogVisible: {
      type: Boolean,
      default: false
    },
    // 是否是AI小结列表
    isSummaryList: {
      type: Boolean,
      default: true
    },
    summaryStatus: {
      type: String,
      default: SummaryTypeEnum.AI_SUMMARIZED
      // 默认状态为AI小结
    },
    aiSummaryList: {
      type: Array,
      default: () => []
    }
  },
  emits: ["close", "openSettingDialog", "startSummarize"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    const props = __props;
    const { isSummaryList, aiSummaryList } = props;
    const dialogVisible = ref(false);
    const currentSummay = ref(null);
    const currentSummayIndex = ref(0);
    onMounted(async () => {
      currentSummay.value = aiSummaryList[0];
      console.log("是否是AI小结列表1", currentSummay.value);
    });
    watch(
      () => props.summaryDialogVisible,
      (value) => {
        dialogVisible.value = value;
      },
      {
        immediate: true
      }
    );
    watch(
      () => props.summaryStatus,
      () => {
        console.log("是否是AI小结列表10S", props.aiSummaryList);
        currentSummay.value = props.aiSummaryList[0];
      }
    );
    function getCurrentDateAndWeekday() {
      const date = /* @__PURE__ */ new Date();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const weekday = date.getDay();
      const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
      return `${month}月${day}日 ${weekdays[weekday]}`;
    }
    function onSelectSubject(index) {
      currentSummay.value = null;
      nextTick(() => {
        currentSummay.value = aiSummaryList[index];
        currentSummayIndex.value = index;
      });
    }
    function onClose() {
      dialogVisible.value = false;
      emits("close");
    }
    function onOpenSettingDialog() {
      emits("openSettingDialog");
    }
    async function onStartSummarize() {
      console.log("开始总结");
      emits("startSummarize");
    }
    return (_ctx, _cache) => {
      const _component_el_dialog = ElDialog;
      return openBlock(), createBlock(_component_el_dialog, {
        modelValue: dialogVisible.value,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => dialogVisible.value = $event),
        "append-to-body": true,
        "show-close": false,
        class: "summary-dialog",
        onClose
      }, {
        default: withCtx(() => [
          createBaseVNode("div", _hoisted_1, [
            createBaseVNode("div", _hoisted_2, [
              unref(isSummaryList) ? (openBlock(), createElementBlock("div", _hoisted_3, [
                createBaseVNode("div", _hoisted_4, toDisplayString(getCurrentDateAndWeekday()), 1),
                (openBlock(true), createElementBlock(Fragment, null, renderList(unref(aiSummaryList), (item, index) => {
                  return openBlock(), createElementBlock("div", {
                    key: index,
                    class: normalizeClass(["classify-item", {
                      "classify-item-active": index === currentSummayIndex.value
                    }]),
                    onClick: ($event) => onSelectSubject(index)
                  }, [
                    createTextVNode(toDisplayString(item.saasSubjectName) + " ", 1),
                    _cache[1] || (_cache[1] = createBaseVNode("img", {
                      class: "classify-item-active-img",
                      src: _imports_0$4,
                      alt: ""
                    }, null, -1))
                  ], 10, _hoisted_5);
                }), 128))
              ])) : createCommentVNode("", true),
              createBaseVNode("div", _hoisted_6, [
                _cache[2] || (_cache[2] = createBaseVNode("img", {
                  class: "class-summary-detail__title",
                  src: _imports_1,
                  alt: ""
                }, null, -1)),
                createBaseVNode("div", _hoisted_7, [
                  __props.summaryStatus === unref(SummaryTypeEnum).SHORT_TIME ? (openBlock(), createBlock(ShortTime, {
                    key: 0,
                    onCloseSummarize: onClose
                  })) : createCommentVNode("", true),
                  __props.summaryStatus === unref(SummaryTypeEnum).NOT_FINISHED ? (openBlock(), createBlock(NotFinished, {
                    key: 1,
                    onCloseSummarize: onClose,
                    onStartSummarize
                  })) : createCommentVNode("", true),
                  __props.summaryStatus === unref(SummaryTypeEnum).AI_GENERATING ? (openBlock(), createBlock(AIGenerating, { key: 2 })) : createCommentVNode("", true),
                  __props.summaryStatus === unref(SummaryTypeEnum).AI_SUMMARIZED && currentSummay.value?.summary ? renderSlot(_ctx.$slots, "ai-summarized", {
                    key: 3,
                    xmindJsonData: currentSummay.value?.summary,
                    snapshotUrl: currentSummay.value?.snapshotUrl,
                    isSummaryList: unref(isSummaryList)
                  }, () => [
                    createVNode(AISummarized, {
                      "xmind-json-data": currentSummay.value?.summary,
                      "snapshot-url": currentSummay.value?.snapshotUrl,
                      "is-summary-list": unref(isSummaryList)
                    }, null, 8, ["xmind-json-data", "snapshot-url", "is-summary-list"])
                  ], true) : createCommentVNode("", true),
                  __props.summaryStatus === unref(SummaryTypeEnum).AI_DEFEATED ? (openBlock(), createBlock(AIDefeated, {
                    key: 4,
                    onCloseSummarize: onClose,
                    onStartSummarize
                  })) : createCommentVNode("", true)
                ])
              ]),
              createBaseVNode("img", {
                class: "close-icon",
                src: _imports_2,
                alt: "toggle",
                onClick: onClose
              }),
              !unref(isSummaryList) ? (openBlock(), createElementBlock("div", {
                key: 1,
                class: "set-icon",
                onClick: onOpenSettingDialog
              }, [
                createVNode(SvgIcon, {
                  class: "setting",
                  "icon-class": "setting"
                }),
                _cache[3] || (_cache[3] = createTextVNode("设置 "))
              ])) : createCommentVNode("", true)
            ])
          ])
        ]),
        _: 3
      }, 8, ["modelValue"]);
    };
  }
});
const index_vue_vue_type_style_index_0_scoped_33c5d4e2_lang = "";
const index_vue_vue_type_style_index_1_lang = "";
const ClassSummary = /* @__PURE__ */ _export_sfc$1(_sfc_main, [["__scopeId", "data-v-33c5d4e2"]]);
export {
  ClassSummary as C,
  ElDialog as E
};
