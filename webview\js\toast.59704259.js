import { ai as h, Y as render } from "./bootstrap.ab073eb8.js";
import { S as SvgIcon } from "./index.24eb8257.js";
const toastQueue = [];
function getIconClass(type) {
  if (!type)
    return "gantanhao";
  return {
    info: "gantanhao",
    success: "select",
    warning: "gantanhao",
    error: "close"
  }[type] || "gantanhao";
}
function showError(message, options = {}) {
  showToast(message, {
    ...options,
    type: "error"
  });
}
function showSuccess(message, options = {}) {
  showToast(message, {
    ...options,
    type: "success"
  });
}
function showWarning(message, options = {}) {
  showToast(message, {
    ...options,
    type: "warning"
  });
}
function showToast(message, options = {}) {
  const { duration = 3e3, type = "info", position = "bottom", gap = 20 } = options;
  const container = document.createElement("div");
  container.className = `toast toast-${type}`;
  document.body.appendChild(container);
  const vnode = h("div", { class: "toast-content" }, [
    h("div", { class: "toast-icon-container" }, [
      h(SvgIcon, { class: "toast-icon", iconClass: getIconClass(type) })
    ]),
    h(
      "span",
      {
        class: "toast-message text-ellipsis" + (String(message).length <= 6 ? "toast-message-few" : "")
      },
      message
    )
  ]);
  render(vnode, container);
  const initialTop = calculateToastPosition(position, gap);
  container.style.position = "fixed";
  container.style.bottom = initialTop + "px";
  container.style.opacity = "1";
  if (position === "bottom" || position.startsWith("bottom")) {
    container.style.left = "50%";
    container.style.transform = "translateX(-50%)";
  } else if (position.endsWith("left")) {
    container.style.left = "20px";
  } else if (position.endsWith("right")) {
    container.style.right = "20px";
  }
  toastQueue.push({ container, position });
  setTimeout(() => {
    removeToast(container, position, gap);
  }, duration);
}
function calculateToastPosition(position, gap) {
  let bottom = 99;
  for (const toast of toastQueue) {
    if (toast.position === position) {
      const prevToastHeight = toast.container.offsetHeight;
      bottom += prevToastHeight + gap;
    }
  }
  return bottom;
}
function removeToast(container, position, gap) {
  container.style.opacity = "0";
  setTimeout(() => {
    render(null, container);
    document.body.removeChild(container);
    const index = toastQueue.findIndex((item) => item.container === container);
    if (index > -1)
      toastQueue.splice(index, 1);
    updateToastPositions(position, gap);
  }, 300);
}
function updateToastPositions(position, gap) {
  let bottom = 35;
  for (const toast of toastQueue) {
    if (toast.position === position) {
      toast.container.style.bottom = `${top}px`;
      bottom += toast.container.offsetHeight + gap;
    }
  }
}
export {
  showToast as a,
  showWarning as b,
  showSuccess as c,
  showError as s
};
