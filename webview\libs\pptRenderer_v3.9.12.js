!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.PgReader=t():e.PgReader=t()}(self,(function(){return function(){"use strict";var e={4559:function(e,t,a){e.exports=a(9335)},1786:function(e,t,a){var r=a(8266),n=a(5608),i=a(159),o=a(9568),s=a(3943),l=a(8201),c=a(1745),u=a(7979);e.exports=function(e){return new Promise((function(t,a){var h=e.data,d=e.headers;r.isFormData(h)&&delete d["Content-Type"];var f=new XMLHttpRequest;if(e.auth){var p=e.auth.username||"",y=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";d.Authorization="Basic "+btoa(p+":"+y)}var v=s(e.baseURL,e.url);if(f.open(e.method.toUpperCase(),o(v,e.params,e.paramsSerializer),!0),f.timeout=e.timeout,f.onreadystatechange=function(){if(f&&4===f.readyState&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))){var r="getAllResponseHeaders"in f?l(f.getAllResponseHeaders()):null,i={data:e.responseType&&"text"!==e.responseType?f.response:f.responseText,status:f.status,statusText:f.statusText,headers:r,config:e,request:f};n(t,a,i),f=null}},f.onabort=function(){f&&(a(u("Request aborted",e,"ECONNABORTED",f)),f=null)},f.onerror=function(){a(u("Network Error",e,null,f)),f=null},f.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),a(u(t,e,"ECONNABORTED",f)),f=null},r.isStandardBrowserEnv()){var g=(e.withCredentials||c(v))&&e.xsrfCookieName?i.read(e.xsrfCookieName):void 0;g&&(d[e.xsrfHeaderName]=g)}if("setRequestHeader"in f&&r.forEach(d,(function(e,t){void 0===h&&"content-type"===t.toLowerCase()?delete d[t]:f.setRequestHeader(t,e)})),r.isUndefined(e.withCredentials)||(f.withCredentials=!!e.withCredentials),e.responseType)try{f.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&f.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&f.upload&&f.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){f&&(f.abort(),a(e),f=null)})),h||(h=null),f.send(h)}))}},9335:function(e,t,a){var r=a(8266),n=a(4345),i=a(7929),o=a(650);function s(e){var t=new i(e),a=n(i.prototype.request,t);return r.extend(a,i.prototype,t),r.extend(a,t),a}var l=s(a(9046));l.Axios=i,l.create=function(e){return s(o(l.defaults,e))},l.Cancel=a(9760),l.CancelToken=a(7510),l.isCancel=a(8825),l.all=function(e){return Promise.all(e)},l.spread=a(4346),l.isAxiosError=a(3276),e.exports=l,e.exports.default=l},9760:function(e){function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},7510:function(e,t,a){var r=a(9760);function n(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var a=this;e((function(e){a.reason||(a.reason=new r(e),t(a.reason))}))}n.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},n.source=function(){var e;return{token:new n((function(t){e=t})),cancel:e}},e.exports=n},8825:function(e){e.exports=function(e){return!(!e||!e.__CANCEL__)}},7929:function(e,t,a){var r=a(8266),n=a(9568),i=a(6252),o=a(6029),s=a(650);function l(e){this.defaults=e,this.interceptors={request:new i,response:new i}}l.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[o,void 0],a=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)a=a.then(t.shift(),t.shift());return a},l.prototype.getUri=function(e){return e=s(this.defaults,e),n(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},r.forEach(["delete","get","head","options"],(function(e){l.prototype[e]=function(t,a){return this.request(s(a||{},{method:e,url:t,data:(a||{}).data}))}})),r.forEach(["post","put","patch"],(function(e){l.prototype[e]=function(t,a,r){return this.request(s(r||{},{method:e,url:t,data:a}))}})),e.exports=l},6252:function(e,t,a){var r=a(8266);function n(){this.handlers=[]}n.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},n.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},n.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=n},3943:function(e,t,a){var r=a(406),n=a(5027);e.exports=function(e,t){return e&&!r(t)?n(e,t):t}},7979:function(e,t,a){var r=a(2050);e.exports=function(e,t,a,n,i){var o=new Error(e);return r(o,t,a,n,i)}},6029:function(e,t,a){var r=a(8266),n=a(2661),i=a(8825),o=a(9046);function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return s(e),e.headers=e.headers||{},e.data=n(e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||o.adapter)(e).then((function(t){return s(e),t.data=n(t.data,t.headers,e.transformResponse),t}),(function(t){return i(t)||(s(e),t&&t.response&&(t.response.data=n(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},2050:function(e){e.exports=function(e,t,a,r,n){return e.config=t,a&&(e.code=a),e.request=r,e.response=n,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},650:function(e,t,a){var r=a(8266);e.exports=function(e,t){t=t||{};var a={},n=["url","method","data"],i=["headers","auth","proxy","params"],o=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],s=["validateStatus"];function l(e,t){return r.isPlainObject(e)&&r.isPlainObject(t)?r.merge(e,t):r.isPlainObject(t)?r.merge({},t):r.isArray(t)?t.slice():t}function c(n){r.isUndefined(t[n])?r.isUndefined(e[n])||(a[n]=l(void 0,e[n])):a[n]=l(e[n],t[n])}r.forEach(n,(function(e){r.isUndefined(t[e])||(a[e]=l(void 0,t[e]))})),r.forEach(i,c),r.forEach(o,(function(n){r.isUndefined(t[n])?r.isUndefined(e[n])||(a[n]=l(void 0,e[n])):a[n]=l(void 0,t[n])})),r.forEach(s,(function(r){r in t?a[r]=l(e[r],t[r]):r in e&&(a[r]=l(void 0,e[r]))}));var u=n.concat(i).concat(o).concat(s),h=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===u.indexOf(e)}));return r.forEach(h,c),a}},5608:function(e,t,a){var r=a(7979);e.exports=function(e,t,a){var n=a.config.validateStatus;a.status&&n&&!n(a.status)?t(r("Request failed with status code "+a.status,a.config,null,a.request,a)):e(a)}},2661:function(e,t,a){var r=a(8266);e.exports=function(e,t,a){return r.forEach(a,(function(a){e=a(e,t)})),e}},9046:function(e,t,a){var r=a(8266),n=a(1490),i={"Content-Type":"application/x-www-form-urlencoded"};function o(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var s,l={adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(s=a(1786)),s),transformRequest:[function(e,t){return n(t,"Accept"),n(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(o(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)?(o(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){l.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){l.headers[e]=r.merge(i)})),e.exports=l},4345:function(e){e.exports=function(e,t){return function(){for(var a=new Array(arguments.length),r=0;r<a.length;r++)a[r]=arguments[r];return e.apply(t,a)}}},9568:function(e,t,a){var r=a(8266);function n(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,a){if(!t)return e;var i;if(a)i=a(t);else if(r.isURLSearchParams(t))i=t.toString();else{var o=[];r.forEach(t,(function(e,t){null!=e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),o.push(n(t)+"="+n(e))})))})),i=o.join("&")}if(i){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}},5027:function(e){e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},159:function(e,t,a){var r=a(8266);e.exports=r.isStandardBrowserEnv()?{write:function(e,t,a,n,i,o){var s=[];s.push(e+"="+encodeURIComponent(t)),r.isNumber(a)&&s.push("expires="+new Date(a).toGMTString()),r.isString(n)&&s.push("path="+n),r.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},406:function(e){e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},3276:function(e){var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};e.exports=function(e){return"object"===(void 0===e?"undefined":t(e))&&!0===e.isAxiosError}},1745:function(e,t,a){var r=a(8266);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),a=document.createElement("a");function n(e){var r=e;return t&&(a.setAttribute("href",r),r=a.href),a.setAttribute("href",r),{href:a.href,protocol:a.protocol?a.protocol.replace(/:$/,""):"",host:a.host,search:a.search?a.search.replace(/^\?/,""):"",hash:a.hash?a.hash.replace(/^#/,""):"",hostname:a.hostname,port:a.port,pathname:"/"===a.pathname.charAt(0)?a.pathname:"/"+a.pathname}}return e=n(window.location.href),function(t){var a=r.isString(t)?n(t):t;return a.protocol===e.protocol&&a.host===e.host}}():function(){return!0}},1490:function(e,t,a){var r=a(8266);e.exports=function(e,t){r.forEach(e,(function(a,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=a,delete e[r])}))}},8201:function(e,t,a){var r=a(8266),n=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,a,i,o={};return e?(r.forEach(e.split("\n"),(function(e){if(i=e.indexOf(":"),t=r.trim(e.substr(0,i)).toLowerCase(),a=r.trim(e.substr(i+1)),t){if(o[t]&&n.indexOf(t)>=0)return;o[t]="set-cookie"===t?(o[t]?o[t]:[]).concat([a]):o[t]?o[t]+", "+a:a}})),o):o}},4346:function(e){e.exports=function(e){return function(t){return e.apply(null,t)}}},8266:function(e,t,a){var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=a(4345),i=Object.prototype.toString;function o(e){return"[object Array]"===i.call(e)}function s(e){return void 0===e}function l(e){return null!==e&&"object"===(void 0===e?"undefined":r(e))}function c(e){if("[object Object]"!==i.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function u(e){return"[object Function]"===i.call(e)}function h(e,t){if(null!=e)if("object"!==(void 0===e?"undefined":r(e))&&(e=[e]),o(e))for(var a=0,n=e.length;a<n;a++)t.call(null,e[a],a,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}e.exports={isArray:o,isArrayBuffer:function(e){return"[object ArrayBuffer]"===i.call(e)},isBuffer:function(e){return null!==e&&!s(e)&&null!==e.constructor&&!s(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:l,isPlainObject:c,isUndefined:s,isDate:function(e){return"[object Date]"===i.call(e)},isFile:function(e){return"[object File]"===i.call(e)},isBlob:function(e){return"[object Blob]"===i.call(e)},isFunction:u,isStream:function(e){return l(e)&&u(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:h,merge:function e(){var t={};function a(a,r){c(t[r])&&c(a)?t[r]=e(t[r],a):c(a)?t[r]=e({},a):o(a)?t[r]=a.slice():t[r]=a}for(var r=0,n=arguments.length;r<n;r++)h(arguments[r],a);return t},extend:function(e,t,a){return h(t,(function(t,r){e[r]=a&&"function"==typeof t?n(t,a):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},6276:function(e,t,a){var r=function(){return this}()||Function("return this")(),n=r.regeneratorRuntime&&Object.getOwnPropertyNames(r).indexOf("regeneratorRuntime")>=0,i=n&&r.regeneratorRuntime;if(r.regeneratorRuntime=void 0,e.exports=a(5366),n)r.regeneratorRuntime=i;else try{delete r.regeneratorRuntime}catch(e){r.regeneratorRuntime=void 0}},5366:function(e,t,a){e=a.nmd(e);var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(t){var a,n=Object.prototype,i=n.hasOwnProperty,o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",l=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag",u="object"===r(e),h=t.regeneratorRuntime;if(h)u&&(e.exports=h);else{(h=t.regeneratorRuntime=u?e.exports:{}).wrap=x;var d="suspendedStart",f="suspendedYield",p="executing",y="completed",v={},g={};g[s]=function(){return this};var m=Object.getPrototypeOf,M=m&&m(m(D([])));M&&M!==n&&i.call(M,s)&&(g=M);var b=T.prototype=k.prototype=Object.create(g);I.prototype=b.constructor=T,T.constructor=I,T[c]=I.displayName="GeneratorFunction",h.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===I||"GeneratorFunction"===(t.displayName||t.name))},h.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,T):(e.__proto__=T,c in e||(e[c]="GeneratorFunction")),e.prototype=Object.create(b),e},h.awrap=function(e){return{__await:e}},w(N.prototype),N.prototype[l]=function(){return this},h.AsyncIterator=N,h.async=function(e,t,a,r){var n=new N(x(e,t,a,r));return h.isGeneratorFunction(t)?n:n.next().then((function(e){return e.done?e.value:n.next()}))},w(b),b[c]="Generator",b[s]=function(){return this},b.toString=function(){return"[object Generator]"},h.keys=function(e){var t=[];for(var a in e)t.push(a);return t.reverse(),function a(){for(;t.length;){var r=t.pop();if(r in e)return a.value=r,a.done=!1,a}return a.done=!0,a}},h.values=D,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=a,this.done=!1,this.delegate=null,this.method="next",this.arg=a,this.tryEntries.forEach(j),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=a)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,n){return s.type="throw",s.arg=e,t.next=r,n&&(t.method="next",t.arg=a),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],s=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var l=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(l&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var a=this.tryEntries.length-1;a>=0;--a){var r=this.tryEntries[a];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var n=r;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,v):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.finallyLoc===e)return this.complete(a.completion,a.afterLoc),j(a),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var a=this.tryEntries[t];if(a.tryLoc===e){var r=a.completion;if("throw"===r.type){var n=r.arg;j(a)}return n}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:D(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=a),v}}}function x(e,t,a,r){var n=t&&t.prototype instanceof k?t:k,i=Object.create(n.prototype),o=new S(r||[]);return i._invoke=function(e,t,a){var r=d;return function(n,i){if(r===p)throw new Error("Generator is already running");if(r===y){if("throw"===n)throw i;return z()}for(a.method=n,a.arg=i;;){var o=a.delegate;if(o){var s=A(o,a);if(s){if(s===v)continue;return s}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(r===d)throw r=y,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r=p;var l=P(e,t,a);if("normal"===l.type){if(r=a.done?y:f,l.arg===v)continue;return{value:l.arg,done:a.done}}"throw"===l.type&&(r=y,a.method="throw",a.arg=l.arg)}}}(e,a,o),i}function P(e,t,a){try{return{type:"normal",arg:e.call(t,a)}}catch(e){return{type:"throw",arg:e}}}function k(){}function I(){}function T(){}function w(e){["next","throw","return"].forEach((function(t){e[t]=function(e){return this._invoke(t,e)}}))}function N(e){function t(a,n,o,s){var l=P(e[a],e,n);if("throw"!==l.type){var c=l.arg,u=c.value;return u&&"object"===(void 0===u?"undefined":r(u))&&i.call(u,"__await")?Promise.resolve(u.__await).then((function(e){t("next",e,o,s)}),(function(e){t("throw",e,o,s)})):Promise.resolve(u).then((function(e){c.value=e,o(c)}),s)}s(l.arg)}var a;this._invoke=function(e,r){function n(){return new Promise((function(a,n){t(e,r,a,n)}))}return a=a?a.then(n,n):n()}}function A(e,t){var r=e.iterator[t.method];if(r===a){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=a,A(e,t),"throw"===t.method))return v;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return v}var n=P(r,e.iterator,t.arg);if("throw"===n.type)return t.method="throw",t.arg=n.arg,t.delegate=null,v;var i=n.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=a),t.delegate=null,v):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,v)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function j(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function D(e){if(e){var t=e[s];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,n=function t(){for(;++r<e.length;)if(i.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=a,t.done=!0,t};return n.next=n}}return{next:z}}function z(){return{value:a,done:!0}}}(function(){return this}()||Function("return this")())},2754:function(e,t,a){e.exports=a(6276)},1075:function(e){var t=Object.prototype.hasOwnProperty,a="~";function r(){}function n(e,t,a){this.fn=e,this.context=t,this.once=a||!1}function i(e,t,r,i,o){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new n(r,i||e,o),l=a?a+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],s]:e._events[l].push(s):(e._events[l]=s,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(a=!1)),s.prototype.eventNames=function(){var e,r,n=[];if(0===this._eventsCount)return n;for(r in e=this._events)t.call(e,r)&&n.push(a?r.slice(1):r);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(e)):n},s.prototype.listeners=function(e){var t=a?a+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var n=0,i=r.length,o=new Array(i);n<i;n++)o[n]=r[n].fn;return o},s.prototype.listenerCount=function(e){var t=a?a+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,n,i,o){var s=a?a+e:e;if(!this._events[s])return!1;var l,c,u=this._events[s],h=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),h){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,r),!0;case 4:return u.fn.call(u.context,t,r,n),!0;case 5:return u.fn.call(u.context,t,r,n,i),!0;case 6:return u.fn.call(u.context,t,r,n,i,o),!0}for(c=1,l=new Array(h-1);c<h;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var d,f=u.length;for(c=0;c<f;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),h){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,r);break;case 4:u[c].fn.call(u[c].context,t,r,n);break;default:if(!l)for(d=1,l=new Array(h-1);d<h;d++)l[d-1]=arguments[d];u[c].fn.apply(u[c].context,l)}}return!0},s.prototype.on=function(e,t,a){return i(this,e,t,a,!1)},s.prototype.once=function(e,t,a){return i(this,e,t,a,!0)},s.prototype.removeListener=function(e,t,r,n){var i=a?a+e:e;if(!this._events[i])return this;if(!t)return o(this,i),this;var s=this._events[i];if(s.fn)s.fn!==t||n&&!s.once||r&&s.context!==r||o(this,i);else{for(var l=0,c=[],u=s.length;l<u;l++)(s[l].fn!==t||n&&!s[l].once||r&&s[l].context!==r)&&c.push(s[l]);c.length?this._events[i]=1===c.length?c[0]:c:o(this,i)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=a?a+e:e,this._events[t]&&o(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=a,s.EventEmitter=s,e.exports=s},9254:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}();function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var n=t.Point=function(){function e(t,a){r(this,e),this.x=t,this.y=a}return a(e,[{key:"angleTo",value:function(e){return Math.atan2(e.y-this.y,e.x-this.x)}},{key:"applyTransform",value:function(e){var t=this.x*e[0]+this.y*e[2]+e[4],a=this.x*e[1]+this.y*e[3]+e[5];this.x=t,this.y=a}}]),e}(),i=t.BoundingBox=function(){function e(t,a,n,i){r(this,e),this.x1=Number.NaN,this.y1=Number.NaN,this.x2=Number.NaN,this.y2=Number.NaN,this.addPoint(t,a),this.addPoint(n,i)}return a(e,[{key:"x",value:function(){return this.x1}},{key:"y",value:function(){return this.y1}},{key:"width",value:function(){return this.x2-this.x1}},{key:"height",value:function(){return this.y2-this.y1}},{key:"addPoint",value:function(e,t){null!=e&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),null!=t&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=t,this.y2=t),t<this.y1&&(this.y1=t),t>this.y2&&(this.y2=t))}},{key:"addX",value:function(e){this.addPoint(e,null)}},{key:"addY",value:function(e){this.addPoint(null,e)}},{key:"addBoundingBox",value:function(e){this.addPoint(e.x1,e.y1),this.addPoint(e.x2,e.y2)}},{key:"addQuadraticCurve",value:function(e,t,a,r,n,i){var o=e+2/3*(a-e),s=t+2/3*(r-t),l=o+1/3*(n-e),c=s+1/3*(i-t);this.addBezierCurve(e,t,o,l,s,c,n,i)}},{key:"addBezierCurve",value:function(e,t,a,r,n,i,o,s){var l=[e,t],c=[a,r],u=[n,i],h=[o,s];this.addPoint(l[0],l[1]),this.addPoint(h[0],h[1]);for(var d=0;d<=1;d++){var f=function(e){return Math.pow(1-e,3)*l[d]+3*Math.pow(1-e,2)*e*c[d]+3*(1-e)*Math.pow(e,2)*u[d]+Math.pow(e,3)*h[d]},p=6*l[d]-12*c[d]+6*u[d],y=-3*l[d]+9*c[d]-9*u[d]+3*h[d],v=3*c[d]-3*l[d];if(0!=y){var g=Math.pow(p,2)-4*v*y;if(!(g<0)){var m=(-p+Math.sqrt(g))/(2*y);0<m&&m<1&&(0==d&&this.addX(f(m)),1==d&&this.addY(f(m)));var M=(-p-Math.sqrt(g))/(2*y);0<M&&M<1&&(0==d&&this.addX(f(M)),1==d&&this.addY(f(M)))}}else{if(0==p)continue;var b=-v/p;0<b&&b<1&&(0==d&&this.addX(f(b)),1==d&&this.addY(f(b)))}}}},{key:"isPointInBox",value:function(e,t){return this.x1<=e&&e<=this.x2&&this.y1<=t&&t<=this.y2}}]),e}(),o=t.PathParser=function(){function e(){r(this,e)}return a(e,[{key:"reset",value:function(e){this.i=-1,this.command="",this.previousCommand="",this.start=new n(0,0),this.control=new n(0,0),this.current=new n(0,0),this.points=[],this.angles=[],this.tokens=e.split(" ")}},{key:"isEnd",value:function(){return this.i>=this.tokens.length-1}},{key:"isCommandOrEnd",value:function(){return!!this.isEnd()||null!=this.tokens[this.i+1].match(/^[A-Za-z]$/)}},{key:"isRelativeCommand",value:function(){switch(this.command){case"m":case"l":case"h":case"v":case"c":case"s":case"q":case"t":case"a":case"z":return!0}return!1}},{key:"getToken",value:function(){return this.i++,this.tokens[this.i]}},{key:"getScalar",value:function(){return parseFloat(this.getToken())}},{key:"nextCommand",value:function(){this.previousCommand=this.command,this.command=this.getToken()}},{key:"getPoint",value:function(){var e=new n(this.getScalar(),this.getScalar());return this.makeAbsolute(e)}},{key:"getAsControlPoint",value:function(){var e=this.getPoint();return this.control=e,e}},{key:"getAsCurrentPoint",value:function(){var e=this.getPoint();return this.current=e,e}},{key:"getReflectedControlPoint",value:function(){return"c"!=this.previousCommand.toLowerCase()&&"s"!=this.previousCommand.toLowerCase()&&"q"!=this.previousCommand.toLowerCase()&&"t"!=this.previousCommand.toLowerCase()?this.current:new n(2*this.current.x-this.control.x,2*this.current.y-this.control.y)}},{key:"makeAbsolute",value:function(e){return this.isRelativeCommand()&&(e.x+=this.current.x,e.y+=this.current.y),e}},{key:"addMarker",value:function(e,t,a){null!=a&&this.angles.length>0&&null==this.angles[this.angles.length-1]&&(this.angles[this.angles.length-1]=this.points[this.points.length-1].angleTo(a)),this.addMarkerAngle(e,null==t?null:t.angleTo(e))}},{key:"addMarkerAngle",value:function(e,t){this.points.push(e),this.angles.push(t)}},{key:"getMarkerPoints",value:function(){return this.points}},{key:"getMarkerAngles",value:function(){for(var e=0;e<this.angles.length;e++)if(null==this.angles[e])for(var t=e+1;t<this.angles.length;t++)if(null!=this.angles[t]){this.angles[e]=this.angles[t];break}return this.angles}}]),e}();(t.PathPainter=function(){function e(){r(this,e)}return a(e,null,[{key:"paintPath",value:function(t,a){var r,o=e.pathParser;o.reset(a.v);var s=new i;for(null!=t&&t.beginPath();!o.isEnd();)switch(o.nextCommand(),o.command){case"M":case"m":var l=o.getAsCurrentPoint();for(o.addMarker(l),s.addPoint(l.x,l.y),null!=t&&t.moveTo(l.x,l.y),o.start=o.current;!o.isCommandOrEnd();)l=o.getAsCurrentPoint(),o.addMarker(l,o.start),s.addPoint(l.x,l.y),null!=t&&t.lineTo(l.x,l.y);break;case"L":case"l":for(;!o.isCommandOrEnd();){var c=o.current;l=o.getAsCurrentPoint(),o.addMarker(l,c),s.addPoint(l.x,l.y),null!=t&&t.lineTo(l.x,l.y)}break;case"H":case"h":for(;!o.isCommandOrEnd();){var u=new n((o.isRelativeCommand()?o.current.x:0)+o.getScalar(),o.current.y);o.addMarker(u,o.current),o.current=u,s.addPoint(o.current.x,o.current.y),null!=t&&t.lineTo(o.current.x,o.current.y)}break;case"V":case"v":for(;!o.isCommandOrEnd();)u=new n(o.current.x,(o.isRelativeCommand()?o.current.y:0)+o.getScalar()),o.addMarker(u,o.current),o.current=u,s.addPoint(o.current.x,o.current.y),null!=t&&t.lineTo(o.current.x,o.current.y);break;case"C":case"c":for(;!o.isCommandOrEnd();){var h=o.current,d=o.getPoint(),f=o.getAsControlPoint(),p=o.getAsCurrentPoint();o.addMarker(p,f,d),s.addBezierCurve(h.x,h.y,d.x,d.y,f.x,f.y,p.x,p.y),null!=t&&t.bezierCurveTo(d.x,d.y,f.x,f.y,p.x,p.y)}break;case"S":case"s":for(;!o.isCommandOrEnd();)h=o.current,d=o.getReflectedControlPoint(),f=o.getAsControlPoint(),p=o.getAsCurrentPoint(),o.addMarker(p,f,d),s.addBezierCurve(h.x,h.y,d.x,d.y,f.x,f.y,p.x,p.y),null!=t&&t.bezierCurveTo(d.x,d.y,f.x,f.y,p.x,p.y);break;case"Q":case"q":for(;!o.isCommandOrEnd();)h=o.current,f=o.getAsControlPoint(),p=o.getAsCurrentPoint(),o.addMarker(p,f,f),s.addQuadraticCurve(h.x,h.y,f.x,f.y,p.x,p.y),null!=t&&t.quadraticCurveTo(f.x,f.y,p.x,p.y);break;case"T":case"t":for(;!o.isCommandOrEnd();)h=o.current,f=o.getReflectedControlPoint(),o.control=f,p=o.getAsCurrentPoint(),o.addMarker(p,f,f),s.addQuadraticCurve(h.x,h.y,f.x,f.y,p.x,p.y),null!=t&&t.quadraticCurveTo(f.x,f.y,p.x,p.y);break;case"A":case"a":for(;!o.isCommandOrEnd();){h=o.current;var y=o.getScalar(),v=o.getScalar(),g=o.getScalar()*(Math.PI/180),m=o.getScalar()?1:0,M=o.getScalar()?1:0,b=(p=o.getAsCurrentPoint(),new n(Math.cos(g)*(h.x-p.x)/2+Math.sin(g)*(h.y-p.y)/2,-Math.sin(g)*(h.x-p.x)/2+Math.cos(g)*(h.y-p.y)/2)),x=Math.pow(b.x,2)/Math.pow(y,2)+Math.pow(b.y,2)/Math.pow(v,2);x>1&&(y*=Math.sqrt(x),v*=Math.sqrt(x));var P=(m==M?-1:1)*Math.sqrt((Math.pow(y,2)*Math.pow(v,2)-Math.pow(y,2)*Math.pow(b.y,2)-Math.pow(v,2)*Math.pow(b.x,2))/(Math.pow(y,2)*Math.pow(b.y,2)+Math.pow(v,2)*Math.pow(b.x,2)));isNaN(P)&&(P=0);var k=new n(P*y*b.y/v,P*-v*b.x/y),I=new n((h.x+p.x)/2+Math.cos(g)*k.x-Math.sin(g)*k.y,(h.y+p.y)/2+Math.sin(g)*k.x+Math.cos(g)*k.y),T=function(e){return Math.sqrt(Math.pow(e[0],2)+Math.pow(e[1],2))};r=function(e,t){return(e[0]*t[0]+e[1]*t[1])/(T(e)*T(t))};var w=function(e,t){return(e[0]*t[1]<e[1]*t[0]?-1:1)*Math.acos(r(e,t))},N=w([1,0],[(b.x-k.x)/y,(b.y-k.y)/v]),A=[(b.x-k.x)/y,(b.y-k.y)/v],C=[(-b.x-k.x)/y,(-b.y-k.y)/v],j=w(A,C);r(A,C)<=-1&&(j=Math.PI),r(A,C)>=1&&(j=0);var S=1-M?1:-1,D=N+S*(j/2),z=new n(I.x+y*Math.cos(D),I.y+v*Math.sin(D));if(o.addMarkerAngle(z,D-S*Math.PI/2),o.addMarkerAngle(p,D-S*Math.PI),s.addPoint(p.x,p.y),null!=t&&!isNaN(N)&&!isNaN(j)){r=y>v?y:v;var L=y>v?1:y/v,O=y>v?v/y:1;t.translate(I.x,I.y),t.rotate(g),t.scale(L,O),t.arc(0,0,r,N,N+j,!!(1-M)),t.scale(1/L,1/O),t.rotate(-g),t.translate(-I.x,-I.y)}}break;case"Z":case"z":null!=t&&s.x1!==s.x2&&s.y1!==s.y2&&t.closePath(),o.current=o.start}return s}}]),e}()).pathParser=new o;var s=t.convertSVGPathToPath=function(e,t){for(var a=Object.create(null),r=t.split(" "),n=0;n<r.length;n++)switch(r[n]){case"m":case"M":a.x=Number.parseFloat(r[n+1]),a.y=Number.parseFloat(r[n+2]),e.moveTo(Number.parseFloat(r[n+1]),Number.parseFloat(r[n+2])),n+=2;break;case"l":case"L":a.x=Number.parseFloat(r[n+1]),a.y=Number.parseFloat(r[n+2]),e.lineTo(Number.parseFloat(r[n+1]),Number.parseFloat(r[n+2])),n+=2;break;case"h":case"H":a.x=Number.parseFloat(r[n+1]),e.lineTo(a.x,a.y),n+=1;break;case"v":case"V":a.y=Number.parseFloat(r[n+1]),e.lineTo(a.x,a.y),n+=1;break;case"c":case"C":a.x=Number.parseFloat(r[n+5]),a.y=Number.parseFloat(r[n+6]),e.bezierCurveTo(Number.parseFloat(r[n+1]),Number.parseFloat(r[n+2]),Number.parseFloat(r[n+3]),Number.parseFloat(r[n+4]),Number.parseFloat(r[n+5]),Number.parseFloat(r[n+6])),n+=6;break;case"s":case"S":e.bezierCurveTo(a.x,a.y,Number.parseFloat(r[n+1]),Number.parseFloat(r[n+2]),Number.parseFloat(r[n+3]),Number.parseFloat(r[n+4])),a.x=Number.parseFloat(r[n+3]),a.y=Number.parseFloat(r[n+4]),n+=4;break;case"q":case"Q":a.x=Number.parseFloat(r[n+3]),a.y=Number.parseFloat(r[n+4]),e.quadraticCurveTo(Number.parseFloat(r[n+1]),Number.parseFloat(r[n+2]),Number.parseFloat(r[n+3]),Number.parseFloat(r[n+4])),n+=4;break;case"t":case"T":e.quadraticCurveTo(a.x,a.y,Number.parseFloat(r[n+1]),Number.parseFloat(r[n+2])),a.x=Number.parseFloat(r[n+1]),a.y=Number.parseFloat(r[n+2]),n+=2;break;case"a":case"A":break;case"z":case"Z":e.closePath()}},l=(t.paintPathGradient1=function(e,t,a,r,n){var i=t.paintInfo?t.paintInfo.colors:t.colors,o=t.paintInfo?t.paintInfo.intervals:t.intervals,l=t.paintInfo?t.paintInfo.gradientType:t.gradientType,u=t.paintInfo?t.paintInfo.textureIndex:t.textureIndex,h=a[0],d=a[1],f=a[2],p=a[3],y=void 0,v=[];if("stroke"!=n&&(e.save(),e.beginPath(),s(e,r),e.clip()),t.ctm&&e.transform(t.ctm[0],t.ctm[1],t.ctm[2],t.ctm[3],t.ctm[4],t.ctm[5]),0==l)switch(e.beginPath(),u){case 0:y=e.createLinearGradient(0,d,0,d+p),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f,p),e.fill()):(e.strokeStyle=y,e.rect(h,d,f,p),e.stroke());break;case 1:y=e.createLinearGradient(0,d+p,0,d),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f,p),e.fill()):(e.strokeStyle=y,e.rect(h,d,f,p),e.stroke());break;case 2:var g=e.createLinearGradient(0,d+p,0,d+p/2);y=e.createLinearGradient(0,d,0,d+p/2),v.push(y,g),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f,p/2),e.fill(),e.beginPath(),e.fillStyle=g,e.rect(h,d+p/2,f,p/2),e.fill()):(e.strokeStyle=y,e.rect(h,d,f,p/2),e.stroke(),e.beginPath(),e.strokeStyle=g,e.rect(h,d+p/2,f,p/2),e.stroke());break;case 3:var m=e.createLinearGradient(0,d+p/2,0,d+p);y=e.createLinearGradient(0,d+p/2,0,d),v.push(y,m),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f,p/2),e.fill(),e.beginPath(),e.fillStyle=m,e.rect(h,d+p/2,f,p/2),e.fill()):(e.strokeStyle=y,e.rect(h,d,f,p/2),e.stroke(),e.beginPath(),e.strokeStyle=m,e.rect(h,d+p/2,f,p/2),e.stroke())}else if(1==l)switch(e.beginPath(),u){case 0:y=e.createLinearGradient(h,0,h+f,0),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f,p),e.fill()):(e.strokeStyle=y,e.rect(h,d,f,p),e.stroke());break;case 1:y=e.createLinearGradient(h+f,0,h,0),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f,p),e.fill()):(e.strokeStyle=y,e.rect(h,d,f,p),e.stroke());break;case 2:var M=e.createLinearGradient(h+f,0,h+f/2,0);y=e.createLinearGradient(h,0,h+f/2,0),v.push(y,M),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f/2,p),e.fill(),e.beginPath(),e.fillStyle=M,e.rect(h+f/2,d,f/2,p),e.fill()):(e.strokeStyle=y,e.rect(h,d,f/2,p),e.stroke(),e.beginPath(),e.strokeStyle=M,e.rect(h+f/2,d,f/2,p),e.stroke());break;case 3:var b=e.createLinearGradient(h+f/2,0,h+f,0);y=e.createLinearGradient(h+f/2,0,h,0),v.push(y,b),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.rect(h,d,f/2,p),e.fill(),e.beginPath(),e.fillStyle=b,e.rect(h+f/2,d,f/2,p),e.fill()):(e.strokeStyle=y,e.rect(h,d,f/2,p),e.stroke(),e.beginPath(),e.strokeStyle=b,e.rect(h+f/2,d,f/2,p),e.stroke())}else if(2==l)switch(e.beginPath(),u){case 0:var x=Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),P=f*p/x,k=Math.sqrt(Math.pow(p,2)-Math.pow(P,2)),I=x-2*k,T=d-k,w=h+Math.sqrt(Math.abs(Math.pow(I,2)-Math.pow(k,2)));y=e.createLinearGradient(w,T,h+f,d+p),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.fillRect(h,d,f,p)):(e.strokeStyle=y,e.strokeRect(h,d,f,p));break;case 1:var N=Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),A=f*p/N,C=Math.sqrt(Math.pow(p,2)-Math.pow(A,2)),j=N-2*C,S=d-C,D=h+Math.sqrt(Math.abs(Math.pow(j,2)-Math.pow(C,2)));y=e.createLinearGradient(h+f,d+p,D,S),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.fillRect(h,d,f,p)):(e.strokeStyle=y,e.strokeRect(h,d,f,p));break;case 2:var z=f*p/Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),L=Math.sqrt(Math.pow(f,2)-Math.pow(z,2)),O=z*L/f,E=Math.sqrt(Math.pow(L,2)-Math.pow(O,2)),R=h+E,_=d+(p-O),U=h+(f-E),F=d+O,G=e.createLinearGradient(h,d,U,F);y=e.createLinearGradient(h+f,d+p,R,_),v.push(y,G),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.beginPath(),e.moveTo(h,d+p),e.lineTo(h+f,d+p),e.lineTo(h+f,d),e.closePath(),e.fill(),e.fillStyle=G,e.beginPath(),e.moveTo(h+f,d),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),e.fill()):(e.strokeStyle=y,e.beginPath(),e.moveTo(h,d+p),e.lineTo(h+f,d+p),e.lineTo(h+f,d),e.closePath(),e.stroke(),e.strokeStyle=G,e.beginPath(),e.moveTo(h+f,d),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),e.stroke());break;case 3:var Y=f*p/Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),V=Math.sqrt(Math.pow(f,2)-Math.pow(Y,2)),Q=Y*V/f,B=Math.sqrt(Math.pow(V,2)-Math.pow(Q,2)),W=h+B,Z=d+(p-Q),H=h+(f-B),q=d+Q,J=e.createLinearGradient(H,q,h,d);y=e.createLinearGradient(W,Z,h+f,d+p),v.push(y,J),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.beginPath(),e.moveTo(h,d+p),e.lineTo(h+f,d+p),e.lineTo(h+f,d),e.closePath(),e.fill(),e.fillStyle=J,e.beginPath(),e.moveTo(h+f,d),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),e.fill()):(e.strokeStyle=y,e.beginPath(),e.moveTo(h,d+p),e.lineTo(h+f,d+p),e.lineTo(h+f,d),e.closePath(),e.stroke(),e.strokeStyle=J,e.beginPath(),e.moveTo(h+f,d),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),e.stroke())}else if(3==l)switch(u){case 0:var X=Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),K=f*p/X,$=Math.sqrt(Math.pow(p,2)-Math.pow(K,2)),ee=X-2*$,te=d-$,ae=h+(f-Math.sqrt(Math.abs(Math.pow(ee,2)-Math.pow($,2))));y=e.createLinearGradient(ae,te,h,d+p),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.fillRect(h,d,f,p)):(e.strokeStyle=y,e.strokeRect(h,d,f,p));break;case 1:var re=Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),ne=f*p/re,ie=Math.sqrt(Math.pow(p,2)-Math.pow(ne,2)),oe=re-2*ie,se=d-ie,le=h+(f-Math.sqrt(Math.abs(Math.pow(oe,2)-Math.pow(ie,2))));y=e.createLinearGradient(h,d+p,le,se),v.push(y),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.fillRect(h,d,f,p)):(e.strokeStyle=y,e.strokeRect(h,d,f,p));break;case 2:var ce=f*p/Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),ue=Math.sqrt(Math.pow(f,2)-Math.pow(ce,2)),he=ce*ue/f,de=Math.sqrt(Math.pow(ue,2)-Math.pow(he,2)),fe=h+(f-de),pe=d+(p-he),ye=h+de,ve=d+he,ge=e.createLinearGradient(h+f,d,ye,ve);y=e.createLinearGradient(h,d+p,fe,pe),v.push(y,ge),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.beginPath(),e.moveTo(h+f,d+p),e.lineTo(h,d+p),e.lineTo(h,d),e.closePath(),e.fill(),e.fillStyle=ge,e.beginPath(),e.moveTo(h,d),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),e.fill()):(e.strokeStyle=y,e.beginPath(),e.moveTo(h+f,d+p),e.lineTo(h,d+p),e.lineTo(h,d),e.closePath(),e.stroke(),e.strokeStyle=ge,e.beginPath(),e.moveTo(h,d),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),e.stroke());break;case 3:var me=f*p/Math.sqrt(Math.pow(f,2)+Math.pow(p,2)),Me=Math.sqrt(Math.pow(f,2)-Math.pow(me,2)),be=me*Me/f,xe=Math.sqrt(Math.pow(Me,2)-Math.pow(be,2)),Pe=h+(f-xe),ke=d+(p-be),Ie=h+xe,Te=d+be,we=e.createLinearGradient(Ie,Te,h+f,d);y=e.createLinearGradient(Pe,ke,h,d+p),v.push(y,we),c(i,o,v),"stroke"!=n?(e.fillStyle=y,e.beginPath(),e.moveTo(h+f,d+p),e.lineTo(h,d+p),e.lineTo(h,d),e.closePath(),e.fill(),e.fillStyle=we,e.beginPath(),e.moveTo(h,d),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),e.fill()):(e.strokeStyle=y,e.beginPath(),e.moveTo(h+f,d+p),e.lineTo(h,d+p),e.lineTo(h,d),e.closePath(),e.stroke(),e.strokeStyle=we,e.beginPath(),e.moveTo(h,d),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),e.stroke())}else if(4==l){var Ne=void 0;switch(u){case 0:y=e.createLinearGradient(0,d,0,d+p),Ne=e.createLinearGradient(h,0,h+f,0),v.push(y,Ne),c(i,o,v),e.beginPath(),e.moveTo(h,d),e.lineTo(h+f,d+p),e.lineTo(h,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=y,e.fill()):(e.strokeStyle=y,e.stroke()),e.moveTo(h,d),e.lineTo(h+f,d+p),e.lineTo(h+f,d),e.closePath(),"stroke"!=n?(e.fillStyle=Ne,e.fill()):(e.strokeStyle=Ne,e.stroke());break;case 1:y=e.createLinearGradient(h+f,0,h,0),Ne=e.createLinearGradient(0,d,0,d+p),v.push(y,Ne),c(i,o,v),e.beginPath(),e.moveTo(h+f,d),e.lineTo(h,d+p),e.lineTo(h,d),e.closePath(),"stroke"!=n?(e.fillStyle=y,e.fill()):(e.strokeStyle=y,e.stroke()),e.beginPath(),e.moveTo(h+f,d),e.lineTo(h,d+p),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=Ne,e.fill()):(e.strokeStyle=Ne,e.stroke());break;case 2:y=e.createLinearGradient(0,d+p,0,d),Ne=e.createLinearGradient(h,0,h+f,0),v.push(y,Ne),c(i,o,v),e.beginPath(),e.moveTo(h,d+p),e.lineTo(h+f,d),e.lineTo(h,d),e.closePath(),"stroke"!=n?(e.fillStyle=y,e.fill()):(e.strokeStyle=y,e.stroke()),e.beginPath(),e.moveTo(h,d+p),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=Ne,e.fill()):(e.strokeStyle=Ne,e.stroke());break;case 3:y=e.createLinearGradient(h+f,0,h,0),Ne=e.createLinearGradient(0,d+p,0,d),v.push(y,Ne),c(i,o,v),e.beginPath(),e.moveTo(h+f,d+p),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=y,e.fill()):(e.strokeStyle=y,e.stroke()),e.beginPath(),e.moveTo(h+f,d+p),e.lineTo(h,d),e.lineTo(h+f,d),e.closePath(),"stroke"!=n?(e.fillStyle=Ne,e.fill()):(e.strokeStyle=Ne,e.stroke());break;case 6:var Ae,Ce;y=e.createLinearGradient(h+f/2,0,h,0),Ne=e.createLinearGradient(0,d+p/2,0,d),Ae=e.createLinearGradient(h+f/2,0,h+f,0),Ce=e.createLinearGradient(0,d+p/2,0,d+p),v.push(y,Ne,Ae,Ce),c(i,o,v),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=y,e.fill()):(e.strokeStyle=y,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h+f,d),e.lineTo(h,d),e.closePath(),"stroke"!=n?(e.fillStyle=Ne,e.fill()):(e.strokeStyle=Ne,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=Ae,e.fill()):(e.strokeStyle=Ae,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h,d+p),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=Ce,e.fill()):(e.strokeStyle=Ce,e.stroke())}}else if(5==l){var je=void 0,Se=void 0,De=void 0;switch(u){case 0:y=e.createLinearGradient(h+f/2,0,h,0),je=e.createLinearGradient(0,d+p/2,0,d),Se=e.createLinearGradient(h+f/2,0,h+f,0),De=e.createLinearGradient(0,d+p/2,0,d+p),v.push(y,je,Se,De),c(i,o,v),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=y,e.fill()):(e.strokeStyle=y,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h+f,d),e.lineTo(h,d),e.closePath(),"stroke"!=n?(e.fillStyle=je,e.fill()):(e.strokeStyle=je,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=Se,e.fill()):(e.strokeStyle=Se,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h,d+p),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=De,e.fill()):(e.strokeStyle=De,e.stroke());break;case 1:y=e.createLinearGradient(h,0,h+f/2,0),je=e.createLinearGradient(0,d,0,d+p/2),Se=e.createLinearGradient(h+f,0,h+f/2,0),De=e.createLinearGradient(0,d+p,0,d+p/2),v.push(y,je,Se,De),c(i,o,v),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h,d),e.lineTo(h,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=y,e.fill()):(e.strokeStyle=y,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h+f,d),e.lineTo(h,d),e.closePath(),"stroke"!=n?(e.fillStyle=je,e.fill()):(e.strokeStyle=je,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h+f,d),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=Se,e.fill()):(e.strokeStyle=Se,e.stroke()),e.beginPath(),e.moveTo(h+f/2,d+p/2),e.lineTo(h,d+p),e.lineTo(h+f,d+p),e.closePath(),"stroke"!=n?(e.fillStyle=De,e.fill()):(e.strokeStyle=De,e.stroke())}}"stroke"!=n&&e.restore()},t.paintPathGradient2=function(e,t,a,r,n){var i=t.paintInfo?t.paintInfo.colors:t.colors,o=t.paintInfo?t.paintInfo.intervals:t.intervals,h=t.paintInfo?t.paintInfo.gradientType:t.gradientType,d=t.paintInfo?t.paintInfo.direction:t.direction,f=a[0],p=a[1],y=a[2],v=a[3],g=void 0,m=[];if("stroke"!=n&&(e.save(),e.beginPath(),s(e,r),e.clip()),t.ctm&&e.transform(t.ctm[0],t.ctm[1],t.ctm[2],t.ctm[3],t.ctm[4],t.ctm[5]),0==h){var M=t.paintInfo?t.paintInfo.angle:t.angle,b=void 0,x=void 0,P=void 0,k=void 0;e.beginPath(),90==M||270==M?(g=e.createLinearGradient(0,p,0,p+v),m.push(g),c(i,o,m),e.translate(f+y/2,p+v/2),e.rotate((M-90)*Math.PI/180),e.translate(-(f+y/2),-(p+v/2)),e.rect(f,p,y,v)):(g=e.createLinearGradient(f,0,f+y,0),m.push(g),c(i,o,m),e.translate(f+y/2,p+v/2),e.rotate(M*Math.PI/180),e.translate(-(f+y/2),-(p+v/2)),b=l(y/2,v/2,M%180),x=l(y/2,-v/2,M%180),P=Math.max(Math.abs(b[0]),Math.abs(x[0]),y/2),k=Math.max(Math.abs(b[1]),Math.abs(x[1]),v/2),e.rect(f+y/2-P,p+v/2-k,2*P,2*k)),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke())}else if(1==h){var I=Math.abs(v>y?y:v);switch(e.beginPath(),d){case 4:g=e.createRadialGradient(f+y,p+v,I/8,f+y,p+v,I);break;case 5:g=e.createRadialGradient(f,p+v,I/8,f,p+v,I);break;case 6:g=e.createRadialGradient(f+y/2,p+v/2,I/100,f+y/2,p+v/2,I);break;case 7:g=e.createRadialGradient(f+y,p,I/8,f+y,p,I);break;case 8:g=e.createRadialGradient(f,p,I/8,f,p,I)}m.push(g),u(i,o,m),e.rect(f,p,y,v),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke())}else if(2==h){var T=void 0;switch(d){case 4:g=e.createLinearGradient(f+y,0,f,0),T=e.createLinearGradient(0,p+v,0,p),m.push(g,T),u(i,o,m),e.beginPath(),e.moveTo(f+y,p+v),e.lineTo(f,p),e.lineTo(f,p+v),e.closePath(),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke()),e.beginPath(),e.moveTo(f+y,p+v),e.lineTo(f,p),e.lineTo(f+y,p),e.closePath(),"stroke"!=n?(e.fillStyle=T,e.fill()):(e.strokeStyle=T,e.stroke());break;case 5:g=e.createLinearGradient(0,p+v,0,p),T=e.createLinearGradient(f,0,f+y,0),m.push(g,T),u(i,o,m),e.beginPath(),e.moveTo(f,p+v),e.lineTo(f+y,p),e.lineTo(f,p),e.closePath(),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke()),e.beginPath(),e.moveTo(f,p+v),e.lineTo(f+y,p),e.lineTo(f+y,p+v),e.closePath(),"stroke"!=n?(e.fillStyle=T,e.fill()):(e.strokeStyle=T,e.stroke());break;case 6:var w,N;g=e.createLinearGradient(f+y/2,0,f,0),T=e.createLinearGradient(0,p+v/2,0,p),w=e.createLinearGradient(f+y/2,0,f+y,0),N=e.createLinearGradient(0,p+v/2,0,p+v),m.push(g,T,w,N),u(i,o,m),e.beginPath(),e.moveTo(f+y/2,p+v/2),e.lineTo(f,p),e.lineTo(f,p+v),e.closePath(),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke()),e.beginPath(),e.moveTo(f+y/2,p+v/2),e.lineTo(f+y,p),e.lineTo(f,p),e.closePath(),"stroke"!=n?(e.fillStyle=T,e.fill()):(e.strokeStyle=T,e.stroke()),e.beginPath(),e.moveTo(f+y/2,p+v/2),e.lineTo(f+y,p),e.lineTo(f+y,p+v),e.closePath(),"stroke"!=n?(e.fillStyle=w,e.fill()):(e.strokeStyle=w,e.stroke()),e.beginPath(),e.moveTo(f+y/2,p+v/2),e.lineTo(f,p+v),e.lineTo(f+y,p+v),e.closePath(),"stroke"!=n?(e.fillStyle=N,e.fill()):(e.strokeStyle=N,e.stroke());break;case 7:g=e.createLinearGradient(f+y,0,f,0),T=e.createLinearGradient(0,p,0,p+v),m.push(g,T),u(i,o,m),e.beginPath(),e.moveTo(f+y,p),e.lineTo(f,p+v),e.lineTo(f,p),e.closePath(),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke()),e.beginPath(),e.moveTo(f+y,p),e.lineTo(f,p+v),e.lineTo(f+y,p+v),e.closePath(),"stroke"!=n?(e.fillStyle=T,e.fill()):(e.strokeStyle=T,e.stroke());break;case 8:g=e.createLinearGradient(0,p,0,p+v),T=e.createLinearGradient(f,0,f+y,0),m.push(g,T),u(i,o,m),e.beginPath(),e.moveTo(f,p),e.lineTo(f+y,p+v),e.lineTo(f,p+v),e.closePath(),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke()),e.beginPath(),e.moveTo(f,p),e.lineTo(f+y,p+v),e.lineTo(f+y,p),e.closePath(),"stroke"!=n?(e.fillStyle=T,e.fill()):(e.strokeStyle=T,e.stroke())}}else if(3==h){var A=Math.abs(v>y?y:v);e.beginPath(),g=e.createRadialGradient(f+y/2,p+v/2,A/600,f+y/2,p+v/2,A),m.push(g),u(i,o,m),e.rect(f,p,y,v),"stroke"!=n?(e.fillStyle=g,e.fill()):(e.strokeStyle=g,e.stroke())}"stroke"!=n&&e.restore()},t.getRotatePosition=function(e,t,a){var r=new Array;return r[0]=e*Math.cos(a*Math.PI/180)+t*Math.sin(a*Math.PI/180),r[1]=-e*Math.sin(a*Math.PI/180)+t*Math.cos(a*Math.PI/180),r}),c=t.checkWordOrExcelToAddColorStopHandle=function(e,t,a){if(e.length>t.length)for(var r=0,n=0;n<e.length&&!(r>1);n++){var i=!0,o=!1,s=void 0;try{for(var l,c=a[Symbol.iterator]();!(i=(l=c.next()).done);i=!0)l.value.addColorStop(r,"rgba("+e[n].r+","+e[n].g+","+e[n].b+","+(e[n].alpha/255||.01)+")")}catch(e){o=!0,s=e}finally{try{!i&&c.return&&c.return()}finally{if(o)throw s}}r+=Math.round(10*t[n])/10}else for(var u=0;u<e.length;u++){var h=!0,d=!1,f=void 0;try{for(var p,y=a[Symbol.iterator]();!(h=(p=y.next()).done);h=!0)p.value.addColorStop(t[u],"rgba("+e[u].r+","+e[u].g+","+e[u].b+","+(e[u].alpha/255||.01)+")")}catch(e){d=!0,f=e}finally{try{!h&&y.return&&y.return()}finally{if(d)throw f}}}},u=t.addColorStopHandle=function(e,t,a){for(var r=0;r<e.length;r++){var n=!0,i=!1,o=void 0;try{for(var s,l=a[Symbol.iterator]();!(n=(s=l.next()).done);n=!0)s.value.addColorStop(t[r],"rgba("+e[r].r+","+e[r].g+","+e[r].b+","+(e[r].alpha/255||.01)+")")}catch(e){i=!0,o=e}finally{try{!n&&l.return&&l.return()}finally{if(i)throw o}}}}},2812:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}();t.Animation=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.randomList=[2,19,26,9,1,18,8,15,23,21,20,30,38,25,4,6,29,14,11,7,12,0,5,10,13,17,22,33],this.pgReader=t,this._$Status=Object.create(null),this._$Status.other=Object.create(null),this.u_presentation=Object.create(null),this.u_presentation._$U_A_Cons=Object.create(null)}return a(e,[{key:"play",value:function(e,t){var a=e.slide,r=e.animInfo,n=e.shape;if(n)try{this.animHandler(this._$Status,n,t,e.count,r.animIdx,r.animType,r.endTime,r.direct,r.emphAttr,a,r.textInfo,r.oldParaIdx,r.paraIdx)}catch(e){console.error(e)}}},{key:"animHandler",value:function(e,t,a,r,n,i,o,s,l,c,u,h,d){var f,p=t.shapeRect,y=t.textRect,v=p.x,g=p.y,m=p.width,M=p.height,b=y.x,x=y.y,P=y.width,k=y.height,I=this.pgReader.doc.pagesize.width,T=this.pgReader.doc.pagesize.height,w=e.other.animDis,N=(this.u_presentation,this);if(0!=i)return 1==i?16==n?U():24==n?(a.globalAlpha=r<=30*o?1-1/(30*o)*r:1/(30*o)*(r-30*o),void(l&&r<60*o&&(a.fillStyle="rgb("+l.r+","+l.g+","+l.b+")",a.strokeStyle=h<=0?"#000001":"#000002"))):23==n?(u?a.translate(u[0]+u[2]/2,u[1]+u[3]/2):a.translate(Y(t).x,Y(t).y),r<=60*o/8?a.rotate(-Math.PI/45/(60*o/8)*r):r>60*o/8&&r<=60*o*3/8?a.rotate(-Math.PI/45+Math.PI/45/(60*o/4)*(r-60*o/8)):r>60*o*3/8&&r<=60*o*5/8?a.rotate(Math.PI/45-Math.PI/45/(60*o/4)*(r-60*o*3/8)):r>60*o*5/8&&r<=60*o*7/8?a.rotate(-Math.PI/45+Math.PI/45/(60*o/4)*(r-60*o*5/8)):a.rotate(Math.PI/45-Math.PI/45/(60*o/8)*(r-60*o*7/8)),void(u?a.translate(-u[0]-u[2]/2,-u[1]-u[3]/2):a.translate(-Y(t).x,-Y(t).y))):8==n?function(){if(u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var l=e+n/2,c=t+i/2,h=s[0],d=s[1];switch(a.translate(l,c),h){case 0:a.rotate(Math.PI*(d/180)/(60*o)*r);break;case 1:a.rotate(-Math.PI*(d/180)/(60*o)*r)}a.translate(-l,-c)}():0==n?function(){if(u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var l=e+n/2,c=t+i/2,h=s[0],d=s[1];switch(h){case 0:a.translate(l-l*(1-(1-d)/(60*o)*r),0),a.scale(1-(1-d)/(60*o)*r,1);break;case 1:a.translate(0,c-c*(1-(1-d)/(60*o)*r)),a.scale(1,1-(1-d)/(60*o)*r);break;case 2:a.translate(l-l*(1-(1-d)/(60*o)*r),c-c*(1-(1-d)/(60*o)*r)),a.scale(1-(1-d)/(60*o)*r,1-(1-d)/(60*o)*r)}}():12==n?(A=l.br,C=l.bg,j=l.bb,S=l.fr,D=l.fb,z=l.fg,L=Math.floor((C-z)/(60*o)*r+z),O=Math.floor((A-S)/(60*o)*r+S),E=Math.floor((j-D)/(60*o)*r+D),a.fillStyle="rgb("+O+", "+L+", "+E+")",void(a.strokeStyle=d<0?"#000001":"#000002")):19==n?function(){var e=l.br,t=l.bg,n=l.bb,i=l.fr,s=l.fb,c=l.fg,u=Math.floor((t-c)/(60*o)*r+c),h=Math.floor((e-i)/(60*o)*r+i),f=Math.floor((n-s)/(60*o)*r+s);a.fillStyle="rgb("+h+", "+u+", "+f+")",a.strokeStyle=d<0?"#000002":"#000001"}():9==n?function(){var e=l.br,t=l.bg,n=l.bb,i=l.fr,s=l.fb,c=l.fg,u=Math.floor((t-c)/(60*o)*r+c),h=Math.floor((e-i)/(60*o)*r+i),f=Math.floor((n-s)/(60*o)*r+s);a.fillStyle="rgb("+h+", "+u+", "+f+")",a.strokeStyle=d<0?"#000002":"#000001"}():7==n?(s[1]>=1&&(s[1]=1),void(a.globalAlpha=1-s[1]/(60*o)*r)):17==n?function(){var e=l.br,t=l.bg,n=l.bb,i=l.fr,s=l.fb,c=l.fg,u=Math.floor((t-c)/(60*o)*r+c),h=Math.floor((e-i)/(60*o)*r+i),f=Math.floor((n-s)/(60*o)*r+s);a.fillStyle="rgb("+h+", "+u+", "+f+")",a.strokeStyle=d<0?"#000002":"#000001"}():10==n?function(){var e=l.br,t=l.bg,n=l.bb,i=l.fr,s=l.fb,c=l.fg,u=Math.floor((t-c)/(60*o)*r+c),h=Math.floor((e-i)/(60*o)*r+i),f=Math.floor((n-s)/(60*o)*r+s);a.fillStyle="rgb("+h+", "+u+", "+f+")",a.strokeStyle=d<0?"#000002":"#000001"}():2==n?function(){var e=l.r,t=l.g,r=l.b;a.fillStyle="rgb("+e+", "+t+", "+r+")",a.strokeStyle="#00000b"}():1==n?function(){var e=l.r,t=l.g,n=l.b;a.globalAlpha=1/(60*o)*r,a.fillStyle="rgb("+e+", "+t+", "+n+")"}():18==n?void(a.strokeStyle=r<60*o*.6&&r>60*o*.5?"#000004":"#000000"):5==n?function(){var e=l.r,t=l.g,n=l.b;a.globalAlpha=1/(60*o)*r,a.fillStyle="rgb("+e+", "+t+", "+n+")",a.strokeStyle="#000001"}():U():2==i?17==n?void(a.globalAlpha=0):19==n?F():2==n?function(){0==r&&(w=G(),e.other.animDis=w);var t=w[0],n=w[1];switch(e.other.addTimes&&e.other.addTimes!=r&&(t=-t*e.other.addTimes,n=-n*e.other.addTimes),r!=60*o||e.other.addTimes||(t=-t*r,n=-n*r),s){case 0:a.translate(0,n/(60*o)*r);break;case 1:a.translate(-t/(60*o)*r,0);break;case 2:a.translate(t/(60*o)*r,0);break;case 3:a.translate(0,-n/(60*o)*r);break;case 4:a.translate(-t/(60*o)*r,n/(60*o)*r);break;case 5:a.translate(t/(60*o)*r,n/(60*o)*r);break;case 6:a.translate(-t/(60*o)*r,-n/(60*o)*r);break;case 7:a.translate(t/(60*o)*r,-n/(60*o)*r)}}():31==n?(a.globalAlpha=1-1/(60*o)*r,e.other.addTimes&&e.other.addTimes!=r&&a.translate(0,-250/(60*o)*e.other.addTimes),r!=60*o||e.other.addTimes||a.translate(0,-250),void(!e.other.addTimes&&a.translate(0,250/(60*o)*r))):8==n?function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;switch(r=60*o-r,s){case 1:a.rect(e-.5,t-.5,n+1,i/2/(60*o)*r+.001),a.rect(e-.5,t+i-i/2/(60*o)*r-.5,n+1,i/2/(60*o)*r+.001);break;case 0:a.rect(e-.5,t+i/2-i/2/(60*o)*r,n+1,i/(60*o)*r+.001);break;case 3:a.rect(e+.5,t-.5,n/2/(60*o)*r+.001,i+1),a.rect(e+n-n/2/(60*o)*r-.5,t-.5,n/2/(60*o)*r+.001,i+1);break;case 2:a.rect(e+n/2-n/2/(60*o)*r,t-.5,n/(60*o)*r+.001,i+1)}a.clip()}():1==n?function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;switch(r=60*o-r,s){case 3:a.rect(e-.5,t+i-i/(60*o)*r+1,n+1,i/(60*o)*r+1);break;case 2:a.rect(e-.5,t-.5,n/(60*o)*r-1,i+1);break;case 1:a.rect(e+n-n/(60*o)*r-.5,t-.5,n/(60*o)*r,i+1);break;case 0:a.rect(e-.5,t-1.5,n+1,i/(60*o)*r)}a.clip()}():18==n?function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var s=e+n/2,l=t+i/2,c=n/Math.sqrt(2),h=i/Math.sqrt(2);a.rect(e-.5,t-.5,n+1,i+1),a.clip(),a.beginPath(),a.ellipse(s,l,c-c/(60*o)*r,h-h/(60*o)*r,0,0,2*Math.PI),a.clip()}():7==n?function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var l=e+n/2,c=t+i/2,h=n>i?n:i;switch(r=60*o-r,s){case 0:a.moveTo(l,c),a.arc(l,c,h,1.5*Math.PI,1.5*Math.PI-2*Math.PI/(60*o)*r,!0);break;case 1:a.moveTo(l,c),a.arc(l,c,h,.5*Math.PI,.5*Math.PI-Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1.5*Math.PI,1.5*Math.PI-Math.PI/(60*o)*r,!0);break;case 2:a.moveTo(l,c),a.arc(l,c,h,Math.PI/6,Math.PI/6-2/3*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,5*Math.PI/6,5*Math.PI/6-2/3*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1.5*Math.PI,1.5*Math.PI-2/3*Math.PI/(60*o)*r,!0);break;case 3:a.moveTo(l,c),a.arc(l,c,h,0*Math.PI,0*Math.PI-.5*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,.5*Math.PI,.5*Math.PI-.5*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1*Math.PI,1*Math.PI-.5*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1.5*Math.PI,1.5*Math.PI-.5*Math.PI/(60*o)*r,!0);break;case 4:a.moveTo(l,c),a.arc(l,c,h,.25*Math.PI,.25*Math.PI-.25*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,.5*Math.PI,.5*Math.PI-.25*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,.75*Math.PI,.75*Math.PI-.25*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1*Math.PI,1*Math.PI-.25*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1.25*Math.PI,1.25*Math.PI-.25*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1.5*Math.PI,1.5*Math.PI-.25*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,1.75*Math.PI,1.75*Math.PI-.25*Math.PI/(60*o)*r,!0),a.moveTo(l,c),a.arc(l,c,h,2*Math.PI,2*Math.PI-.25*Math.PI/(60*o)*r,!0)}a.clip()}():14==n?function(){var e=30,t=[0,8,4,5,2,9,3,6,7,1],n=[5,4,6,3,7,2,8,1,9,0],i=[9,1,2,8,0,7,3,5,4,6];if(a.beginPath(),u)var l=u[0],c=u[1],h=u[2],d=u[3];else l=v,c=g,h=m,d=M;switch(s){case 1:for(var f=10-Math.floor(r/(6*o)),p=0;p<e;p++)if(p%3==0)for(var y=0;y<f;y++)a.rect(l+p*h/e+t[y]*h/300,c,h/300,d);else if(p%3==1)for(var b=0;b<f;b++)a.rect(l+p*h/e+n[b]*h/300,c,h/300,d);else for(var x=0;x<f;x++)a.rect(l+p*h/e+i[x]*h/300,c,h/300,d);break;case 0:for(var P=10-Math.floor(r/(6*o)),k=0;k<e;k++)if(k%3==0)for(var I=0;I<P;I++)a.rect(l,c+k*d/e+t[I]*d/300,h,d/300);else if(k%3==1)for(var T=0;T<P;T++)a.rect(l,c+k*d/e+n[T]*d/300,h,d/300);else for(var w=0;w<P;w++)a.rect(l,c+k*d/e+i[w]*d/300,h,d/300)}a.clip()}():21==n?function(){if(u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var s=e+n/2,l=t+i/2;a.globalAlpha=1-1/(60*o)*r,a.translate(s*(1-(1-1/(60*o)*r)),l*(1-(1-1/(60*o)*r))),a.scale(1-1/(60*o)*r,1-1/(60*o)*r)}():24==n?function(){a.globalAlpha=1-1/(60*o)*r;var e=Y(t).x,n=Y(t).y;a.translate(e,n),a.rotate(Math.PI/4/(60*o)*r),a.translate(-e,-n),a.translate(e*(1-(1-.75/(60*o)*r)),n*(1-(1-.75/(60*o)*r))),a.scale(1-.75/(60*o)*r,1-.75/(60*o)*r)}():20==n?function(){var e;if(a.globalAlpha=1-1/(60*o)*r,u){var n=u[0];u[1];e=n+u[2]/2,u[3]}else e=Y(t).x,Y(t).y;r<=30*o?a.transform(1-2/(30*o)*r,0,0,1,e*(1-(1-2/(30*o)*r)),0):a.transform(2/(30*o)*(r-30*o)-1,0,0,1,e*(1-(2/(30*o)*(r-30*o)-1)),0)}():35==n?function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;for(var l=t+i/2,c=s[1].pathPoints,h=[],d=0;d<c.length;d++)h.push([c[d],c[d+1]]),d++;var f=h.length,p=f/(60*o),y=Math.floor(p*r),b=p*r-y;if(p*r<f&&y<f-1){var x=[h[y][0],h[y][1]],P=[h[y+1][0],h[y+1][1]];a.translate(x[0]+(P[0]-x[0])*b,x[1]+(P[1]-x[1])*b),r>1&&y<f-1&&h[y][1]>=h[y+1][1]&&h[y][1]>=h[y-1][1]&&(a.translate(0,.2*l),a.scale(1,.8))}else a.globalAlpha=0;a.globalAlpha&&a.rect(e-.5,t-.5+i/(f/p)*r+1,n+1,i-i/(f/p)*r+1),a.clip()}():27==n?(a.globalAlpha=1-1/(60*o)*r,e.other.addTimes&&e.other.addTimes!=r&&a.translate(0,-100/(60*o)*e.other.addTimes),r!=60*o||e.other.addTimes||a.translate(0,-100),void(!e.other.addTimes&&a.translate(0,-50/(60*o)*r))):0==n?function(){switch(a.beginPath(),s){case 0:for(var e=0;e<=5;e++)a.rect(v,g+M/6*e,m,M/6/(60*o)*(60*o-r));break;case 1:for(var t=0;t<=5;t++)a.rect(v+m/6*t,g,m/6/(60*o)*(60*o-r),M)}a.clip()}():4==n?function(){switch(a.beginPath(),s){case 0:a.translate(0,(a.canvas.height/2-g+M)/(60*o)*r),a.rect(v,g,m,M);break;case 1:a.translate(-(v+m)/(60*o)*r,0),a.rect(v,g,m,M);break;case 2:a.translate((a.canvas.width/2-v+m)/(60*o)*r,0),a.rect(v,g,m,M);break;case 3:a.translate(0,-(g+M)/(60*o)*r),a.rect(v,g,m,M)}a.clip()}():5==n?function(){var e=Math.floor(20-r/(3*o));switch(a.beginPath(),s){case 2:if(e<=10)for(var t=0;t<=20;t++)t<=2*e&&a.rect(v+t*m/20,g,m/20,(2*e-t)*M/20);else for(var n=0;n<=20;n++)n<2*(e-10)?a.rect(v+n*m/20,g,m/20,M):a.rect(v+n*m/20,g,m/20,M-M*(n+20-2*e)/20);break;case 0:if(e<=10)for(var i=0;i<=20;i++)i<=2*e&&a.rect(v+m-i*m/20,g,m/20,(2*e-i)*M/20);else for(var l=0;l<=20;l++)l<2*(e-10)?a.rect(v+m-l*m/20,g,m/20,M):a.rect(v+m-l*m/20,g,m/20,M-M*(l+20-2*e)/20);break;case 3:if(e<=10)for(var c=0;c<=20;c++)c<=2*e&&a.rect(v+c*m/20,g+M-(2*e-c)*M/20,m/20,(2*e-c)*M/20);else for(var u=0;u<=20;u++)u<2*(e-10)?a.rect(v+u*m/20,g,m/20,M):a.rect(v+u*m/20,g+M*(u+20-2*e)/20,m/20,M-M*(u+20-2*e)/20);break;case 1:if(e<=10)for(var h=0;h<=20;h++)h<=2*e&&a.rect(v+m-h*m/20,g+M-(2*e-h)*M/20,m/20,(2*e-h)*M/20);else for(var d=0;d<=20;d++)d<2*(e-10)?a.rect(v+m-d*m/20,g,m/20,M):a.rect(v+m-d*m/20,g+M*(d+20-2*e)/20,m/20,M-M*(d+20-2*e)/20)}a.clip()}():9==n?function(){switch(r=60*o-r,a.beginPath(),s){case 0:for(var e=0;e<=11;e++)for(var t=0;t<=5;t++)(e+t)%2==0?30*o-r>0?a.rect(v+m/12*e,g+M/6*t,m/12/(30*o)*r,M/6):a.rect(v+m/12*e,g+M/6*t,m/12,M/6):30*o-r<0&&a.rect(v+m/12*e,g+M/6*t,m/12/(30*o)*(r-30*o),M/6);break;case 1:for(var n=0;n<=5;n++)for(var i=0;i<=11;i++)(n+i)%2==0?30*o-r>0?a.rect(v+m/6*n,g+M/12*i,m/6,M/12/(30*o)*r):a.rect(v+m/6*n,g+M/12*i,m/6,M/12):30*o-r<0&&a.rect(v+m/6*n,g+M/12*i,m/6,M/12/(30*o)*(r-30*o))}a.clip()}():12==n?function(){var e=v+m/2,t=g+M/2;a.beginPath(),a.moveTo(e,t),a.arc(e,t,(M+m)/2,.5*-Math.PI+Math.PI/(60*o)*r,1.5*Math.PI-Math.PI/(60*o)*r),a.closePath(),a.clip()}():16==n?function(){var e=Math.floor(20-r/(3*o)),t=Math.floor(m/60)+1,n=Math.floor(M/20)+1;a.beginPath();for(var i=0;i<t;i++)for(var s=0;s<n;s++)for(var l=1;l<=e;l++)switch(l){case 1:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 2:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 3:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 4:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 5:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 6:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 7:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 8:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 9:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 10:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 11:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 12:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 13:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 14:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 15:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 16:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 17:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 18:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 19:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 20:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*3,m/t/5,M/n/4)}a.clip()}():6==n?function(){var e=60*o-r;switch(a.beginPath(),s){case 0:a.moveTo(v+m/2-m/(60*o)*e,g+M/2),a.lineTo(v+m/2,g+M/2-M/(60*o)*e),a.lineTo(v+m-m/2+m/(60*o)*e,g+M/2),a.lineTo(v+m/2,g+M-M/2+M/(60*o)*e),a.lineTo(v+m/2-m/(60*o)*e,g+M/2),a.closePath();break;case 1:e/(60*o)<.5?(a.moveTo(v,g),a.lineTo(v+m/(60*o)*e,g),a.lineTo(v,g+M/(60*o)*e),a.lineTo(v,g),a.closePath(),a.moveTo(v+m,g),a.lineTo(v+m-m/(60*o)*e,g),a.lineTo(v+m,g+M/(60*o)*e),a.lineTo(v+m,g),a.closePath(),a.moveTo(v,g+M),a.lineTo(v,g+M-M/(60*o)*e),a.lineTo(v+m/(60*o)*e,g+M),a.lineTo(v,g+M),a.closePath(),a.moveTo(v+m,g+M),a.lineTo(v+m,g+M-M/(60*o)*e),a.lineTo(v+m-m/(60*o)*e,g+M),a.lineTo(v+m,g+M),a.closePath()):(a.moveTo(v,g),a.lineTo(v,g+M),a.lineTo(v+m,g+M),a.lineTo(v+m,g),a.lineTo(v,g),a.moveTo(v+m/2,g-M/2+M/(60*o)*e),a.lineTo(v+1.5*m-m/(60*o)*e,g+M/2),a.lineTo(v+m/2,g+1.5*M-M/(60*o)*e),a.lineTo(v-m/2+m/(60*o)*e,g+M/2),a.lineTo(v+m/2,g-M/2+M/(60*o)*e),a.closePath())}a.clip()}():10==n?function(){var e=60*o-r;switch(a.beginPath(),s){case 0:a.translate(0,M-M/(60*o)*e),a.rect(v,g,m,M/(60*o)*e);break;case 1:a.translate(m/(60*o)*e-m,0),a.rect(v+m-m/(60*o)*e,g,m/(60*o)*e,M);break;case 2:a.translate(m-m/(60*o)*e,0),a.rect(v,g,m/(60*o)*e,M);break;case 3:a.translate(0,M/(60*o)*e-M),a.rect(v,g+M-M/(60*o)*e,m,M/(60*o)*e)}a.clip()}():3==n?function(){var e=60*o-r;if(a.beginPath(),u)var t=u[0],n=u[1],i=u[2],l=u[3];switch(s){case 1:u?(a.rect(t,n,i,l/2/(60*o)*e),a.rect(t,n+l-l/2/(60*o)*e,i,l/2/(60*o)*e),a.rect(t,n,i/2/(60*o)*e,l),a.rect(t+i-i/2/(60*o)*e,n,i/2/(60*o)*e,l)):(a.rect(v-.5,g-.5,m+1,M/2/(60*o)*e+1),a.rect(v-.5,g+M-M/2/(60*o)*e-.5,m+1,M/2/(60*o)*e+1),a.rect(v-.5,g-.5,m/2/(60*o)*e+1,M+1),a.rect(v+m-m/2/(60*o)*e-.5,g-.5,m/2/(60*o)*e+1,M+1));break;case 0:u?a.rect(t+i/2*(1-1/(60*o)*e),n+l/2*(1-1/(60*o)*e),i/(60*o)*e,l/(60*o)*e):a.rect(v+m/2*(1-1/(60*o)*e),g+M/2*(1-1/(60*o)*e),m/(60*o)*e,M/(60*o)*e)}a.clip()}():11==n?void(r!==30*o&&r!==60*o||(a.globalAlpha=0)):F():3==i?function(){a.beginPath();for(var e=s[1].pathPoints,n=[],i=0;i<e.length;i++)n.push([e[i],e[i+1]]),i++;u&&t&&a.translate(Y(t).x-(u[0]+u[2]/2),Y(t).y-(u[1]+u[3]/2));var l=n.length,c=l/(60*o);if(c*r<l&&Math.floor(c*r)<l-1){var h=Math.floor(c*r),d=c*r-h,f=[n[h][0],n[h][1]],p=[n[h+1][0],n[h+1][1]];a.translate(f[0]+(p[0]-f[0])*d,f[1]+(p[1]-f[1])*d)}else a.translate(n[l-1][0],n[l-1][1])}():void 0;if(2!=n)if(19!=n){if(3==n)return function(){0==r&&(w=G(),e.other.animDis=w);var t=w[0],n=w[1];switch(s){case 0:r?a.translate(0,n-n/(60*o)*r):a.translate(0,n);break;case 1:r?a.translate(t/(60*o)*r-t,0):a.translate(-t,0);break;case 2:r?a.translate(t-t/(60*o)*r,0):a.translate(t,0);break;case 3:r?a.translate(0,n/(60*o)*r-n):a.translate(0,-n);break;case 4:r?a.translate(t/(60*o)*r-t,n-n/(60*o)*r):a.translate(-t,n);break;case 5:r?a.translate(t-t/(60*o)*r,n-n/(60*o)*r):a.translate(t,n);break;case 6:r?a.translate(t/(60*o)*r-t,n/(60*o)*r-n):a.translate(-t,-n);break;case 7:r?a.translate(t-t/(60*o)*r,n/(60*o)*r-n):a.translate(t,-n)}}();if(26==n)return f=100,e.other.addTimes&&e.other.addTimes!=r&&a.translate(0,(f-f/(60*o)*r)*(60*o-e.other.addTimes)),a.globalAlpha=1/(60*o)*r,void(r?e.other.addTimes||a.translate(0,f-f/(60*o)*r):a.translate(0,f));if(9==n)return function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];switch(s){case 0:u?(a.rect(e,t,n,i/2/(60*o)*r),a.rect(e,t+i-i/2/(60*o)*r,n,i/2/(60*o)*r)):(a.rect(v-.5,g-.5,m+1,M/2/(60*o)*r+1),a.rect(v-.5,g+M-M/2/(60*o)*r-.5,m+1,M/2/(60*o)*r+1));break;case 1:u?a.rect(e,t+i/2-i/2/(60*o)*r,n,i/(60*o)*r):a.rect(v-.5,g+M/2-M/2/(60*o)*r-.5,m+1,M/(60*o)*r+1);break;case 2:u?(a.rect(e,t,n/2/(60*o)*r,i),a.rect(e+n-n/2/(60*o)*r,t,n/2/(60*o)*r,i)):(a.rect(v-.5,g-.5,m/2/(60*o)*r+1,M+1),a.rect(v+m-m/2/(60*o)*r-.5,g-.5,m/2/(60*o)*r+1,M+1));break;case 3:u?a.rect(e+n/2-n/2/(60*o)*r,t,n/(60*o)*r,i):a.rect(v+m/2-m/2/(60*o)*r-.5,g-.5,m/(60*o)*r+1,M+1)}a.clip()}();if(1==n)return function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];switch(s){case 0:u?a.rect(e,t+i-i/(60*o)*r,n,i/(60*o)*r):a.rect(v-.5,g+M-M/(60*o)*r-.5,m+1,M/(60*o)*r+1);break;case 1:u?a.rect(e,t,n/(60*o)*r,i):a.rect(v-.5,g-.5,m/(60*o)*r,M+1);break;case 2:u?a.rect(e+n-n/(60*o)*r,t,n/(60*o)*r,i):a.rect(v+m-m/(60*o)*r-.5,g-.5,m/(60*o)*r+1,M+1);break;case 3:u?a.rect(e,t,n,i/(60*o)*r):a.rect(v-.5,g-.5,m+1,M/(60*o)*r+1)}a.clip()}();if(18==n)return function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3],s=e+n/2,l=t+i/2,c=n/2,h=i/2;u?a.ellipse(s,l,(c*Math.sqrt(2)/(60*o)*r).toFixed(2),(h*Math.sqrt(2)/(60*o)*r).toFixed(2),0,0,2*Math.PI):a.ellipse(v+m/2,g+M/2,(m/Math.sqrt(2)/(60*o)*r).toFixed(2),(M/Math.sqrt(2)/(60*o)*r).toFixed(2),0,0,2*Math.PI),a.clip()}();if(8==n)return function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3],l=e+n/2,c=t+i/2,h=i>=n?i:n;else l=v+m/2,c=g+M/2,h=m>=M?m:M;switch(s){case 0:a.moveTo(l,c),a.arc(l,c,h,.5*-Math.PI,.5*-Math.PI+2*Math.PI/(60*o)*r,!1);break;case 1:a.moveTo(l,c),a.arc(l,c,h,.5*-Math.PI,.5*-Math.PI+Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,.5*Math.PI,.5*Math.PI+Math.PI/(60*o)*r,!1);break;case 2:a.moveTo(l,c),a.arc(l,c,h,.5*-Math.PI,.5*-Math.PI+2/3*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,Math.PI/6,Math.PI/6+2/3*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,5*Math.PI/6,5*Math.PI/6+2/3*Math.PI/(60*o)*r,!1);break;case 3:a.moveTo(l,c),a.arc(l,c,h,.5*-Math.PI,.5*-Math.PI+.5*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,0*Math.PI,0*Math.PI+.5*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,.5*Math.PI,.5*Math.PI+.5*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,1*Math.PI,1*Math.PI+.5*Math.PI/(60*o)*r,!1);break;case 4:a.moveTo(l,c),a.arc(l,c,h,0*Math.PI,0*Math.PI+.25*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,.25*Math.PI,.25*Math.PI+.25*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,.5*Math.PI,.5*Math.PI+.25*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,.75*Math.PI,.75*Math.PI+.25*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,1*Math.PI,1*Math.PI+.25*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,1.25*Math.PI,1.25*Math.PI+.25*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,1.5*Math.PI,1.5*Math.PI+.25*Math.PI/(60*o)*r,!1),a.moveTo(l,c),a.arc(l,c,h,1.75*Math.PI,1.75*Math.PI+.25*Math.PI/(60*o)*r,!1)}a.clip()}();if(15==n)return function(){var e=30,t=[0,8,4,5,2,9,3,6,7,1],n=[5,4,6,3,7,2,8,1,9,0],i=[9,1,2,8,0,7,3,5,4,6];if(a.beginPath(),u)var l=u[0],c=u[1],h=u[2],d=u[3];else l=v,c=g,h=m,d=M;switch(s){case 1:for(var f=Math.floor(r/(6*o)),p=0;p<e;p++)if(p%3==0)for(var y=0;y<f;y++)a.rect(l+p*h/e+t[y]*h/300,c,h/300,d);else if(p%3==1)for(var b=0;b<f;b++)a.rect(l+p*h/e+n[b]*h/300,c,h/300,d);else for(var x=0;x<f;x++)a.rect(l+p*h/e+i[x]*h/300,c,h/300,d);break;case 0:for(var P=Math.floor(r/(6*o)),k=0;k<e;k++)if(k%3==0)for(var I=0;I<P;I++)a.rect(l,c+k*d/e+t[I]*d/300,h,d/300);else if(k%3==1)for(var T=0;T<P;T++)a.rect(l,c+k*d/e+n[T]*d/300,h,d/300);else for(var w=0;w<P;w++)a.rect(l,c+k*d/e+i[w]*d/300,h,d/300)}a.clip()}();if(23==n)return function(){if(a.globalAlpha=1/(60*o)*r,u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var s=e+n/2,l=t+i/2;a.translate(s,l),a.rotate(Math.PI/4-Math.PI/4/(60*o)*r),a.translate(-s,-l),a.translate(s*(1-1/(60*o)*r),l*(1-1/(60*o)*r)),a.scale(1/(60*o)*r,1/(60*o)*r)}();if(21==n)return function(){if(a.globalAlpha=1/(60*o)*r,u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var s=e+n/2,l=t+i/2;a.translate(s*(1-(.5+.5/(60*o)*r)),l*(1-(.5+.5/(60*o)*r))),a.scale(.5+.5/(60*o)*r,.5+.5/(60*o)*r)}();if(20==n)return function(){var e;if(a.globalAlpha=1/(60*o)*r,u){var n=u[0];u[1];e=n+u[2]/2,u[3]}else e=Y(t).x,Y(t).y;0<r&&r<=20*o?r=20*o-r:20*o<r&&r<=40*o?r-=20*o:40*o<r&&r<=60*o&&(r=60*o-r),a.transform(1-2/(20*o)*r,0,0,1,e*(1-(1-2/(20*o)*r)),0)}();if(35==n)return function(){if(u){u[0];var e=u[1],t=(u[2],u[3])}else e=g,t=M;for(var n=e+t/2,i=s[1].pathPoints,l=[],c=0;c<i.length;c++)l.push([i[c],i[c+1]]),c++;var h=l.length,d=h/(60*o),f=Math.floor(d*r),p=d*r-f;if(d*r<h&&f<h-1){var y=[l[f][0],l[f][1]],v=[l[f+1][0],l[f+1][1]];a.translate(y[0]/3+(v[0]-y[0])*p,y[1]/3+(v[1]-y[1])*p),r>1&&f<h-1&&l[f][1]>=l[f+1][1]&&l[f][1]>=l[f-1][1]&&(a.translate(0,.2*n),a.scale(1,.8))}else a.translate(0,0)}();if(30==n)return e.other.addTimes&&e.other.addTimes!=r&&a.translate(0,(250/(60*o)*r-250)*(60*o-e.other.addTimes)),a.globalAlpha=1/(60*o)*r,void(r?e.other.addTimes||a.translate(0,250/(60*o)*r-250):a.translate(0,-250));if(38!=n)return 25==n?(250,a.translate(250/(60*o)*r-250,0),a.beginPath(),a.rect(v+m-m/(60*o)*r,g,60*o*r,M),void a.clip()):4==n?function(){if(a.beginPath(),u)var e=u[0],t=u[1],n=u[2],i=u[3];switch(s){case 0:u?(a.rect(e,t,n,i/2/(60*o)*r),a.rect(e,t+i-i/2/(60*o)*r,n,i/2/(60*o)*r),a.rect(e,t,n/2/(60*o)*r,i),a.rect(e+n-n/2/(60*o)*r,t,n/2/(60*o)*r,i)):(a.rect(v-.5,g-.5,m+1,M/2/(60*o)*r+1),a.rect(v-.5,g+M-M/2/(60*o)*r-.5,m+1,M/2/(60*o)*r+1),a.rect(v-.5,g-.5,m/2/(60*o)*r+1,M+1),a.rect(v+m-m/2/(60*o)*r-.5,g-.5,m/2/(60*o)*r+1,M+1));break;case 1:u?a.rect(e+n/2*(1-1/(60*o)*r),t+i/2*(1-1/(60*o)*r),n/(60*o)*r,i/(60*o)*r):a.rect(v+m/2*(1-1/(60*o)*r),g+M/2*(1-1/(60*o)*r),m/(60*o)*r,M/(60*o)*r)}a.clip()}():6==n?function(){var e=Math.floor(r/(3*o));switch(a.beginPath(),s){case 2:if(e<=10)for(var t=0;t<=20;t++)t<=2*e&&a.rect(v+t*m/20,g,m/20,(2*e-t)*M/20);else for(var n=0;n<=20;n++)n<2*(e-10)?a.rect(v+n*m/20,g,m/20,M):a.rect(v+n*m/20,g,m/20,M-M*(n+20-2*e)/20);break;case 0:if(e<=10)for(var i=0;i<=20;i++)i<=2*e&&a.rect(v+m-i*m/20,g,m/20,(2*e-i)*M/20);else for(var l=0;l<=20;l++)l<2*(e-10)?a.rect(v+m-l*m/20,g,m/20,M):a.rect(v+m-l*m/20,g,m/20,M-M*(l+20-2*e)/20);break;case 3:if(e<=10)for(var c=0;c<=20;c++)c<=2*e&&a.rect(v+c*m/20,g+M-(2*e-c)*M/20,m/20,(2*e-c)*M/20);else for(var u=0;u<=20;u++)u<2*(e-10)?a.rect(v+u*m/20,g,m/20,M):a.rect(v+u*m/20,g+M*(u+20-2*e)/20,m/20,M-M*(u+20-2*e)/20);break;case 1:if(e<=10)for(var h=0;h<=20;h++)h<=2*e&&a.rect(v+m-h*m/20,g+M-(2*e-h)*M/20,m/20,(2*e-h)*M/20);else for(var d=0;d<=20;d++)d<2*(e-10)?a.rect(v+m-d*m/20,g,m/20,M):a.rect(v+m-d*m/20,g+M*(d+20-2*e)/20,m/20,M-M*(d+20-2*e)/20)}a.clip()}():29==n?function(){if(u)var e=u[0],t=u[1],n=u[2],i=u[3];else e=v,t=g,n=m,i=M;var l=e+n/2,c=t+i/2,h=a.canvas.width/2.5-e-n/2,d=a.canvas.height/3-t-i/2;switch(s){case 0:a.translate(l*(1-(.5+.5/(60*o)*r)),c*(1-(.5+.5/(60*o)*r))),a.scale(.5+.5/(60*o)*r,.5+.5/(60*o)*r);break;case 1:a.translate(h*(1-(.3+.7/(60*o)*r)),d*(1-(.3+.7/(60*o)*r))),a.scale(.3+.7/(60*o)*r,.3+.7/(60*o)*r);break;case 2:a.translate(l*(1-(.8+.2/(60*o)*r)),(t+i)*(1-(.8+.2/(60*o)*r))),a.scale(.8+.2/(60*o)*r,.8+.2/(60*o)*r);break;case 3:a.translate(l*(1/(60*o)*r-1),c*(1/(60*o)*r-1)),a.scale(2-1/(60*o)*r,2-1/(60*o)*r);break;case 4:a.translate(0-l*(1-1/(60*o)*r),d*(1-1/(60*o)*r)),a.scale(2-1/(60*o)*r,2-1/(60*o)*r);break;case 5:a.translate(l*(.2/(60*o)*r-.2),c*(.2/(60*o)*r-.2)),a.scale(1.2-.2/(60*o)*r,1.2-.2/(60*o)*r)}}():14==n?function(){switch(a.beginPath(),s){case 0:a.rect(v,g,m/(120*o)*r,M/(120*o)*r),a.rect(v+m-m/(120*o)*r,g,m/(120*o)*r,M/(120*o)*r),a.rect(v,g+M-M/(120*o)*r,m/(120*o)*r,M/(120*o)*r),a.rect(v+m-m/(120*o)*r,g+M-M/(120*o)*r,m/(120*o)*r,M/(120*o)*r);break;case 1:a.rect(v,g+M/2-M/(120*o)*r,m,M/(60*o)*r),a.rect(v+m/2-m/(120*o)*r,g,m/(60*o)*r,M)}a.clip()}():11==n?function(){switch(a.beginPath(),s){case 0:a.translate(0,M-M/(60*o)*r),a.rect(v,g,m,M/(60*o)*r);break;case 1:a.translate(m/(60*o)*r-m,0),a.rect(v+m-m/(60*o)*r,g,m/(60*o)*r,M);break;case 2:a.translate(m-m/(60*o)*r,0),a.rect(v,g,m/(60*o)*r,M);break;case 3:a.translate(0,M/(60*o)*r-M),a.rect(v,g+M-M/(60*o)*r,m,M/(60*o)*r)}a.clip()}():7==n?function(){switch(a.beginPath(),s){case 1:a.moveTo(v+m/2-m/(60*o)*r,g+M/2),a.lineTo(v+m/2,g+M/2-M/(60*o)*r),a.lineTo(v+m-m/2+m/(60*o)*r,g+M/2),a.lineTo(v+m/2,g+M-M/2+M/(60*o)*r),a.lineTo(v+m/2-m/(60*o)*r,g+M/2),a.closePath();break;case 0:r/(60*o)<.5?(a.moveTo(v,g),a.lineTo(v+m/(60*o)*r,g),a.lineTo(v,g+M/(60*o)*r),a.lineTo(v,g),a.closePath(),a.moveTo(v+m,g),a.lineTo(v+m-m/(60*o)*r,g),a.lineTo(v+m,g+M/(60*o)*r),a.lineTo(v+m,g),a.closePath(),a.moveTo(v,g+M),a.lineTo(v,g+M-M/(60*o)*r),a.lineTo(v+m/(60*o)*r,g+M),a.lineTo(v,g+M),a.closePath(),a.moveTo(v+m,g+M),a.lineTo(v+m,g+M-M/(60*o)*r),a.lineTo(v+m-m/(60*o)*r,g+M),a.lineTo(v+m,g+M),a.closePath()):(a.moveTo(v,g),a.lineTo(v,g+M),a.lineTo(v+m,g+M),a.lineTo(v+m,g),a.lineTo(v,g),a.moveTo(v+m/2,g-M/2+M/(60*o)*r),a.lineTo(v+1.5*m-m/(60*o)*r,g+M/2),a.lineTo(v+m/2,g+1.5*M-M/(60*o)*r),a.lineTo(v-m/2+m/(60*o)*r,g+M/2),a.lineTo(v+m/2,g-M/2+M/(60*o)*r),a.closePath())}a.clip()}():12==n?void(r===60*o&&(a.globalAlpha=0)):0==n?function(){switch(a.beginPath(),s){case 0:for(var e=0;e<=5;e++)a.rect(v,g+M/6*e,m,M/6/(60*o)*r);break;case 1:for(var t=0;t<=5;t++)a.rect(v+m/6*t,g,m/6/(60*o)*r,M)}a.clip()}():5==n?function(){a.beginPath();var e=void 0;switch(s){case 0:e=700-g,a.translate(0,e-e/(60*o)*r),a.rect(v,g,m,M);break;case 1:e=v+m,a.translate(e/(60*o)*r-e,0),a.rect(v,g,m,M);break;case 2:e=900-v,a.translate(e-e/(60*o)*r,0),a.rect(v,g,m,M);break;case 3:e=g+M,a.translate(0,e/(60*o)*r-e),a.rect(v,g,m,M)}a.clip()}():10==n?function(){switch(a.beginPath(),s){case 0:for(var e=0;e<=11;e++)for(var t=0;t<=5;t++)(e+t)%2==0?30*o-r>0?a.rect(v+m/12*e,g+M/6*t,m/12/(30*o)*r,M/6):a.rect(v+m/12*e,g+M/6*t,m/12,M/6):30*o-r<0&&a.rect(v+m/12*e,g+M/6*t,m/12/(30*o)*(r-30*o),M/6);break;case 1:for(var n=0;n<=5;n++)for(var i=0;i<=11;i++)(n+i)%2==0?30*o-r>0?a.rect(v+m/6*n,g+M/12*i,m/6,M/12/(30*o)*r):a.rect(v+m/6*n,g+M/12*i,m/6,M/12):30*o-r<0&&a.rect(v+m/6*n,g+M/12*i,m/6,M/12/(30*o)*(r-30*o))}a.clip()}():13==n?(R=v+m/2,_=g+M/2,a.beginPath(),a.moveTo(R,_),a.arc(R,_,(M+m)/2,.5*-Math.PI-Math.PI/(60*o)*r,.5*-Math.PI+Math.PI/(60*o)*r),a.closePath(),void a.clip()):17==n?function(){var e=Math.floor(r/(3*o)),t=Math.floor(m/60)+1,n=Math.floor(M/20)+1;a.beginPath();for(var i=0;i<t;i++)for(var s=0;s<n;s++)for(var l=1;l<=e;l++)switch(l){case 1:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 2:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 3:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 4:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 5:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 6:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 7:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 8:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 9:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 10:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 11:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 12:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 13:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 14:a.rect(v+m/t*i+m/t/5*0,g+M/n*s+M/n/4*3,m/t/5,M/n/4);break;case 15:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 16:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*0,m/t/5,M/n/4);break;case 17:a.rect(v+m/t*i+m/t/5*4,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 18:a.rect(v+m/t*i+m/t/5*1,g+M/n*s+M/n/4*2,m/t/5,M/n/4);break;case 19:a.rect(v+m/t*i+m/t/5*2,g+M/n*s+M/n/4*1,m/t/5,M/n/4);break;case 20:a.rect(v+m/t*i+m/t/5*3,g+M/n*s+M/n/4*3,m/t/5,M/n/4)}a.clip()}():16==n?function(){var n=void 0;if(N.randomNum)n=N.randomNum;else{var f=N.randomList.length-1;n=N.randomNum=N.randomList[Math.floor(Math.random()*(f-0))+0]}N.animHandler(e,t,a,r,n,i,o,s,l,c,u,h,d),r===60*o*r&&(N.randomNum=void 0)}():22==n?(a.globalAlpha=1/(60*o)*r,a.translate((.5-.5/(60*o)*r)*(v+m/2),0),void a.scale(.5+.5/(60*o)*r,1)):33==n?(a.globalAlpha=1/(60*o)*r,void a.translate((.5/(60*o)*r-.5)*m,0)):void 0;var A,C,j,S,D,z,L,O,E,R,_}else a.globalAlpha=r/(60*o);function U(){u?(a.globalAlpha=r<=30*o?1-1.8/(60*o)*r:.1+.9/(60*o)*r,a.translate(u[0]*(1-(r<=30*o?1+1/90*r:1+1/90*(60*o-r))),u[1]*(1-(r<=30*o?1+1/90*r:1+1/90*(60*o-r)))),a.scale(r<=30*o?1+1/90*r:1+1/90*(60*o-r),r<=30*o?1+1/90*r:1+1/90*(60*o-r))):(a.globalAlpha=r<=30*o?1-1.8/(60*o)*r:.1+.9/(60*o)*r,a.translate(Y(t).x*(1-(r<=30*o?1+1/90*r:1+1/90*(60*o-r))),Y(t).y*(1-(r<=30*o?1+1/90*r:1+1/90*(60*o-r)))),a.scale(r<=30*o?1+1/90*r:1+1/90*(60*o-r),r<=30*o?1+1/90*r:1+1/90*(60*o-r)))}function F(){a.globalAlpha=1-1/(60*o)*r}function G(){return 0==s?[0,x>=g?T-g:T-x]:1==s?[v+m>=b+P?v+m:b+P,0]:2==s?[b>=v?I-v:I-b,0]:3==s?[0,x+k>=g+M?x+k:g+M]:4==s?[v+m>=b+P?v+m:b+P,x>=g?T-g:T-x]:5==s?[b>=v?I-v:I-b,x>=g?T-g:T-x]:6==s?[v+m>=b+P?v+m:b+P,x+k>=g+M?x+k:g+M]:7==s?[b>=v?I-v:I-b,x+k>=g+M?x+k:g+M]:[0,x>=g?T-g:T-x]}function Y(e){var t=Object.create(null);return e.shapeRect?(t.x=e.shapeRect.x+e.shapeRect.width/2,t.y=e.shapeRect.y+e.shapeRect.height/2):e.textRect&&(t.x=e.textRect.x+e.textRect.width/2,t.y=e.textRect.y+e.textRect.height/2),t}}}]),e}()},9274:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}();function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var n=t.AnimationContext=function(){function e(){r(this,e)}return a(e,[{key:"reset",value:function(e,t,a){this.g=t,this.slide=e,this.count=0,this.animInfo=a,this.shape=a.shape}}]),e}();t.AnimationManager=function(){function e(t){var a=this;r(this,e),this.currAnimationData=[],this.speed=1,this._onPageChange=function(e){a._resetAnimationState({animPosition:e.animPosition})},this._onAnimationListChange=function(e){e&&a.currPage&&e.index===a.currPage.index&&void 0===a.currentAnimIndex&&a._jumpTo({animIndex:a.currentAnimIndex||0,animState:a.animState||"start"},!0)},this.pgReader=t,this.pgReader.on("pageChange",this._onPageChange),this.pgReader.on("animationListChange",this._onAnimationListChange)}return a(e,[{key:"updateAnimPos",value:function(e,t){var a=this.getAnimPos({animIndex:this.currentAnimIndex,animState:this.animState});void 0!==e&&e!==this.currentAnimIndex&&(this.currentAnimIndex=e),void 0!==t&&this.animState!==t&&(this.animState=t);var r=this.getAnimPos({animIndex:this.currentAnimIndex,animState:this.animState});a!==r&&this.checkMediaAutoPlay(a,r)}},{key:"checkMediaAutoPlay",value:function(e,t){var a=this.pgReader.mediaAutoPlayInfo[this.currPage.index];for(var r in a){var n=a[r];n.start>=e&&n.start<t?this.pgReader.mediaToProcess[this.currPage.index+"_"+r]="autoplay":(n.end&&e<=n.end&&t>n.end||e>n.start&&t<=n.start)&&(this.pgReader.mediaToProcess[this.currPage.index+"_"+r]="stop")}}},{key:"isCurrPageReady",value:function(){return this.pgReader.isPageReady(this.pgReader.currPageIndex)}},{key:"isDuringAnimation",value:function(){return"inAnim"===this.animState}},{key:"hasNextAnimation",value:function(){return!(0===this.animationList.length||this.currentAnimIndex>=this.animationList.length||this.currentAnimIndex===this.animationList.length-1&&"end"===this.animState)}},{key:"hasPreAnimation",value:function(){return!(0===this.animationList.length||this.currentAnimIndex<0||0===this.currentAnimIndex&&"start"===this.animState)}},{key:"getCurrentAnimationPosition",value:function(){return{animIndex:this.currentAnimIndex?this.currentAnimIndex:0,animState:this.animState?this.animState:"start"}}},{key:"getAnimPos",value:function(e){return e?(e.animIndex?e.animIndex:0)+(e&&"start"===e.animState?0:1):0}},{key:"isFirstPosPrevSecondPos",value:function(e,t){return this.getAnimPos(e)<this.getAnimPos(t)}},{key:"gotoAnimation",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];!t&&this.animationDest&&this.animationDest.animIndex===e.animIndex&&this.animationDest.animState===e.animState||(this.clearAnimation(),this.isCurrPageReady()?t||!this.isFirstPosPrevSecondPos(this.getCurrentAnimationPosition(),e)||this.animationDest&&!this.isFirstPosPrevSecondPos(this.animationDest,e)?(this.animationDest=void 0,this._jumpTo(e)):this.animationDest?(this._jumpTo(this.animationDest),this.animationDest=e,this._animateTo()):(this.animationDest=e,this._animateTo()):(this.animationDest=void 0,this._jumpTo(e)))}},{key:"getFirstAnimationPosition",value:function(e){if(!this.isCurrPageReady())return{animIndex:0,animState:"start"};for(var t=this.pgReader.doc.pages[e.pageIndex].animation,a=0;a<t.length;a++){var r=t[a];if(Array.isArray(r)&&r.length>0&&(r=r[0]),0===r.startCondition)break;if(2===r.startCondition&&e.stopOnContinuousAnimation)break}return{animIndex:a,animState:"start"}}},{key:"getNextAnimationPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{stopOnContinuousAnimation:!1};if(!this.isCurrPageReady())return null;if(this.hasNextAnimation()){var t=this.getCurrentAnimationPosition();if("inAnim"===this.animState&&e.stopOnContinuousAnimation){var a=Math.min(this.animationList.length-1,t.animIndex+2);return{animIndex:a,animState:"end"}}if(e.stopOnContinuousAnimation)return{animIndex:Math.min(this.animationList.length-1,t.animIndex+1),animState:t.animState};var r=this.animationList,n=t.animIndex+("start"===t.animState?0:1)+1;for("inAnim"===t.animState&&this.animationDest&&(n=Math.max(n,this.animationDest.animIndex+("start"===this.animationDest.animState?1:2)));n<r.length&&(!r[n]||!r[n][0]||2===r[n][0].startCondition);n++);return{animIndex:n,animState:"start"}}return null}},{key:"getPrevAnimationPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{stopOnContinuousAnimation:!1};if(!this.isCurrPageReady())return null;if(this.hasPreAnimation()){var t=this.getCurrentAnimationPosition();if(e.stopOnContinuousAnimation)return{animIndex:t.animIndex-1,animState:t.animState};for(var a=this.animationList,r=t.animIndex+("start"===t.animState?0:1)-1;r>0&&(!a[r]||!a[r][0]||2===a[r][0].startCondition);r--);return{animIndex:r,animState:"start"}}return null}},{key:"clearAnimation",value:function(){clearTimeout(this.delayAnimationTimer),cancelAnimationFrame(this.animationTimer)}},{key:"_jumpTo",value:function(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.clearAnimation(),this.animationDest=void 0,this.updateAnimPos(e.animIndex,e.animState);for(var a=e.animIndex+("start"===e.animState?0:1),r=0;r<this.animationList.length;r++){var i=r<a?2:0,o=!0,s=!1,l=void 0;try{for(var c,u=this.animationList[r][Symbol.iterator]();!(o=(c=u.next()).done);o=!0){var h=c.value;if(h.animationState=i,1===h.animType||3===h.animType){var d=new n;d.reset(this.inAnimationPage,this.pgReader.g,h),h.stressContent=d}}}catch(e){s=!0,l=e}finally{try{!o&&u.return&&u.return()}finally{if(s)throw l}}}t&&this.pgReader.paint()}},{key:"_resetAnimationState",value:function(e){if(this.clearAnimation(),this.animationDest=void 0,this.currPage){"start"===e.animPosition?this.updateAnimPos(0,"start"):this.updateAnimPos(Math.max(this.animationList.length,0),"end");var t=!0,a=!1,r=void 0;try{for(var n,i=this.animationList[Symbol.iterator]();!(t=(n=i.next()).done);t=!0){var o=n.value,s=!0,l=!1,c=void 0;try{for(var u,h=o[Symbol.iterator]();!(s=(u=h.next()).done);s=!0)u.value.animationState="start"===e.animPosition?0:2}catch(e){l=!0,c=e}finally{try{!s&&h.return&&h.return()}finally{if(l)throw c}}}}catch(e){a=!0,r=e}finally{try{!t&&i.return&&i.return()}finally{if(a)throw r}}}else console.error("无法获取当前页面，页面索引为: "+this.pgReader.currPageIndex)}},{key:"_animateTo",value:function(){var e=this;if(this.hasNextAnimation()){void 0===this.currentAnimIndex&&this.updateAnimPos(void 0,0),this.currAnimationData=[],"end"===this.animState&&this.updateAnimPos(this.currentAnimIndex+1,"start");var t=this.animationList[this.currentAnimIndex],a=!0,r=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(a=(o=s.next()).done);a=!0){var l=o.value;l.animationState=0;var c=new n;c.reset(this.inAnimationPage,this.pgReader.g,l),this.currAnimationData.push(c),1!==l.animType&&3!==l.animType||(l.stressContent=c),this.currentAnimIndex===this.animationList.length-1&&4===l.effectAfterPlay?this.isEffectAfterPlay=!0:this.isEffectAfterPlay=!1}}catch(e){r=!0,i=e}finally{try{!a&&s.return&&s.return()}finally{if(r)throw i}}this.delayAnimationTimer=window.setTimeout((function(){e._play(Date.now(),e.speed)}),0)}}},{key:"playAtState",value:function(e){for(var t=0;t<this.currAnimationData.length;t++){var a=this.currAnimationData[t],r=a.animInfo,n=Math.ceil(60*r.endTime),i=Math.ceil(60*(r.startTime||0));a.count=Math.max(-1,e-i),a.count=Math.min(a.count,n),a.count>=n?r.animationState=2:a.count>=0?r.animationState=1:r.animationState=0}this.pgReader.paint(!1,this.currAnimationData)}},{key:"_play",value:function(e,t){var a=this;if(this.updateAnimPos(void 0,"inAnim"),this.currAnimationData){for(var r=!1,n=!1,i=0;i<this.currAnimationData.length;i++){var o=this.currAnimationData[i],s=o.animInfo,l=4===s.animType?0:Math.ceil(60*s.endTime);o.count>=l?s.animationState=2:o.count>=0?s.animationState=1:s.animationState=0,o.count<l&&(n=!0)}n?this.pgReader.paint(!1,this.currAnimationData):(cancelAnimationFrame(this.animationTimer),this.animationTimer=requestAnimationFrame((function(){a.pgReader.paint()})));for(var c=0;c<this.currAnimationData.length;c++){var u=this.currAnimationData[c],h=u.animInfo,d=Math.ceil(60*(h.startTime||0)),f=4===h.animType?0:Math.ceil(60*h.endTime);u.count<f-.01&&(r=!0),u.count=Math.max(-1,t-d),u.count=Math.min(u.count,f)}r?(cancelAnimationFrame(this.animationTimer),this.animationTimer=requestAnimationFrame((function(){var r=Date.now()-e,n=Math.max(1,r/16)*a.speed;a._play(Date.now(),t+n)}))):(this.updateAnimPos(void 0,"end"),this.animationDest&&this.hasNextAnimation()&&this.isFirstPosPrevSecondPos(this.getCurrentAnimationPosition(),this.animationDest)?this._animateTo():this.animationDest=void 0,this.animationList[this.currentAnimIndex-1]&&3===this.animationList[this.currentAnimIndex-1][0].effectAfterPlay&&(this.delayAnimationTimer=setTimeout((function(){a.pgReader.paint()}),200)))}else this.updateAnimPos(void 0,"end")}},{key:"animationList",get:function(){return this.currPage&&Array.isArray(this.currPage.animation)?this.currPage.animation:[]}},{key:"currPage",get:function(){return this.pgReader.currPage}}]),e}()},8802:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=l(a(2754)),n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=l(a(3206)),o=a(5357),s=a(1310);function l(e){return e&&e.__esModule?e:{default:e}}var c=function(e,t,a,r){return new(a||(a=Promise))((function(n,i){function o(e){try{l(r.next(e))}catch(e){i(e)}}function s(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof a?t:new a((function(e){e(t)}))).then(o,s)}l((r=r.apply(e,t||[])).next())}))},u=0,h=function(){function e(t){var a=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.id=u++,this.state={},this.pageVisible=!0,this.containerVisible=!0,this.playerMap=new Map,this.removeAllPlayers=function(e){var t=a.getPlayers();for(var r in t.forEach((function(t){var r=t.id();a.state[r]?a.state[r].page!==e&&(a.removePlayer(t),delete a.state[r]):a.removePlayer(t)})),a.state)a.state[r].page!==e&&delete a.state[r]},this.hidePlayersExcept=function(e){a.getPlayers().forEach((function(t){e.has(t.id())||(delete a.state[t.id()],t.hide())}))},this.appendAudio=function(e,t){var r=a.reader,n=r.zoom,i=r.currPageIndex+"_"+e.shapeID+"_"+a.id,l=a.getPlayerIdFromShapeId(e.shapeID),c=a.getPlayer(l),u=e.rect[2]*n,h=e.rect[3]*n,d=(r.viewHeight/n-r.doc.pagesize.height)/2,f=(e.rect[1]+d)*n;if(c){c.show();var p=c.container();p&&(Object.assign(p.style,{left:(e.rect[0]+e.rect[2]/2)*n-u/2+"px",width:u+"px",height:h+"px"}),isNaN(f)||(p.style.top=f+"px"))}else{if(document.body.contains&&!document.body.contains(r.canvasWrapper))return;var y=document.createElement("img");y.src=t,y.className="ppt-audio",Object.assign(y.style,{position:"absolute",left:0,top:0,width:"100%",height:"100%",cursor:"pointer"}),r.canvasWrapper.appendChild(y);var v=document.createElement("audio");v.id=i,v.className="ppt-audio",v.controls=!1,v.preload="metadata";var g=document.createElement("source");g.src=(0,o.getTidyResourceUrl)(r.resPrefixArr[s.lastSuccessUrlIndex],e.mediaPath),v.appendChild(g),r.canvasWrapper.appendChild(v),c=a.createPlayer(a.reader,i,l,{controlVideo:r.controlVideo,autoplay:!1,preload:"auto",isVideo:!1,audioImage:y}),a.bindVideoEvents(c),c.ready((function(){var t=c.container();t&&(Object.assign(t.style,{position:"absolute",left:(e.rect[0]+e.rect[2]/2)*n-u/2+"px",width:u+"px",height:h+"px"}),isNaN(f)||(t.style.top=f+"px"),e.ctm&&Object.assign(t.style,{transform:"matrix("+e.ctm[0]+","+e.ctm[1]+","+e.ctm[2]+","+e.ctm[3]+",0,0)"}),a.afterInit(c))}))}return c.id()},this.appendVideo=function(e,t){var r=a.reader,n=r.zoom,i=r.currPageIndex+"_"+e.shapeID+"_"+a.id,l=a.getPlayerIdFromShapeId(e.shapeID),c=a.getPlayer(l),u=e.rect[2]*n,h=e.rect[3]*n,d=(r.viewHeight/n-r.doc.pagesize.height)/2,f=(e.rect[1]+d)*n;if(c){c.show();var p=c.container();p&&(Object.assign(p.style,{left:(e.rect[0]+e.rect[2]/2)*n-u/2+"px",width:u+"px",height:h+"px"}),isNaN(f)||(p.style.top=f+"px"))}else{if(document.body.contains&&!document.body.contains(r.canvasWrapper))return;var y=document.createElement("video");y.id=i,y.className="ppt-video ",y.controls=!1,y.playsInline=!0,y.preload="metadata",y.disablePictureInPicture=!0,y.style.backgroundColor="black",y.poster=t;var v=document.createElement("source");v.src=(0,o.getTidyResourceUrl)(r.resPrefixArr[s.lastSuccessUrlIndex],e.mediaPath)+"#t=0.1",v.type="video/mp4",y.appendChild(v),r.canvasWrapper.appendChild(y),c=a.createPlayer(a.reader,i,l,{controlVideo:r.controlVideo,autoplay:!1,preload:"auto",isVideo:!0}),a.bindVideoEvents(c),c.ready((function(){var t=c.container();t&&(Object.assign(t.style,{position:"absolute",left:(e.rect[0]+e.rect[2]/2)*n-u/2+"px",width:u+"px",height:h+"px"}),isNaN(f)||(t.style.top=f+"px"),e.ctm&&Object.assign(t.style,{transform:"matrix("+e.ctm[0]+","+e.ctm[1]+","+e.ctm[2]+","+e.ctm[3]+",0,0)"}),a.afterInit(c))}))}return c.id()},this.onReceiveVideoState=function(e){var t=a.getPlayers();for(var r in t.forEach((function(t){a.updatePlayer(t,e&&e[t.id()])})),a.state={},e)e[r].page===a.reader.currPageIndex&&(a.state[r]=e[r])},this.getPlayerIdFromShapeId=function(e){return a.reader.currPageIndex+"_"+e},this.afterInit=function(e){e.show();var t=a.state[e.id()];t&&a.updatePlayer(e,t)},this.bindVideoEvents=function(e){e.on("playerChange",(function(t){var r,n,i;a.reader.controlVideo&&a.updateState((r={},n=e.id(),i={type:"manual",page:a.reader.currPageIndex,play:t.play,seek:t.seek,t:a.reader.ntpTime},n in r?Object.defineProperty(r,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[n]=i,r),"action")}))},this.updatePlayer=function(e,t){return c(a,void 0,void 0,r.default.mark((function a(){var n,i;return r.default.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(n=e.id(),!t||!this.state[n]||"autoplay"!==t.type||"autoplay"!==this.state[n].type){a.next=6;break}if(!(this.state[n].t-t.t<3e3)){a.next=6;break}return a.abrupt("return");case 6:t?this.state[n]=t:delete this.state[n],i={play:!1,seek:0,t:this.reader.ntpTime},t&&"manual"===t.type?i={play:t.play,seek:t.seek,t:t.t}:t&&"autoplay"===t.type?i={play:!0,seek:0,t:t.t}:t&&"stop"===t.type&&(i.seek=t.seek),e.onAttributeUpdated(i);case 10:case"end":return a.stop()}}),a,this)})))},this.updateState=function(e,t){for(var r in e)e[r].page===a.reader.currPageIndex&&(a.state[r]=e[r]);a.reader.onMediaStateUpdate(a.state,t)},this.onPageInVisible=function(){a.pageVisible=!1,a.getPlayers().forEach((function(e){e.onPageVisible()}))},this.onPageVisible=function(){return c(a,void 0,void 0,r.default.mark((function e(){return r.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.pageVisible=!0,this.getPlayers().forEach((function(e){e.onPageVisible()}));case 3:case"end":return e.stop()}}),e,this)})))},this.createPlayer=function(e,t,r,n){var o=document.getElementById(t);if(o){if(a.playerMap.has(r))return a.playerMap.get(r);if(o instanceof HTMLMediaElement){var s=new i.default(e,o,r,n);return a.playerMap.set(r,s),s}console.error("id: "+r+" is not video element")}else console.error("video not exist(id: "+t+")")},this.getPlayer=function(e){return a.playerMap.has(e)?a.playerMap.get(e):void 0},this.getPlayers=function(){return a.playerMap},this.removePlayer=function(e){a.playerMap.delete(e.id()),e.destroy()},this.reader=t,"function"==typeof this.unregisterVisibleChangeListener&&this.unregisterVisibleChangeListener(),this.pageVisible=(0,o.isPageVisible)(),this.unregisterVisibleChangeListener=(0,o.registerVisibleChangeHandler)(this.onPageVisible,this.onPageInVisible)}return n(e,[{key:"autoPlayMedia",value:function(e){var t=this;this.getPlayers().forEach((function(a){a.id()===e&&t.updatePlayer(a,{type:"autoplay",page:t.reader.currPageIndex,t:t.reader.ntpTime})}))}},{key:"stopMedia",value:function(e){var t=this;this.getPlayers().forEach((function(a){a.id()===e&&t.updatePlayer(a,{type:"stop",page:t.reader.currPageIndex,t:t.reader.ntpTime,seek:a.currentTime()})}))}},{key:"updateControl",value:function(e){this.getPlayers().forEach((function(t){t.updateControl(e)}))}},{key:"destroy",value:function(){this.removeAllPlayers(-1),this.unregisterVisibleChangeListener()}},{key:"show",value:function(){this.containerVisible=!0,this.pageVisible&&this.getPlayers().forEach((function(e){e.onPageVisible()}))}},{key:"hide",value:function(){this.containerVisible=!1,this.getPlayers().forEach((function(e){e.onPageInVisible()}))}}]),e}();t.default=h},8678:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.PgReader=void 0;var r=y(a(2754)),n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=y(a(1075)),o=a(9254),s=a(5233),l=a(2812),c=a(9274),u=a(5357),h=y(a(9934)),d=a(1310),f=y(d),p=y(a(8802));function y(e){return e&&e.__esModule?e:{default:e}}function v(e){if(Array.isArray(e)){for(var t=0,a=Array(e.length);t<e.length;t++)a[t]=e[t];return a}return Array.from(e)}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var M=function(e,t,a,r){return new(a||(a=Promise))((function(n,i){function o(e){try{l(r.next(e))}catch(e){i(e)}}function s(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof a?t:new a((function(e){e(t)}))).then(o,s)}l((r=r.apply(e,t||[])).next())}))},b=1,x={symbol:"oelx5YG2zdLMi0rcuNW7",mtextra:"5NID1zjbXr9QnToO3fWs",webdings:"E5a10pMvmyJnBIcLPVW4",wingdings:"VFPprHgOLcXhfK6Uz92e","windings 2":"2S3JsTxCqaLQWEDeU1ZY","windings 3":"LQRDBd04orz5kWmYpUci"};(t.PgReader=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};g(this,t);var a=m(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return a.canvasWidth=100,a.canvasHeight=100,a.zoom=1,a.mediaAutoPlayInfo={},a.mediaToProcess={},a.resPrefixArr=[],a.currPageIndex=0,a.loadingImage=new Image,a.canvasGifIdsToKeep=new Set,a.videoIdsToKeep=new Set,a.pageCount=0,a.loadedImages=new Map,a.pageJsonLoaded=new Set,a.pageImgToDownload=new Map,a.loadVideo=!1,a.controlVideo=!1,a.videoFullIcon=!1,a.nearestRange=3,a.lastPreUrl="",a.mediaController=new p.default(a),a.logger=console,a._waitTilPageReady=function(e,t){return e=Number(e),isNaN(e)?(console.error("waitTilPageReady的参数不是有效的页码"),Promise.reject()):a.isPageReady(e)?Promise.resolve():(a.preloadPages(e),new Promise((function(r,n){var i=void 0,o=function t(n,o){"pageLoaded"===n&&o===e&&(clearTimeout(i),a.off("event:pptState:change",t),r())};a.on("event:pptState:change",o),clearTimeout(i),i=setTimeout((function(){console.error("page: "+e+"加载超时，请重试"),a.off("event:pptState:change",o),n()}),(null==t?void 0:t.timeout)||6e3)})))},a.waitTilImgReady=function(e,t){return a.isPageImgReady(e)?Promise.resolve():new Promise((function(r,n){var i=void 0,o=function t(n,o){if("pageImgLoaded"===n&&o===e)return clearTimeout(i),a.off("event:pptState:change",t),r()};a.on("event:pptState:change",o),clearTimeout(i),i=setTimeout((function(){var t=a.pageImgToDownload.get(e)||[];if(a.off("event:pptState:change",o),0===t.length)return r();console.error("images of page: "+e+"加载超时，请重试","img to download",t.join(",")),n()}),(null==t?void 0:t.timeout)||2e3)}))},a.player=new l.Animation(a),a.isMobile=!1,a.paraIdMin=0,e.logger&&(a.logger=e.logger),e.drawPlugin&&(a.drawPlugin=e.drawPlugin),a.ntpDiff=e.ntpDiff?e.ntpDiff:0,a.docIndex=b++,a.animationManager=new c.AnimationManager(a),a.backgroundColor="rgba(0,0,0,0)",a.loadingImage.src=h.default,a.loadingImage.crossOrigin="anonymous",a.fontDownloadPrefix=e.fontDownloadPrefix||"https://wb.vod.126.net/courseware/pptview/fonts/",a.loadVideo=e.loadVideo,a.controlVideo=e.controlVideo,a.videoFullIcon=e.videoFullIcon,document.addEventListener("fullscreenchange",(function(e){a.fullStateListener()})),document.addEventListener("webkitfullscreenchange",(function(e){a.fullStateListener()})),document.onmsfullscreenchange=function(e){a.fullStateListener()},window.addEventListener("resize",a.resizeWindow),window.addEventListener("orientationchange",a.resizeWindow),a}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"onMediaStateUpdate",value:function(e,t){var a=void 0===e?{}:e,r={};for(var n in a)a[n].page===this.currPageIndex&&(r[n]=Object.assign({},a[n]));this.emit("event:pptState:change","mediaUpdate",{videos:r,cause:t})}},{key:"updateControl",value:function(e){this.controlVideo=e,this.mediaController&&this.mediaController.updateControl(e)}},{key:"getMediaState",value:function(){return this.mediaController.state}},{key:"show",value:function(){this.mediaController&&this.mediaController.show()}},{key:"hide",value:function(){this.mediaController&&this.mediaController.hide()}},{key:"destroy",value:function(){this.container&&this.removeElementsInContainer(this.container),this.mediaController&&this.mediaController.destroy(),this.animationManager.clearAnimation(),f.default.stopDownloadResourceOfDoc(this.docIndex),this.removeAllListeners(),window.removeEventListener("resize",this.resizeWindow),window.removeEventListener("orientationchange",this.resizeWindow),this._ro&&this._ro.disconnect()}},{key:"setVideoState",value:function(e){if(this.loadVideo)try{e?this.mediaController.onReceiveVideoState(JSON.parse(JSON.stringify(e))):this.mediaController.onReceiveVideoState({})}catch(e){this.logger.error("setVideoState Error",e&&e.message)}}},{key:"setAnimSpeed",value:function(e){this.animationManager.speed=e}},{key:"updateContainer",value:function(e){var t,a=this;e&&e===this.container||(e?(this.container&&this.removeElementsInContainer(this.container),this.container=e,this.appendElementsInContainer(e),this.resizeWindow(),this._ro&&(this._ro.disconnect(),this._ro=void 0),window.ResizeObserver&&(this._ro=new window.ResizeObserver((function(e,t){a.resizeWindow(!0)})),null===(t=this._ro)||void 0===t||t.observe(e))):(this.parentWidth=100,this.parentHeight=100))}},{key:"getFirstAnimationPosition",value:function(e){return this.animationManager.getFirstAnimationPosition({pageIndex:e,stopOnContinuousAnimation:!1})}},{key:"waitTilPageReady",value:function(e,t){var a=this;return this._waitTilSummaryReady().then((function(){return a._waitTilPageReady(e,t)}))}},{key:"_waitTilSummaryReady",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6e3;return this.doc?Promise.resolve():new Promise((function(a,r){var n=void 0,i=function(e){"indexLoaded"===e&&(clearTimeout(n),a())};e.on("event:pptState:change",i),clearTimeout(n),n=setTimeout((function(){console.error("waitTilSummaryReady 加载超时"),e.off("event:pptState:change",i),r()}),t||6e3)}))}},{key:"isPageReady",value:function(e){return this.pageJsonLoaded.has(e)}},{key:"isPageImgReady",value:function(e){var t=this.pageImgToDownload.get(e);return this.isPageReady(e)&&t&&0===t.length}},{key:"hasNextPage",value:function(){return this.currPageIndex<this.pageCount-1}},{key:"hasPrevPage",value:function(){return this.currPageIndex>0}},{key:"hasPage",value:function(e){return e===Math.round(e)&&e>=0&&e<this.pageCount}},{key:"gotoPage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{animPosition:"start"};if(e=Number(e),isNaN(e))console.error("gotoPage index is NaN");else if(this.pageCount>0&&this.preloadPages(e),0===this.pageCount)this.currPageIndex=e;else{if(!this.hasPage(e))return console.warn("gotoPage: 页面"+e+"不存在");(0,u.removeElementsByClassName)("ppt-audio"),this.currPageIndex=e,this.mediaController.removeAllPlayers(this.currPageIndex),this.emit("pageChange",t),this.paint(),this.maskg.clearRect(0,0,this.canvasWidth,this.canvasHeight)}}},{key:"nextPage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{animPosition:"end"};this.hasNextPage()?this.gotoPage(this.currPageIndex+1,e):console.warn("nextPage: 已经是最新一页: "+this.currPageIndex)}},{key:"prevPage",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{animPosition:"end"};this.hasPrevPage()?this.gotoPage(this.currPageIndex-1,e):console.warn("prevPage: 已经是第一页: "+this.currPageIndex)}},{key:"getCurrPageIndex",value:function(){return this.currPageIndex}},{key:"getAnimationLengthOfPage",value:function(e){if(this.doc&&this.doc.pages){var t=this.doc.pages[e];return t&&Array.isArray(t.animation)?t.animation.length:0}return 0}},{key:"open",value:function(e,t){return M(this,void 0,void 0,r.default.mark((function a(){var n,i,o=this;return r.default.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(!this.lastPreUrl||this.lastPreUrl===e){a.next=4;break}return a.abrupt("return",Promise.reject("上一次open函数preUrl地址为: "+this.lastPreUrl+"\n\n 当前preUrl为: "+e+"。\n\n 如果需要加载两个不同的ppt，请创建两个PgReader"));case 4:if(!this.lastPreUrl||this.lastPreUrl!==e){a.next=6;break}return a.abrupt("return",Promise.resolve());case 6:return n=(0,u.parseUrl)(e),i=((null==t?void 0:t.backupResultUrls)||[]).map((function(e){return(0,u.parseUrl)(e)})).map((function(e){return e.resPrefix})),this.resPrefixArr=[n.resPrefix].concat(i),a.abrupt("return",new Promise((function(a,r){var n=void 0;clearTimeout(n),n=setTimeout((function(){console.error("ppt文档加载超时，请重试"),r()}),(null==t?void 0:t.timeout)||2e4),f.default.loadRes({urlPath:"index.json",resPrefixArr:o.resPrefixArr,type:"summaryJson",docIndex:o.docIndex,pageIndex:-1},{overwriteCb:!1,callback:function(r){if(r&&Array.isArray(r.attr)){var i=function(e){if(e&&e.font){var t=e.font.toLowerCase();t.startsWith("'")&&t.endsWith("'")&&(t=t.slice(1,t.length-1)),x[t]&&(e.font=x[t],f.default.loadRes({urlPath:'url("'+o.fontDownloadPrefix+e.font+'.ttf")',type:"font",docIndex:o.docIndex,pageIndex:-1,payload:{fontName:e.font}},{callback:function(){o.g&&(o.g.font="12px "+e.font,o.g.fillStyle="rgba(0,0,0,0)",o.g.fillText(" ",0,0)),o.paint()}}))}},s=!0,l=!1,c=void 0;try{for(var u,h=r.attr[Symbol.iterator]();!(s=(u=h.next()).done);s=!0)i(u.value)}catch(e){l=!0,c=e}finally{try{!s&&h.return&&h.return()}finally{if(l)throw c}}}o.calcModelSize(r,t),o.updatePageCount(r.pageCount),o.emit("event:pptState:change","indexLoaded",{pageCount:r.pageCount,docWidth:o.docWidth,docHeight:o.docHeight}),clearTimeout(n),o.lastPreUrl=e,a()}})})));case 10:case"end":return a.stop()}}),a,this)})))}},{key:"enterFullScreen",value:function(){var e=document.getElementById("wrapper");if(this.isInFullScreenMode)console.warn("enterFullScreen: 已经处于全屏状态");else{(0,u.removeElementsByClassName)("ppt-audio",!0),(0,u.removeElementsByClassName)("canvasGif");var t=e.webkitRequestFullScreen||e.requestFullScreen||e.mozRequestFullScreen;document.body.msRequestFullscreen?document.body.msRequestFullscreen.call(e):t.call(e)}}},{key:"exitFullScreen",value:function(){if(this.isInFullScreenMode){var e=document.mozCancelFullScreen||document.exitFullscreen||document.webkitExitFullscreen;document.msExitFullscreen?document.msExitFullscreen.call(document):e.call(document)}else console.warn("exitFullScreen: 已经退出了全屏状态")}},{key:"getNextAnimationPosition",value:function(e){return this.animationManager.getNextAnimationPosition(e)}},{key:"getPrevAnimationPosition",value:function(e){return this.animationManager.getPrevAnimationPosition(e)}},{key:"gotoAnimation",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.animationManager.gotoAnimation(e,t)}},{key:"hasNextAnimation",value:function(){return this.animationManager.hasNextAnimation()}},{key:"hasPrevAnimation",value:function(){return this.animationManager.hasPreAnimation()}},{key:"paintOnCanvas",value:function(e){this.paint(!1,void 0,Object.assign(Object.assign({},e),{skipClear:!0}))}},{key:"updatePageCount",value:function(e){this.pageCount=e,this.currPageIndex>=e&&(this.currPageIndex=0)}},{key:"removeElementsInContainer",value:function(e){var t,a=e.querySelector("[id='dcs-pgreader-wrapper']");a&&(null===(t=a.parentNode)||void 0===t||t.removeChild(a))}},{key:"appendElementsInContainer",value:function(e){var t=this.readerWrapper=document.createElement("div");t.id="dcs-pgreader-wrapper",Object.assign(t.style,{background:this.backgroundColor,position:"absolute",left:0,right:0,top:0,bottom:0});var a=this.canvas=(0,u.createCanvas)(this.canvasWidth,this.canvasHeight);a.style.background=this.backgroundColor,this.g=a.getContext("2d");var r=this.canvasWrapper=document.createElement("div");r.id="dcs-pgreader-canvaswrapper",Object.assign(r.style,{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}),Object.assign(a.style,{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)"}),r.appendChild(a);var n=this.mask=(0,u.createCanvas)(this.canvasWidth,this.canvasHeight);this.maskg=n.getContext("2d"),Object.assign(n.style,{position:"absolute",left:"50%",top:"50%",transform:"translate(-50%, -50%)",pointerEvents:"none"}),r.appendChild(n),t.appendChild(r),e.appendChild(t)}},{key:"resizeWindow",value:function(){var e=this;if(this.container&&this.doc){this.parentWidth=this.container.offsetWidth,this.parentHeight=this.container.offsetHeight;var t=this.isInFullScreenMode?window.innerWidth:this.parentWidth,a=this.isInFullScreenMode?window.innerHeight:this.parentHeight;t/a>this.docWidth/this.docHeight?t=this.docWidth/this.docHeight*a:a=this.docHeight/this.docWidth*t,this.zoom=t/this.docWidth,this.canvasWidth=t,this.canvasHeight=a,this.resizeCanvas(t,a),requestAnimationFrame((function(){e.paint()}))}}},{key:"resizeCanvas",value:function(e,t){this.canvasWrapper&&Object.assign(this.canvasWrapper.style,{width:e+"px",height:t+"px"}),this.viewHeight=t,this.canvasWidth=e,this.canvasHeight=t;var a=this.canvas,r=this.mask;a.width=2*e,a.height=2*this.canvasHeight,this.g.setTransform(2,0,0,2,0,0),a.style.width=e+"px",a.style.height=this.canvasHeight+"px",r.width=a.width,r.height=a.height,this.maskg.setTransform(2,0,0,2,0,0),r.style.width=a.style.width,r.style.height=a.style.height}},{key:"calcModelSize",value:function(e,t){if(e.ref){var a=e.refById=Object.create(null),r=!0,n=!1,i=void 0;try{for(var o,l=e.ref[Symbol.iterator]();!(r=(o=l.next()).done);r=!0){var c=o.value;a[c.id]=c}}catch(e){n=!0,i=e}finally{try{!r&&l.return&&l.return()}finally{if(n)throw i}}}e.refById||(e.refById=Object.create(null));var u=e.attrById=Object.create(null),h=!0,d=!1,f=void 0;try{for(var p,y=e.attr[Symbol.iterator]();!(h=(p=y.next()).done);h=!0){var v=p.value;u[v.id]=v}}catch(e){d=!0,f=e}finally{try{!h&&y.return&&y.return()}finally{if(d)throw f}}var g=e.pagesize,m=g.width,M=g.height,b=m,x=Object.create(null);if(g.special){var P=!0,k=!1,I=void 0;try{for(var T,w=g.special[Symbol.iterator]();!(P=(T=w.next()).done);P=!0){var N=T.value;x[N.index]=N,b<N.width&&(b=N.width)}}catch(e){k=!0,I=e}finally{try{!P&&w.return&&w.return()}finally{if(k)throw I}}}for(var A=0,C=e.pageCount,j=s.WpConst.PageGap,S=j,D=e.pages=[],z=0;z<C;z++){var L=Object.create(null);L.index=z,x[z]?(L.width=x[z].width,L.height=x[z].height):(L.width=m,L.height=M),A+=L.height+j,D[z]=L,L.y=S,L.x=(b-L.width)/2,S+=L.height+j}e.maxPageWidth=b,e.docHeight=A,this.doc=e,this.isCopy=!e.param.antiCopy,this.resizeWindow(1),this.pageLoadMode=t?t.mode:"nearest",t&&"nearest"===t.mode&&(this.nearestRange=t.k||3),this.preloadPages(this.currPageIndex||0)}},{key:"preloadPages",value:function(e){return M(this,void 0,void 0,r.default.mark((function t(){var a,n,i;return r.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=this.doc.pages,"eager"===this.pageLoadMode)for(n=0;n<a.length;n++)this.loadPageData(a[n]);else if("lazy"===this.pageLoadMode)this.loadPageData(a[e]);else if("nearest"===this.pageLoadMode)for(this.loadPageData(a[e]),i=1;i<=this.nearestRange;i++)e-i>=0&&this.loadPageData(a[e-i]),e+i<a.length&&this.loadPageData(a[e+i]);else console.error("Cannot parse your page load mode, it should either be eager, lazy, or nearest");case 2:case"end":return t.stop()}}),t,this)})))}},{key:"loadPageData",value:function(e){var t=this;this.isPageReady(e.index)||f.default.loadRes({resPrefixArr:this.resPrefixArr,urlPath:"/"+e.index+".json",pageIndex:e.index,docIndex:this.docIndex,type:"pageJson"},{callback:function(a){t.pageJsonLoaded.add(e.index),t.emit("event:pptState:change","pageLoaded",e.index),t.parsePageData(e,a),t.repaintPage(e)}})}},{key:"paint",value:function(e,t,a){a||f.default.setEnvironment({activeDoc:this.docIndex,activePage:this.currPageIndex});var r=a?a.ctx:this.g,n=a&&this.canvas?a.ctx.canvas.width/this.canvas.width:1;if(this.doc&&this.doc.pages){var i,o=a?a.pageIndex:this.currPageIndex,s=this.doc.pages[o];if(!s||!this.isPageReady(s.index))return this.paintBlankImage(r,a,n);if(a&&a.skipClear||r.clearRect(0,0,3*r.canvas.width,3*r.canvas.height),r.save(),r.setTransform(2*this.zoom*n,0,0,2*this.zoom*n,0,0),a&&a.transforms&&r.transform.apply(r,v(a.transforms)),r.lineWidth=1,i=(this.viewHeight/this.zoom-this.doc.pagesize.height)/2,r.fillStyle="#FFFFFF",r.strokeStyle="#CECECE",r.fillRect(s.x,i,s.width,s.height),r.strokeRect(s.x,i,s.width,s.height),r.fillStyle="black",r.beginPath(),r.rect(s.x,i,s.width,s.height),r.clip(),r.translate(s.x,i),!e){var l=!a||!a.hasOwnProperty("keepAnimState")||a.keepAnimState;this.paintPage(r,s,t,!!a,l)}r.restore(),this.processAutplayMedias()}}},{key:"processAutplayMedias",value:function(){var e=Object.keys(this.mediaToProcess);if(e.length>0){var t=!0,a=!1,r=void 0;try{for(var n,i=e[Symbol.iterator]();!(t=(n=i.next()).done);t=!0){var o=n.value;"autoplay"===this.mediaToProcess[o]?this.mediaController.autoPlayMedia(o):this.mediaController.stopMedia(o),delete this.mediaToProcess[o]}}catch(e){a=!0,r=e}finally{try{!t&&i.return&&i.return()}finally{if(a)throw r}}this.mediaController.updateState({},"anim")}}},{key:"paintBlankImage",value:function(e,t,a){var r=this.loadingImage;r&&r.complete&&r.width>0&&r.height>0&&(e.save(),e.setTransform(2*this.zoom*a,0,0,2*this.zoom*a,0,0),t&&t.transforms&&e.transform.apply(e,v(t.transforms)),e.drawImage(r,0,0,this.canvas.width/2,this.canvas.height/2),e.restore())}},{key:"paintPage",value:function(e,t,a,r,n){var i=this.player;this.canvasGifIdsToKeep.clear(),this.videoIdsToKeep.clear();var o=!0,s=!1,l=void 0;try{for(var c,h=t.layers[Symbol.iterator]();!(o=(c=h.next()).done);o=!0){var d=c.value,f=!0,p=!1,y=void 0;try{for(var v,g=d[Symbol.iterator]();!(f=(v=g.next()).done);f=!0){var m=v.value;if(!r||n){if(void 0===m.ref||(m=this.doc.refById[m.ref]))if(m.shapeID){var M=[],b=void 0,x=!0,P=!1,k=!1,I=-1,T=1/0;if(this.animationManager.currPage.index===t.index){var w=this.animationManager.getCurrentAnimationPosition();T=w.animIndex+("start"===w.animState?-1:0)}var N=!0,A=!1,C=void 0;try{for(var j,S=t.animation[Symbol.iterator]();!(N=(j=S.next()).done);N=!0){var D=j.value;if(I+=1,P)break;if(k&&D.length>0&&0===D[0].animationState)break;var z=!1,L=!0,O=!1,E=void 0;try{for(var R,_=D[Symbol.iterator]();!(L=(R=_.next()).done);L=!0){var U=R.value;if(m.shapeID===U.shapeID){if(1===U.animState&&(P=!0),!m.meta&&U.hasOwnProperty("newParaIdx")&&!isNaN(U.newParaIdx))continue;if(m.meta&&U.skipPara)continue;if(Number(U.paraIdx)>=0&&m.meta&&m.meta[2]!==U.newParaIdx)continue;if(-1===U.animTargetType&&-1===U.paraIdx&&0===m.t&&4===m.meta.length&&0!==m.meta[1]&&!U.isGroup||U.paraIdx>=0&&0!==m.t&&!m.hyperlinkType)continue;k=!0;var F=0===U.animType&&(-1!==[3,35,45].indexOf(U.animIdx)||U.emphAttr&&Array.isArray(U.emphAttr.pathPoints));if(2===U.animType&&(z=!0),0===U.animType&&z)continue;if(0===U.animationState){0===U.animType&&(x=!1);continue}1===U.animationState?(x=!1!==x||0===U.animType,F&&(b=void 0)):2===U.animationState&&(3===U.effectAfterPlay||4===U.effectAfterPlay&&T>I||0===U.animType&&12===U.animIdx?x=!1:0===U.animType?(x=!0,F&&(b=void 0)):(2===U.animType||U.isBackAfterPlay)&&(x=!1)),1===U.animType?U.stressContent&&(2===U.animationState&&U.stressContent&&(U.stressContent.count=60*U.stressContent.animInfo.endTime),M.push(U.stressContent)):3===U.animType?(2===U.animationState&&U.stressContent&&(U.stressContent.count=60*U.stressContent.animInfo.endTime),U.stressContent&&U.stressContent.count>=0&&(b=U.stressContent)):1===U.animationState&&a&&a[U.oldParaIdx]&&a[U.oldParaIdx].animInfo.shapeID===m.shapeID&&M.push(a[U.oldParaIdx])}}}catch(e){O=!0,E=e}finally{try{!L&&_.return&&_.return()}finally{if(O)throw E}}}}catch(e){A=!0,C=e}finally{try{!N&&S.return&&S.return()}finally{if(A)throw C}}if(x){e.save(),b&&i.play(b,e);var G=!0,Y=!1,V=void 0;try{for(var Q,B=M[Symbol.iterator]();!(G=(Q=B.next()).done);G=!0){var W=Q.value;W&&i.play(W,e)}}catch(e){Y=!0,V=e}finally{try{!G&&B.return&&B.return()}finally{if(Y)throw V}}this.switchPaintType(e,t,m,{isThumbnail:r}),e.restore()}}else this.switchPaintType(e,t,m,{isThumbnail:r})}else this.switchPaintType(e,t,m,{isThumbnail:r})}}catch(e){p=!0,y=e}finally{try{!f&&g.return&&g.return()}finally{if(p)throw y}}}}catch(e){s=!0,l=e}finally{try{!o&&h.return&&h.return()}finally{if(s)throw l}}r||((0,u.removeElementsByClassNameAndExcludeCertainIds)("canvasGif",this.canvasGifIdsToKeep),this.loadVideo&&this.mediaController.hidePlayersExcept(this.videoIdsToKeep))}},{key:"switchPaintType",value:function(e,t,a,r){switch(a.t){case s.ObjType.Text:this.paintText(e,t,a);break;case s.ObjType.Image:this.paintImage(e,t,a,r);break;case s.ObjType.Path:this.paintPath(e,t,a)}}},{key:"paintText",value:function(e,t,a){var r=a.v+"";""==r&&(r=" "),e.save();var n=this.doc.attrById[a.aid||0];n.gfont||(n.gfont=this.calcTextFont(n));var i=e.fillStyle,s=a.ctm;if(a.clip){var l=Object.create(null);l.v=a.clip,o.PathPainter.paintPath(e,l),e.clip()}s&&e.transform(s[0],s[1],s[2],s[3],s[4],s[5]),n.color&&("#000001"===e.strokeStyle?e.fillStyle=i:e.fillStyle=n.color),a.alpha&&(e.globalAlpha=a.alpha*e.globalAlpha),"#000004"===e.strokeStyle?e.font="900 "+n.gfont.replace("bold",""):e.font=n.gfont.replace("900","");var c=a.y,u=a.charX,h=a.startX,d=[];if(2==r.length&&r.charCodeAt(0)>=55296&&r.charCodeAt(0)<56320&&r.charCodeAt(1)>=56320&&r.charCodeAt(1)<57344)e.fillText(r,a.startX,c);else if(u){if(!a.parsedCharx){d.push(a.startX);for(var f=0;f<u.length;f++)if(f==u.length-1)"number"==typeof u[f]&&d.push(u[f]);else if("number"==typeof u[f+1])h=u[f],d.push(h);else{for(var p=parseInt(u[f+1].replace("g","")),y=1;y<p+1;y++)h+=u[f],d.push(h);f++}a.parsedCharx=d}if(r.length>1e3)e.fillText(r,a.startX,c);else for(var v=0;v<r.length;v++)v<r.length-1?e.fillText(r[v],a.parsedCharx[v],c,a.parsedCharx[v+1]-a.parsedCharx[v]):e.fillText(r[v],a.parsedCharx[v],c)}else e.fillText(r[0],a.startX,c);e.restore(),e.fillStyle=i}},{key:"calcTextFont",value:function(e){var t="";return e.b&&(t+="bold "),e.i&&(t+="italic "),(t+=e.size+"px ")+e.font}},{key:"paintImage",value:function(e,t,a,r){try{r=r||{};var n=a.mediaPath;if(n)return this.createMedia(e,n,a,r.isThumbnail);if((a.v.match(/GIF/)||a.v.match(/gif/))&&!r.isThumbnail&&(r.c&&60*r.c.animInfo.endTime<=r.c.count||!r.c))return void this.createGif(a);var i=this.loadedImages.get(a.v);if(a.alpha&&(e.globalAlpha=a.alpha*e.globalAlpha),i){e.save();var s=a.ctm;if(a.clip){var l=Object.create(null);l.v=a.clip,o.PathPainter.paintPath(e,l),e.clip()}s&&e.transform(s[0],s[1],s[2],s[3],s[4],s[5]);var c=a.rect;a.alpha&&(e.globalAlpha=a.alpha),a.repeat?(e.fillStyle=e.createPattern(i,"repeat"),e.fillRect(c[0],c[1],c[2],c[3])):e.drawImage(i,c[0],c[1],c[2],c[3]),e.restore(),e.globalAlpha=1}}catch(e){console.error("paint image error",e)}}},{key:"paintPath",value:function(e,t,a){e.save();var r=a.ctm;if(a.clip){var n=Object.create(null);n.v=a.clip,o.PathPainter.paintPath(e,n),e.clip()}o.PathPainter.paintPath(e,a),e.lineWidth=a.lineWidth,e.lineCap=a.lineCap,a.dashArr&&e.setLineDash(a.dashArr);var i=void 0;switch(a.type){case 1:!this.isInFullScreenMode||"#000001"!==e.strokeStyle&&"#000002"!==e.strokeStyle&&"#00000b"!==e.strokeStyle?(e.strokeStyle=a.paintInfo,e.stroke()):(e.strokeStyle=e.fillStyle,e.stroke());break;case 2:!this.isInFullScreenMode||"#000001"!==e.strokeStyle&&"#000002"!==e.strokeStyle&&"#00000a"!==e.strokeStyle?(e.fillStyle=a.paintInfo,e.fill("evenodd")):e.fill("evenodd");break;case 3:e.translate(a.rect[0],a.rect[1]),(i=this.loadedImages.get(a.paintInfo))&&((i.width>a.rect[2]||i.height>a.rect[3])&&e.scale(a.rect[2]/i.width,a.rect[3]/i.height),e.strokeStyle=e.createPattern(i,"repeat"),e.stroke()),e.translate(-a.rect[0],-a.rect[1]);break;case 4:(i=this.loadedImages.get(a.paintInfo))&&(e.save(),r&&Array.isArray(a.ctm)&&6==a.ctm.length?e.transform.apply(e,v(a.ctm)):r&&(e.translate(a.rect[0],a.rect[1]),e.transform(a.ctm[0],0,0,a.ctm[1],0,0),e.translate(-a.rect[0],-a.rect[1])),e.fillStyle=e.createPattern(i,"repeat"),e.fill(),e.restore());break;case 5:(0,o.paintPathGradient1)(e,a,a.rect,a.v,"stroke");break;case 6:(0,o.paintPathGradient1)(e,a,a.rect,a.v,"fill");break;case 7:(0,o.paintPathGradient2)(e,a,a.rect,a.v,"stroke");break;case 8:(0,o.paintPathGradient2)(e,a,a.rect,a.v,"fill")}e.restore(),e.lineWidth=.5,e.setLineDash([])}},{key:"createMedia",value:function(e,t,a,r){try{var n=a.mediaPath.slice(a.mediaPath.lastIndexOf(".")+1),i=-1!==["mp3","aac","ogg","webm","wav"].indexOf(n);if(!this.loadVideo||r){var o=(this.viewHeight/this.zoom-this.doc.pagesize.height)/2,s=this.loadedImages.get(a.v);s&&e.drawImage(s,1*a.rect[0],1*(a.rect[1]+o),1*a.rect[2],1*a.rect[3])}else if(i){var l=this.mediaController.appendAudio(a,(0,u.getTidyResourceUrl)(this.resPrefixArr[d.lastSuccessUrlIndex],a.v));this.videoIdsToKeep.add(l)}else{var c=this.mediaController.appendVideo(a,(0,u.getTidyResourceUrl)(this.resPrefixArr[d.lastSuccessUrlIndex],a.v));this.videoIdsToKeep.add(c)}}catch(e){console.error("create media error",e)}}},{key:"createGif",value:function(e){var t=(this.viewHeight/this.zoom-this.doc.pagesize.height)/2,a="gif-"+this.docIndex+"-"+e.shapeID+"-"+e.rect[0]+"-"+e.rect[1],r=this.zoom,n=(0,u.getTidyResourceUrl)(this.resPrefixArr[d.lastSuccessUrlIndex],e.v),i=e.rect[2]*r,o=e.rect[3]*r,s=document.getElementById(a);this.canvasGifIdsToKeep.add(a),s?Object.assign(s.style,{left:(e.rect[0]+e.rect[2]/2)*r-i/2+"px",top:(e.rect[1]+t)*r+"px",width:i+"px",height:o+"px"}):((s=document.createElement("img")).src=n,s.id=a,s.className="canvasGif",Object.assign(s.style,{position:"absolute",left:(e.rect[0]+e.rect[2]/2)*r-i/2+"px",top:(e.rect[1]+t)*r+"px",width:i+"px",height:o+"px"}),this.canvasWrapper.append(s),e.ctm&&Object.assign(s.style,{transform:"matrix("+e.ctm[0]+","+e.ctm[1]+","+e.ctm[2]+","+e.ctm[3]+",0,0)"}))}},{key:"parsePageData",value:function(e,t){Object.assign(e,t);var a=Object.create(null),r="",n=Object.create(null),i=void 0,o=void 0,l=void 0,c=void 0;if(e.layers[1]){var u=!0,h=!1,d=void 0;try{for(var f,p=e.layers[1][Symbol.iterator]();!(u=(f=p.next()).done);u=!0){var y=f.value;switch(y.t){case s.ObjType.Text:y.charx,(i=n[y.y])?i.height<y.h&&(i.height=y.h):((i=Object.create(null)).y=y.y,i.height=y.h,i.chars=[],i.phrases=[],n[y.y]=i);var v=y.charX,g=1,m=y.startX;if(o=y.v,l=Object.create(null),o&&(l.t=o[0]),l.x=m,(c=Object.create(null)).t=y.v,c.x=m,y.hyperlinkAddress&&(l.hyperlinkAddress=y.hyperlinkAddress),l.width=this.lastCharWidth(y.h),i.chars.push(l),v){for(var M=0;M<v.length;M++)if(M==v.length-1)"number"==typeof v[M]&&(l=Object.create(null),y.hyperlinkAddress&&(l.hyperlinkAddress=y.hyperlinkAddress),l.t=o[g],l.x=v[M],l.width=this.lastCharWidth(y.h),i.chars.push(l));else if("number"==typeof v[M+1])l=Object.create(null),y.hyperlinkAddress&&(l.hyperlinkAddress=y.hyperlinkAddress),m=v[M],l.t=o[g],l.x=m,l.width=v[M+1]-v[M],i.chars.push(l),g++;else{for(var b=parseInt(v[M+1].replace("g","")),x=1;x<b+1;x++)l=Object.create(null),y.hyperlinkAddress&&(l.hyperlinkAddress=y.hyperlinkAddress),m+=v[M],l.t=o[g],l.x=m,l.width=v[M],i.chars.push(l),g++;M++}var P=v.length-1;if("number"==typeof v[P])c.width=v[P]-c.x+this.lastCharWidth(y.h);else{var k=parseInt(v[P].replace("g",""));c.width=1!==P?v[P-2]-c.x+v[P-1]*(k+1):v[P-1]*(k+1)}}else y.ctm&&y.ctm[0]&&1!==y.ctm[0]?c.width=this.lastCharWidth(y.ctm[0]*y.h):c.width=l.width;y.meta&&(c.meta=y.meta,y.meta[0]<0&&(i.type=-1)),i.phrases.push(c),r+=y.v}y.shapeID&&(a[y.shapeID]||(a[y.shapeID]=[]),a[y.shapeID].push(y))}}catch(e){h=!0,d=e}finally{try{!u&&p.return&&p.return()}finally{if(h)throw d}}var I=null,T=!1,w=void 0,N=!0,A=!1,C=void 0;try{for(var j,S=e.layers[1][Symbol.iterator]();!(N=(j=S.next()).done);N=!0){var D=j.value;D.shapeID===w&&D.hyperlinkType&&T&&I&&!D.meta&&(D.meta=I),I=D.meta,w=D.shapeID,T=0===D.hyperlinkType&&0===D.t}}catch(e){A=!0,C=e}finally{try{!N&&S.return&&S.return()}finally{if(A)throw C}}var z=[];for(var L in n){n[L].x=n[L].chars[0].x;for(var O=void 0,E=0;E<n[L].chars.length;E++)O&&(O.width=n[L].chars[E].x-O.x),O=n[L].chars[E];n[L].width=n[L].chars[n[L].chars.length-1].x-n[L].chars[0].x+n[L].chars[n[L].chars.length-1].width,z.push(n[L])}z.sort(this.yComparator),e.lines=z,e.text=r;var R=new Set,_=!0,U=!1,F=void 0;try{for(var G,Y=e.animation[Symbol.iterator]();!(_=(G=Y.next()).done);_=!0){var V=G.value;(K=Number(V.paraIdx))>-1&&R.add(V.shapeID)}}catch(e){U=!0,F=e}finally{try{!_&&Y.return&&Y.return()}finally{if(U)throw F}}for(var Q in a){var B=[],W=!0,Z=!1,H=void 0;try{for(var q,J=e.animation[Symbol.iterator]();!(W=(q=J.next()).done);W=!0){var X=q.value;if(Number(Q)===X.shapeID){var K,$=a[Q];if((K=Number(X.paraIdx))>=0){0===B.length&&(B=this.paraDataSolve($));var ee=B[K];ee&&(X.shape=this.solveRectAimate(X.rect),X.textInfo=this.solveTextInfo(this.solveAimate(ee))),X.newParaIdx=Number(X.paraIdx)+Number(this.paraIdMin)}else X.shape=this.solveRectAimate(X.rect);"{}"!==JSON.stringify(X.emphAttr)?X.direct=[X.emphIdx,X.emphAttr]:X.direct=X.animDir,Number(X.startTime)<Number(X.endTime)&&(X.endTime=Number(X.endTime)-Number(X.startTime),16===X.animIdx&&X.endTime<.5&&(X.endTime=.5))}}}catch(e){Z=!0,H=e}finally{try{!W&&J.return&&J.return()}finally{if(Z)throw H}}}for(var te=[],ae=[],re=0;re<e.animation.length;re++){var ne=e.animation[re];if(-1===Number(ne.paraIdx)&&R.has(ne.shapeID)&&(ne.skipPara=!0),ae.push(ne),re===e.animation.length-1){te.push(ae);break}var ie=e.animation[re+1];0!==ie.startCondition&&2!==ie.startCondition||(te.push(ae),ae=[])}for(var oe=te.length-1;oe>=0;oe--){for(var se=te[oe],le=se.length-1;le>=0;le--){var ce=se[le];ce.oldParaIdx=le,ce.error&&se.splice(le,1)}0===se.length&&te.splice(oe,1)}var ue=[];this.mediaAutoPlayInfo[e.index]={};for(var he=0;he<te.length;he++)if(Array.isArray(te[he])&&te[he].length>0){if(2!==te[he][0].startCondition){var de=!0,fe=!1,pe=void 0;try{for(var ye,ve=ue[Symbol.iterator]();!(de=(ye=ve.next()).done);de=!0){var ge=ye.value;this.mediaAutoPlayInfo[e.index][ge]&&(this.mediaAutoPlayInfo[e.index][ge].end=he)}}catch(e){fe=!0,pe=e}finally{try{!de&&ve.return&&ve.return()}finally{if(fe)throw pe}}}var me=!0,Me=!1,be=void 0;try{for(var xe,Pe=te[he][Symbol.iterator]();!(me=(xe=Pe.next()).done);me=!0){var ke=xe.value;if(4===ke.animType){var Ie=this.mediaController.getPlayerIdFromShapeId(ke.shapeID);ue.push(Ie),this.mediaAutoPlayInfo[e.index][Ie]={start:he}}}}catch(e){Me=!0,be=e}finally{try{!me&&Pe.return&&Pe.return()}finally{if(Me)throw be}}}e.animation=te,this.emit("animationListChange",e),this.loadPageImage(e)}}},{key:"yComparator",value:function(e,t){return e.y>t.y?1:e.y<t.y?-1:0}},{key:"calTextHeightOffset",value:function(e){return 2*Math.round(e/10)}},{key:"lastCharWidth",value:function(e){return e-1*Math.round(e/10)}},{key:"loadPageImage",value:function(e){return M(this,void 0,void 0,r.default.mark((function t(){var a,n,i,o,l,c,u,h,d,f,p,y,v,g,m,M,b,x,P;return r.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:this.pageImgToDownload.get(e.index)||this.pageImgToDownload.set(e.index,[]),a=this.pageImgToDownload.get(e.index),n=!0,i=!1,o=void 0,t.prev=5,l=e.layers[Symbol.iterator]();case 7:if(n=(c=l.next()).done){t.next=47;break}u=c.value,h=!0,d=!1,f=void 0,t.prev=12,p=u[Symbol.iterator]();case 14:if(h=(y=p.next()).done){t.next=30;break}if(!(v=y.value).ref){t.next=20;break}if(v=this.doc.refById[v.ref]){t.next=20;break}return t.abrupt("continue",27);case 20:t.t0=v.t,t.next=t.t0===s.ObjType.Image?23:t.t0===s.ObjType.Path?25:27;break;case 23:return-1===a.indexOf(v.v)&&a.push(v.v),t.abrupt("break",27);case 25:return 4!==v.type&&3!==v.type||"string"!=typeof v.paintInfo||-1!==v.paintInfo.indexOf("rgb")||-1===a.indexOf(v.paintInfo)&&a.push(v.paintInfo),t.abrupt("break",27);case 27:h=!0,t.next=14;break;case 30:t.next=36;break;case 32:t.prev=32,t.t1=t.catch(12),d=!0,f=t.t1;case 36:t.prev=36,t.prev=37,!h&&p.return&&p.return();case 39:if(t.prev=39,!d){t.next=42;break}throw f;case 42:return t.finish(39);case 43:return t.finish(36);case 44:n=!0,t.next=7;break;case 47:t.next=53;break;case 49:t.prev=49,t.t2=t.catch(5),i=!0,o=t.t2;case 53:t.prev=53,t.prev=54,!n&&l.return&&l.return();case 56:if(t.prev=56,!i){t.next=59;break}throw o;case 59:return t.finish(56);case 60:return t.finish(53);case 61:if(this.pageImgToDownload.set(e.index,a.slice()),0!==a.length){t.next=66;break}this.emit("event:pptState:change","pageImgLoaded",e.index),t.next=85;break;case 66:for(g=!0,m=!1,M=void 0,t.prev=69,b=a[Symbol.iterator]();!(g=(x=b.next()).done);g=!0)P=x.value,this.loadImage(e,P);t.next=77;break;case 73:t.prev=73,t.t3=t.catch(69),m=!0,M=t.t3;case 77:t.prev=77,t.prev=78,!g&&b.return&&b.return();case 80:if(t.prev=80,!m){t.next=83;break}throw M;case 83:return t.finish(80);case 84:return t.finish(77);case 85:case"end":return t.stop()}}),t,this,[[5,49,53,61],[12,32,36,44],[37,,39,43],[54,,56,60],[69,73,77,85],[78,,80,84]])})))}},{key:"loadImage",value:function(e,t){var a=this;f.default.loadRes({type:"image",resPrefixArr:this.resPrefixArr,urlPath:t,pageIndex:e.index,docIndex:this.docIndex},{callback:function(r){var n=a.pageImgToDownload.get(e.index)||[],i=n.indexOf(t);i>-1&&n.splice(i,1),a.pageImgToDownload.set(e.index,n.slice()),0===n.length&&a.emit("event:pptState:change","pageImgLoaded",e.index),a.loadedImages.set(t,r),a.repaintPage(e)},overwriteCb:!1})}},{key:"repaintPage",value:function(e){var t=this;e.index!==this.currPageIndex||this.animationManager.isDuringAnimation()||(clearTimeout(this.repaintTimer),this.repaintTimer=setTimeout((function(){t.paint()}),100))}},{key:"fullStateListener",value:function(){this.isInFullScreenMode}},{key:"solveRectAimate",value:function(e){var t=Object.create(null);return t.textRect=Object.create(null),t.textRect.x=e[0],t.textRect.y=e[1],t.textRect.width=e[2],t.textRect.height=e[3],t.shapeRect=Object.create(null),t.shapeRect.x=e[0],t.shapeRect.y=e[1],t.shapeRect.width=e[2],t.shapeRect.height=e[3],t}},{key:"solveTextInfo",value:function(e){if(e){var t=[],a=e.textRect;return t[0]=a.x,t[1]=a.y,t[2]=a.width,t[3]=a.height,t}}},{key:"solveAimate",value:function(e){var t=void 0;if(1===e.length)t=this.solveAimateEach(e[0]);else{var a=[],r=!0,n=!1,i=void 0;try{for(var o,s=e[Symbol.iterator]();!(r=(o=s.next()).done);r=!0){var l=o.value;a.push(this.solveAimateEach(l))}}catch(e){n=!0,i=e}finally{try{!r&&s.return&&s.return()}finally{if(n)throw i}}t=this.calAllFlyerRect(a)}if(t)return t.shapeRect&&!t.textRect?(t.textRect=Object.create(null),t.textRect.x=t.shapeRect.x,t.textRect.y=t.shapeRect.y,t.textRect.width=t.shapeRect.width,t.textRect.height=t.shapeRect.height):!t.shapeRect&&t.textRect&&(t.shapeRect=Object.create(null),t.shapeRect.x=t.textRect.x,t.shapeRect.y=t.textRect.y,t.shapeRect.width=t.textRect.width,t.shapeRect.height=t.textRect.height),t}},{key:"calAllFlyerRect",value:function(e){for(var t=this.textOrShape(e[0]),a=1;a<e.length;a++){var r=this.textOrShape(e[a]);r.x1<t.x1&&(t.x1=r.x1),r.y1<t.y1&&(t.y1=r.y1),r.x2>t.x2&&(t.x2=r.x2),r.y2>t.y2&&(t.y2=r.y2)}var n=Object.create(null);return n.shapeRect=Object.create(null),n.shapeRect.x=t.x1,n.shapeRect.y=t.y1,n.shapeRect.width=t.x2-t.x1,n.shapeRect.height=t.y2-t.y1,n}},{key:"textOrShape",value:function(e){var t=Object.create(null);return e.shapeRect&&!e.textRect?(t.x1=e.shapeRect.x,t.y1=e.shapeRect.y,t.x2=e.shapeRect.width+e.shapeRect.x,t.y2=e.shapeRect.height+e.shapeRect.y):!e.shapeRect&&e.textRect&&(t.x1=e.textRect.x,t.y1=e.textRect.y,t.x2=e.textRect.width+e.textRect.x,t.y2=e.textRect.height+e.textRect.y),t}},{key:"solveAimateEach",value:function(e){var t=void 0;switch(e.t){case s.ObjType.Text:t=this.calAnimateTextInfo(e);break;case s.ObjType.Image:t=this.calAnimateImageInfo(e);break;case s.ObjType.Path:t=this.calAnimatePathInfo(e)}return t}},{key:"calAnimatePathInfo",value:function(e){var t=Object.create(null);return t.shapeRect=Object.create(null),t.shapeRect.x=e.rect[0],t.shapeRect.y=e.rect[1],t.shapeRect.width=e.rect[2],t.shapeRect.height=e.rect[3],t}},{key:"calAnimateImageInfo",value:function(e){var t=Object.create(null);return t.shapeRect=Object.create(null),t.shapeRect.x=e.rect[0],t.shapeRect.y=e.rect[1],t.shapeRect.width=e.rect[2],t.shapeRect.height=e.rect[3],t}},{key:"calAnimateTextInfo",value:function(e){var t,a=Object.create(null),r=void 0,n=void 0;e.charx,(r=a[e.y])?r.height<e.h&&(r.height=e.h):((r=Object.create(null)).y=e.y,r.height=e.h,r.chars=[],a[e.y]=r);var i=e.charX,o=1,s=e.startX;if(t=e.v,(n=Object.create(null)).t=t[0],n.x=s,n.width=this.lastCharWidth(e.h),r.chars.push(n),i)for(var l=0;l<i.length;l++)if(l==i.length-1)"number"==typeof i[l]&&((n=Object.create(null)).t=t[o],n.x=i[l],n.width=this.lastCharWidth(e.h),r.chars.push(n));else if("number"==typeof i[l+1])n=Object.create(null),s=i[l],n.t=t[o],n.x=s,n.width=i[l+1]-i[l],r.chars.push(n),o++;else{for(var c=parseInt(i[l+1].replace("g","")),u=1;u<c+1;u++)n=Object.create(null),s+=i[l],n.t=t[o],n.x=s,n.width=i[l],r.chars.push(n),o++;l++}var h=[];for(var d in a){a[d].x=a[d].chars[0].x;for(var f=void 0,p=0;p<a[d].chars.length;p++)f&&(f.width=a[d].chars[p].x-f.x),f=a[d].chars[p];a[d].width=a[d].chars[a[d].chars.length-1].x-a[d].chars[0].x+a[d].chars[a[d].chars.length-1].width,h.push(a[d])}h.sort(this.yComparator);var y=!0,v=!1,g=void 0;try{for(var m,M=h[Symbol.iterator]();!(y=(m=M.next()).done);y=!0){var b=m.value;b.y=b.y-b.height+this.calTextHeightOffset(b.height)}}catch(e){v=!0,g=e}finally{try{!y&&M.return&&M.return()}finally{if(v)throw g}}var x=Object.create(null);return x.textRect=Object.create(null),x.textRect.x=h[0].x,x.textRect.y=h[0].y,x.textRect.width=h[0].width,x.textRect.height=h[0].height,x}},{key:"paraDataSolve",value:function(e){var t={},a=!0,r=!1,n=void 0;try{for(var i,o=e[Symbol.iterator]();!(a=(i=o.next()).done);a=!0){var s=i.value;s.meta&&(t[s.meta[2]]||(t[s.meta[2]]=[]),t[s.meta[2]].push(s))}}catch(e){r=!0,n=e}finally{try{!a&&o.return&&o.return()}finally{if(r)throw n}}var l=[];for(var c in t)l.push(c);l.sort((function(e,t){return e-t}));for(var u=this.paraIdMin=Number(l[0]),h=[],d=0;d<l.length;d++)h[Number(l[d])-u]=t[l[d]];return h}},{key:"ntpTime",get:function(){return Date.now()+this.ntpDiff}},{key:"docWidth",get:function(){return this.doc&&this.doc.pagesize&&this.doc.pagesize.width||100}},{key:"docHeight",get:function(){return this.doc&&this.doc.pagesize&&this.doc.pagesize.height||100}},{key:"isInFullScreenMode",get:function(){return document.fullscreenElement||document.msFullscreenElement||document.mozFullScreenElement||document.webkitFullscreenElement||!1}},{key:"currPage",get:function(){return this.doc&&this.doc.pages?this.doc.pages[this.currPageIndex]:void 0}}]),t}(i.default)).loadedFonts=new Set},1310:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.lastSuccessUrlIndex=void 0;var r,n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=(r=a(4559))&&r.__esModule?r:{default:r},o=a(5357),s=t.lastSuccessUrlIndex=0,l=new(function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.env={activeDoc:-1,activePage:-1},this.resTable=new Map,this.maxConcurrentDownload=4,this.minRetryInterval=1e3,this.maxRetryCount=5,this.loadedFonts=[]}return n(e,[{key:"stopDownloadResourceOfDoc",value:function(e){var t=[];this.resTable.forEach((function(a,r){a.docIndex===e&&t.push(r)}));var a=!0,r=!1,n=void 0;try{for(var i,o=t[Symbol.iterator]();!(a=(i=o.next()).done);a=!0){var s=i.value;this.resTable.delete(s)}}catch(e){r=!0,n=e}finally{try{!a&&o.return&&o.return()}finally{if(r)throw n}}}},{key:"setEnvironment",value:function(e){this.env=e,this.checkResourceTask()}},{key:"loadRes",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.hasResource(e)){var a=this.getKeyFromResource(e),r=this.resTable.get(a);this.updateResource(e,t),"fetched"===r.state&&this.onResLoaded(r)}else this.addNewResource(e,t.callback)}},{key:"getKeyFromResource",value:function(e){return e.urlPath+"_"+e.docIndex}},{key:"resourcePriority",value:function(e,t){if(!e)return 1;if(!t)return-1;var a=this.getResourcePriorityRate(e),r=this.getResourcePriorityRate(t);return a>r?-1:a<r?1:0}},{key:"getResourcePriorityRate",value:function(e){var t=e.docIndex===this.env.activeDoc,a=e.docIndex===this.env.activeDoc&&-1!==e.pageIndexArr.indexOf(this.env.activePage),r=0;if(t&&e.pageIndexArr.length>0){r=999;var n=!0,i=!1,o=void 0;try{for(var s,l=e.pageIndexArr[Symbol.iterator]();!(n=(s=l.next()).done);n=!0){var c=s.value;r=Math.min(r,Math.abs(c-this.env.activePage))}}catch(e){i=!0,o=e}finally{try{!n&&l.return&&l.return()}finally{if(i)throw o}}}var u=0;if(t&&e.pageIndexArr.length>0){u=999;var h=!0,d=!1,f=void 0;try{for(var p,y=e.pageIndexArr[Symbol.iterator]();!(h=(p=y.next()).done);h=!0){var v=p.value;u=Math.min(u,v)}}catch(e){d=!0,f=e}finally{try{!h&&y.return&&y.return()}finally{if(d)throw f}}}return("summaryJson"===e.type?1e6:0)+(t?1e5:0)+(a?1e4:0)+("pageJson"===e.type?1e3:0)-10*r+u}},{key:"hasResource",value:function(e){var t=this.getKeyFromResource(e);return this.resTable.has(t)}},{key:"addNewResource",value:function(e,t){var a=this.getKeyFromResource(e),r=[];t&&r.push(t);var n=[e.pageIndex];this.resTable.set(a,Object.assign(Object.assign({},e),{currDomainIndex:void 0,pageIndexArr:n,callbacks:r,result:null,state:"toFetch",errorCount:0,lastFailAt:0})),this.checkResourceTask()}},{key:"updateResource",value:function(e,t){var a=this.getKeyFromResource(e),r=this.resTable.get(a);if(!r)return this.addNewResource(e,t.callback);var n=r.pageIndexArr.slice();-1===n.indexOf(e.pageIndex)&&n.push(e.pageIndex);var i=r.callbacks.slice();t.overwriteCb&&t.callback?i=[t.callback]:t.callback&&i.push(t.callback),Object.assign(r,{pageIndexArr:n,callbacks:i}),this.resTable.set(a,r),this.checkResourceTask()}},{key:"checkResourceTask",value:function(){var e=this;clearTimeout(this.checkTaskTimer),this.checkTaskTimer=setTimeout((function(){e._checkResourceTask()}),0)}},{key:"_checkResourceTask",value:function(){for(var e=this,t=this.resToFetch.sort((function(t,a){return e.resourcePriority(e.resTable.get(t),e.resTable.get(a))})),a=5e3,r=this.resFetching.length;r<this.maxConcurrentDownload&&0!==t.length;r++)for(var n=0;n<t.length;n++){var i=t[n],o=this.resTable.get(i),s=o.lastFailAt;if(0!==s){var l=Date.now(),c=this.minRetryInterval*o.errorCount;if(l-s<c){a=Math.max(1e3,Math.min(a,c-(l-s)));continue}}var u=t.shift();this.downloadRes(this.resTable.get(u));break}t.length>0&&setTimeout((function(){e.checkResourceTask()}),a)}},{key:"downloadRes",value:function(e){var a=this,r=this;if(e.state="fetching",void 0===e.currDomainIndex&&(e.currDomainIndex=s),"pageJson"===e.type||"summaryJson"===e.type)i.default.get(this.getResourceUrl(e),{timeout:1e4}).then((function(r){t.lastSuccessUrlIndex=s=e.currDomainIndex||0;var n=a.getKeyFromResource(e),i=a.resFetching.indexOf(n);i>-1&&a.resFetching.splice(i,1),e.state="fetched",e.result=r.data,a.onResLoaded(e),a.checkResourceTask()})).catch((function(t){return e.errorCount=e.errorCount+1,e.errorCount>=1&&(e.currDomainIndex=((e.currDomainIndex||0)+1)%e.resPrefixArr.length),e.errorCount>=r.maxRetryCount?e.state="failed":e.state="toFetch",e.lastFailAt=Date.now(),r.checkResourceTask(),Promise.reject(t)}));else if("image"===e.type){var n=new Image,o=void 0,l=function(){clearTimeout(o),e.errorCount=e.errorCount+1,e.errorCount>=1&&(e.currDomainIndex=((e.currDomainIndex||0)+1)%e.resPrefixArr.length),e.errorCount>=r.maxRetryCount?e.state="failed":e.state="toFetch",e.lastFailAt=Date.now(),a.checkResourceTask()};o=setTimeout((function(){l()}),1e4),n.onload=function(){clearTimeout(o),t.lastSuccessUrlIndex=s=e.currDomainIndex||0,n.complete&&n.width>0&&n.height>0?(e.result=n,e.state="fetched",a.onResLoaded(e)):(e.errorCount=e.errorCount+1,e.errorCount>=r.maxRetryCount?e.state="failed":e.state="toFetch",e.lastFailAt=Date.now()),a.checkResourceTask()},n.onerror=l,n.src=this.getResourceUrl(e),n.crossOrigin="anonymous"}else if("font"===e.type){e.state="fetched";var c=e.payload.fontName;if(!c)return console.error("字体下载路径或者字体名字未提供",c);try{if(-1===this.loadedFonts.indexOf(c)){this.loadedFonts.push(c);var u=e.urlPath,h=document.createElement("style");h.setAttribute("type","text/css"),h.appendChild(document.createTextNode("@font-face {\n  font-family: "+c+";\n  src: "+u+";\n}\n")),document.head.appendChild(h),this.onResLoaded(e)}}catch(e){console.error("字体加载异常",e)}}}},{key:"getResourceUrl",value:function(e){var t=e.resPrefixArr[e.currDomainIndex||0],a=e.urlPath;return(0,o.getTidyResourceUrl)(t,a)}},{key:"onResLoaded",value:function(e){if(Array.isArray(e.callbacks)){var t=!0,a=!1,r=void 0;try{for(var n,i=e.callbacks[Symbol.iterator]();!(t=(n=i.next()).done);t=!0)(0,n.value)(e.result)}catch(e){a=!0,r=e}finally{try{!t&&i.return&&i.return()}finally{if(a)throw r}}}e.callbacks=[]}},{key:"resToFetch",get:function(){var e=[];return this.resTable.forEach((function(t,a){"toFetch"===t.state&&e.push(a)})),e}},{key:"resFetching",get:function(){var e=[];return this.resTable.forEach((function(t,a){"fetching"===t.state&&e.push(a)})),e}}]),e}());t.default=l},5357:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.createCanvas=function(e,t){var a=document.createElement("canvas");return a.width=2*e,a.height=2*t,a.getContext("2d").setTransform(2,0,0,2,0,0),a.style.width=e+"px",a.style.height=t+"px",a},t.throttle=function(e,t,a){t||(t=250);var r=void 0,n=void 0;return function(){var i=a||this,o=+new Date,s=arguments;r&&o<r+t?(clearTimeout(n),n=setTimeout((function(){r=o,e.apply(i,s)}),t)):(r=o,e.apply(i,s))}};var a=t.removeElement=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},r=(t.removeElementsByTagName=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=Array.prototype.slice.apply(document.getElementsByTagName(e));if(r.length>0&&t)setTimeout((function(){for(var e=0;e<r.length;e++)a(r[e])}),0);else if(r.length>0)for(var n=0;n<r.length;n++)a(r[n])},t.removeElementsByClassName=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=Array.prototype.slice.apply(document.getElementsByClassName(e));if(r.length>0&&t)setTimeout((function(){for(var e=0;e<r.length;e++)a(r[e])}),0);else if(r.length>0)for(var n=0;n<r.length;n++)a(r[n])},t.removeElementsByClassNameAndExcludeCertainIds=function(e,t){for(var r=Array.prototype.slice.apply(document.getElementsByClassName(e)),n=0;n<r.length;n++)r[n].id&&(t.has(Number(r[n].id))||t.has(r[n].id.toString()))||a(r[n])},t.parseUrl=function(e){var t=e.lastIndexOf("/"),a=e.substring(t+1);return-1!==a.indexOf(".json")||-1!==a.indexOf(".jpg")||-1!==a.indexOf(".png")||-1!==a.indexOf(".jpeg")?{resPrefix:e.substring(0,t),res:a}:{resPrefix:e,res:""}},void(t.getTidyResourceUrl=function(e,t){return e.endsWith("/")&&(e=e.slice(0,e.length-1)),t.startsWith("/")&&(t=t.slice(1)),e+"/"+t})),n=void 0;void 0!==document.hidden?(r="hidden",n="visibilitychange"):void 0!==document.msHidden?(r="msHidden",n="msvisibilitychange"):void 0!==document.webkitHidden&&(r="webkitHidden",n="webkitvisibilitychange"),t.registerVisibleChangeHandler=function(e,t){function a(){document[r]?t():e()}return document.addEventListener(n,a,!1),function(){document.removeEventListener(n,a)}},t.isPageVisible=function(){return!document[r]}},611:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r,n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=function(e){function t(e,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a));return r.el.innerText="点击同步",r.el.onclick=function(t){r.hide(),e.onAttributeUpdated(e.data)},r.el.style.pointerEvents="all",Object.assign(r.el.style,{display:"none",height:"40px",width:"56px",lineHeight:"40px",verticalAlign:"middle",padding:"0 8px",color:"skyblue",fontSize:"14px",cursor:"pointer"}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"show",value:function(){this.el.style.display="inline-block"}},{key:"hide",value:function(){this.el.style.display="none"}}]),t}(((r=a(1298))&&r.__esModule?r:{default:r}).default);t.default=i},4616:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r,n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=function(e){function t(e,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a));return Object.assign(r.el.style,{width:"fit-content",display:"none",height:"40px",position:"absolute",bottom:"-46px",background:"#f0f0f0",border:"1px solid #aaa",borderRadius:"8px",left:"50%",transform:"translateX(-50%)"}),r.el.addEventListener("mouseover",(function(){r.show()})),r.el.addEventListener("mouseleave",(function(){r.fade()})),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"show",value:function(){this.el.style.display="flex",clearTimeout(this.fadeTimer),this.fadeTimer=null}},{key:"fade",value:function(){var e=this;this.fadeTimer||(this.fadeTimer=setTimeout((function(){e.el.style.display="none",clearTimeout(e.fadeTimer),e.fadeTimer=null}),40))}}]),t}(((r=a(1298))&&r.__esModule?r:{default:r}).default);t.default=i},2815:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=o(a(1298)),n=a(8411),i=o(a(5311));function o(e){return e&&e.__esModule?e:{default:e}}var s=function(e){function t(e,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a)),o=document.createElement("div");return r.el.appendChild(o),r.icon=o,(0,n.appendTooltip)(o,"最大化"),r.el.onclick=function(t){e.maximum()},Object.assign(r.el.style,{display:"inline-flex",height:"40px",width:"24px",color:"white",flexDirection:"row",alignItems:"center",verticalAlign:"top",margin:"0 4px"}),Object.assign(o.style,{display:"inline-block",height:"20px",width:"20px",backgroundSize:"contain",pointerEvents:"auto",cursor:"pointer",backgroundImage:"url("+i.default+")"}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(r.default);t.default=s},1298:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),r=function(){function e(t,a,r){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.children={},this.player=t,this.name=a,this.el=r||document.createElement("div")}return a(e,[{key:"addChild",value:function(t,a){var r=new e(this.player,t,a);return this.addChildComp(r),r}},{key:"addChildComp",value:function(e){return this.children[e.name]=e,this.el.appendChild(e.el),e}}]),e}();t.default=r},1445:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=o(a(1298)),n=o(a(9929)),i=o(a(2891));function o(e){return e&&e.__esModule?e:{default:e}}var s=function(e){function t(e,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a));return r.updateControl=function(){Object.assign(r.el.style,{cursor:r.player.controlVideo?"pointer":"none",pointerEvents:r.player.controlVideo?"auto":"none"})},r.update=function(e){r.el.style.backgroundImage=!0===e?"url("+n.default+")":"url("+i.default+")"},r.update(e.paused()),r.el.onclick=function(t){r.player.controlVideo&&(e.paused()?e.onNativePlayChange(!0):e.onNativePlayChange(!1))},r.updateControl(),r.player.addControlUpdateCallback(r.updateControl),Object.assign(r.el.style,{display:"inline-block",height:"100%",width:"40px",backgroundSize:"contain"}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(r.default);t.default=s},59:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r,n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=(r=a(1298))&&r.__esModule?r:{default:r},o=a(5688);function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var c=function(e){function t(e,a){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;s(this,t);var n=l(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a));n.isDragging=!1,n.lastPercent=0,n.updateControl=function(){Object.assign(n.el.style,{pointerEvents:n.player.controlVideo?"auto":"none",cursor:n.player.controlVideo?"pointer":"auto"})},n.onMouseDown=function(e){n.player.controlVideo&&(e.stopPropagation(),e.preventDefault(),n.isDragging=!0,n.lastPercent=-1,n.onMouseMove(e,!0))},n.onMouseMove=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(n.player.controlVideo&&n.isDragging){e.stopPropagation(),e.preventDefault();var a=n.getPercent(e);n.lastPercent=a,n.player.onNativeSeek(a,!0,t),n.updateProgress(a)}},n.onMouseUp=function(e){if(n.player.controlVideo){if(n.isDragging){e.stopPropagation(),e.preventDefault();var t=n.lastPercent;n.updateProgress(t),n.player.onNativeSeek(t,!1,!1)}n.isDragging=!1}},n.getPercent=function(e){var t=0,a=n.widget.getBoundingClientRect(),r=a.left;t=e.type.search(/mouse|click/)>-1?e.clientX-r:e.touches[0].clientX-r;var i=a.width;return t<0&&(t=0),t>i&&(t=i),t/i},n.updateProgress=function(e){n.frontBar.style.width=100*e+"%"};var i=document.createElement("div");n.el=i;var c=document.createElement("div");i.appendChild(c);var u=document.createElement("div"),h=document.createElement("div");return c.appendChild(u),c.appendChild(h),Object.assign(h.style,{width:100*r+"%"}),n.widget=i,n.frontBar=h,n.bindEvents(),n.player.v.addEventListener("timeupdate",(function(){var e=n.player.currentTime();e=e<=.1+1e-4?0:e,(0,o.isVideoEnd)(e,n.player.duration())&&(e=n.player.duration()),n.updateProgress(e/n.player.duration())})),n.updateControl(),n.player.addControlUpdateCallback(n.updateControl),Object.assign(i.style,{display:"inline-block",height:"100%",width:"220px",position:"relative"}),Object.assign(c.style,{margin:"4px 0",height:"calc(100% - 8px)",boxSizing:"border-box",border:"1px solid #909090",bordeRadius:"4px",position:"relative",overflow:"hidden"}),Object.assign(u.style,{height:"100%",position:"absolute",top:"50%",transform:"translateY(-50%)",width:"100%",backgroundColor:"#fafafa"}),Object.assign(h.style,{height:"100%",position:"absolute",top:"50%",transform:"translateY(-50%)",backgroundColor:"#a1a1a1"}),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"bindEvents",value:function(){this.widget.addEventListener("mousedown",this.onMouseDown),this.widget.addEventListener("touchstart",this.onMouseDown),window.document.addEventListener("mousemove",this.onMouseMove),window.document.addEventListener("touchmove",this.onMouseMove),window.document.addEventListener("mouseup",this.onMouseUp),window.document.addEventListener("touchend",this.onMouseUp)}},{key:"destroy",value:function(){this.widget.removeEventListener("mousedown",this.onMouseDown),this.widget.removeEventListener("touchstart",this.onMouseDown),window.document.removeEventListener("mousemove",this.onMouseMove),window.document.removeEventListener("touchmove",this.onMouseMove),window.document.removeEventListener("mouseup",this.onMouseUp),window.document.removeEventListener("touchend",this.onMouseUp)}}]),t}(i.default);t.default=c},9645:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r,n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=(r=a(1298))&&r.__esModule?r:{default:r},o=a(5688),s=function(e){function t(e,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a)),n=document.createElement("span");return r.text=n,r.el.appendChild(n),r.player.v.addEventListener("timeupdate",(function(t){r.update(r.player.currentTime(),e.duration())})),r.player.v.addEventListener("durationchange",(function(t){r.update(r.player.currentTime(),e.duration())})),r.update(r.player.currentTime(),e.duration()),Object.assign(r.el.style,{display:"inline-block",height:"40px",width:"84px",whiteSpace:"nowrap",color:"black",verticalAlign:"top",marginLeft:"16px"}),Object.assign(n.style,{lineHeight:"40px",height:"40px",verticalAlign:"middle",fontSize:"14px"}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"update",value:function(e,t){var a=!1,r="00:00";e||(e=0),(0,o.isVideoEnd)(e,t)&&(e=t),"number"!=typeof t||isNaN(t)||(r=l(t,a=t>=3600));var n=l(e,a)+" / "+r;this.text.innerText=n}}]),t}(i.default);function l(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=function(e){return e<10?"0"+e:""+e},r=(e=e>1?Math.round(e):0)%60,n=(e=~~((e-=r)/60))%60,i=~~(e/60);return 0===i&&!1===t?a(n)+":"+a(r):i+":"+a(n)+":"+a(r)}t.default=s},8411:function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),r=function(){function e(t,a){var r=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.onMouseOver=function(){r.tooltipText.style.visibility="visible"},this.onTouchStart=function(){clearTimeout(r.hideTimer),r.hideTimer=setTimeout((function(){r.onMouseLeave()}),1e3)},this.onMouseLeave=function(){r.tooltipText.style.visibility="hidden"};var n=document.createElement("span");n.innerText=a,t.appendChild(n),Object.assign(t.style,{position:"relative",display:"inline-block"}),Object.assign(n.style,{visibility:"hidden",width:"120px",backgroundColor:"black",color:" #fff",textAlign:"center",borderRadius:"6px",padding:"5px 0",position:"absolute",zIndex:1,bottom:" 30px",left:"50%",transform:"translateX(-50%)"}),this.widget=t,this.tooltipText=n,this.bindEvents()}return a(e,[{key:"bindEvents",value:function(){this.widget.addEventListener("mouseover",this.onMouseOver),this.widget.addEventListener("mouseleave",this.onMouseLeave),this.widget.addEventListener("touchstart",this.onTouchStart),window.document.addEventListener("touchend",this.onMouseLeave)}}]),e}();t.appendTooltip=function(e,t){return new r(e,t)}},3711:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),n=l(a(1298)),i=a(8411),o=l(a(5525)),s=l(a(3057));function l(e){return e&&e.__esModule?e:{default:e}}var c=function(e){function t(e,a){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,a)),n=document.createElement("div");return r.el.appendChild(n),r.icon=n,(0,i.appendTooltip)(n,"开关本端声音"),r.el.onclick=function(t){e.muted()?e.mute(!1,!0):e.mute(!0,!0)},r.player.v.addEventListener("volumechange",(function(){r.update(r.player.v.muted)})),r.update(r.player.v.muted),Object.assign(r.el.style,{display:"inline-flex",height:"40px",width:"24px",color:"white",flexDirection:"row",alignItems:"center",verticalAlign:"top",margin:"0 4px"}),Object.assign(n.style,{display:"inline-block",height:"20px",width:"20px",backgroundSize:"contain",pointerEvents:"auto",cursor:"pointer"}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),r(t,[{key:"update",value:function(e){this.icon.style.backgroundImage=!0===e?"url("+o.default+")":"url("+s.default+")"}}]),t}(n.default);t.default=c},3206:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.FRAME_BACK=void 0;var r=c(a(2754)),n=function(){function e(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,a,r){return a&&e(t.prototype,a),r&&e(t,r),t}}(),i=c(a(1075)),o=c(a(1298)),s=a(2498),l=a(5688);function c(e){return e&&e.__esModule?e:{default:e}}var u=function(e,t,a,r){return new(a||(a=Promise))((function(n,i){function o(e){try{l(r.next(e))}catch(e){i(e)}}function s(e){try{l(r.throw(e))}catch(e){i(e)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof a?t:new a((function(e){e(t)}))).then(o,s)}l((r=r.apply(e,t||[])).next())}))},h=t.FRAME_BACK=.22,d=!1,f=(0,l.isIosDevice)(),p=function(e){return new Promise((function(t){return setTimeout(t,e)}))},y=function(e){function t(e,a,n,i){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var c=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return c.controlUpdateCbs=[],c.data={play:!1,seek:0,t:0},c.shouldPlay=!1,c.lastTimeCalibrateCheck=0,c.isWaiting=!0,c.hasCalibrate=!1,c.destroyed=!1,c.isDragging=!1,c.isPlayingBeforeDrag=!1,c.playing=!0,c.isVideo=!0,c.onAttributeUpdated=function(e){return u(c,void 0,void 0,r.default.mark((function t(){var a,n,i,o;return r.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=e.play===this.v.paused,n=e.seek!==this.data.seek||e.t!==this.data.t,i=this.getCurrTimeFromData(e),n&&(this.data=e,this.setNativeCurrTime(e)),!a||(0,l.isVideoEnd)(i,this.duration())){t.next=9;break}return o=e.play,this.data=e,t.next=9,this.setNativePlayState(o);case 9:case"end":return t.stop()}}),t,this)})))},c.setNativePlayState=function(e){return u(c,void 0,void 0,r.default.mark((function t(){var a;return r.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=this.v,this.shouldPlay=e||!1,!(e&&a&&this.pageVisible&&this.playing)){t.next=24;break}return this.ui.children.controlBar.children.play.update(!1),t.prev=4,t.next=7,a.play();case 7:t.next=22;break;case 9:if(t.prev=9,t.t0=t.catch(4),!t.t0||""+t.t0.name!="NotAllowedError"&&""+t.t0.name!="AbortError"){t.next=22;break}if(!this.shouldPlay){t.next=22;break}return this.mute(!0,!1),t.prev=14,t.next=17,a.play();case 17:t.next=22;break;case 19:t.prev=19,t.t1=t.catch(14),this.getC2p().show();case 22:t.next=28;break;case 24:if(!a){t.next=28;break}return this.ui.children.controlBar.children.play.update(!0),t.next=28,a.pause();case 28:case"end":return t.stop()}}),t,this,[[4,9],[14,19]])})))},c.onNativePlayChange=function(e){e&&c.v.ended||(0,l.isVideoEnd)(c.v.currentTime,c.v.duration)?c.putAttributes({play:e,seek:0,t:c.ntpTime}):c.putAttributes({play:e,seek:c.v.currentTime,t:c.ntpTime})},c.onDurationChange=function(){c.onAttributeUpdated(c.data)},c.onTimeUpdate=function(){if(c.v){var e=c.ntpTime;if(null===c.lastTimeCalibrateCheck||e-c.lastTimeCalibrateCheck>5e3){var t=c.getCurrTimeFromData(c.data);Math.abs(t-c.v.currentTime)>1.2&&c.setNativeCurrTime(c.data),c.lastTimeCalibrateCheck=e}}},c.onPageInVisible=function(){c.v&&c.v.pause()},c.onPageVisible=function(){return u(c,void 0,void 0,r.default.mark((function e(){var t,a;return r.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t=this.hasMediaEnd(),this.data.hasOwnProperty("play")&&(a=this.data.play&&!t,this.setNativePlayState(a)),this.setNativeCurrTime(this.data);case 3:case"end":return e.stop()}}),e,this)})))},c.onMediaEnd=function(){c.ui.children.controlBar.children.play.update(!0)},c.onMediaWait=function(){c.isWaiting=!0},c.onMediaCanPlay=function(){clearTimeout(c.canPlayTimer),c.canPlayTimer=setTimeout((function(){if(c.isWaiting&&c.v){c.isWaiting=!1;var e=c.getCurrTimeFromData(c.data),t=Math.abs(e-c.v.currentTime);(t>1.5||!c.hasCalibrate&&t>.2)&&(c.hasCalibrate=!0,c.setNativeCurrTime(c.data))}}),30)},c.onMediaPause=function(){c.data.play&&setTimeout((function(){if(c.v){var e=c.v.duration-c.v.currentTime>1;c.data.play&&!c.destroyed&&e&&!c.isDragging&&c.pageVisible&&c.playing&&(c.setNativePlayState(c.data.play),c.setNativeCurrTime(c.data))}}),100)},c.handlePlayerPlay=function(){c.playing=!0,c.onAttributeUpdated(c.data)},c.handlePlayerPause=function(){c.playing=!1,c.v&&c.v.pause()},c.handlePlayerPlaySpeed=function(e){c.v.playbackRate=e},c.handlePlayerSeekTo=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;clearTimeout(c.seekToTimer),e>30||(c.v?c.playing?c.onAttributeUpdated(c.data):c.setNativeCurrTime(c.data):c.seekToTimer=setTimeout((function(){c.handlePlayerSeekTo(e+1)}),100))},c.handlePlayerDestroy=function(){c.destroy()},c.reader=e,c._id=n,c.v=a,c.v.controls=!1,c.v.muted=d,c.addMediaListener(),c.c=document.createElement("div"),c.c.id=c._id,c.controlVideo=i.controlVideo,c.isVideo=i.isVideo,c.ui=new o.default(c,"root",c.c),(0,s.initPlayerUI)(c,i),c.reader.drawPlugin&&(c.reader.drawPlugin.player&&(c.playing="play"===c.reader.drawPlugin.player.state),c.reader.drawPlugin.on("event:player:play",c.handlePlayerPlay),c.reader.drawPlugin.on("event:player:pause",c.handlePlayerPause),c.reader.drawPlugin.on("event:player:seekTo",c.handlePlayerSeekTo),c.reader.drawPlugin.on("event:player:playSpeed",c.handlePlayerPlaySpeed),c.reader.drawPlugin.on("event:player:destroy",c.handlePlayerDestroy),c.v.playbackRate=c.playSpeed),c}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),n(t,[{key:"id",value:function(){return this._id}},{key:"ready",value:function(e){var t=this;this.v.readyState>=2?e():(this.rdyCb=function(){t.v.readyState>=2&&e()},this.v.addEventListener("loadeddata",this.rdyCb))}},{key:"maximum",value:function(){if(this.c&&this.c.parentElement){var e=this.c.getBoundingClientRect(),t=this.c.parentElement.getBoundingClientRect();this.reader.emit("event:pptState:change","maxVideo",{left:(e.left-t.left)/t.width,top:(e.top-t.top)/t.height,w:e.width/t.width,h:e.height/t.height})}}},{key:"container",value:function(){return this.c}},{key:"destroy",value:function(){this.destroyed=!0,this.v.removeEventListener("timeupdate",this.onTimeUpdate),this.v.removeEventListener("ended",this.onMediaEnd),this.v.removeEventListener("waiting",this.onMediaWait),this.v.removeEventListener("canplay",this.onMediaCanPlay),this.v.removeEventListener("pause",this.onMediaPause),this.v.removeEventListener("durationchange",this.onDurationChange),"function"==typeof this.rdyCb&&this.v.removeEventListener("loadeddata",this.rdyCb),this.removeAllListeners(),this.c.parentNode&&this.c.parentNode.removeChild(this.c),this.reader.drawPlugin&&(this.reader.drawPlugin.off("event:player:play",this.handlePlayerPlay),this.reader.drawPlugin.off("event:player:pause",this.handlePlayerPause),this.reader.drawPlugin.off("event:player:seekTo",this.handlePlayerSeekTo),this.reader.drawPlugin.off("event:player:playSpeed",this.handlePlayerPlaySpeed),this.reader.drawPlugin.off("event:player:destroy",this.handlePlayerDestroy))}},{key:"hide",value:function(){this.c.style.display="none"}},{key:"show",value:function(){this.c.style.display="block"}},{key:"hideControl",value:function(){this.ui.children.controlBar.el.style.display="none"}},{key:"showControl",value:function(){this.ui.children.controlBar.el.style.display="block"}},{key:"onNativeSeek",value:function(e,t,a){var r=this.duration();if(a&&(this.isPlayingBeforeDrag=!this.v.paused),"number"==typeof r&&!isNaN(r)){var n=e*r;t&&this.v?(this.v.pause(),this.v.currentTime=n,this.isDragging=!0):this.v&&(this.isDragging=!1,this.setNativePlayState(this.data.play),this.controlVideo&&this.putAttributes({play:this.isPlayingBeforeDrag,seek:n,t:this.ntpTime}))}}},{key:"getC2p",value:function(){return this.ui.children.controlBar.children.c2p}},{key:"setNativeCurrTime",value:function(e){return u(this,void 0,void 0,r.default.mark((function t(){var a;return r.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a=this.getCurrTimeFromData(e),!this.paused()){t.next=6;break}if(!(Math.abs(this.v.currentTime-a)<h+.02)){t.next=4;break}return t.abrupt("return");case 4:t.next=8;break;case 6:if(!(Math.abs(this.v.currentTime-a)<1)){t.next=8;break}return t.abrupt("return");case 8:if(!this.v){t.next=13;break}if(!f){t.next=12;break}return t.next=12,p(300);case 12:isNaN(this.v.duration)||(this.v.currentTime=Math.min(a,this.v.duration-h));case 13:case"end":return t.stop()}}),t,this)})))}},{key:"getCurrTimeFromData",value:function(e){var t=0;e.play&&e.t&&e.hasOwnProperty("seek")?t=this.reader.drawPlugin&&this.reader.drawPlugin.isRecordPlayer?e.seek+(this.reader.drawPlugin.player.lastTime-e.t)/1e3:e.seek+(this.ntpTime-e.t)/1e3:e.hasOwnProperty("seek")&&(t=e.seek);var a=this.v.duration;return"number"!=typeof a||isNaN(a)||(t=a>2?Math.min(t,a-h):Math.min(t,a)),t}},{key:"putAttributes",value:function(e){var t=Object.assign(Object.assign({},this.data),e);this.onAttributeUpdated(t),this.emit("playerChange",t)}},{key:"currentTime",value:function(){return this.v.currentTime}},{key:"duration",value:function(){return this.v.duration}},{key:"paused",value:function(){return this.v.paused}},{key:"hasMediaEnd",value:function(){try{return!!this.data&&!!this.data.play&&this.data.seek+(this.ntpTime-this.data.t)/1e3-this.v.duration>0}catch(e){return!1}}},{key:"mute",value:function(e,t){d=e,this.v.muted=e,t&&this.onAttributeUpdated(this.data)}},{key:"muted",value:function(){return this.v.muted}},{key:"addControlUpdateCallback",value:function(e){this.controlUpdateCbs.push(e)}},{key:"updateControl",value:function(e){if(this.controlVideo!==e){this.controlVideo=e;var t=!0,a=!1,r=void 0;try{for(var n,i=this.controlUpdateCbs[Symbol.iterator]();!(t=(n=i.next()).done);t=!0)(0,n.value)()}catch(e){a=!0,r=e}finally{try{!t&&i.return&&i.return()}finally{if(a)throw r}}}}},{key:"addMediaListener",value:function(){this.v.addEventListener("timeupdate",this.onTimeUpdate),this.v.addEventListener("ended",this.onMediaEnd),this.v.addEventListener("waiting",this.onMediaWait),this.v.addEventListener("canplay",this.onMediaCanPlay),this.v.addEventListener("pause",this.onMediaPause),this.v.addEventListener("durationchange",this.onDurationChange)}},{key:"pageVisible",get:function(){return this.reader.mediaController.pageVisible&&this.reader.mediaController.containerVisible}},{key:"playSpeed",get:function(){return this.reader&&this.reader.drawPlugin&&this.reader.drawPlugin.player?this.reader.drawPlugin.player.playSpeed:1}},{key:"ntpTime",get:function(){return this.reader.ntpTime}}]),t}(i.default);t.default=y},2498:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.initPlayerUI=function(e,t){var a=(0,r.getPNode)(e.v);a&&a.appendChild(e.c),e.c.appendChild(e.v),t&&!t.isVideo&&t.audioImage&&(e.c.appendChild(t.audioImage),t.audioImage.addEventListener("click",(function(){e.controlVideo&&(e.paused()?e.onNativePlayChange(!0):e.onNativePlayChange(!1))}))),function(e){var t=e.ui.addChildComp(new i.default(e,"controlBar"));t.addChildComp(new l.default(e,"play")),t.addChildComp(new n.default(e,"progress",0)),t.addChildComp(new s.default(e,"time")),t.addChildComp(new o.default(e,"volume")),e.isVideo&&e.reader.videoFullIcon&&t.addChildComp(new u.default(e,"full")),t.addChildComp(new c.default(e,"c2p")),e.c.addEventListener("mouseover",(function(){t.show()})),e.c.addEventListener("mouseleave",(function(){t.fade()}))}(e),function(e){Object.assign(e.v.style,{width:"100%",height:"100%"}),e.v.controls=!1,e.c.style.position="fixed"}(e)};var r=a(5688),n=h(a(59)),i=h(a(4616)),o=h(a(3711)),s=h(a(9645)),l=h(a(1445)),c=h(a(611)),u=h(a(2815));function h(e){return e&&e.__esModule?e:{default:e}}},5688:function(e,t,a){Object.defineProperty(t,"__esModule",{value:!0}),t.isVideoEnd=t.isIosDevice=void 0,t.getPNode=function(e){return e.parentNode===document?null:e.parentNode};var r=a(3206);t.isIosDevice=function(){return/iPad|iPhone|iPod/.test(navigator.platform)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1},t.isVideoEnd=function(e,t){return!!isNaN(t)||t>=2&&t-e<=r.FRAME_BACK||t<2&&t-e<=.4}},5233:function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.WpConst={pgLeftpanel:249,VScrollerWidth:17,PageGap:10},t.ObjType={Text:0,Image:1,Path:2}},5311:function(e){e.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiPjxzdmcgdD0iMTY5MDM2MDM0NTU0NSIKICAgIGNsYXNzPSJpY29uIiB2aWV3Qm94PSIxNTAgMTUwIDcyNCA3MjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBwLWlkPSIyMjkzIgogICAgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCI+CiAgICA8cGF0aAogICAgICAgIGQ9Ik0yOTguNjY2NjY3IDU5Ny4zMzMzMzNoLTg1LjMzMzMzNHYyMTMuMzMzMzM0aDIxMy4zMzMzMzR2LTg1LjMzMzMzNGgtMTI4di0xMjh6IG0tODUuMzMzMzM0LTE3MC42NjY2NjZoODUuMzMzMzM0di0xMjhoMTI4di04NS4zMzMzMzRIMjEzLjMzMzMzM3YyMTMuMzMzMzM0eiBtNTEyIDI5OC42NjY2NjZoLTEyOHY4NS4zMzMzMzRoMjEzLjMzMzMzNFY1OTcuMzMzMzMzaC04NS4zMzMzMzR2MTI4eiBtLTEyOC01MTJ2ODUuMzMzMzM0aDEyOHYxMjhoODUuMzMzMzM0VjIxMy4zMzMzMzNINTk3LjMzMzMzM3oiCiAgICAgICAgcC1pZD0iMjI5NCIgZmlsbD0icmdiYSgwLDAsMCwuOCkiPjwvcGF0aD4KPC9zdmc+"},9934:function(e){e.exports="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgwIiBoZWlnaHQ9IjM2MCIgdmlld0JveD0iMCAwIDQ4MCAzNjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0wIDBINDgwVjM2MEgwVjBaIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTQ3OCAySDJWMzU4SDQ3OFYyWk0wIDBWMzYwSDQ4MFYwSDBaIiBmaWxsPSIjRDlEOURCIi8+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTY2LjI5NCAxMjZIMzE1TDMxNSAxODkuMzg2TDI5NS4wNDkgMTY3LjY0QzI5MS4xNDQgMTYzLjM4NCAyODQuNDU2IDE2My4zMTEgMjgwLjQ2IDE2Ny40ODNMMjM4Ljc5MiAyMTAuOTc3TDIxOS45MTcgMTkxLjU3MkMyMTYuNzg0IDE4OC4zNTEgMjExLjYxMyAxODguMzQxIDIwOC40NjggMTkxLjU1MUwxNjcuMzM4IDIzMy41MjlIMTY2LjI5NEwxNjYuMjk0IDEyNlpNMTY2LjI5NCAyNDEuNTI5QzE2NC40NzggMjQxLjUyOSAxNjIuODAzIDI0MC45MjQgMTYxLjQ2IDIzOS45MDRIMTYxLjA5MkwxNjEuMjUyIDIzOS43NDFDMTU5LjQ0NyAyMzguMjc0IDE1OC4yOTQgMjM2LjAzNiAxNTguMjk0IDIzMy41MjlWMTI2QzE1OC4yOTQgMTIxLjU4MiAxNjEuODc2IDExOCAxNjYuMjk0IDExOEgzMTVDMzE5LjQxOCAxMTggMzIzIDEyMS41ODIgMzIzIDEyNlYyMzMuNTI5QzMyMyAyMzcuOTQ4IDMxOS40MTggMjQxLjUyOSAzMTUgMjQxLjUyOUgxNjYuMjk0Wk0yMDEgMTcxLjgwNEMyMDkuMjg0IDE3MS44MDQgMjE2IDE2NS4wODggMjE2IDE1Ni44MDRDMjE2IDE0OC41MiAyMDkuMjg0IDE0MS44MDQgMjAxIDE0MS44MDRDMTkyLjcxNiAxNDEuODA0IDE4NiAxNDguNTIgMTg2IDE1Ni44MDRDMTg2IDE2NS4wODggMTkyLjcxNiAxNzEuODA0IDIwMSAxNzEuODA0WiIgZmlsbD0iI0U2RTdFQiIvPgo8L3N2Zz4K"},5525:function(e){e.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiPjxzdmcgdD0iMTYyMDg5MTk2NzgxNCIKICAgIGZpbGw9JyMwMDAwMDAnIGNsYXNzPSJpY29uIiB2aWV3Qm94PSIwIDAgMTAyNCAxMDI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgcC1pZD0iMTg3NCIKICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMzIiIGhlaWdodD0iMzIiPgogICAgPGRlZnM+CiAgICAgICAgPHN0eWxlIHR5cGU9InRleHQvY3NzIj48L3N0eWxlPgogICAgPC9kZWZzPgogICAgPHBhdGgKICAgICAgICBkPSJNODk5LjY0OCA1MzMuOTUybDEwMi40LTEwMi40YzE0LjYyNC0xNC42MjQgMTQuNjI0LTM2LjU3NiAwLTUxLjJzLTM2LjU3Ni0xNC42MjQtNTEuMiAwbC0xMDIuNCAxMDIuNC0xMDkuNzI4LTEwOS43MjhjLTcuMzI4LTcuMzI4LTI5LjI0OC0yMS45NTItNTEuMiAwYTM1LjM2IDM1LjM2IDAgMCAwIDAgNTEuMmwxMDIuNCAxMDIuNC0xMDIuNCAxMDkuNzI4Yy0xNC42MjQgMTQuNjI0LTE0LjYyNCAzNi41NzYgMCA1MS4yczM2LjU3NiAxNC42MjQgNTEuMiAwbDEwOS43MjgtMTAyLjQgMTAyLjQgMTAyLjRjMTQuNjI0IDE0LjYyNCAzNi41NzYgMTQuNjI0IDUxLjIgMHMxNC42MjQtMzYuNTc2IDAtNTEuMmwtMTAyLjQtMTAyLjR6IG0tNDAyLjI3Mi00NjAuOGMtNy4zMjggMC0xNC42MjQgMC0yMS45NTIgNy4zMjhMMTUzLjYgMzUxLjEwNEg3My4xNTJjLTI5LjI0OC03LjMyOC01MS4yIDIxLjk1Mi01MS4yIDUxLjJ2MjE5LjQyNGMwIDI5LjI0OCAyMS45NTIgNTguNTI4IDUxLjIgNTguNTI4IDAgMCA4Ny43NzYgMCA5NS4wNzIgNy4zMjhsMzA3LjIgMjYzLjMyOGgyMS45NTJjMjEuOTUyIDAgMzYuNTc2LTIxLjk1MiAzNi41NzYtNTEuMlYxMjQuMzg0YzAtMjkuMjQ4LTE0LjYyNC01MS4yLTM2LjU3Ni01MS4yeiIKICAgICAgICBwLWlkPSIxODc1Ij48L3BhdGg+Cjwvc3ZnPg=="},2891:function(e){e.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiPjxzdmcgdD0iMTYxNDE1MTk1MTk2MSIKICAgIGNsYXNzPSJpY29uIiB2aWV3Qm94PSItNjAwIC02MDAgMjIyNCAyMjI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgcC1pZD0iMjkwOCIKICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+CiAgICA8ZGVmcz4KICAgICAgICA8c3R5bGUgdHlwZT0idGV4dC9jc3MiPjwvc3R5bGU+CiAgICA8L2RlZnM+CiAgICA8cGF0aAogICAgICAgIGQ9Ik0yNTkuOTQ3OTI5MDkgMTMzLjkyMTg5M2gxMjYuMDI2MDM0ODFjMjMuMzM4MTU0NzMgMCA0Mi4wMDg2NzgyNyAxOC42NzA1MjM1MyA0Mi4wMDg2Nzk1NyA0Mi4wMDg2NzgyNnY2NzIuMTM4ODU3NDhjMCAyMy4zMzgxNTQ3My0xOC42NzA1MjM1MyA0Mi4wMDg2NzgyNy00Mi4wMDg2Nzk1NyA0Mi4wMDg2NzgyNkgyNTkuOTQ3OTI5MDljLTIzLjMzODE1NDczIDAtNDIuMDA4Njc4MjctMTguNjcwNTIzNTMtNDIuMDA4Njc5NTYtNDIuMDA4Njc4MjZWMTc1LjkzMDU3MTI2YzAtMjMuMzM4MTU0NzMgMTguNjcwNTIzNTMtNDIuMDA4Njc4MjcgNDIuMDA4Njc5NTYtNDIuMDA4Njc4MjZ6TTYzOC4wMjYwMzYxIDEzMy45MjE4OTNoMTI2LjAyNjAzNDgxYzIzLjMzODE1NDczIDAgNDIuMDA4Njc4MjcgMTguNjcwNTIzNTMgNDIuMDA4Njc5NTYgNDIuMDA4Njc4MjZ2NjcyLjEzODg1NzQ4YzAgMjMuMzM4MTU0NzMtMTguNjcwNTIzNTMgNDIuMDA4Njc4MjctNDIuMDA4Njc5NTYgNDIuMDA4Njc4MjZoLTEyNi4wMjYwMzQ4MWMtMjMuMzM4MTU0NzMgMC00Mi4wMDg2NzgyNy0xOC42NzA1MjM1My00Mi4wMDg2Nzk1Ny00Mi4wMDg2NzgyNlYxNzUuOTMwNTcxMjZjMC0yMy4zMzgxNTQ3MyAxOC42NzA1MjM1My00Mi4wMDg2NzgyNyA0Mi4wMDg2Nzk1Ny00Mi4wMDg2NzgyNnoiCiAgICAgICAgZmlsbD0iI2FhYWFhYSIgcC1pZD0iMjkwOSI+PC9wYXRoPgo8L3N2Zz4="},9929:function(e){e.exports="data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/Pgo8IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiPjxzdmcgdD0iMTYxNDE1MTkyNDIxNiIKICAgIGNsYXNzPSJpY29uIiB2aWV3Qm94PSItODAwIC04MDAgMjYyNCAyNjI0IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgcC1pZD0iMjEzMSIKICAgIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+CiAgICA8ZGVmcz4KICAgICAgICA8c3R5bGUgdHlwZT0idGV4dC9jc3MiPjwvc3R5bGU+CiAgICA8L2RlZnM+CiAgICA8cGF0aAogICAgICAgIGQ9Ik05MTIuNzI0ODg0IDQyOS4zNTU2ODFMMjA4Ljc5NzU0NSAxMy4xOTg2MzhDMTUxLjYwMzQ0OS0yMC41OTc4NzQgNjQuMDEyNDkgMTIuMTk4NzQxIDY0LjAxMjQ5IDk1Ljc5MDExMlY5MjcuOTA0MjE5YzAgNzQuOTkyMjU5IDgxLjM5MTU5OSAxMjAuMTg3NTk0IDE0NC43ODUwNTUgODIuNTkxNDc1bDcwMy45MjczMzktNDE1Ljk1NzA2NGM2Mi43OTM1MTgtMzYuOTk2MTgxIDYyLjk5MzQ5OC0xMjguMTg2NzY4IDAtMTY1LjE4Mjk0OXoiCiAgICAgICAgZmlsbD0iIzAwMDAwMCIgcC1pZD0iMjEzMiI+PC9wYXRoPgo8L3N2Zz4="},3057:function(e){e.exports="data:image/svg+xml;base64,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"}},t={};function a(r){var n=t[r];if(void 0!==n)return n.exports;var i=t[r]={id:r,loaded:!1,exports:{}};return e[r](i,i.exports,a),i.loaded=!0,i.exports}a.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e};var r={};return function(){var e=r,t=a(8678);e.default=t.PgReader}(),r.default}()}));
//# sourceMappingURL=pptRenderer.js.map

/** 
 Git Hash: 48dd7831b4af39fecc05ad2bf1ff16626441a531
 Create At: 4/26/2024, 4:24:57 PM
*/
